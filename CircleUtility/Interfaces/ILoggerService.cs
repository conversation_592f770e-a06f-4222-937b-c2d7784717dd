using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for logging services
    /// </summary>
    public interface ILoggerService
    {
        /// <summary>
        /// Logs a message with an exception
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="level">The log level</param>
        /// <param name="exception">The exception to log</param>
        void Log(string message, LogLevel level, Exception exception = null);

        /// <summary>
        /// Logs a message asynchronously
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="level">The log level</param>
        /// <param name="exception">The exception to log</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task LogAsync(string message, LogLevel level, Exception exception = null);

        /// <summary>
        /// Gets all log entries
        /// </summary>
        IReadOnlyList<LogEntry> LogEntries { get; }

        /// <summary>
        /// Gets or sets the log level
        /// </summary>
        LogLevel LogLevel { get; set; }

        /// <summary>
        /// Clears all log entries
        /// </summary>
        void ClearLogs();

        /// <summary>
        /// Gets log entries filtered by level
        /// </summary>
        /// <param name="level">The log level</param>
        /// <returns>The filtered log entries</returns>
        IEnumerable<LogEntry> GetLogsByLevel(LogLevel level);

        /// <summary>
        /// Gets log entries filtered by date range
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <returns>The filtered log entries</returns>
        List<LogEntry> GetLogsByDateRange(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets log entries filtered by search text
        /// </summary>
        /// <param name="searchText">The search text</param>
        /// <returns>The filtered log entries</returns>
        List<LogEntry> GetLogsBySearchText(string searchText);

        /// <summary>
        /// Exports logs to a file
        /// </summary>
        /// <param name="filePath">The file path</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> ExportLogsAsync(string filePath);

        /// <summary>
        /// Event raised when a log entry is added
        /// </summary>
        event EventHandler<Models.LogEntryEventArgs> LogEntryAdded;

        /// <summary>
        /// Gets the log file path
        /// </summary>
        /// <returns>The log file path</returns>
        string GetLogFilePath();
    }
}
