<UserControl x:Class="CircleUtility.Controls.AnimatedButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="150">
    
    <UserControl.Resources>
        <!-- Glow animation -->
        <Storyboard x:Key="GlowAnimation">
            <DoubleAnimation 
                Storyboard.TargetName="GlowBorder" 
                Storyboard.TargetProperty="Opacity"
                From="0" To="1" Duration="0:0:0.2" />
        </Storyboard>
        
        <!-- Unglow animation -->
        <Storyboard x:Key="UnglowAnimation">
            <DoubleAnimation 
                Storyboard.TargetName="GlowBorder" 
                Storyboard.TargetProperty="Opacity"
                From="1" To="0" Duration="0:0:0.2" />
        </Storyboard>
        
        <!-- Click animation -->
        <Storyboard x:Key="ClickAnimation">
            <DoubleAnimation 
                Storyboard.TargetName="MainButton" 
                Storyboard.TargetProperty="RenderTransform.ScaleX"
                From="1" To="0.95" Duration="0:0:0.1" 
                AutoReverse="True" />
            <DoubleAnimation 
                Storyboard.TargetName="MainButton" 
                Storyboard.TargetProperty="RenderTransform.ScaleY"
                From="1" To="0.95" Duration="0:0:0.1" 
                AutoReverse="True" />
        </Storyboard>
        
        <!-- Success animation -->
        <Storyboard x:Key="SuccessAnimation">
            <ColorAnimation 
                Storyboard.TargetName="GlowEffect" 
                Storyboard.TargetProperty="Color"
                To="#00C853" Duration="0:0:0.3" />
            <ColorAnimation 
                Storyboard.TargetName="ButtonBorder" 
                Storyboard.TargetProperty="BorderBrush.(SolidColorBrush.Color)"
                To="#00C853" Duration="0:0:0.3" />
            <DoubleAnimation 
                Storyboard.TargetName="GlowBorder" 
                Storyboard.TargetProperty="Opacity"
                To="1" Duration="0:0:0.2" />
            <DoubleAnimation 
                Storyboard.TargetName="SuccessIcon" 
                Storyboard.TargetProperty="Opacity"
                From="0" To="1" Duration="0:0:0.3" />
            <DoubleAnimation 
                Storyboard.TargetName="ButtonText" 
                Storyboard.TargetProperty="Opacity"
                To="0" Duration="0:0:0.2" />
        </Storyboard>
        
        <!-- Reset animation -->
        <Storyboard x:Key="ResetAnimation">
            <ColorAnimation 
                Storyboard.TargetName="GlowEffect" 
                Storyboard.TargetProperty="Color"
                To="#00C8FF" Duration="0:0:0.3" />
            <ColorAnimation 
                Storyboard.TargetName="ButtonBorder" 
                Storyboard.TargetProperty="BorderBrush.(SolidColorBrush.Color)"
                To="#00C8FF" Duration="0:0:0.3" />
            <DoubleAnimation 
                Storyboard.TargetName="GlowBorder" 
                Storyboard.TargetProperty="Opacity"
                To="0" Duration="0:0:0.2" />
            <DoubleAnimation 
                Storyboard.TargetName="SuccessIcon" 
                Storyboard.TargetProperty="Opacity"
                To="0" Duration="0:0:0.2" />
            <DoubleAnimation 
                Storyboard.TargetName="ButtonText"
                Storyboard.TargetProperty="Opacity"
                To="1" Duration="0:0:0.2" />
        </Storyboard>
    </UserControl.Resources>
    
    <Grid>
        <Button x:Name="MainButton"
                Width="{Binding ElementName=UserControl, Path=Width}"
                Height="{Binding ElementName=UserControl, Path=Height}"
                Background="#FF001428"
                BorderBrush="#FF00C8FF"
                BorderThickness="1"
                Cursor="Hand"
                Click="MainButton_Click"
                MouseEnter="MainButton_MouseEnter"
                MouseLeave="MainButton_MouseLeave"
                RenderTransformOrigin="0.5,0.5">
            <Button.RenderTransform>
                <ScaleTransform ScaleX="1" ScaleY="1" />
            </Button.RenderTransform>
            <Button.Template>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Outer glow effect -->
                        <Border x:Name="GlowBorder"
                                Background="#3300C8FF"
                                CornerRadius="3"
                                Margin="-2"
                                Opacity="0">
                            <Border.Effect>
                                <DropShadowEffect x:Name="GlowEffect"
                                                  Color="#00C8FF"
                                                  BlurRadius="10"
                                                  ShadowDepth="0"
                                                  Opacity="0.8"/>
                            </Border.Effect>
                        </Border>
                        
                        <!-- Button background -->
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                            
                            <!-- Button content -->
                            <Grid>
                                <TextBlock x:Name="ButtonText"
                                           Text="{Binding ElementName=UserControl, Path=ButtonText}"
                                           FontFamily="Consolas"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                                
                                <!-- Success icon (checkmark) -->
                                <Path x:Name="SuccessIcon"
                                      Data="M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"
                                      Fill="White"
                                      Width="16"
                                      Height="16"
                                      Stretch="Uniform"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Opacity="0"/>
                            </Grid>
                        </Border>
                    </Grid>
                    
                    <!-- Visual states -->
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="#FF002A4E"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="#FF003A6B"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5"/>
                            <Setter TargetName="ButtonText" Property="Foreground" Value="#FF666666"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Button.Template>
        </Button>
    </Grid>
</UserControl>
