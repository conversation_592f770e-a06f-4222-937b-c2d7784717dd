// Created by Arsenal on 5-17-25 12:15PM
using System;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for log entries
    /// </summary>
    public class LogEntryModel
    {
        /// <summary>
        /// Gets or sets the timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the log level
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// Gets or sets the exception
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// Gets the formatted timestamp
        /// </summary>
        public string FormattedTimestamp => Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");

        /// <summary>
        /// Gets the log level as string
        /// </summary>
        public string LevelString => Level.ToString();

        /// <summary>
        /// Gets the log level color
        /// </summary>
        public string LevelColor
        {
            get
            {
                switch (Level)
                {
                    case LogLevel.ERROR:
                        return "#FF0000"; // Red
                    case LogLevel.WARNING:
                        return "#FFA500"; // Orange
                    case LogLevel.SUCCESS:
                        return "#00FF00"; // Green
                    case LogLevel.INFO:
                        return "#00C8FF"; // Blue
                    case LogLevel.DEBUG:
                        return "#AAAAAA"; // Gray
                    // case LogLevel.VERBOSE: // VERBOSE is not in the LogLevel enum in Enums.cs, commenting out
                    //    return "#666666"; // Dark Gray
                    default:
                        return "#FFFFFF"; // White
                }
            }
        }

        /// <summary>
        /// Gets the exception message
        /// </summary>
        public string ExceptionMessage => Exception?.Message;

        /// <summary>
        /// Gets the exception stack trace
        /// </summary>
        public string ExceptionStackTrace => Exception?.StackTrace;

        /// <summary>
        /// Gets a value indicating whether the log entry has an exception
        /// </summary>
        public bool HasException => Exception != null;

        /// <summary>
        /// Creates a new instance of the LogEntryModel class from a LogEntry
        /// </summary>
        /// <param name="logEntry">The log entry</param>
        /// <returns>The log entry model</returns>
        public static LogEntryModel FromLogEntry(Models.LogEntry logEntry)
        {
            return new LogEntryModel
            {
                Timestamp = logEntry.Timestamp,
                Message = logEntry.Message,
                Level = logEntry.Level,
                Exception = logEntry.Exception
            };
        }
    }
}




