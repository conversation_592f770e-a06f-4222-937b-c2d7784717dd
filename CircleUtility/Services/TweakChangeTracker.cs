using System.Collections.Generic;
using CircleUtility.Interfaces;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for tracking tweak changes
    /// </summary>
    public class TweakChangeTracker : ITweakChangeTracker
    {
        private static TweakChangeTracker _instance;
        private static readonly object _lock = new object();
        private readonly List<TweakHistoryEntry> _trackedChanges;

        /// <summary>
        /// Gets the singleton instance of the tweak change tracker
        /// </summary>
        public static TweakChangeTracker Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TweakChangeTracker();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private TweakChangeTracker()
        {
            _trackedChanges = new List<TweakHistoryEntry>();
        }

        /// <summary>
        /// Tracks a tweak change
        /// </summary>
        /// <param name="tweak">The tweak that was changed</param>
        public void TrackChange(TweakHistoryEntry tweak)
        {
            _trackedChanges.Add(tweak);
        }

        /// <summary>
        /// Gets all tracked changes
        /// </summary>
        /// <returns>List of tracked changes</returns>
        public List<TweakHistoryEntry> GetTrackedChanges()
        {
            return new List<TweakHistoryEntry>(_trackedChanges);
        }

        /// <summary>
        /// Clears all tracked changes
        /// </summary>
        public void ClearTrackedChanges()
        {
            _trackedChanges.Clear();
        }

        /// <summary>
        /// Gets active changes
        /// </summary>
        /// <returns>List of active changes</returns>
        public List<TweakHistoryEntry> GetActiveChanges()
        {
            return new List<TweakHistoryEntry>(_trackedChanges);
        }

        /// <summary>
        /// Gets changes by time period
        /// </summary>
        /// <param name="timePeriod">Time period to filter by</param>
        /// <returns>List of changes in the time period</returns>
        public List<TweakHistoryEntry> GetChangesByTimePeriod(object timePeriod)
        {
            return new List<TweakHistoryEntry>(_trackedChanges);
        }
    }
}
