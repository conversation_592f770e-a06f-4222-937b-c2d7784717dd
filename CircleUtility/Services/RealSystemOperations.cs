using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Principal;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using CircleUtility.Interfaces;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Implementation of ISystemOperations that performs real system operations
    /// </summary>
    public class RealSystemOperations : ISystemOperations
    {
        private readonly LoggingService _logger;
        private readonly Dictionary<string, object> _pendingChanges;

        /// <summary>
        /// Initializes a new instance of the RealSystemOperations class
        /// </summary>
        public RealSystemOperations()
        {
            _logger = LoggingService.Instance;
            _pendingChanges = new Dictionary<string, object>();
        }

        /// <summary>
        /// Checks if the application is running with administrator privileges
        /// </summary>
        /// <returns>True if running as administrator, false otherwise</returns>
        public bool IsRunningAsAdmin()
        {
            try
            {
                using (WindowsIdentity identity = WindowsIdentity.GetCurrent())
                {
                    WindowsPrincipal principal = new WindowsPrincipal(identity);
                    return principal.IsInRole(WindowsBuiltInRole.Administrator);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking admin status: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restarts the application with administrator privileges
        /// </summary>
        /// <param name="commandLineArgs">Optional command line arguments to pass to the new process</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RestartAsAdmin(string commandLineArgs = "")
        {
            try
            {
                // Check if already running as admin
                if (IsRunningAsAdmin())
                {
                    return true;
                }

                // Get the executable path
                string exePath = Process.GetCurrentProcess().MainModule.FileName;

                // Create process start info
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    Arguments = commandLineArgs,
                    UseShellExecute = true,
                    Verb = "runas" // Run as administrator
                };

                // Start the process
                Process.Start(startInfo);

                // Exit the current process
                Environment.Exit(0);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restarting as admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Executes a function that requires administrator privileges
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="showPrompt">Whether to show a UAC prompt if not running as admin</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ExecuteAsAdmin(Action action, bool showPrompt = true)
        {
            try
            {
                // Check if running as admin
                if (IsRunningAsAdmin())
                {
                    // Execute the action
                    action();
                    return true;
                }
                else if (showPrompt)
                {
                    // Show UAC prompt
                    MessageBoxResult result = MessageBox.Show(
                        "This operation requires administrator privileges. Do you want to restart the application as administrator?",
                        "Administrator Privileges Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // Restart as admin
                        return RestartAsAdmin();
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error executing as admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Executes a function that requires administrator privileges asynchronously
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="showPrompt">Whether to show a UAC prompt if not running as admin</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public async Task<bool> ExecuteAsAdminAsync(Func<Task> action, bool showPrompt = true)
        {
            try
            {
                // Check if running as admin
                if (IsRunningAsAdmin())
                {
                    // Execute the action
                    await action();
                    return true;
                }
                else if (showPrompt)
                {
                    // Show UAC prompt
                    MessageBoxResult result = MessageBox.Show(
                        "This operation requires administrator privileges. Do you want to restart the application as administrator?",
                        "Administrator Privileges Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // Restart as admin
                        return RestartAsAdmin();
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error executing as admin asynchronously: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Runs a process with administrator privileges
        /// </summary>
        /// <param name="fileName">The process file name</param>
        /// <param name="arguments">The process arguments</param>
        /// <param name="waitForExit">Whether to wait for the process to exit</param>
        /// <returns>The process exit code, or -1 if an error occurred</returns>
        public int RunProcessAsAdmin(string fileName, string arguments, bool waitForExit = true)
        {
            try
            {
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = fileName,
                    Arguments = arguments,
                    Verb = "runas", // Run as administrator
                    UseShellExecute = true,
                    CreateNoWindow = true
                };

                using (Process process = Process.Start(psi))
                {
                    if (waitForExit)
                    {
                        process.WaitForExit();
                        return process.ExitCode;
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running process as admin: {ex.Message}", LogLevel.ERROR);
                return -1;
            }
        }

        /// <summary>
        /// Sets a registry value
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <param name="value">The value to set</param>
        /// <param name="valueKind">The value kind</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool SetRegistryValue(string keyPath, string valueName, object value, RegistryValueKind valueKind)
        {
            try
            {
                // Track the change
                string fullPath = $"{keyPath}\\{valueName}";
                _pendingChanges[fullPath] = value;

                // Determine the root key
                RegistryKey rootKey = GetRootKey(keyPath);
                if (rootKey == null)
                {
                    _logger.Log($"Invalid registry key path: {keyPath}", LogLevel.ERROR);
                    return false;
                }

                // Get the subkey path
                string subKeyPath = GetSubKeyPath(keyPath);

                // Open or create the key
                using (RegistryKey key = rootKey.OpenSubKey(subKeyPath, true))
                {
                    if (key == null)
                    {
                        _logger.Log($"Failed to open registry key: {keyPath}", LogLevel.ERROR);
                        return false;
                    }

                    // Set the value
                    key.SetValue(valueName, value, valueKind);
                    _logger.Log($"Set registry value: {keyPath}\\{valueName} = {value}", LogLevel.INFO);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error setting registry value: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a registry value
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <returns>The registry value, or null if not found</returns>
        public object GetRegistryValue(string keyPath, string valueName)
        {
            try
            {
                // Determine the root key
                RegistryKey rootKey = GetRootKey(keyPath);
                if (rootKey == null)
                {
                    _logger.Log($"Invalid registry key path: {keyPath}", LogLevel.ERROR);
                    return null;
                }

                // Get the subkey path
                string subKeyPath = GetSubKeyPath(keyPath);

                // Open the key
                using (RegistryKey key = rootKey.OpenSubKey(subKeyPath, false))
                {
                    if (key == null)
                    {
                        _logger.Log($"Failed to open registry key: {keyPath}", LogLevel.ERROR);
                        return null;
                    }

                    // Get the value
                    return key.GetValue(valueName);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting registry value: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Deletes a registry value
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool DeleteRegistryValue(string keyPath, string valueName)
        {
            try
            {
                // Track the change
                string fullPath = $"{keyPath}\\{valueName}";
                _pendingChanges[fullPath] = null; // null indicates deletion

                // Determine the root key
                RegistryKey rootKey = GetRootKey(keyPath);
                if (rootKey == null)
                {
                    _logger.Log($"Invalid registry key path: {keyPath}", LogLevel.ERROR);
                    return false;
                }

                // Get the subkey path
                string subKeyPath = GetSubKeyPath(keyPath);

                // Open the key
                using (RegistryKey key = rootKey.OpenSubKey(subKeyPath, true))
                {
                    if (key == null)
                    {
                        _logger.Log($"Failed to open registry key: {keyPath}", LogLevel.ERROR);
                        return false;
                    }

                    // Delete the value
                    key.DeleteValue(valueName, false);
                    _logger.Log($"Deleted registry value: {keyPath}\\{valueName}", LogLevel.INFO);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error deleting registry value: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Creates a registry key
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool CreateRegistryKey(string keyPath)
        {
            try
            {
                // Track the change
                _pendingChanges[keyPath] = "KEY_CREATED";

                // Determine the root key
                RegistryKey rootKey = GetRootKey(keyPath);
                if (rootKey == null)
                {
                    _logger.Log($"Invalid registry key path: {keyPath}", LogLevel.ERROR);
                    return false;
                }

                // Get the subkey path
                string subKeyPath = GetSubKeyPath(keyPath);

                // Create the key
                using (RegistryKey key = rootKey.CreateSubKey(subKeyPath))
                {
                    if (key == null)
                    {
                        _logger.Log($"Failed to create registry key: {keyPath}", LogLevel.ERROR);
                        return false;
                    }

                    _logger.Log($"Created registry key: {keyPath}", LogLevel.INFO);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating registry key: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Deletes a registry key
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool DeleteRegistryKey(string keyPath)
        {
            try
            {
                // Track the change
                _pendingChanges[keyPath] = "KEY_DELETED";

                // Determine the root key
                RegistryKey rootKey = GetRootKey(keyPath);
                if (rootKey == null)
                {
                    _logger.Log($"Invalid registry key path: {keyPath}", LogLevel.ERROR);
                    return false;
                }

                // Get the subkey path
                string subKeyPath = GetSubKeyPath(keyPath);

                // Delete the key
                rootKey.DeleteSubKey(subKeyPath, false);
                _logger.Log($"Deleted registry key: {keyPath}", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error deleting registry key: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Checks if a registry key exists
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>True if the key exists, false otherwise</returns>
        public bool RegistryKeyExists(string keyPath)
        {
            try
            {
                // Determine the root key
                RegistryKey rootKey = GetRootKey(keyPath);
                if (rootKey == null)
                {
                    _logger.Log($"Invalid registry key path: {keyPath}", LogLevel.ERROR);
                    return false;
                }

                // Get the subkey path
                string subKeyPath = GetSubKeyPath(keyPath);

                // Check if the key exists
                using (RegistryKey key = rootKey.OpenSubKey(subKeyPath, false))
                {
                    return key != null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking if registry key exists: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Checks if a registry value exists
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <returns>True if the value exists, false otherwise</returns>
        public bool RegistryValueExists(string keyPath, string valueName)
        {
            try
            {
                // Determine the root key
                RegistryKey rootKey = GetRootKey(keyPath);
                if (rootKey == null)
                {
                    _logger.Log($"Invalid registry key path: {keyPath}", LogLevel.ERROR);
                    return false;
                }

                // Get the subkey path
                string subKeyPath = GetSubKeyPath(keyPath);

                // Check if the value exists
                using (RegistryKey key = rootKey.OpenSubKey(subKeyPath, false))
                {
                    if (key == null)
                    {
                        return false;
                    }

                    return key.GetValue(valueName) != null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking if registry value exists: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets all changes that would be made in dev mode
        /// </summary>
        /// <returns>A dictionary of changes</returns>
        public Dictionary<string, object> GetPendingChanges()
        {
            return new Dictionary<string, object>(_pendingChanges);
        }

        /// <summary>
        /// Clears all pending changes
        /// </summary>
        public void ClearPendingChanges()
        {
            _pendingChanges.Clear();
        }

        /// <summary>
        /// Gets the root registry key from a path
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>The root registry key</returns>
        private RegistryKey GetRootKey(string keyPath)
        {
            if (keyPath.StartsWith("HKEY_LOCAL_MACHINE") || keyPath.StartsWith("HKLM"))
            {
                return Registry.LocalMachine;
            }
            else if (keyPath.StartsWith("HKEY_CURRENT_USER") || keyPath.StartsWith("HKCU"))
            {
                return Registry.CurrentUser;
            }
            else if (keyPath.StartsWith("HKEY_CLASSES_ROOT") || keyPath.StartsWith("HKCR"))
            {
                return Registry.ClassesRoot;
            }
            else if (keyPath.StartsWith("HKEY_USERS") || keyPath.StartsWith("HKU"))
            {
                return Registry.Users;
            }
            else if (keyPath.StartsWith("HKEY_CURRENT_CONFIG") || keyPath.StartsWith("HKCC"))
            {
                return Registry.CurrentConfig;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Gets the subkey path from a full registry path
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>The subkey path</returns>
        private string GetSubKeyPath(string keyPath)
        {
            if (keyPath.StartsWith("HKEY_LOCAL_MACHINE\\") || keyPath.StartsWith("HKLM\\"))
            {
                return keyPath.Replace("HKEY_LOCAL_MACHINE\\", "").Replace("HKLM\\", "");
            }
            else if (keyPath.StartsWith("HKEY_CURRENT_USER\\") || keyPath.StartsWith("HKCU\\"))
            {
                return keyPath.Replace("HKEY_CURRENT_USER\\", "").Replace("HKCU\\", "");
            }
            else if (keyPath.StartsWith("HKEY_CLASSES_ROOT\\") || keyPath.StartsWith("HKCR\\"))
            {
                return keyPath.Replace("HKEY_CLASSES_ROOT\\", "").Replace("HKCR\\", "");
            }
            else if (keyPath.StartsWith("HKEY_USERS\\") || keyPath.StartsWith("HKU\\"))
            {
                return keyPath.Replace("HKEY_USERS\\", "").Replace("HKU\\", "");
            }
            else if (keyPath.StartsWith("HKEY_CURRENT_CONFIG\\") || keyPath.StartsWith("HKCC\\"))
            {
                return keyPath.Replace("HKEY_CURRENT_CONFIG\\", "").Replace("HKCC\\", "");
            }
            else
            {
                return keyPath;
            }
        }
    }
}
