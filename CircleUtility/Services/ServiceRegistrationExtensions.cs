using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Extension methods for service registration
    /// </summary>
    public static class ServiceRegistrationExtensions
    {
        /// <summary>
        /// Registers all application services
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection RegisterApplicationServices(this IServiceCollection services)
        {
            // Register core services
            services.AddSingleton<ILoggerService>(provider => LoggingService.Instance);
            services.AddSingleton<IConfigurationService>(provider => ConfigurationManager.Instance);
            
            // Register hardware services
            services.AddSingleton<IHardwareDetectionService>(provider => HardwareDetectionService.Instance);
            services.AddSingleton<IHardwareCompatibilityService>(provider => HardwareCompatibilityService.Instance);
            services.AddSingleton<IHardwareRecommendationService>(provider => HardwareRecommendationService.Instance);
            services.AddSingleton<IPerformanceMonitoringService>(provider => PerformanceMonitoringService.Instance);
            services.AddSingleton<IHardwareDetectionBadgeService>(provider => HardwareDetectionBadgeService.Instance);
            
            // Register optimization services
            services.AddSingleton<ISystemOptimizationService>(provider => SystemOptimizationService.Instance);
            
            // Register tracking services
            services.AddSingleton<IUserTrackingService>(provider => UserTrackingService.Instance);
            services.AddSingleton<ITweakChangeTracker>(provider => TweakChangeTracker.Instance);
            
            // Register communication services
            services.AddSingleton<IDiscordService>(provider => DiscordService.Instance);
            
            // Register utility services
            services.AddSingleton<ISecurityService>(provider => SecurityService.Instance);
            
            return services;
        }

        /// <summary>
        /// Registers hardware optimization services
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection RegisterHardwareOptimizationServices(this IServiceCollection services)
        {
            services.AddSingleton<IHardwareDetectionService>(provider => HardwareDetectionService.Instance);
            services.AddSingleton<IHardwareCompatibilityService>(provider => HardwareCompatibilityService.Instance);
            services.AddSingleton<IHardwareRecommendationService>(provider => HardwareRecommendationService.Instance);
            services.AddSingleton<IPerformanceMonitoringService>(provider => PerformanceMonitoringService.Instance);
            
            return services;
        }

        /// <summary>
        /// Registers Discord services
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection RegisterDiscordServices(this IServiceCollection services)
        {
            services.AddSingleton<IDiscordService>(provider => DiscordService.Instance);
            services.AddSingleton<EnhancedDiscordUserService>(provider => EnhancedDiscordUserService.Instance);
            
            return services;
        }
    }
}
