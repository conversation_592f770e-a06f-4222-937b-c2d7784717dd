using System;
using System.Globalization;
using System.Windows.Data;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts a boolean value to a "Yes" or "No" string
    /// </summary>
    public class BoolToYesNoConverter : IValueConverter
    {
        /// <summary>
        /// Converts a boolean value to a "Yes" or "No" string
        /// </summary>
        /// <param name="value">The boolean value to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>"Yes" if true, "No" if false</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? "Yes" : "No";
            }
            
            return "No";
        }

        /// <summary>
        /// Converts a "Yes" or "No" string back to a boolean value
        /// </summary>
        /// <param name="value">The string value to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>True if "Yes", false otherwise</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.Equals("Yes", StringComparison.OrdinalIgnoreCase);
            }
            
            return false;
        }
    }
}
