using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for QuickActionPanel.xaml
    /// </summary>
    public partial class QuickActionPanel : UserControl
    {
        private readonly Storyboard _buttonHoverAnimation;
        private readonly Storyboard _buttonLeaveAnimation;
        private readonly Storyboard _buttonClickAnimation;

        /// <summary>
        /// Event raised when the optimize performance button is clicked
        /// </summary>
        public event EventHandler OptimizePerformanceClicked;

        /// <summary>
        /// Event raised when the optimize GPU button is clicked
        /// </summary>
        public event EventHandler OptimizeGpuClicked;

        /// <summary>
        /// Event raised when the optimize network button is clicked
        /// </summary>
        public event EventHandler OptimizeNetworkClicked;

        /// <summary>
        /// Event raised when the clean system button is clicked
        /// </summary>
        public event EventHandler CleanSystemClicked;

        /// <summary>
        /// Event raised when the optimize input button is clicked
        /// </summary>
        public event EventHandler OptimizeInputClicked;

        /// <summary>
        /// Event raised when the revert tweaks button is clicked
        /// </summary>
        public event EventHandler RevertTweaksClicked;

        /// <summary>
        /// Event raised when the tweak system button is clicked
        /// </summary>
        public event EventHandler TweakSystemClicked;

        /// <summary>
        /// Event raised when the test notifications button is clicked
        /// </summary>
        public event EventHandler TestNotificationsClicked;

        /// <summary>
        /// Initializes a new instance of the QuickActionPanel class
        /// </summary>
        public QuickActionPanel()
        {
            InitializeComponent();

            // Get animations from resources
            _buttonHoverAnimation = (Storyboard)FindResource("ButtonHoverAnimation");
            _buttonLeaveAnimation = (Storyboard)FindResource("ButtonLeaveAnimation");
            _buttonClickAnimation = (Storyboard)FindResource("ButtonClickAnimation");
        }

        private void OptimizePerformanceButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizePerformanceButton);
            OptimizePerformanceClicked?.Invoke(this, EventArgs.Empty);
        }

        private void OptimizeGpuButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizeGpuButton);
            OptimizeGpuClicked?.Invoke(this, EventArgs.Empty);
        }

        private void OptimizeNetworkButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizeNetworkButton);
            OptimizeNetworkClicked?.Invoke(this, EventArgs.Empty);
        }

        private void CleanSystemButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(CleanSystemButton);
            CleanSystemClicked?.Invoke(this, EventArgs.Empty);
        }

        private void OptimizeInputButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizeInputButton);
            OptimizeInputClicked?.Invoke(this, EventArgs.Empty);
        }

        private void RevertTweaksButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(RevertTweaksButton);
            RevertTweaksClicked?.Invoke(this, EventArgs.Empty);
        }

        private void TweakSystemButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(TweakSystemButton);
            TweakSystemClicked?.Invoke(this, EventArgs.Empty);
        }

        private void TestNotificationsButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(TestNotificationsButton);
            TestNotificationsClicked?.Invoke(this, EventArgs.Empty);
        }

        private void Button_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (sender is Button button)
            {
                // Clone the animation to avoid conflicts
                Storyboard animation = _buttonHoverAnimation.Clone();
                animation.Begin(button);
            }
        }

        private void Button_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (sender is Button button)
            {
                // Clone the animation to avoid conflicts
                Storyboard animation = _buttonLeaveAnimation.Clone();
                animation.Begin(button);
            }
        }

        private void PlayClickAnimation(Button button)
        {
            // Set initial scale
            button.RenderTransform = new System.Windows.Media.ScaleTransform(0.95, 0.95);

            // Clone the animation to avoid conflicts
            Storyboard animation = _buttonClickAnimation.Clone();
            animation.Begin(button);
        }
    }
}
