using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a user activity
    /// </summary>
    public class UserActivity
    {
        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username { get; set; }
        
        /// <summary>
        /// Gets or sets the action
        /// </summary>
        public string Action { get; set; }
        
        /// <summary>
        /// Gets or sets the timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// Gets or sets the details
        /// </summary>
        public string Details { get; set; }
    }
}
