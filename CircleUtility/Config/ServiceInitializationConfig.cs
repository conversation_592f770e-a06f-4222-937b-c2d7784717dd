using System;
using System.Collections.Generic;
using CircleUtility.Services;

namespace CircleUtility.Config
{
    public static class ServiceInitializationConfig
    {
        // Define service initialization order
        public static readonly Type[] InitializationOrder = new[]
        {
            typeof(LoggingService),           // Must be first
            typeof(ConfigurationManager),      // Must be second
            typeof(ExceptionHandlingService),  // Must be third
            typeof(SecurityService),
            typeof(HardwareDetectionService),
            typeof(InputValidationService),
            typeof(WindowLoadingManager),
            typeof(DiscordBotService),
            typeof(CompatibilityTestingService),
            typeof(DocumentationService)
        };

        // Define critical services that should prevent app startup if they fail
        public static readonly HashSet<Type> CriticalServices = new()
        {
            typeof(LoggingService),
            typeof(ConfigurationManager),
            typeof(ExceptionHandlingService),
            typeof(SecurityService)
        };

        // Define service dependencies
        public static readonly Dictionary<Type, Type[]> ServiceDependencies = new()
        {
            { typeof(DiscordBotService), new[] { typeof(LoggingService), typeof(SecurityService) } },
            { typeof(CompatibilityTestingService), new[] { typeof(HardwareDetectionService) } },
            { typeof(WindowLoadingManager), new[] { typeof(LoggingService), typeof(ConfigurationManager) } }
        };
    }
} 