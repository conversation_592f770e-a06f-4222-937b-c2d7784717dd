using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Shapes;

namespace CircleUtility.Controls
{
    /// <summary>
    /// A custom control that draws an arc.
    /// </summary>
    public class Arc : Shape
    {
        public static readonly DependencyProperty StartAngleProperty =
            DependencyProperty.Register(nameof(StartAngle), typeof(double), typeof(Arc),
                new FrameworkPropertyMetadata(0.0, FrameworkPropertyMetadataOptions.AffectsRender));

        public static readonly DependencyProperty EndAngleProperty =
            DependencyProperty.Register(nameof(EndAngle), typeof(double), typeof(Arc),
                new FrameworkPropertyMetadata(90.0, FrameworkPropertyMetadataOptions.AffectsRender));

        /// <summary>
        /// Gets or sets the start angle in degrees.
        /// </summary>
        public double StartAngle
        {
            get => (double)GetValue(StartAngleProperty);
            set => SetValue(StartAngleProperty, value);
        }

        /// <summary>
        /// Gets or sets the end angle in degrees.
        /// </summary>
        public double EndAngle
        {
            get => (double)GetValue(EndAngleProperty);
            set => SetValue(EndAngleProperty, value);
        }

        protected override Geometry DefiningGeometry
        {
            get
            {
                double width = Math.Max(0, ActualWidth - StrokeThickness);
                double height = Math.Max(0, ActualHeight - StrokeThickness);

                double centerX = StrokeThickness / 2 + width / 2;
                double centerY = StrokeThickness / 2 + height / 2;

                double radiusX = width / 2;
                double radiusY = height / 2;

                double startAngleRadians = StartAngle * Math.PI / 180.0;
                double endAngleRadians = EndAngle * Math.PI / 180.0;

                Point startPoint = new(
                    centerX + radiusX * Math.Cos(startAngleRadians),
                    centerY + radiusY * Math.Sin(startAngleRadians));

                Point endPoint = new(
                    centerX + radiusX * Math.Cos(endAngleRadians),
                    centerY + radiusY * Math.Sin(endAngleRadians));

                bool isLargeArc = Math.Abs(NormalizeAngle(EndAngle) - NormalizeAngle(StartAngle)) > 180;

                StreamGeometry geometry = new();
                using (StreamGeometryContext context = geometry.Open())
                {
                    context.BeginFigure(startPoint, false, false);
                    context.ArcTo(endPoint, new Size(radiusX, radiusY), 0,
                        isLargeArc, SweepDirection.Clockwise, true, false);
                }

                geometry.Freeze();
                return geometry;
            }
        }

        private static double NormalizeAngle(double angle)
        {
            angle %= 360;
            return angle < 0 ? angle + 360 : angle;
        }
    }
}
