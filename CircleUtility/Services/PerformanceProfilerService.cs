// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for profiling application performance
    /// </summary>
    public class PerformanceProfilerService : IDisposable
    {
        private readonly LoggingService _logger;
        private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
        private readonly ConcurrentDictionary<string, Stopwatch> _activeStopwatches;
        private readonly ConcurrentDictionary<string, List<TimeSpan>> _metricHistory;
        private readonly int _maxHistorySize = 100;
        private bool _isEnabled;

        /// <summary>
        /// Event raised when a performance metric is recorded
        /// </summary>
        public event EventHandler<PerformanceMetricEventArgs> MetricRecorded;

        /// <summary>
        /// Initializes a new instance of the PerformanceProfilerService class
        /// </summary>
        public PerformanceProfilerService(LoggingService logger)
        {
            _logger = logger;
            _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
            _activeStopwatches = new ConcurrentDictionary<string, Stopwatch>();
            _metricHistory = new ConcurrentDictionary<string, List<TimeSpan>>();
            _isEnabled = true;

            _logger.Log("Performance profiler service initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets or sets a value indicating whether profiling is enabled
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                _isEnabled = value;
                _logger.Log($"Performance profiling {(_isEnabled ? "enabled" : "disabled")}", LogLevel.INFO);
            }
        }

        /// <summary>
        /// Gets all performance metrics
        /// </summary>
        public IEnumerable<PerformanceMetric> Metrics => _metrics.Values;

        /// <summary>
        /// Starts profiling a section of code
        /// </summary>
        /// <param name="name">The name of the section</param>
        /// <param name="category">The category of the section</param>
        /// <returns>A unique identifier for the profiling session</returns>
        public string StartProfiling(string name, string category = "General")
        {
            if (!_isEnabled)
            {
                return null;
            }

            try
            {
                string id = $"{name}_{Guid.NewGuid()}";
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();
                _activeStopwatches[id] = stopwatch;

                _logger.Log($"Started profiling: {name}", LogLevel.DEBUG);
                return id;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error starting profiling: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Stops profiling a section of code
        /// </summary>
        /// <param name="id">The identifier returned by StartProfiling</param>
        /// <param name="name">The name of the section</param>
        /// <param name="category">The category of the section</param>
        public void StopProfiling(string id, string name, string category = "General")
        {
            if (!_isEnabled || id == null)
            {
                return;
            }

            try
            {
                if (_activeStopwatches.TryRemove(id, out Stopwatch stopwatch))
                {
                    stopwatch.Stop();
                    TimeSpan elapsed = stopwatch.Elapsed;

                    // Create or update metric
                    PerformanceMetric metric = _metrics.GetOrAdd(name, new PerformanceMetric
                    {
                        Name = name,
                        Category = category,
                        CallCount = 0,
                        TotalTime = TimeSpan.Zero,
                        MinTime = elapsed,
                        MaxTime = elapsed,
                        AverageTime = elapsed
                    });

                    // Update metric
                    metric.CallCount++;
                    metric.TotalTime += elapsed;
                    metric.MinTime = elapsed < metric.MinTime ? elapsed : metric.MinTime;
                    metric.MaxTime = elapsed > metric.MaxTime ? elapsed : metric.MaxTime;
                    metric.AverageTime = TimeSpan.FromTicks(metric.TotalTime.Ticks / metric.CallCount);
                    metric.LastTime = elapsed;

                    // Add to history
                    List<TimeSpan> history = _metricHistory.GetOrAdd(name, new List<TimeSpan>());
                    lock (history)
                    {
                        history.Add(elapsed);
                        if (history.Count > _maxHistorySize)
                        {
                            history.RemoveAt(0);
                        }
                    }

                    // Raise event
                    OnMetricRecorded(metric);

                    _logger.Log($"Profiling result for {name}: {elapsed.TotalMilliseconds:F2}ms", LogLevel.DEBUG);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error stopping profiling: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Profiles an action
        /// </summary>
        /// <param name="name">The name of the action</param>
        /// <param name="action">The action to profile</param>
        /// <param name="category">The category of the action</param>
        public void ProfileAction(string name, Action action, string category = "General")
        {
            if (!_isEnabled)
            {
                action();
                return;
            }

            string id = StartProfiling(name, category);
            try
            {
                action();
            }
            finally
            {
                StopProfiling(id, name, category);
            }
        }

        /// <summary>
        /// Profiles an asynchronous function
        /// </summary>
        /// <typeparam name="T">The return type of the function</typeparam>
        /// <param name="name">The name of the function</param>
        /// <param name="func">The function to profile</param>
        /// <param name="category">The category of the function</param>
        /// <returns>The result of the function</returns>
        public async Task<T> ProfileFunctionAsync<T>(string name, Func<Task<T>> func, string category = "General")
        {
            if (!_isEnabled)
            {
                return await func();
            }

            string id = StartProfiling(name, category);
            try
            {
                return await func();
            }
            finally
            {
                StopProfiling(id, name, category);
            }
        }

        /// <summary>
        /// Gets the performance history for a metric
        /// </summary>
        /// <param name="name">The name of the metric</param>
        /// <returns>The performance history</returns>
        public IEnumerable<TimeSpan> GetMetricHistory(string name)
        {
            if (_metricHistory.TryGetValue(name, out List<TimeSpan> history))
            {
                lock (history)
                {
                    return history.ToList();
                }
            }
            return Enumerable.Empty<TimeSpan>();
        }

        /// <summary>
        /// Clears all performance metrics
        /// </summary>
        public void ClearMetrics()
        {
            _metrics.Clear();
            _metricHistory.Clear();
            _logger.Log("Performance metrics cleared", LogLevel.INFO);
        }

        /// <summary>
        /// Raises the MetricRecorded event
        /// </summary>
        /// <param name="metric">The performance metric</param>
        private void OnMetricRecorded(PerformanceMetric metric)
        {
            MetricRecorded?.Invoke(this, new PerformanceMetricEventArgs(metric));
        }

        /// <summary>
        /// Disposes the performance profiler service
        /// </summary>
        public void Dispose()
        {
            try
            {
                // Clear all metrics
                _metrics.Clear();
                _metricHistory.Clear();
                _activeStopwatches.Clear();

                _logger.Log("Performance profiler service disposed", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disposing performance profiler service: {ex.Message}", LogLevel.ERROR);
            }
        }
    }
}
