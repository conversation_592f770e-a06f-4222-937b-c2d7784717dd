<Window x:Class="CircleUtility.SecurityDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CircleUtility"
        xmlns:views="clr-namespace:CircleUtility.Views"
        mc:Ignorable="d"
        Title="Security Settings" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#222222"
        ResizeMode="CanResize"
        MinWidth="600"
        MinHeight="400">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#111111" Padding="20,10">
            <StackPanel>
                <TextBlock Text="Security Settings" 
                           FontSize="24" 
                           FontWeight="Bold" 
                           Foreground="#00C8FF"/>
                <TextBlock Text="Configure security options and user management" 
                           FontSize="12" 
                           Foreground="#CCCCCC" 
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Content -->
        <views:SecuritySettingsView Grid.Row="1" x:Name="SecuritySettingsView"/>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#111111" Padding="20,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="Close" 
                        Width="100" 
                        Height="30" 
                        Click="CloseButton_Click"
                        Style="{StaticResource StandardButton}">
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>

