using System;
using System.Collections.Generic;

namespace CircleUtility.Models
{
    public class UserLogin
    {
        public string Username { get; set; }
        public int LoginCount { get; set; }
        public DateTime FirstLogin { get; set; }
        public DateTime LastLogin { get; set; }
        public List<string> IpAddresses { get; set; } = new List<string>();
        public string LastIpAddress { get; set; }
        public string HardwareFingerprint { get; set; }
        public bool IsLocked { get; set; }
        public string LockReason { get; set; }
        public DateTime? LockDate { get; set; }
    }
} 