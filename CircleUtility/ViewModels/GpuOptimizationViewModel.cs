using CircleUtility.Commands;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using CircleUtility.Helpers;
using CircleUtility.Models;
using CircleUtility.Services;
using Microsoft.Win32;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for GPU optimization settings
    /// </summary>
    public class GpuOptimizationViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly IUserTrackingService _userTracking;
        private readonly SystemOptimizationService _systemOptimization;
        private ObservableCollection<SystemTweak> _gpuTweaks;
        private string _selectedGpuType;
        private string _statusMessage;
        private bool _isStatusVisible;
        private bool _isStatusSuccess;
        private bool _isNvidiaSelected;
        private bool _isAmdSelected;

        /// <summary>
        /// Initializes a new instance of the GpuOptimizationViewModel class
        /// </summary>
        public GpuOptimizationViewModel(IUserTrackingService userTracking)
        {
            _logger = LoggingService.Instance;
            _userTracking = userTracking;
            _systemOptimization = SystemOptimizationService.Instance;

            // Initialize collections
            GpuTweaks = new ObservableCollection<SystemTweak>();

            // Initialize commands
            ApplyTweakCommand = new RelayCommand<SystemTweak>(ApplyTweak);
            RevertTweakCommand = new RelayCommand<SystemTweak>(RevertTweak);
            ApplyAllTweaksCommand = new RelayCommand(ApplyAllTweaks);
            RevertAllTweaksCommand = new RelayCommand(RevertAllTweaks);
            DetectGpuCommand = new RelayCommand(DetectGpu);
            SelectNvidiaCommand = new RelayCommand(SelectNvidia);
            SelectAmdCommand = new RelayCommand(SelectAmd);

            // Initialize properties
            SelectedGpuType = "Auto-Detect";
            StatusMessage = "";
            IsStatusVisible = false;
            IsStatusSuccess = false;
            IsNvidiaSelected = true;
            IsAmdSelected = false;

            // Initialize tweaks
            InitializeGpuTweaks();

            // Auto-detect GPU
            DetectGpu();

            // Show initial status
            ShowStatus("GPU Optimization ready. Select tweaks to apply.", true);

            _logger.Log("GpuOptimizationViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of GPU tweaks
        /// </summary>
        public ObservableCollection<SystemTweak> GpuTweaks
        {
            get => _gpuTweaks;
            private set => SetProperty(ref _gpuTweaks, value);
        }

        /// <summary>
        /// Gets the collection of GPU types
        /// </summary>
        public ObservableCollection<string> GpuTypes { get; } = new ObservableCollection<string>
        {
            "Auto-Detect",
            "NVIDIA",
            "AMD",
            "Other"
        };

        /// <summary>
        /// Gets or sets the selected GPU type
        /// </summary>
        public string SelectedGpuType
        {
            get => _selectedGpuType;
            set
            {
                if (SetProperty(ref _selectedGpuType, value))
                {
                    UpdateGpuTweaks();
                }
            }
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is a success
        /// </summary>
        public bool IsStatusSuccess
        {
            get => _isStatusSuccess;
            set => SetProperty(ref _isStatusSuccess, value);
        }

        /// <summary>
        /// Gets the command to apply a tweak
        /// </summary>
        public ICommand ApplyTweakCommand { get; }

        /// <summary>
        /// Gets the command to revert a tweak
        /// </summary>
        public ICommand RevertTweakCommand { get; }

        /// <summary>
        /// Gets the command to apply all tweaks
        /// </summary>
        public ICommand ApplyAllTweaksCommand { get; }

        /// <summary>
        /// Gets the command to revert all tweaks
        /// </summary>
        public ICommand RevertAllTweaksCommand { get; }

        /// <summary>
        /// Gets the command to detect the GPU
        /// </summary>
        public ICommand DetectGpuCommand { get; }

        /// <summary>
        /// Gets the command to select NVIDIA GPU
        /// </summary>
        public ICommand SelectNvidiaCommand { get; }

        /// <summary>
        /// Gets the command to select AMD GPU
        /// </summary>
        public ICommand SelectAmdCommand { get; }

        /// <summary>
        /// Gets or sets a value indicating whether NVIDIA is selected
        /// </summary>
        public bool IsNvidiaSelected
        {
            get => _isNvidiaSelected;
            set => SetProperty(ref _isNvidiaSelected, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether AMD is selected
        /// </summary>
        public bool IsAmdSelected
        {
            get => _isAmdSelected;
            set => SetProperty(ref _isAmdSelected, value);
        }

        /// <summary>
        /// Initializes the GPU tweaks
        /// </summary>
        private void InitializeGpuTweaks()
        {
            // Call UpdateGpuTweaks to populate the tweaks based on the selected GPU type
            UpdateGpuTweaks();

            _logger.Log("GPU tweaks initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Updates the GPU tweaks based on the selected GPU type
        /// </summary>
        private void UpdateGpuTweaks()
        {
            try
            {
                _logger.Log($"Updating GPU tweaks for {SelectedGpuType}", LogLevel.INFO);

                // Clear all tweaks
                GpuTweaks.Clear();

                // Add common tweaks first
                GpuTweaks.Add(new SystemTweak
                {
                    Name = "Maximum Performance Power Mode",
                    Description = "Sets the GPU power management mode to prefer maximum performance.",
                    Category = "Common",
                    ApplyAction = () => SetMaximumPerformancePowerMode(),
                    RevertAction = () => RevertPowerMode(),
                    RequiresAdmin = false
                });

                GpuTweaks.Add(new SystemTweak
                {
                    Name = "Disable Fullscreen Optimizations",
                    Description = "Disables Windows fullscreen optimizations to reduce input lag.",
                    Category = "Common",
                    ApplyAction = () => DisableFullscreenOptimizations(),
                    RevertAction = () => EnableFullscreenOptimizations(),
                    RequiresAdmin = false
                });

                GpuTweaks.Add(new SystemTweak
                {
                    Name = "Set Pre-rendered Frames to 1",
                    Description = "Limits pre-rendered frames to 1 for reduced input lag.",
                    Category = "Common",
                    ApplyAction = () => SetPrerenderedFrames(),
                    RevertAction = () => RevertPrerenderedFrames(),
                    RequiresAdmin = false
                });

                // Add GPU-specific tweaks
                if (SelectedGpuType == "NVIDIA" || IsNvidiaSelected)
                {
                    AddNvidiaSpecificTweaks();
                    IsNvidiaSelected = true;
                    IsAmdSelected = false;
                }
                else if (SelectedGpuType == "AMD" || IsAmdSelected)
                {
                    AddAmdSpecificTweaks();
                    IsNvidiaSelected = false;
                    IsAmdSelected = true;
                }

                _logger.Log("GPU tweaks updated successfully", LogLevel.SUCCESS);
                ShowStatus($"Updated tweaks for {SelectedGpuType} GPU", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating GPU tweaks: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error updating GPU tweaks", false);
            }
        }

        /// <summary>
        /// Adds NVIDIA-specific tweaks
        /// </summary>
        private void AddNvidiaSpecificTweaks()
        {
            GpuTweaks.Add(new SystemTweak
            {
                Name = "Enable NVIDIA Reflex",
                Description = "Enables NVIDIA Reflex Low Latency mode for supported games.",
                Category = "NVIDIA",
                ApplyAction = () => EnableNvidiaReflex(),
                RevertAction = () => DisableNvidiaReflex(),
                RequiresAdmin = false
            });

            GpuTweaks.Add(new SystemTweak
            {
                Name = "Optimize Shader Cache",
                Description = "Optimizes NVIDIA shader cache settings for better performance.",
                Category = "NVIDIA",
                ApplyAction = () => OptimizeShaderCache(),
                RevertAction = () => RevertShaderCache(),
                RequiresAdmin = false
            });

            GpuTweaks.Add(new SystemTweak
            {
                Name = "Set Threaded Optimization to On",
                Description = "Enables threaded optimization for better CPU utilization.",
                Category = "NVIDIA",
                ApplyAction = () => EnableThreadedOptimization(),
                RevertAction = () => DisableThreadedOptimization(),
                RequiresAdmin = false
            });

            GpuTweaks.Add(new SystemTweak
            {
                Name = "Set Low Latency Mode to Ultra",
                Description = "Sets NVIDIA Low Latency Mode to Ultra for minimum input lag.",
                Category = "NVIDIA",
                ApplyAction = () => SetLowLatencyModeUltra(),
                RevertAction = () => RevertLowLatencyMode(),
                RequiresAdmin = false
            });
        }

        /// <summary>
        /// Adds AMD-specific tweaks
        /// </summary>
        private void AddAmdSpecificTweaks()
        {
            GpuTweaks.Add(new SystemTweak
            {
                Name = "Enable AMD Anti-Lag",
                Description = "Enables AMD Anti-Lag for reduced input latency.",
                Category = "AMD",
                ApplyAction = () => EnableAmdAntiLag(),
                RevertAction = () => DisableAmdAntiLag(),
                RequiresAdmin = false
            });

            GpuTweaks.Add(new SystemTweak
            {
                Name = "Optimize Radeon Chill",
                Description = "Configures Radeon Chill for optimal performance.",
                Category = "AMD",
                ApplyAction = () => OptimizeRadeonChill(),
                RevertAction = () => RevertRadeonChill(),
                RequiresAdmin = false
            });

            GpuTweaks.Add(new SystemTweak
            {
                Name = "Set Texture Filtering Quality to Performance",
                Description = "Sets texture filtering quality to performance mode.",
                Category = "AMD",
                ApplyAction = () => SetTextureFilteringPerformance(),
                RevertAction = () => RevertTextureFiltering(),
                RequiresAdmin = false
            });

            GpuTweaks.Add(new SystemTweak
            {
                Name = "Disable Frame Rate Target Control",
                Description = "Disables Frame Rate Target Control for maximum performance.",
                Category = "AMD",
                ApplyAction = () => DisableFrameRateTargetControl(),
                RevertAction = () => EnableFrameRateTargetControl(),
                RequiresAdmin = false
            });
        }

        // Implementation of GPU tweak methods

        private void SetMaximumPerformancePowerMode()
        {
            _logger.Log("Setting GPU power mode to maximum performance", LogLevel.INFO);
            // Implementation would go here
        }

        private void RevertPowerMode()
        {
            _logger.Log("Reverting GPU power mode", LogLevel.INFO);
            // Implementation would go here
        }

        private void DisableFullscreenOptimizations()
        {
            _logger.Log("Disabling fullscreen optimizations", LogLevel.INFO);
            // Implementation would go here
        }

        private void EnableFullscreenOptimizations()
        {
            _logger.Log("Enabling fullscreen optimizations", LogLevel.INFO);
            // Implementation would go here
        }

        private void SetPrerenderedFrames()
        {
            _logger.Log("Setting pre-rendered frames to 1", LogLevel.INFO);
            // Implementation would go here
        }

        private void RevertPrerenderedFrames()
        {
            _logger.Log("Reverting pre-rendered frames setting", LogLevel.INFO);
            // Implementation would go here
        }

        private void EnableNvidiaReflex()
        {
            _logger.Log("Enabling NVIDIA Reflex", LogLevel.INFO);
            // Implementation would go here
        }

        private void DisableNvidiaReflex()
        {
            _logger.Log("Disabling NVIDIA Reflex", LogLevel.INFO);
            // Implementation would go here
        }

        private void OptimizeShaderCache()
        {
            _logger.Log("Optimizing shader cache", LogLevel.INFO);
            // Implementation would go here
        }

        private void RevertShaderCache()
        {
            _logger.Log("Reverting shader cache settings", LogLevel.INFO);
            // Implementation would go here
        }

        private void EnableThreadedOptimization()
        {
            _logger.Log("Enabling threaded optimization", LogLevel.INFO);
            // Implementation would go here
        }

        private void DisableThreadedOptimization()
        {
            _logger.Log("Disabling threaded optimization", LogLevel.INFO);
            // Implementation would go here
        }

        private void SetLowLatencyModeUltra()
        {
            _logger.Log("Setting low latency mode to Ultra", LogLevel.INFO);
            // Implementation would go here
        }

        private void RevertLowLatencyMode()
        {
            _logger.Log("Reverting low latency mode", LogLevel.INFO);
            // Implementation would go here
        }

        private void EnableAmdAntiLag()
        {
            _logger.Log("Enabling AMD Anti-Lag", LogLevel.INFO);
            // Implementation would go here
        }

        private void DisableAmdAntiLag()
        {
            _logger.Log("Disabling AMD Anti-Lag", LogLevel.INFO);
            // Implementation would go here
        }

        private void OptimizeRadeonChill()
        {
            _logger.Log("Optimizing Radeon Chill", LogLevel.INFO);
            // Implementation would go here
        }

        private void RevertRadeonChill()
        {
            _logger.Log("Reverting Radeon Chill settings", LogLevel.INFO);
            // Implementation would go here
        }

        private void SetTextureFilteringPerformance()
        {
            _logger.Log("Setting texture filtering to performance", LogLevel.INFO);
            // Implementation would go here
        }

        private void RevertTextureFiltering()
        {
            _logger.Log("Reverting texture filtering settings", LogLevel.INFO);
            // Implementation would go here
        }

        private void DisableFrameRateTargetControl()
        {
            _logger.Log("Disabling Frame Rate Target Control", LogLevel.INFO);
            // Implementation would go here
        }

        private void EnableFrameRateTargetControl()
        {
            _logger.Log("Enabling Frame Rate Target Control", LogLevel.INFO);
            // Implementation would go here
        }

        /// <summary>
        /// Detects the GPU type and updates the UI accordingly
        /// </summary>
        private void DetectGpu()
        {
            try
            {
                _logger.Log("Detecting GPU type...", LogLevel.INFO);
                ShowStatus("Detecting GPU...", true);

                // Get the GPU name using WMI
                string gpuName = GetGpuName();

                if (gpuName == "Unknown")
                {
                    _logger.Log("Could not detect GPU, defaulting to NVIDIA", LogLevel.WARNING);
                    SelectedGpuType = "NVIDIA";
                    IsNvidiaSelected = true;
                    IsAmdSelected = false;
                    ShowStatus("Could not detect GPU, defaulting to NVIDIA", false);
                    return;
                }

                // Determine GPU type based on name
                if (gpuName.Contains("NVIDIA", StringComparison.OrdinalIgnoreCase) ||
                    gpuName.Contains("GeForce", StringComparison.OrdinalIgnoreCase))
                {
                    SelectedGpuType = "NVIDIA";
                    IsNvidiaSelected = true;
                    IsAmdSelected = false;
                    _logger.Log($"Detected NVIDIA GPU: {gpuName}", LogLevel.SUCCESS);
                }
                else if (gpuName.Contains("AMD", StringComparison.OrdinalIgnoreCase) ||
                         gpuName.Contains("Radeon", StringComparison.OrdinalIgnoreCase) ||
                         gpuName.Contains("ATI", StringComparison.OrdinalIgnoreCase))
                {
                    SelectedGpuType = "AMD";
                    IsNvidiaSelected = false;
                    IsAmdSelected = true;
                    _logger.Log($"Detected AMD GPU: {gpuName}", LogLevel.SUCCESS);
                }
                else
                {
                    // For Intel or other GPUs, default to NVIDIA UI but show correct name
                    SelectedGpuType = "Other";
                    IsNvidiaSelected = true; // Default to NVIDIA UI
                    IsAmdSelected = false;
                    _logger.Log($"Detected other GPU type: {gpuName}, defaulting to NVIDIA UI", LogLevel.INFO);
                }

                // Update the tweaks based on the selected GPU type
                UpdateGpuTweaks();

                // Show success message with GPU details
                ShowStatus($"Detected GPU: {gpuName}", true);
                _userTracking.AddUserActivity("User", "GPU Optimization", $"Detected GPU: {gpuName}");
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting GPU: {ex.Message}", LogLevel.ERROR);
                _logger.Log($"Stack trace: {ex.StackTrace}", LogLevel.ERROR);
                ShowStatus("Error detecting GPU. Please try again.", false);

                // Default to NVIDIA in case of error
                SelectedGpuType = "NVIDIA";
                IsNvidiaSelected = true;
                IsAmdSelected = false;
            }
        }

        /// <summary>
        /// Gets the GPU name using WMI
        /// </summary>
        /// <returns>The GPU name</returns>
        private string GetGpuName()
        {
            try
            {
                _logger.Log("Getting GPU name using WMI...", LogLevel.INFO);

                // Use WMI to get the GPU information
                using (var searcher = new System.Management.ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                {
                    var gpus = new List<string>();
                    foreach (var obj in searcher.Get())
                    {
                        string name = obj["Name"].ToString();
                        // Skip duplicates
                        if (!gpus.Contains(name))
                        {
                            gpus.Add(name);
                            _logger.Log($"Found GPU: {name}", LogLevel.INFO);
                        }
                    }

                    // Prioritize dedicated GPUs (NVIDIA, AMD) over integrated ones
                    var dedicatedGpu = gpus.FirstOrDefault(g =>
                        g.Contains("NVIDIA", StringComparison.OrdinalIgnoreCase) ||
                        g.Contains("AMD", StringComparison.OrdinalIgnoreCase) ||
                        g.Contains("Radeon", StringComparison.OrdinalIgnoreCase) ||
                        g.Contains("GeForce", StringComparison.OrdinalIgnoreCase));

                    if (!string.IsNullOrEmpty(dedicatedGpu))
                    {
                        _logger.Log($"Selected dedicated GPU: {dedicatedGpu}", LogLevel.INFO);
                        return dedicatedGpu;
                    }
                    else if (gpus.Count > 0)
                    {
                        _logger.Log($"No dedicated GPU found, using first available: {gpus[0]}", LogLevel.INFO);
                        return gpus[0];
                    }
                }

                _logger.Log("No GPU found, returning Unknown", LogLevel.WARNING);
                return "Unknown";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting GPU name: {ex.Message}", LogLevel.ERROR);
                _logger.Log($"Stack trace: {ex.StackTrace}", LogLevel.ERROR);
                return "Unknown";
            }
        }

        /// <summary>
        /// Applies a tweak
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        private void ApplyTweak(SystemTweak tweak)
        {
            if (tweak == null)
            {
                return;
            }

            try
            {
                _logger.Log($"Applying GPU tweak: {tweak.Name}", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "GPU Optimization", $"Applied tweak: {tweak.Name}");

                tweak.ApplyAction?.Invoke();
                bool result = true;

                if (result)
                {
                    ShowStatus($"Applied: {tweak.Name}", true);
                    _logger.Log($"GPU tweak applied successfully: {tweak.Name}", LogLevel.SUCCESS);
                }
                else
                {
                    ShowStatus($"Failed to apply: {tweak.Name}", false);
                    _logger.Log($"Failed to apply GPU tweak: {tweak.Name}", LogLevel.ERROR);
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Error: {ex.Message}", false);
                _logger.Log($"Error applying GPU tweak: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Reverts a tweak
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        private void RevertTweak(SystemTweak tweak)
        {
            if (tweak == null)
            {
                return;
            }

            try
            {
                _logger.Log($"Reverting GPU tweak: {tweak.Name}", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "GPU Optimization", $"Reverted tweak: {tweak.Name}");

                tweak.RevertAction?.Invoke();
                bool result = true;

                if (result)
                {
                    ShowStatus($"Reverted: {tweak.Name}", true);
                    _logger.Log($"GPU tweak reverted successfully: {tweak.Name}", LogLevel.SUCCESS);
                }
                else
                {
                    ShowStatus($"Failed to revert: {tweak.Name}", false);
                    _logger.Log($"Failed to revert GPU tweak: {tweak.Name}", LogLevel.ERROR);
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Error: {ex.Message}", false);
                _logger.Log($"Error reverting GPU tweak: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Applies all tweaks
        /// </summary>
        public void ApplyAllTweaks()
        {
            try
            {
                _logger.Log("Applying all GPU tweaks", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "GPU Optimization", "Applied all tweaks");

                int successCount = 0;
                int failCount = 0;

                foreach (var tweak in GpuTweaks)
                {
                    tweak.ApplyAction?.Invoke();
                    bool result = true;
                    if (result)
                    {
                        successCount++;
                    }
                    else
                    {
                        failCount++;
                    }
                }

                ShowStatus($"Applied {successCount} tweaks, {failCount} failed", successCount > 0 && failCount == 0);
                _logger.Log($"Applied {successCount} GPU tweaks, {failCount} failed", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                ShowStatus($"Error: {ex.Message}", false);
                _logger.Log($"Error applying all GPU tweaks: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Reverts all tweaks
        /// </summary>
        public void RevertAllTweaks()
        {
            try
            {
                _logger.Log("Reverting all GPU tweaks", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "GPU Optimization", "Reverted all tweaks");

                int successCount = 0;
                int failCount = 0;

                foreach (var tweak in GpuTweaks)
                {
                    tweak.RevertAction?.Invoke();
                    bool result = true;
                    if (result)
                    {
                        successCount++;
                    }
                    else
                    {
                        failCount++;
                    }
                }

                ShowStatus($"Reverted {successCount} tweaks, {failCount} failed", successCount > 0 && failCount == 0);
                _logger.Log($"Reverted {successCount} GPU tweaks, {failCount} failed", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                ShowStatus($"Error: {ex.Message}", false);
                _logger.Log($"Error reverting all GPU tweaks: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Selects NVIDIA GPU
        /// </summary>
        private void SelectNvidia()
        {
            IsNvidiaSelected = true;
            IsAmdSelected = false;
            SelectedGpuType = "NVIDIA";
            UpdateGpuTweaks();
            ShowStatus("NVIDIA GPU selected", true);
        }

        /// <summary>
        /// Selects AMD GPU
        /// </summary>
        private void SelectAmd()
        {
            IsNvidiaSelected = false;
            IsAmdSelected = true;
            SelectedGpuType = "AMD";
            UpdateGpuTweaks();
            ShowStatus("AMD GPU selected", true);
        }

        /// <summary>
        /// Shows a status message
        /// </summary>
        /// <param name="message">The message to show</param>
        /// <param name="isSuccess">Whether the message is a success message</param>
        private void ShowStatus(string message, bool isSuccess)
        {
            StatusMessage = message;
            IsStatusSuccess = isSuccess;
            IsStatusVisible = true;

            // Hide the status after 5 seconds
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(5);
            timer.Tick += (sender, e) =>
            {
                IsStatusVisible = false;
                timer.Stop();
            };
            timer.Start();
        }
    }
}


