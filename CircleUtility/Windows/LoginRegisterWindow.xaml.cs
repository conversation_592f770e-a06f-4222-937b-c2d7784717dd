using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.Windows
{
    /// <summary>
    /// Interaction logic for LoginRegisterWindow.xaml
    /// </summary>
    public partial class LoginRegisterWindow : Window
    {
        private readonly SecurityService _securityService;
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTrackingService;
        private readonly HardwareFingerprintService _hardwareFingerprintService;
        private readonly SessionManager _sessionManager;
        private readonly string _credentialsFilePath;
        private readonly Storyboard _fadeInAnimation;
        private readonly Storyboard _slideInAnimation;
        private readonly Storyboard _slideOutAnimation;
        private readonly Storyboard _pulseAnimation;
        private readonly Storyboard _loadingAnimation;
        private bool _isLoading;

        // Loading sequence fields
        private DispatcherTimer _loadingTimer;
        private int _currentLoadingStep;
        private readonly string[] _loadingMessages = new string[]
        {
            "INITIALIZING CIRCLE UTILITY...",
            "LOADING HARDWARE SERVICES...",
            "SCANNING SYSTEM CONFIGURATION...",
            "OPTIMIZING PERFORMANCE SETTINGS...",
            "CONFIGURING SECURITY PROTOCOLS...",
            "PREPARING USER INTERFACE...",
            "FINALIZING SETUP...",
            "SYSTEM READY"
        };

        /// <summary>
        /// Gets a value indicating whether the login was successful
        /// </summary>
        public bool LoginSuccessful { get; private set; }

        /// <summary>
        /// Gets the username that was used to log in
        /// </summary>
        public string Username { get; private set; }

        /// <summary>
        /// Gets the session token
        /// </summary>
        public string SessionToken { get; private set; }

        /// <summary>
        /// Gets or sets a value indicating whether the login is in progress
        /// </summary>
        private bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                LoadingOverlay.Visibility = value ? Visibility.Visible : Visibility.Collapsed;

                if (value)
                {
                    // Make sure the loading animation is visible and running
                    LoadingOverlay.Visibility = Visibility.Visible;
                    _loadingAnimation.Begin(LoadingSpinnerSmall);
                }
                else
                {
                    // Stop the animation but keep the overlay visible briefly
                    // to ensure the user sees the success message
                    _loadingAnimation.Stop(LoadingSpinnerSmall);
                }
            }
        }

        /// <summary>
        /// Initializes a new instance of the LoginRegisterWindow class
        /// </summary>
        public LoginRegisterWindow()
        {
            InitializeComponent();

            // Initialize services
            _securityService = SecurityService.Instance;
            _logger = LoggingService.Instance;
            _userTrackingService = UserTrackingService.Instance;
            _hardwareFingerprintService = HardwareFingerprintService.Instance;
            _sessionManager = SessionManager.Instance;

            // Get animations
            _fadeInAnimation = (Storyboard)FindResource("FadeInAnimation");
            _slideInAnimation = (Storyboard)FindResource("SlideInAnimation");
            _slideOutAnimation = (Storyboard)FindResource("SlideOutAnimation");
            _pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            _loadingAnimation = (Storyboard)FindResource("LoadingAnimation");

            // Set up credentials file path
            string appDataPath = System.IO.Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CircleUtility");

            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _credentialsFilePath = System.IO.Path.Combine(appDataPath, "credentials.dat");

            // Start pulse animation
            _pulseAnimation.Begin(LoginTitle, HandoffBehavior.SnapshotAndReplace);
            _pulseAnimation.Begin(RegisterTitle, HandoffBehavior.SnapshotAndReplace);

            // Load saved credentials if available
            LoadSavedCredentials();

            // Set focus to username box
            Loaded += (s, e) => LoginUsernameBox.Focus();

            // Check if Arsenal account exists, if not create it
            EnsureArsenalAccountExists();
        }

        /// <summary>
        /// Ensures the Arsenal account exists
        /// </summary>
        private void EnsureArsenalAccountExists()
        {
            try
            {
                // Check if Arsenal account exists
                if (!_securityService.UserExists("Arsenal"))
                {
                    // Create Arsenal account
                    _logger.Log("Creating Arsenal account", LogLevel.INFO);
                    bool created = _securityService.CreateUser("Arsenal", "Arsenal", 4, true);

                    if (created)
                    {
                        _logger.Log("Arsenal account created successfully", LogLevel.SUCCESS);
                    }
                    else
                    {
                        _logger.Log("Failed to create Arsenal account", LogLevel.ERROR);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error ensuring Arsenal account exists: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Loads saved credentials
        /// </summary>
        private void LoadSavedCredentials()
        {
            try
            {
                if (File.Exists(_credentialsFilePath))
                {
                    string username = File.ReadAllText(_credentialsFilePath);
                    LoginUsernameBox.Text = username;
                    RememberMeCheckBox.IsChecked = true;
                    LoginPasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading saved credentials: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Saves credentials
        /// </summary>
        /// <param name="username">The username to save</param>
        private void SaveCredentials(string username)
        {
            try
            {
                if (RememberMeCheckBox.IsChecked == true)
                {
                    File.WriteAllText(_credentialsFilePath, username);
                }
                else if (File.Exists(_credentialsFilePath))
                {
                    File.Delete(_credentialsFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving credentials: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Handles the login button click
        /// </summary>
        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await AttemptLoginAsync();
        }

        /// <summary>
        /// Attempts to log in with the provided credentials
        /// </summary>
        private async Task AttemptLoginAsync()
        {
            try
            {
                // Get username and password
                string username = LoginUsernameBox.Text.Trim();
                string password = LoginPasswordBox.Password;

                // Validate input
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    ShowLoginError("Please enter both username and password.");
                    return;
                }

                // Show loading spinner
                IsLoading = true;
                LoginStatusText.Text = "Authenticating...";

                // Disable login button while authenticating
                LoginButton.IsEnabled = false;

                // Simulate network delay for better UX (remove in production)
                await Task.Delay(1000);

                // Special case for Arsenal account
                bool isAuthenticated;
                if (username.Equals("Arsenal", StringComparison.OrdinalIgnoreCase) &&
                    password.Equals("Arsenal", StringComparison.OrdinalIgnoreCase))
                {
                    // Force Arsenal login to succeed
                    isAuthenticated = true;

                    // Ensure Arsenal account exists
                    EnsureArsenalAccountExists();
                }
                else
                {
                    // Normal authentication for other accounts
                    isAuthenticated = _securityService.AuthenticateUser(username, password);
                }

                if (isAuthenticated)
                {
                    // Save credentials if remember me is checked
                    SaveCredentials(username);

                    // Create session
                    SessionToken = _sessionManager.CreateSession(username, _securityService.IsAdmin(username));

                    // Register login
                    _userTrackingService.RegisterUserLogin(username);

                    // Associate hardware with username
                    _hardwareFingerprintService.AssociateUsernameWithHardware(username);

                    // Login successful
                    _logger.Log($"User login successful: {username}", LogLevel.SUCCESS);
                    LoginStatusText.Text = "Login successful. Initializing...";

                    // Set properties
                    LoginSuccessful = true;
                    Username = username;

                    // Start the loading sequence
                    try
                    {
                        await StartLoadingSequence();
                        _logger.Log("Loading sequence completed successfully", LogLevel.SUCCESS);
                    }
                    catch (Exception loadingEx)
                    {
                        _logger.Log($"Error in loading sequence: {loadingEx.Message}", LogLevel.ERROR);
                        _logger.Log($"Stack trace: {loadingEx.StackTrace}", LogLevel.ERROR);

                        // Show error but continue
                        LoginStatusText.Text = "Loading completed with warnings. Continuing...";
                        await Task.Delay(1000);
                    }

                    // Set DialogResult to true to signal successful login
                    // This will close the window and continue to the main application
                    _logger.Log("Setting DialogResult to true", LogLevel.INFO);
                    DialogResult = true;
                }
                else
                {
                    // Authentication failed
                    _logger.Log($"Login failed: Invalid credentials for user: {username}", LogLevel.WARNING);
                    ShowLoginError("Invalid username or password.");

                    // Hide loading spinner
                    IsLoading = false;

                    // Re-enable login button
                    LoginButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error in login: {ex.Message}", LogLevel.ERROR);
                ShowLoginError($"An error occurred: {ex.Message}");

                // Hide loading spinner
                IsLoading = false;

                // Re-enable login button
                LoginButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Shows a login error message
        /// </summary>
        private void ShowLoginError(string message)
        {
            LoginErrorMessage.Text = message;
            LoginErrorMessage.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// Shows a register error message
        /// </summary>
        private void ShowRegisterError(string message)
        {
            RegisterErrorMessage.Text = message;
            RegisterErrorMessage.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// Handles the register button click
        /// </summary>
        private async void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            await AttemptRegisterAsync();
        }

        /// <summary>
        /// Attempts to register a new user
        /// </summary>
        private async Task AttemptRegisterAsync()
        {
            try
            {
                // Get registration info
                string username = RegisterUsernameBox.Text.Trim();
                string password = RegisterPasswordBox.Password;
                string confirmPassword = RegisterConfirmPasswordBox.Password;

                // Validate input
                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowRegisterError("Please enter a username.");
                    return;
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    ShowRegisterError("Please enter a password.");
                    return;
                }

                if (password != confirmPassword)
                {
                    ShowRegisterError("Passwords do not match.");
                    return;
                }

                // Show loading spinner
                IsLoading = true;
                RegisterStatusText.Text = "Creating account...";

                // Disable register button while processing
                RegisterButton.IsEnabled = false;

                // Simulate network delay for better UX (remove in production)
                await Task.Delay(1000);

                // Check if username already exists
                if (_securityService.UserExists(username))
                {
                    ShowRegisterError("Username already exists. Please choose a different username.");
                    IsLoading = false;
                    RegisterButton.IsEnabled = true;
                    return;
                }

                // Create the user
                bool isCreated = _securityService.CreateUser(username, password, 1, false);

                if (isCreated)
                {
                    // Register login
                    _userTrackingService.RegisterUserLogin(username);

                    // Associate hardware with username
                    _hardwareFingerprintService.AssociateUsernameWithHardware(username);

                    // Create session
                    SessionToken = _sessionManager.CreateSession(username, false);

                    // Registration successful
                    _logger.Log($"User registered: {username}", LogLevel.SUCCESS);
                    RegisterStatusText.Text = "Registration successful. Initializing...";

                    // Set properties
                    LoginSuccessful = true;
                    Username = username;

                    // Start the loading sequence
                    await StartLoadingSequence();

                    // Set DialogResult to true to signal successful registration
                    // This will close the window and continue to the main application
                    DialogResult = true;
                }
                else
                {
                    // Registration failed
                    _logger.Log($"Registration failed for user: {username}", LogLevel.WARNING);
                    ShowRegisterError("Failed to create account. Please try again.");

                    // Hide loading spinner
                    IsLoading = false;

                    // Re-enable register button
                    RegisterButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error in registration: {ex.Message}", LogLevel.ERROR);
                ShowRegisterError($"An error occurred: {ex.Message}");

                // Hide loading spinner
                IsLoading = false;

                // Re-enable register button
                RegisterButton.IsEnabled = true;
            }
        }



        /// <summary>
        /// Handles the register link click
        /// </summary>
        private void RegisterLink_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // Show register panel
            LoginPanel.Visibility = Visibility.Collapsed;
            RegisterPanel.Visibility = Visibility.Visible;

            // Clear register form
            RegisterUsernameBox.Text = "";
            RegisterPasswordBox.Password = "";
            RegisterConfirmPasswordBox.Password = "";
            RegisterErrorMessage.Visibility = Visibility.Collapsed;

            // Reset error messages
            LoginErrorMessage.Visibility = Visibility.Collapsed;
            RegisterErrorMessage.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// Handles the login link click
        /// </summary>
        private void LoginLink_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // Show login panel, hide register panel
            LoginPanel.Visibility = Visibility.Visible;
            RegisterPanel.Visibility = Visibility.Collapsed;

            // Reset error messages
            LoginErrorMessage.Visibility = Visibility.Collapsed;
            RegisterErrorMessage.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// Handles the key down event for input boxes
        /// </summary>
        private async void InputBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (LoginPanel.Visibility == Visibility.Visible)
                {
                    await AttemptLoginAsync();
                }
                else if (RegisterPanel.Visibility == Visibility.Visible)
                {
                    await AttemptRegisterAsync();
                }
            }
        }

        /// <summary>
        /// Handles the window mouse down event for dragging
        /// </summary>
        private void Window_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
                DragMove();
            }
        }

        /// <summary>
        /// Starts the loading sequence after successful login
        /// </summary>
        private async Task StartLoadingSequence()
        {
            try
            {
                _logger.Log("Starting loading sequence", LogLevel.INFO);
                Console.WriteLine("Starting loading sequence"); // Debug output

                // Reset loading step
                _currentLoadingStep = 0;

                // Hide login/register panels and show loading screen
                LoginPanel.Visibility = Visibility.Collapsed;
                RegisterPanel.Visibility = Visibility.Collapsed;
                LoadingPanel.Visibility = Visibility.Visible;

                // Start the loading spinner animation
                _loadingAnimation.Begin(LoadingSpinner);

                // Update loading status text
                LoadingStatusText.Text = _loadingMessages[_currentLoadingStep];
                LoadingDetailsText.Text = "Initializing Circle Utility components...";

                // Start the loading timer
                _loadingTimer = new DispatcherTimer();
                _loadingTimer.Interval = TimeSpan.FromMilliseconds(800);
                _loadingTimer.Tick += LoadingTimer_Tick;
                _loadingTimer.Start();

                // Run window loading validation in background (simplified for now)
                // _ = Task.Run(async () => await RunWindowLoadingValidation());

                // Wait for loading sequence to complete
                Console.WriteLine("Waiting for loading completion..."); // Debug output
                await WaitForLoadingCompletion();

                _logger.Log("Loading sequence completed", LogLevel.SUCCESS);
                Console.WriteLine("Loading sequence completed"); // Debug output
            }
            catch (Exception ex)
            {
                _logger.Log($"Error in loading sequence: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Handles the loading timer tick
        /// </summary>
        private void LoadingTimer_Tick(object sender, EventArgs e)
        {
            if (_currentLoadingStep < _loadingMessages.Length - 1)
            {
                _currentLoadingStep++;

                // Update loading screen elements
                string currentMessage = _loadingMessages[_currentLoadingStep];
                LoadingStatusText.Text = currentMessage;

                // Update progress bar
                double progress = (double)_currentLoadingStep / (_loadingMessages.Length - 1) * 100;
                LoadingProgressBar.Value = progress;

                // Update progress percentage
                ProgressPercentage.Text = $"{progress:F0}%";

                // Update details text based on current step
                LoadingDetailsText.Text = GetLoadingDetails(_currentLoadingStep);

                _logger.Log($"Loading step: {currentMessage}", LogLevel.INFO);
                Console.WriteLine($"Loading step: {currentMessage} ({progress:F0}%)"); // Debug output
            }
            else
            {
                // Stop the timer when we've shown all messages
                _loadingTimer?.Stop();
                LoadingProgressBar.Value = 100;
                LoadingStatusText.Text = "SYSTEM READY";
                LoadingDetailsText.Text = "Circle Utility is ready to launch!";
                Console.WriteLine("Loading timer stopped - all messages shown"); // Debug output
            }
        }

        /// <summary>
        /// Gets detailed loading text for each step
        /// </summary>
        private string GetLoadingDetails(int step)
        {
            return step switch
            {
                0 => "Starting up Circle Utility framework...",
                1 => "Loading hardware detection modules...",
                2 => "Scanning system configuration and settings...",
                3 => "Applying performance optimizations...",
                4 => "Configuring security and authentication...",
                5 => "Preparing user interface components...",
                6 => "Finalizing system setup and validation...",
                7 => "Circle Utility is ready to launch!",
                _ => "Please wait while we prepare your system..."
            };
        }

        /// <summary>
        /// Waits for the loading sequence to complete
        /// </summary>
        private async Task WaitForLoadingCompletion()
        {
            // Wait for all loading messages to be shown
            int totalSteps = _loadingMessages.Length;
            int waitTime = totalSteps * 800; // 800ms per step

            await Task.Delay(waitTime);

            // Stop the timer if it's still running
            _loadingTimer?.Stop();

            // Show final message on loading screen
            LoadingStatusText.Text = "LAUNCHING APPLICATION...";
            LoadingDetailsText.Text = "Opening Circle Utility main interface...";
            LoadingProgressBar.Value = 100;

            // Wait a bit more for the final message
            await Task.Delay(1500);
        }

        /// <summary>
        /// Runs window loading validation in the background
        /// </summary>
        private async Task RunWindowLoadingValidation()
        {
            try
            {
                var windowLoadingManager = WindowLoadingManager.Instance;

                // Subscribe to events to update UI
                windowLoadingManager.StepCompleted += (sender, args) =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        // Update loading message based on step
                        string stepMessage = GetMessageForStep(args.Step.StepName);
                        if (!string.IsNullOrEmpty(stepMessage))
                        {
                            LoginStatusText.Text = stepMessage;
                            RegisterStatusText.Text = stepMessage;
                        }
                    });
                };

                // Start validation
                bool result = await windowLoadingManager.StartLoadingSequence();

                _logger.Log($"Window loading validation completed: {result}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error in window loading validation: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets appropriate loading message for validation step
        /// </summary>
        private string GetMessageForStep(string stepName)
        {
            return stepName switch
            {
                "LoginWindow" => "Validating login system...",
                "WelcomeScreen" => "Preparing welcome interface...",
                "MainWindow" => "Loading main application...",
                "Services" => "Initializing core services...",
                "UI_Components" => "Finalizing user interface...",
                _ => ""
            };
        }



        /// <summary>
        /// Handles the minimize button click
        /// </summary>
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// Handles the close button click
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        /// <summary>
        /// Handles the show password button click
        /// </summary>
        private void ShowPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            Button button = (Button)sender;
            string tag = button.Tag.ToString();

            switch (tag)
            {
                case "LoginPassword":
                    // Toggle visibility of password
                    if (LoginPasswordBox.Visibility == Visibility.Visible)
                    {
                        // Transfer password to visible textbox
                        LoginPasswordBoxVisible.Text = LoginPasswordBox.Password;
                        LoginPasswordBox.Visibility = Visibility.Collapsed;
                        LoginPasswordBoxVisible.Visibility = Visibility.Visible;
                        LoginShowPasswordButton.Content = "🔒";
                    }
                    else
                    {
                        // Transfer password back to password box
                        LoginPasswordBox.Password = LoginPasswordBoxVisible.Text;
                        LoginPasswordBox.Visibility = Visibility.Visible;
                        LoginPasswordBoxVisible.Visibility = Visibility.Collapsed;
                        LoginShowPasswordButton.Content = "👁";
                    }
                    break;

                case "RegisterPassword":
                    // Toggle visibility of password
                    if (RegisterPasswordBox.Visibility == Visibility.Visible)
                    {
                        // Transfer password to visible textbox
                        RegisterPasswordBoxVisible.Text = RegisterPasswordBox.Password;
                        RegisterPasswordBox.Visibility = Visibility.Collapsed;
                        RegisterPasswordBoxVisible.Visibility = Visibility.Visible;
                        RegisterShowPasswordButton.Content = "🔒";
                    }
                    else
                    {
                        // Transfer password back to password box
                        RegisterPasswordBox.Password = RegisterPasswordBoxVisible.Text;
                        RegisterPasswordBox.Visibility = Visibility.Visible;
                        RegisterPasswordBoxVisible.Visibility = Visibility.Collapsed;
                        RegisterShowPasswordButton.Content = "👁";
                    }
                    break;

                case "RegisterConfirmPassword":
                    // Toggle visibility of confirm password
                    if (RegisterConfirmPasswordBox.Visibility == Visibility.Visible)
                    {
                        // Transfer password to visible textbox
                        RegisterConfirmPasswordBoxVisible.Text = RegisterConfirmPasswordBox.Password;
                        RegisterConfirmPasswordBox.Visibility = Visibility.Collapsed;
                        RegisterConfirmPasswordBoxVisible.Visibility = Visibility.Visible;
                        RegisterConfirmShowPasswordButton.Content = "🔒";
                    }
                    else
                    {
                        // Transfer password back to password box
                        RegisterConfirmPasswordBox.Password = RegisterConfirmPasswordBoxVisible.Text;
                        RegisterConfirmPasswordBox.Visibility = Visibility.Visible;
                        RegisterConfirmPasswordBoxVisible.Visibility = Visibility.Collapsed;
                        RegisterConfirmShowPasswordButton.Content = "👁";
                    }
                    break;
            }
        }
    }
}
