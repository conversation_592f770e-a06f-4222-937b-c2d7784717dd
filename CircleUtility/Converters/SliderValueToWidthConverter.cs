using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts a slider value to a width for the filled portion of the track
    /// </summary>
    public class SliderValueToWidthConverter : IValueConverter
    {
        /// <summary>
        /// Converts a slider value to a width
        /// </summary>
        /// <param name="value">The slider value</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">The converter parameter</param>
        /// <param name="culture">The culture info</param>
        /// <returns>The width of the filled portion</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double sliderValue)
            {
                // Get the slider from the binding source
                if (parameter is Slider slider)
                {
                    // Calculate the width based on the slider's actual width
                    double min = slider.Minimum;
                    double max = slider.Maximum;
                    double range = max - min;
                    double percentage = (sliderValue - min) / range;
                    
                    // Account for the thumb width (approximately 20px)
                    double thumbWidth = 20;
                    double trackWidth = slider.ActualWidth - thumbWidth;
                    
                    return trackWidth * percentage;
                }
                else
                {
                    // If we don't have a reference to the slider, use a percentage of the available width
                    // This is less accurate but will still work
                    FrameworkElement targetElement = parameter as FrameworkElement;
                    double availableWidth = targetElement?.ActualWidth ?? 100;
                    
                    double min = 0;
                    double max = 100;
                    double range = max - min;
                    double percentage = (sliderValue - min) / range;
                    
                    return availableWidth * percentage;
                }
            }
            
            return 0;
        }

        /// <summary>
        /// Converts a width back to a slider value (not implemented)
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
