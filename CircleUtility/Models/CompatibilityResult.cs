// Created by Arsenal on 5-17-25 12:15PM
using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Compatibility confidence level
    /// </summary>
    public enum CompatibilityConfidence
    {
        /// <summary>
        /// Low confidence
        /// </summary>
        Low,

        /// <summary>
        /// Medium confidence
        /// </summary>
        Medium,

        /// <summary>
        /// High confidence
        /// </summary>
        High
    }

    /// <summary>
    /// Result of a compatibility check
    /// </summary>
    public class CompatibilityResult
    {
        /// <summary>
        /// Gets or sets a value indicating whether the item is compatible
        /// </summary>
        public bool IsCompatible { get; set; }

        /// <summary>
        /// Gets or sets the confidence level of the compatibility check
        /// </summary>
        public CompatibilityConfidence Confidence { get; set; }

        /// <summary>
        /// Gets or sets the reason for incompatibility (if applicable)
        /// </summary>
        public string IncompatibilityReason { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the compatibility was last validated
        /// </summary>
        public DateTime LastValidated { get; set; }

        /// <summary>
        /// Creates a new instance of the CompatibilityResult class
        /// </summary>
        public CompatibilityResult()
        {
            LastValidated = DateTime.Now;
            Confidence = CompatibilityConfidence.Medium;
        }

        /// <summary>
        /// Creates a compatible result with the specified confidence
        /// </summary>
        /// <param name="confidence">The confidence level</param>
        /// <returns>A compatible result</returns>
        public static CompatibilityResult Compatible(CompatibilityConfidence confidence = CompatibilityConfidence.High)
        {
            return new CompatibilityResult
            {
                IsCompatible = true,
                Confidence = confidence,
                LastValidated = DateTime.Now
            };
        }

        /// <summary>
        /// Creates an incompatible result with the specified reason and confidence
        /// </summary>
        /// <param name="reason">The reason for incompatibility</param>
        /// <param name="confidence">The confidence level</param>
        /// <returns>An incompatible result</returns>
        public static CompatibilityResult Incompatible(string reason, CompatibilityConfidence confidence = CompatibilityConfidence.High)
        {
            return new CompatibilityResult
            {
                IsCompatible = false,
                Confidence = confidence,
                IncompatibilityReason = reason,
                LastValidated = DateTime.Now
            };
        }

        /// <summary>
        /// Creates an unknown result
        /// </summary>
        /// <param name="reason">The reason for the unknown status</param>
        /// <returns>An unknown result</returns>
        public static CompatibilityResult Unknown(string reason = "Compatibility status unknown")
        {
            return new CompatibilityResult
            {
                IsCompatible = false,
                Confidence = CompatibilityConfidence.Low,
                IncompatibilityReason = reason,
                LastValidated = DateTime.Now
            };
        }
    }
}
