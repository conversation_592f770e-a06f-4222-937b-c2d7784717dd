// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Effects;
using CircleUtility.Models;

namespace CircleUtility.Controls
{
    public partial class CompatibilityStatusControl : UserControl
    {
        private static readonly Color CompatibleColor = Color.FromRgb(0, 200, 83);      // Green
        private static readonly Color IncompatibleColor = Color.FromRgb(244, 67, 54);    // Red
        private static readonly Color WarningColor = Color.FromRgb(255, 152, 0);         // Orange
        private static readonly Color UnknownColor = Color.FromRgb(158, 158, 158);       // Gray

        public CompatibilityStatusControl()
        {
            InitializeComponent();
            SetStatus(CompatibilityStatus.Unknown);
        }

        /// <summary>
        /// Sets the compatibility status
        /// </summary>
        /// <param name="status">The compatibility status</param>
        /// <param name="reason">The reason for the status</param>
        public void SetStatus(CompatibilityStatus status, string reason = null)
        {
            CompatibleIcon.Visibility = Visibility.Collapsed;
            IncompatibleIcon.Visibility = Visibility.Collapsed;
            WarningIcon.Visibility = Visibility.Collapsed;
            UnknownIcon.Visibility = Visibility.Collapsed;

            Color statusColor;
            string tooltip;

            switch (status)
            {
                case CompatibilityStatus.Compatible:
                    CompatibleIcon.Visibility = Visibility.Visible;
                    statusColor = CompatibleColor;
                    tooltip = "Compatible with your hardware";
                    break;

                case CompatibilityStatus.Incompatible:
                    IncompatibleIcon.Visibility = Visibility.Visible;
                    statusColor = IncompatibleColor;
                    tooltip = string.IsNullOrEmpty(reason)
                        ? "Incompatible with your hardware"
                        : $"Incompatible: {reason}";
                    break;

                case CompatibilityStatus.Warning:
                    WarningIcon.Visibility = Visibility.Visible;
                    statusColor = WarningColor;
                    tooltip = string.IsNullOrEmpty(reason)
                        ? "May not be fully compatible with your hardware"
                        : $"Warning: {reason}";
                    break;

                case CompatibilityStatus.Unknown:
                default:
                    UnknownIcon.Visibility = Visibility.Visible;
                    statusColor = UnknownColor;
                    tooltip = "Compatibility status unknown";
                    break;
            }

            StatusBackground.Fill = new SolidColorBrush(statusColor);

            if (StatusBackground.Effect is DropShadowEffect shadow)
            {
                shadow.Color = statusColor;
            }
            else
            {
                StatusBackground.Effect = new DropShadowEffect
                {
                    Color = statusColor,
                    BlurRadius = 8,
                    ShadowDepth = 0,
                    Opacity = 0.5
                };
            }

            MainGrid.ToolTip = tooltip;
        }

        /// <summary>
        /// Sets the compatibility status from a CompatibilityResult object
        /// </summary>
        /// <param name="result">The result object to interpret</param>
        public void SetStatus(CompatibilityResult result)
        {
            if (result == null)
            {
                SetStatus(CompatibilityStatus.Unknown);
                return;
            }

            if (result.IsCompatible)
            {
                SetStatus(CompatibilityStatus.Compatible);
            }
            else
            {
                if (result.Confidence == CompatibilityConfidence.High)
                {
                    SetStatus(CompatibilityStatus.Incompatible, result.IncompatibilityReason);
                }
                else
                {
                    SetStatus(CompatibilityStatus.Warning, result.IncompatibilityReason);
                }
            }
        }
    }

    public enum CompatibilityStatus
    {
        Compatible,
        Incompatible,
        Warning,
        Unknown
    }
}
