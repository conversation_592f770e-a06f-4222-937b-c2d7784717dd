// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for game tweaks
    /// </summary>
    public class GameTweak
    {
        /// <summary>
        /// Gets or sets the name of the tweak
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the tweak
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the category of the tweak
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the value of the tweak
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the default value of the tweak
        /// </summary>
        public string DefaultValue { get; set; }

        /// <summary>
        /// Gets or sets the possible values of the tweak
        /// </summary>
        public List<string> PossibleValues { get; set; }

        /// <summary>
        /// Gets or sets the type of the tweak
        /// </summary>
        public TweakType Type { get; set; } = TweakType.Toggle;

        /// <summary>
        /// Gets or sets the minimum value for numeric tweaks
        /// </summary>
        public double MinValue { get; set; }

        /// <summary>
        /// Gets or sets the maximum value for numeric tweaks
        /// </summary>
        public double MaxValue { get; set; }

        /// <summary>
        /// Gets or sets the step value for numeric tweaks
        /// </summary>
        public double StepValue { get; set; } = 1;

        /// <summary>
        /// Gets or sets the registry key for registry tweaks
        /// </summary>
        public string RegistryKey { get; set; }

        /// <summary>
        /// Gets or sets the registry value name for registry tweaks
        /// </summary>
        public string RegistryValueName { get; set; }

        /// <summary>
        /// Gets or sets the registry value kind for registry tweaks
        /// </summary>
        public string RegistryValueKind { get; set; }

        /// <summary>
        /// Gets or sets the file path for file tweaks
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// Gets or sets the file pattern for file tweaks
        /// </summary>
        public string FilePattern { get; set; }

        /// <summary>
        /// Gets or sets the file replacement for file tweaks
        /// </summary>
        public string FileReplacement { get; set; }

        /// <summary>
        /// Gets or sets the command for command tweaks
        /// </summary>
        public string Command { get; set; }

        /// <summary>
        /// Gets or sets the arguments for command tweaks
        /// </summary>
        public string Arguments { get; set; }

        /// <summary>
        /// Gets or sets the performance impact rating (0-100)
        /// </summary>
        public int PerformanceImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the visual quality impact rating (0-100)
        /// </summary>
        public int VisualQualityImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the input latency impact rating (0-100)
        /// </summary>
        public int InputLatencyImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the compatibility rating (0-100)
        /// </summary>
        public int CompatibilityRating { get; set; } = 100;

        /// <summary>
        /// Gets or sets a value indicating whether this tweak requires a restart
        /// </summary>
        public bool RequiresRestart { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this tweak requires admin privileges
        /// </summary>
        public bool RequiresAdmin { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this tweak is enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether this tweak is advanced
        /// </summary>
        public bool IsAdvanced { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this tweak is experimental
        /// </summary>
        public bool IsExperimental { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this tweak is read-only
        /// </summary>
        public bool IsReadOnly { get; set; }

        /// <summary>
        /// Gets or sets the author of the tweak
        /// </summary>
        public string Author { get; set; } = "Circle Utility";

        /// <summary>
        /// Gets or sets the version of the tweak
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// Gets or sets the date the tweak was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets or sets the date the tweak was last modified
        /// </summary>
        public DateTime LastModifiedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets or sets the date the tweak was last applied
        /// </summary>
        public DateTime? LastAppliedDate { get; set; }

        /// <summary>
        /// Gets or sets the user notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets the formatted creation date
        /// </summary>
        [JsonIgnore]
        public string FormattedCreatedDate => CreatedDate.ToString("MMM dd, yyyy");

        /// <summary>
        /// Gets the formatted last modified date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastModifiedDate => LastModifiedDate.ToString("MMM dd, yyyy");

        /// <summary>
        /// Gets the formatted last applied date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastAppliedDate => LastAppliedDate?.ToString("MMM dd, yyyy") ?? "Never";

        /// <summary>
        /// Gets the performance impact description
        /// </summary>
        [JsonIgnore]
        public string PerformanceImpactDescription
        {
            get
            {
                if (PerformanceImpact >= 80) return "Extreme Performance Boost";
                if (PerformanceImpact >= 60) return "High Performance Boost";
                if (PerformanceImpact >= 40) return "Moderate Performance Boost";
                if (PerformanceImpact >= 20) return "Slight Performance Boost";
                return "Minimal Performance Impact";
            }
        }

        /// <summary>
        /// Gets the visual quality impact description
        /// </summary>
        [JsonIgnore]
        public string VisualQualityImpactDescription
        {
            get
            {
                if (VisualQualityImpact >= 80) return "Minimal Visual Impact";
                if (VisualQualityImpact >= 60) return "Slight Visual Impact";
                if (VisualQualityImpact >= 40) return "Moderate Visual Impact";
                if (VisualQualityImpact >= 20) return "High Visual Impact";
                return "Extreme Visual Impact";
            }
        }

        /// <summary>
        /// Gets the input latency impact description
        /// </summary>
        [JsonIgnore]
        public string InputLatencyImpactDescription
        {
            get
            {
                if (InputLatencyImpact >= 80) return "Extreme Latency Reduction";
                if (InputLatencyImpact >= 60) return "High Latency Reduction";
                if (InputLatencyImpact >= 40) return "Moderate Latency Reduction";
                if (InputLatencyImpact >= 20) return "Slight Latency Reduction";
                return "Minimal Latency Impact";
            }
        }

        /// <summary>
        /// Gets the compatibility description
        /// </summary>
        [JsonIgnore]
        public string CompatibilityDescription
        {
            get
            {
                if (CompatibilityRating >= 90) return "Universal Compatibility";
                if (CompatibilityRating >= 70) return "High Compatibility";
                if (CompatibilityRating >= 50) return "Moderate Compatibility";
                if (CompatibilityRating >= 30) return "Limited Compatibility";
                return "Experimental";
            }
        }
    }

    /// <summary>
    /// Enum for tweak types
    /// </summary>
    public enum TweakType
    {
        /// <summary>
        /// Toggle tweak (on/off)
        /// </summary>
        Toggle,

        /// <summary>
        /// Dropdown tweak (select from a list)
        /// </summary>
        Dropdown,

        /// <summary>
        /// Slider tweak (numeric value)
        /// </summary>
        Slider,

        /// <summary>
        /// Text tweak (free text)
        /// </summary>
        Text,

        /// <summary>
        /// Registry tweak (modifies registry)
        /// </summary>
        Registry,

        /// <summary>
        /// File tweak (modifies file)
        /// </summary>
        File,

        /// <summary>
        /// Command tweak (runs command)
        /// </summary>
        Command
    }
}
