using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Event arguments for optimization events
    /// </summary>
    public class OptimizationEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the optimization name
        /// </summary>
        public string OptimizationName { get; set; }

        /// <summary>
        /// Gets or sets the optimization type
        /// </summary>
        public string OptimizationType { get; set; }

        /// <summary>
        /// Gets or sets whether the optimization was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Gets or sets the optimization message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the optimization object
        /// </summary>
        public object Optimization { get; set; }

        /// <summary>
        /// Initializes a new instance of the OptimizationEventArgs class
        /// </summary>
        public OptimizationEventArgs()
        {
        }

        /// <summary>
        /// Initializes a new instance of the OptimizationEventArgs class
        /// </summary>
        /// <param name="optimizationName">The optimization name</param>
        /// <param name="optimizationType">The optimization type</param>
        /// <param name="isSuccessful">Whether the optimization was successful</param>
        /// <param name="message">The optimization message</param>
        public OptimizationEventArgs(string optimizationName, string optimizationType, bool isSuccessful, string message)
        {
            OptimizationName = optimizationName;
            OptimizationType = optimizationType;
            IsSuccessful = isSuccessful;
            Message = message;
        }
    }
}

