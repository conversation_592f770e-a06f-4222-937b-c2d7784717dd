using System;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Collections.Generic;
using NUnit;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using CircleUtility.Controls;

namespace CircleUtility.Tests.Controls
{
    [TestFixture]
    public class PerformanceGraphControlTests
    {
        private PerformanceGraphControl _graphControl = null!;

        [SetUp]
        public void Setup()
        {
            // Initialize WPF for testing
            if (!UnitTestHelper.IsWpfInitialized)
            {
                UnitTestHelper.InitializeWpf();
            }

            _graphControl = new PerformanceGraphControl();
        }

        [Test]
        public void AddDataPoint_SinglePoint_UpdatesGraph()
        {
            // Arrange
            double testValue = 50.0;

            // Act
            _graphControl.AddDataPoint(testValue);

            // Assert
            // Use reflection to access private fields
            var dataPoints = GetPrivateField<List<double>>(_graphControl, "_dataPoints");
            ClassicAssert.IsNotNull(dataPoints);
            ClassicAssert.AreEqual(1, dataPoints.Count);
            ClassicAssert.AreEqual(testValue, dataPoints[0]);
        }

        [Test]
        public void AddDataPoint_MultiplePoints_UpdatesGraph()
        {
            // Arrange
            double[] testValues = { 10.0, 20.0, 30.0, 40.0, 50.0 };

            // Act
            foreach (var value in testValues)
            {
                _graphControl.AddDataPoint(value);
            }

            // Assert
            var dataPoints = GetPrivateField<List<double>>(_graphControl, "_dataPoints");
            ClassicAssert.IsNotNull(dataPoints);
            ClassicAssert.AreEqual(testValues.Length, dataPoints.Count);
            for (int i = 0; i < testValues.Length; i++)
            {
                ClassicAssert.AreEqual(testValues[i], dataPoints[i]);
            }
        }

        [Test]
        public void AddDataPoint_ExceedsMaxPoints_UsesCircularBuffer()
        {
            // Arrange
            int maxDataPoints = GetPrivateField<int>(_graphControl, "_maxDataPoints");

            // Fill the buffer with initial values
            for (int i = 0; i < maxDataPoints; i++)
            {
                _graphControl.AddDataPoint(i);
            }

            // Add one more point to trigger circular buffer
            double newValue = 999.0;

            // Act
            _graphControl.AddDataPoint(newValue);

            // Assert
            var dataPoints = GetPrivateField<List<double>>(_graphControl, "_dataPoints");
            ClassicAssert.IsNotNull(dataPoints);
            ClassicAssert.AreEqual(maxDataPoints, dataPoints.Count);

            // Check that the circular buffer is working
            // The oldest value (0) should have been replaced
            ClassicAssert.IsFalse(dataPoints.Contains(0));
            ClassicAssert.IsTrue(dataPoints.Contains(newValue));
        }

        [Test]
        public void ClearDataPoints_RemovesAllPoints()
        {
            // Arrange
            for (int i = 0; i < 10; i++)
            {
                _graphControl.AddDataPoint(i);
            }

            // Act
            _graphControl.ClearDataPoints();

            // Assert
            var dataPoints = GetPrivateField<List<double>>(_graphControl, "_dataPoints");
            ClassicAssert.IsNotNull(dataPoints);
            ClassicAssert.AreEqual(0, dataPoints.Count);
        }

        [Test]
        public void GraphColor_SetProperty_UpdatesGraphElements()
        {
            // Arrange
            Brush testColor = Brushes.Red;

            // Act
            _graphControl.GraphColor = testColor;

            // Assert
            // We can't easily test the visual elements directly in a unit test
            // But we can verify the property was set
            ClassicAssert.AreEqual(testColor, _graphControl.GraphColor);
        }

        [Test]
        public void MaxValue_SetProperty_UpdatesProperty()
        {
            // Arrange
            double testValue = 200.0;

            // Act
            _graphControl.MaxValue = testValue;

            // Assert
            ClassicAssert.AreEqual(testValue, _graphControl.MaxValue);
        }

        [Test]
        public void Format_SetProperty_UpdatesProperty()
        {
            // Arrange
            string testFormat = "{0:F2}";

            // Act
            _graphControl.Format = testFormat;

            // Assert
            ClassicAssert.AreEqual(testFormat, _graphControl.Format);
        }

        [Test]
        public void Title_SetProperty_UpdatesProperty()
        {
            // Arrange
            string testTitle = "Test Graph";

            // Act
            _graphControl.Title = testTitle;

            // Assert
            ClassicAssert.AreEqual(testTitle, _graphControl.Title);
        }

        // Helper method to get private field value using reflection
        private T GetPrivateField<T>(object instance, string fieldName)
        {
            Type type = instance.GetType();
            FieldInfo? field = type.GetField(fieldName, BindingFlags.NonPublic | BindingFlags.Instance);
            if (field == null)
                throw new ArgumentException($"Field '{fieldName}' not found in type '{type.Name}'");

            object? value = field.GetValue(instance);
            if (value == null)
                throw new InvalidOperationException($"Field '{fieldName}' is null");

            return (T)value;
        }
    }

    // Helper class for WPF testing
    public static class UnitTestHelper
    {
        public static bool IsWpfInitialized { get; private set; }

        public static void InitializeWpf()
        {
            if (!IsWpfInitialized)
            {
                // Initialize WPF
                if (Application.Current == null)
                {
                    // Create a new application instance
                    new Application();
                }

                // Now we can safely access the dispatcher
                if (Application.Current != null && !Application.Current.Dispatcher.CheckAccess())
                {
                    Application.Current.Dispatcher.Invoke(() => InitializeWpf());
                    return;
                }

                IsWpfInitialized = true;
            }
        }
    }
}
