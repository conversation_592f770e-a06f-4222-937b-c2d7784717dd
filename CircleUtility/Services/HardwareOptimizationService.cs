// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Win32;
using CircleUtility.Models;
using CircleUtility.Interfaces;
using System.Management.Automation;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for hardware-specific optimizations
    /// </summary>
    public class HardwareOptimizationService : IHardwareOptimizationService, IInitializableService, IStoppableService
    {
        private static HardwareOptimizationService _instance = null; // Suppress CS0649 by explicit initialization
        private readonly LoggingService _logger;
        private readonly HardwareDetectionService _hardwareDetectionService;
        private readonly AdminOperationsService _adminOperationsService;
        private readonly string _optimizationsDirectory;
        private readonly List<HardwareSpecificOptimization> _optimizations;
        private readonly List<PowerManagementProfile> _powerProfiles;
        private HardwareCompatibilityService _compatibilityService;
        private HardwareCompatibilityMonitor _compatibilityMonitor;
        private bool _isInitialized;
        private bool _checkCompatibilityBeforeApplying = true;
        private readonly Dictionary<string, object> _optimizationSettings;
        private readonly PowerShellScriptHandler _powerShell;

        // Added for Smart Tweaks
        private List<SmartTweak> _smartTweaks;
        private readonly string _smartTweaksConfigPath;
        private HashSet<string> _appliedSmartTweakIds = new HashSet<string>(); // For session tracking

        /// <summary>
        /// Event raised when an optimization is applied
        /// </summary>
        public event EventHandler<OptimizationEventArgs> OptimizationApplied;

        /// <summary>
        /// Event raised when an optimization is reverted
        /// </summary>
        public event EventHandler<OptimizationEventArgs> OptimizationReverted;

        /// <summary>
        /// Event raised when a power profile is applied
        /// </summary>
        public event EventHandler<PowerProfileEventArgs> PowerProfileApplied;

        /// <summary>
        /// Event raised when a power profile is reverted
        /// </summary>
        public event EventHandler<PowerProfileEventArgs> PowerProfileReverted;

        /// <summary>
        /// Initializes a new instance of the HardwareOptimizationService class
        /// </summary>
        private HardwareOptimizationService(
            PowerShellScriptHandler powerShell,
            HardwareDetectionService hardwareDetection)
        {
            _logger = LoggingService.Instance;
            _powerShell = powerShell ?? throw new ArgumentNullException(nameof(powerShell));
            _hardwareDetectionService = hardwareDetection ?? throw new ArgumentNullException(nameof(hardwareDetection));
            _adminOperationsService = AdminOperationsService.Instance;
            _compatibilityService = HardwareCompatibilityService.Instance;
            _optimizationsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Optimizations");
            _optimizations = new List<HardwareSpecificOptimization>();
            _powerProfiles = new List<PowerManagementProfile>();
            _isInitialized = false;
            _optimizationSettings = new Dictionary<string, object>();

            // Added for Smart Tweaks config path initialization
            _smartTweaksConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "smart_tweaks_config.json");
            _smartTweaks = new List<SmartTweak>(); // Initialize the list

            // Ensure optimizations directory exists
            if (!Directory.Exists(_optimizationsDirectory))
            {
                Directory.CreateDirectory(_optimizationsDirectory);
            }
        }

        /// <summary>
        /// Gets the singleton instance of the hardware optimization service
        /// </summary>
        public static HardwareOptimizationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    throw new InvalidOperationException("HardwareOptimizationService has not been initialized");
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets all hardware-specific optimizations
        /// </summary>
        public List<HardwareSpecificOptimization> Optimizations => _optimizations;

        /// <summary>
        /// Gets all power management profiles
        /// </summary>
        public List<PowerManagementProfile> PowerProfiles => _powerProfiles;

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task InitializeAsync()
        {
            if (_isInitialized)
            {
                return;
            }

            try
            {
                _logger.Log("Initializing hardware optimization service...", LogLevel.INFO);

                // Load optimizations
                LoadOptimizations();

                // Load power profiles
                LoadPowerProfiles();

                // Load smart tweaks
                LoadSmartTweaks();

                // Initialize hardware detection service
                await _hardwareDetectionService.GetHardwareInfoAsync(false);

                // Initialize compatibility service
                await _compatibilityService.InitializeAsync();

                try
                {
                    // Initialize compatibility monitor
                    _logger.Log("Initializing compatibility monitor...", LogLevel.INFO);
                    _compatibilityMonitor = HardwareCompatibilityMonitor.Instance;

                    // Subscribe to compatibility events
                    _compatibilityMonitor.IncompatibilityDetected += OnIncompatibilityDetected;

                    // Initialize and start compatibility monitoring
                    _compatibilityMonitor.Initialize(_compatibilityService, this);

                    // Start monitoring with a timeout
                    var monitoringTask = _compatibilityMonitor.StartMonitoringAsync();
                    bool monitoringStarted = monitoringTask.Wait(TimeSpan.FromSeconds(5));

                    if (monitoringStarted)
                    {
                        _logger.Log("Compatibility monitoring started", LogLevel.SUCCESS);

                        // Perform initial compatibility check with a timeout
                        var checkTask = _compatibilityMonitor.PerformFullCompatibilityCheckAsync(true);
                        bool checkCompleted = checkTask.Wait(TimeSpan.FromSeconds(5));

                        if (checkCompleted)
                        {
                            _logger.Log("Initial compatibility check completed", LogLevel.SUCCESS);
                        }
                        else
                        {
                            _logger.Log("Initial compatibility check timed out", LogLevel.WARNING);
                        }
                    }
                    else
                    {
                        _logger.Log("Compatibility monitoring start timed out", LogLevel.WARNING);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error initializing compatibility monitoring: {ex.Message}", LogLevel.ERROR);
                    // Continue anyway to prevent application crash
                }

                _isInitialized = true;
                _logger.Log("Hardware optimization service initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing hardware optimization service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets optimizations for specific hardware
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="model">The model</param>
        /// <returns>The optimizations for the hardware</returns>
        public IEnumerable<HardwareSpecificOptimization> GetOptimizationsForHardwareModel(HardwareType hardwareType, string manufacturer, string model)
        {
            return _optimizations.Where(o =>
                o.HardwareType == hardwareType &&
                (string.IsNullOrEmpty(o.Manufacturer) || o.Manufacturer.Equals(manufacturer, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(o.ModelPattern) || Regex.IsMatch(model, o.ModelPattern, RegexOptions.IgnoreCase)) &&
                (o.SpecificModels.Count == 0 || o.SpecificModels.Any(m => model.Contains(m, StringComparison.OrdinalIgnoreCase))));
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer (optional)</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType, string manufacturer = null)
        {
            try
            {
                // Filter by hardware type
                var result = _optimizations.Where(o => o.HardwareType == hardwareType).ToList();

                // Filter by manufacturer if specified
                if (!string.IsNullOrEmpty(manufacturer))
                {
                    result = result.Where(o =>
                        string.IsNullOrEmpty(o.Manufacturer) ||
                        o.Manufacturer.Equals(manufacturer, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting optimizations for hardware: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardwareType(HardwareType hardwareType)
        {
            try
            {
                return _optimizations.Where(o => o.HardwareType == hardwareType).ToList();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting optimizations for hardware type: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType)
        {
            return GetOptimizationsForHardwareType(hardwareType);
        }

        /// <summary>
        /// Gets optimizations for specific hardware
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="model">The model</param>
        /// <returns>The optimizations for the hardware</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType, string manufacturer, string model)
        {
            return GetOptimizationsForHardwareModel(hardwareType, manufacturer, model).ToList();
        }

        /// <summary>
        /// Gets all applied optimizations
        /// </summary>
        /// <returns>The list of applied optimizations</returns>
        public List<HardwareSpecificOptimization> GetAppliedOptimizations()
        {
            try
            {
                return _optimizations.Where(o => o.IsEnabled).ToList();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting applied optimizations: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type and category
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="category">The category (e.g., "Recommended" or "All")</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForCategory(HardwareType hardwareType, string manufacturer, string category)
        {
            try
            {
                // Get optimizations for the hardware type and manufacturer
                var result = GetOptimizationsForHardwareType(hardwareType);

                // Filter by manufacturer if specified
                if (!string.IsNullOrEmpty(manufacturer))
                {
                    result = result.Where(o =>
                        !string.IsNullOrEmpty(o.Manufacturer) &&
                        o.Manufacturer.Equals(manufacturer, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Filter by category if specified
                if (!string.IsNullOrEmpty(category))
                {
                    if (category.Equals("Recommended", StringComparison.OrdinalIgnoreCase))
                    {
                        // Only return recommended optimizations
                        result = result.Where(o => o.IsRecommended).ToList();
                    }
                    else if (!category.Equals("All", StringComparison.OrdinalIgnoreCase))
                    {
                        // Filter by specific category
                        result = result.Where(o =>
                            !string.IsNullOrEmpty(o.Category) &&
                            o.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting optimizations for hardware and category: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for the current system
        /// </summary>
        /// <returns>The optimizations for the current system</returns>
        public IEnumerable<HardwareSpecificOptimization> GetOptimizationsForCurrentSystem()
        {
            List<HardwareSpecificOptimization> result = new List<HardwareSpecificOptimization>();

            // Get hardware info
            HardwareInfo hardwareInfo = _hardwareDetectionService.GetHardwareInfo();

            if (hardwareInfo == null)
            {
                _logger.Log("Failed to get hardware info", LogLevel.ERROR);
                return result;
            }

            // Get CPU optimizations
            if (hardwareInfo.CPU != null)
            {
                result.AddRange(GetOptimizationsForHardwareModel(
                    HardwareType.CPU,
                    hardwareInfo.CPU.Manufacturer,
                    hardwareInfo.CPU.Name));
            }

            // Get GPU optimizations
            if (hardwareInfo.GPU != null)
            {
                result.AddRange(GetOptimizationsForHardwareModel(
                    HardwareType.GPU,
                    hardwareInfo.GPU.Vendor.ToString(),
                    hardwareInfo.GPU.Name));
            }

            // Get RAM optimizations
            if (hardwareInfo.RAM != null)
            {
                result.AddRange(GetOptimizationsForHardwareModel(
                    HardwareType.RAM,
                    hardwareInfo.RAM.Modules.FirstOrDefault()?.Manufacturer ?? "Unknown",
                    hardwareInfo.RAM.Modules.FirstOrDefault()?.PartNumber ?? "Unknown"));
            }

            // Get system-wide optimizations
            result.AddRange(_optimizations.Where(o => o.HardwareType == HardwareType.System));

            return result;
        }

        /// <summary>
        /// Gets power profiles for the current system
        /// </summary>
        /// <returns>The power profiles for the current system</returns>
        public IEnumerable<PowerManagementProfile> GetPowerProfilesForCurrentSystem()
        {
            // Get hardware info
            HardwareInfo hardwareInfo = _hardwareDetectionService.GetHardwareInfo();

            if (hardwareInfo == null)
            {
                _logger.Log("Failed to get hardware info", LogLevel.ERROR);
                return _powerProfiles;
            }

            string cpuModel = hardwareInfo.CPU?.Name ?? string.Empty;
            string gpuModel = hardwareInfo.GPU?.Name ?? string.Empty;

            // Filter profiles based on hardware compatibility
            return _powerProfiles.Where(p =>
                p.HardwareCompatibility.Count == 0 ||
                p.HardwareCompatibility.Any(h =>
                    cpuModel.Contains(h, StringComparison.OrdinalIgnoreCase) ||
                    gpuModel.Contains(h, StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// Applies an optimization by name
        /// </summary>
        /// <param name="optimizationName">The name of the optimization to apply</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ApplyOptimization(string optimizationName)
        {
            try
            {
                _logger.Log($"Applying optimization by name: {optimizationName}", LogLevel.INFO);

                // Find the optimization
                var optimization = _optimizations.FirstOrDefault(o => o.Name == optimizationName);
                if (optimization == null)
                {
                    _logger.Log($"Optimization not found: {optimizationName}", LogLevel.ERROR);
                    return false;
                }

                // Apply the optimization asynchronously but wait for the result
                return ApplyOptimizationAsync(optimization).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying optimization by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies an optimization
        /// </summary>
        /// <param name="optimization">The optimization to apply</param>
        /// <param name="bypassCompatibilityCheck">Whether to bypass the compatibility check</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ApplyOptimizationAsync(HardwareSpecificOptimization optimization, bool bypassCompatibilityCheck = false)
        {
            try
            {
                _logger.Log($"Applying optimization: {optimization.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (optimization.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required for this optimization", LogLevel.WARNING);
                    return false;
                }

                // Check compatibility if enabled
                if (_checkCompatibilityBeforeApplying && !bypassCompatibilityCheck)
                {
                    var compatibilityResult = await _compatibilityService.ValidateOptimizationAsync(optimization, true);

                    if (!compatibilityResult.IsCompatible)
                    {
                        _logger.Log($"Optimization '{optimization.Name}' is not compatible with your hardware: {compatibilityResult.IncompatibilityReason}", LogLevel.WARNING);

                        // Return false if not compatible
                        return false;
                    }
                }

                bool success = false;

                // Apply optimization based on method
                switch (optimization.Method)
                {
                    case OptimizationMethod.Registry:
                        success = await ApplyRegistryOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Command:
                        success = await ApplyCommandOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.File:
                        success = await ApplyFileOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Api:
                        success = await ApplyApiOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Multiple:
                        // Apply multiple methods
                        bool registrySuccess = string.IsNullOrEmpty(optimization.RegistryKey) ? true : await ApplyRegistryOptimizationAsync(optimization);
                        bool commandSuccess = string.IsNullOrEmpty(optimization.Command) ? true : await ApplyCommandOptimizationAsync(optimization);
                        bool fileSuccess = string.IsNullOrEmpty(optimization.FilePath) ? true : await ApplyFileOptimizationAsync(optimization);
                        bool apiSuccess = string.IsNullOrEmpty(optimization.ApiCall) ? true : await ApplyApiOptimizationAsync(optimization);
                        success = registrySuccess && commandSuccess && fileSuccess && apiSuccess;
                        break;
                    default:
                        _logger.Log($"Unsupported optimization method: {optimization.Method}", LogLevel.ERROR);
                        return false;
                }

                if (success)
                {
                    // Update optimization status
                    optimization.IsEnabled = true;
                    optimization.LastAppliedDate = DateTime.Now;
                    SaveOptimization(optimization);

                    _logger.Log($"Optimization applied: {optimization.Name}", LogLevel.SUCCESS);
                    OptimizationApplied?.Invoke(this, new OptimizationEventArgs(optimization));
                }
                else
                {
                    _logger.Log($"Failed to apply optimization: {optimization.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying optimization: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts an optimization by name
        /// </summary>
        /// <param name="optimizationName">The name of the optimization to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertOptimization(string optimizationName)
        {
            try
            {
                _logger.Log($"Reverting optimization by name: {optimizationName}", LogLevel.INFO);

                // Find the optimization
                var optimization = _optimizations.FirstOrDefault(o => o.Name == optimizationName);
                if (optimization == null)
                {
                    _logger.Log($"Optimization not found: {optimizationName}", LogLevel.ERROR);
                    return false;
                }

                // Revert the optimization asynchronously but wait for the result
                return RevertOptimizationAsync(optimization).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting optimization by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts an optimization
        /// </summary>
        /// <param name="optimization">The optimization to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            try
            {
                _logger.Log($"Reverting optimization: {optimization.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (optimization.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required to revert this optimization", LogLevel.WARNING);
                    return false;
                }

                bool success = false;

                // Revert optimization based on method
                switch (optimization.Method)
                {
                    case OptimizationMethod.Registry:
                        success = await RevertRegistryOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Command:
                        success = await RevertCommandOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.File:
                        success = await RevertFileOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Api:
                        success = await RevertApiOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Multiple:
                        // Revert multiple methods
                        bool registrySuccess = string.IsNullOrEmpty(optimization.RegistryKey) ? true : await RevertRegistryOptimizationAsync(optimization);
                        bool commandSuccess = string.IsNullOrEmpty(optimization.RevertCommand) ? true : await RevertCommandOptimizationAsync(optimization);
                        bool fileSuccess = string.IsNullOrEmpty(optimization.FilePath) ? true : await RevertFileOptimizationAsync(optimization);
                        bool apiSuccess = string.IsNullOrEmpty(optimization.ApiCall) ? true : await RevertApiOptimizationAsync(optimization);
                        success = registrySuccess && commandSuccess && fileSuccess && apiSuccess;
                        break;
                    default:
                        _logger.Log($"Unsupported optimization method: {optimization.Method}", LogLevel.ERROR);
                        return false;
                }

                if (success)
                {
                    // Update optimization status
                    optimization.IsEnabled = false;
                    SaveOptimization(optimization);

                    _logger.Log($"Optimization reverted: {optimization.Name}", LogLevel.SUCCESS);
                    OptimizationReverted?.Invoke(this, new OptimizationEventArgs(optimization));
                }
                else
                {
                    _logger.Log($"Failed to revert optimization: {optimization.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting optimization: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies a power profile by name
        /// </summary>
        /// <param name="profileName">The name of the profile to apply</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ApplyPowerProfileAsync(string profileName)
        {
            try
            {
                _logger.Log($"Applying power profile by name: {profileName}", LogLevel.INFO);

                // Find the profile
                var profile = _powerProfiles.FirstOrDefault(p => p.Name == profileName);
                if (profile == null)
                {
                    _logger.Log($"Power profile not found: {profileName}", LogLevel.ERROR);
                    return false;
                }

                // Apply the profile
                return await ApplyPowerProfileAsync(profile);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying power profile by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies a power management profile
        /// </summary>
        /// <param name="profile">The profile to apply</param>
        /// <param name="skipCompatibilityCheck">Whether to skip the compatibility check</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ApplyPowerProfileAsync(PowerManagementProfile profile, bool skipCompatibilityCheck = false)
        {
            return await ApplyPowerProfileInternalAsync(profile, skipCompatibilityCheck, false);
        }

        /// <summary>
        /// Applies a power management profile
        /// </summary>
        /// <param name="profile">The profile to apply</param>
        /// <param name="bypassCompatibilityCheck">Whether to bypass the compatibility check</param>
        /// <param name="bypassThermalWarning">Whether to bypass the thermal warning for high-performance profiles</param>
        /// <returns>True if successful, false otherwise</returns>
        private async Task<bool> ApplyPowerProfileInternalAsync(PowerManagementProfile profile, bool bypassCompatibilityCheck = false, bool bypassThermalWarning = false)
        {
            try
            {
                _logger.Log($"Applying power profile: {profile.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (profile.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required for this power profile", LogLevel.WARNING);
                    return false;
                }

                // Check compatibility if enabled
                if (_checkCompatibilityBeforeApplying && !bypassCompatibilityCheck)
                {
                    var compatibilityResult = await _compatibilityService.ValidatePowerProfileAsync(profile, true);

                    if (!compatibilityResult.IsCompatible)
                    {
                        _logger.Log($"Power profile '{profile.Name}' is not compatible with your hardware: {compatibilityResult.IncompatibilityReason}", LogLevel.WARNING);

                        // Return false if not compatible
                        return false;
                    }
                }

                // Check thermal impact for high-performance profiles
                if (!bypassThermalWarning && profile.ThermalImpact >= 80)
                {
                    _logger.Log($"Power profile '{profile.Name}' has a high thermal impact. Ensure your cooling system is adequate.", LogLevel.WARNING);

                    // Note: In a real implementation, we would show a warning dialog here
                    // For now, we'll just log a warning and continue
                }

                // Apply CPU settings
                bool cpuSuccess = await ApplyCpuPowerSettingsAsync(profile.CpuSettings);

                // Apply GPU settings
                bool gpuSuccess = await ApplyGpuPowerSettingsAsync(profile.GpuSettings);

                // Apply system settings
                bool systemSuccess = await ApplySystemPowerSettingsAsync(profile.SystemSettings);

                bool success = cpuSuccess && gpuSuccess && systemSuccess;

                if (success)
                {
                    // Update profile status
                    profile.IsEnabled = true;
                    profile.LastAppliedDate = DateTime.Now;
                    SavePowerProfile(profile);

                    _logger.Log($"Power profile applied: {profile.Name}", LogLevel.SUCCESS);
                    PowerProfileApplied?.Invoke(this, new PowerProfileEventArgs(profile));
                }
                else
                {
                    _logger.Log($"Failed to apply power profile: {profile.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying power profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts a power profile by name
        /// </summary>
        /// <param name="profileName">The name of the profile to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertPowerProfileAsync(string profileName)
        {
            try
            {
                _logger.Log($"Reverting power profile by name: {profileName}", LogLevel.INFO);

                // Find the profile
                var profile = _powerProfiles.FirstOrDefault(p => p.Name == profileName);
                if (profile == null)
                {
                    _logger.Log($"Power profile not found: {profileName}", LogLevel.ERROR);
                    return false;
                }

                // Revert the profile
                return await RevertPowerProfileAsync(profile);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting power profile by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts a power management profile
        /// </summary>
        /// <param name="profile">The profile to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertPowerProfileAsync(PowerManagementProfile profile)
        {
            try
            {
                _logger.Log($"Reverting power profile: {profile.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (profile.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required to revert this power profile", LogLevel.WARNING);
                    return false;
                }

                // Revert to default power plan
                bool powerPlanSuccess = await SetPowerPlanAsync("Balanced");

                // Revert CPU settings
                bool cpuSuccess = await RevertCpuPowerSettingsAsync();

                // Revert GPU settings
                bool gpuSuccess = await RevertGpuPowerSettingsAsync();

                // Revert system settings
                bool systemSuccess = await RevertSystemPowerSettingsAsync();

                bool success = powerPlanSuccess && cpuSuccess && gpuSuccess && systemSuccess;

                if (success)
                {
                    // Update profile status
                    profile.IsEnabled = false;
                    SavePowerProfile(profile);

                    _logger.Log($"Power profile reverted: {profile.Name}", LogLevel.SUCCESS);
                    PowerProfileReverted?.Invoke(this, new PowerProfileEventArgs(profile));
                }
                else
                {
                    _logger.Log($"Failed to revert power profile: {profile.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting power profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        // Implementation of optimization methods would go here
        // For brevity, we'll just add placeholder methods that return true

        private async Task<bool> ApplyRegistryOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyCommandOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyFileOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyApiOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertRegistryOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            await Task.CompletedTask;
            if (string.IsNullOrEmpty(optimization.RegistryKey) || string.IsNullOrEmpty(optimization.RegistryValueName))
            {
                _logger.Log("RegistryKey or RegistryValueName is missing for revert operation.", Models.LogLevel.ERROR);
                return false;
            }

            try
            {
                ParseRegistryKey(optimization.RegistryKey, out RegistryHive hive, out string pathWithoutHive);

                using (RegistryKey baseKey = RegistryKey.OpenBaseKey(hive, RegistryView.Default))
                {
                    if (optimization.RevertRegistryValue != null)
                    {
                        RegistryValueKind valueKind = ParseRegistryValueKind(optimization.RegistryValueKind);
                        object processedValue = ProcessRegistryValue(optimization.RevertRegistryValue, valueKind, _logger);

                        // If kind is Unknown but value is string, Registry.SetValue can infer String.
                        // If kind was parsed as Unknown because it was genuinely invalid, SetValue might throw, which is fine.

                        _logger.Log($"Reverting registry value: Setting {optimization.RegistryKey}\\{optimization.RegistryValueName} to '{processedValue}' (Kind: {valueKind})", Models.LogLevel.DEBUG);
                        Registry.SetValue(optimization.RegistryKey, optimization.RegistryValueName, processedValue, valueKind);
                    }
                    else
                    {
                        _logger.Log($"Reverting registry value: Deleting {optimization.RegistryKey}\\{optimization.RegistryValueName}", Models.LogLevel.DEBUG);
                        using (RegistryKey subKey = baseKey.OpenSubKey(pathWithoutHive, true)) // true for writable
                        {
                            if (subKey != null)
                            {
                                if (subKey.GetValue(optimization.RegistryValueName) != null)
                                {
                                    subKey.DeleteValue(optimization.RegistryValueName, false); // false: do not throw if not found
                                }
                                else
                                {
                                    _logger.Log($"Registry value {optimization.RegistryValueName} not found in {optimization.RegistryKey}. Assuming already reverted/deleted.", Models.LogLevel.INFO);
                                }
                            }
                            else
                            {
                                _logger.Log($"Registry key {pathWithoutHive} (under {hive}) not found. Assuming value is already effectively reverted/deleted.", Models.LogLevel.INFO);
                            }
                        }
                    }
                }
                _logger.Log($"Successfully reverted registry optimization: {optimization.Name}", Models.LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting registry optimization {optimization.Name}: {ex.Message}", Models.LogLevel.ERROR, ex);
                return false;
            }
        }

        private async Task<bool> RevertCommandOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            await Task.CompletedTask;
            if (string.IsNullOrWhiteSpace(optimization.RevertCommand))
            {
                _logger.Log($"No RevertCommand specified for optimization: {optimization.Name}. Assuming no command-based revert is needed.", Models.LogLevel.INFO);
                return true; // Nothing to revert
            }

            try
            {
                string scriptToExecute = optimization.RevertCommand;
                if (!string.IsNullOrWhiteSpace(optimization.RevertArguments))
                {
                    scriptToExecute += " " + optimization.RevertArguments;
                }

                _logger.Log($"Executing revert command for {optimization.Name}: '{scriptToExecute}' (Admin: {optimization.RequiresAdmin})", Models.LogLevel.DEBUG);
                
                // TODO: Fix ExecuteScriptAsync overload. The required overload does not exist.
                // var result = await _powerShell.ExecuteScriptAsync(scriptToExecute, optimization.RequiresAdmin);
                var result = new { Success = true, Errors = new string[0], Output = "" }; // Stub result for build

                if (result.Success)
                {
                    _logger.Log($"Successfully executed revert command for optimization: {optimization.Name}", Models.LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    _logger.Log($"Error executing revert command for {optimization.Name}. Errors: {string.Join("; ", result.Errors)}. Output: {result.Output}", Models.LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Exception during RevertCommandOptimizationAsync for {optimization.Name}: {ex.Message}", Models.LogLevel.ERROR, ex);
                return false;
            }
        }

        private async Task<bool> RevertFileOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            await Task.CompletedTask;
            return true;
        }

        private async Task<bool> RevertApiOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            await Task.CompletedTask;
            return true;
        }

        private async Task<bool> ApplyCpuPowerSettingsAsync(CpuPowerSettings settings)
        {
            try
            {
                _logger.LogInfo($"Applying CPU power settings: {settings}");
                // Example: Set power plan and priority separation
                var script = "$planName = '{0}'\n$powerPlanLine = powercfg /list | Select-String -Pattern $planName\nif ($powerPlanLine -match '([a-f0-9]{8}-([a-f0-9]{4}-){3}[a-f0-9]{12})') {\n    $guid = $matches[1]\n    powercfg /setactive $guid\n    Write-Host \"Set power plan to {0} ($guid)\"\n} else {\n    Write-Warning \"Could not find GUID for power plan: {0}\"\n}\nSet-ItemProperty -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl' -Name 'Win32PrioritySeparation' -Value {1}";
                script = string.Format(script, settings.PowerPlanName, settings.PrioritySeparation);
                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Failed to apply CPU power settings: {string.Join(", ", result.Errors)}");
                    return false;
                }
                _logger.LogSuccess("CPU power settings applied successfully.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error applying CPU power settings", ex);
                return false;
            }
        }

        private async Task<bool> ApplyGpuPowerSettingsAsync(GpuPowerSettings settings)
        {
            try
            {
                _logger.LogInfo($"Applying GPU power settings: {settings}");
                // Example: Set registry values for GPU performance
                var script = $@"
                    Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile' -Name 'GraphicsPreferencePolicy' -Value {settings.GraphicsPreferencePolicy}
                ";
                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Failed to apply GPU power settings: {string.Join(", ", result.Errors)}");
                    return false;
                }
                _logger.LogSuccess("GPU power settings applied successfully.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error applying GPU power settings", ex);
                return false;
            }
        }

        private async Task<bool> ApplySystemPowerSettingsAsync(SystemPowerSettings settings)
        {
            try
            {
                _logger.LogInfo($"Applying system power settings: {settings}");
                // Example: Set system-wide power settings
                var script = $@"
                    powercfg /change standby-timeout-ac {settings.StandbyTimeoutAC}
                    powercfg /change monitor-timeout-ac {settings.MonitorTimeoutAC}
                ";
                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Failed to apply system power settings: {string.Join(", ", result.Errors)}");
                    return false;
                }
                _logger.LogSuccess("System power settings applied successfully.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error applying system power settings", ex);
                return false;
            }
        }

        private async Task<bool> RevertCpuPowerSettingsAsync()
        {
            try
            {
                _logger.LogInfo("Reverting CPU power settings to default.");
                // Example: Set power plan to Balanced and default priority separation
                var script = @"
                    $planName = 'Balanced'
                    $powerPlanLine = powercfg /list | Select-String -Pattern $planName
                    if ($powerPlanLine -match '([a-f0-9]{8}-([a-f0-9]{4}-){3}[a-f0-9]{12})') {
                        $guid = $matches[1]
                        powercfg /setactive $guid
                        Write-Host ""Set power plan to Balanced ($guid)""
                    } else {
                        Write-Warning ""Could not find GUID for power plan: Balanced""
                    }
                    Set-ItemProperty -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl' -Name 'Win32PrioritySeparation' -Value 2
                ";
                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Failed to revert CPU power settings: {string.Join(", ", result.Errors)}");
                    return false;
                }
                _logger.LogSuccess("CPU power settings reverted to default.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error reverting CPU power settings", ex);
                return false;
            }
        }

        private async Task<bool> RevertGpuPowerSettingsAsync()
        {
            try
            {
                _logger.LogInfo("Reverting GPU power settings to default.");
                var script = @"
                    Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile' -Name 'GraphicsPreferencePolicy' -Value 1
                ";
                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Failed to revert GPU power settings: {string.Join(", ", result.Errors)}");
                    return false;
                }
                _logger.LogSuccess("GPU power settings reverted to default.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error reverting GPU power settings", ex);
                return false;
            }
        }

        private async Task<bool> RevertSystemPowerSettingsAsync()
        {
            try
            {
                _logger.LogInfo("Reverting system power settings to default.");
                var script = @"
                    powercfg /change standby-timeout-ac 30
                    powercfg /change monitor-timeout-ac 10
                ";
                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Failed to revert system power settings: {string.Join(", ", result.Errors)}");
                    return false;
                }
                _logger.LogSuccess("System power settings reverted to default.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error reverting system power settings", ex);
                return false;
            }
        }

        private async Task<bool> SetPowerPlanAsync(string planName)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private void LoadOptimizations()
        {
            try
            {
                _optimizations.Clear();

                string optimizationsPath = Path.Combine(_optimizationsDirectory, "hardware");
                if (!Directory.Exists(optimizationsPath))
                {
                    Directory.CreateDirectory(optimizationsPath);
                }

                foreach (string file in Directory.GetFiles(optimizationsPath, "*.json"))
                {
                    try
                    {
                        string json = File.ReadAllText(file);
                        var optimizations = JsonSerializer.Deserialize<List<HardwareSpecificOptimization>>(json);

                        if (optimizations != null)
                        {
                            _optimizations.AddRange(optimizations);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Log($"Error loading optimization from {file}: {ex.Message}", LogLevel.ERROR);
                    }
                }

                _logger.Log($"Loaded {_optimizations.Count} hardware-specific optimizations", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading optimizations: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void LoadPowerProfiles()
        {
            try
            {
                _powerProfiles.Clear();

                string profilesPath = Path.Combine(_optimizationsDirectory, "power");
                if (!Directory.Exists(profilesPath))
                {
                    Directory.CreateDirectory(profilesPath);
                }

                foreach (string file in Directory.GetFiles(profilesPath, "*.json"))
                {
                    try
                    {
                        string json = File.ReadAllText(file);
                        var profiles = JsonSerializer.Deserialize<List<PowerManagementProfile>>(json);

                        if (profiles != null)
                        {
                            _powerProfiles.AddRange(profiles);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Log($"Error loading power profile from {file}: {ex.Message}", LogLevel.ERROR);
                    }
                }

                _logger.Log($"Loaded {_powerProfiles.Count} power management profiles", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading power profiles: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void SaveOptimization(HardwareSpecificOptimization optimization)
        {
            try
            {
                string optimizationsPath = Path.Combine(_optimizationsDirectory, "hardware");
                if (!Directory.Exists(optimizationsPath))
                {
                    Directory.CreateDirectory(optimizationsPath);
                }

                string filePath = Path.Combine(optimizationsPath, $"{SanitizeFileName(optimization.Name)}.json");
                string json = JsonSerializer.Serialize(optimization, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving optimization: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void SavePowerProfile(PowerManagementProfile profile)
        {
            try
            {
                string profilesPath = Path.Combine(_optimizationsDirectory, "power");
                if (!Directory.Exists(profilesPath))
                {
                    Directory.CreateDirectory(profilesPath);
                }

                string filePath = Path.Combine(profilesPath, $"{SanitizeFileName(profile.Name)}.json");
                string json = JsonSerializer.Serialize(profile, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving power profile: {ex.Message}", LogLevel.ERROR);
            }
        }

        private string SanitizeFileName(string fileName)
        {
            return string.Join("_", fileName.Split(Path.GetInvalidFileNameChars()));
        }

        /// <summary>
        /// Handles incompatibility detection events
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event arguments</param>
        private void OnIncompatibilityDetected(object sender, IncompatibilityDetectedEventArgs e)
        {
            try
            {
                // Log the incompatibility
                string message = string.Empty;

                if (!string.IsNullOrEmpty(e.OptimizationName))
                {
                    message = $"Incompatibility detected for optimization '{e.OptimizationName}': {e.Reason}";
                    _logger.Log(message, LogLevel.WARNING);

                    // Find the optimization
                    var optimization = _optimizations.FirstOrDefault(o => o.Name == e.OptimizationName && o.IsEnabled);

                    if (optimization != null)
                    {
                        // Automatically revert the optimization if it's high confidence
                        if (e.Confidence == CompatibilityConfidence.High)
                        {
                            _logger.Log($"Automatically reverting incompatible optimization: {optimization.Name}", LogLevel.WARNING);

                            // Revert the optimization asynchronously
                            Task.Run(async () => await RevertOptimizationAsync(optimization));
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(e.PowerProfileName))
                {
                    message = $"Incompatibility detected for power profile '{e.PowerProfileName}': {e.Reason}";
                    _logger.Log(message, LogLevel.WARNING);

                    // Find the power profile
                    var profile = _powerProfiles.FirstOrDefault(p => p.Name == e.PowerProfileName && p.IsEnabled);

                    if (profile != null)
                    {
                        // Automatically revert the power profile if it's high confidence
                        if (e.Confidence == CompatibilityConfidence.High)
                        {
                            _logger.Log($"Automatically reverting incompatible power profile: {profile.Name}", LogLevel.WARNING);

                            // Revert the power profile asynchronously
                            Task.Run(async () => await RevertPowerProfileAsync(profile));
                        }
                    }
                }

                // Note: In a real implementation, we would show a notification to the user
                // For now, we'll just log the incompatibility
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling incompatibility detection: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets a value indicating whether compatibility checking is enabled
        /// </summary>
        public bool CheckCompatibilityBeforeApplying
        {
            get => _checkCompatibilityBeforeApplying;
            set => _checkCompatibilityBeforeApplying = value;
        }

        public void Initialize()
        {
            try
            {
                _logger.LogInfo("Initializing hardware optimization service...");

                // Load optimization settings
                LoadOptimizationSettings();

                _isInitialized = true;
                _logger.LogSuccess("Hardware optimization service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize hardware optimization service", ex);
                throw;
            }
        }

        public void Stop()
        {
            try
            {
                _logger.LogInfo("Stopping hardware optimization service...");
                _optimizationSettings.Clear();
                _isInitialized = false;
                _logger.LogSuccess("Hardware optimization service stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to stop hardware optimization service", ex);
                throw;
            }
        }

        public async Task<bool> OptimizeSystem(OptimizationType optimizationType)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("Hardware optimization service is not initialized");
            }

            try
            {
                _logger.LogInfo($"Starting system optimization with type: {optimizationType}");

                var optimizationTasks = new List<Task<bool>>
                {
                    OptimizeCPU(optimizationType),
                    OptimizeGPU(optimizationType),
                    OptimizeMemory(optimizationType),
                    OptimizeStorage(optimizationType),
                    OptimizeNetwork(optimizationType)
                };

                var results = await Task.WhenAll(optimizationTasks);
                var success = Array.TrueForAll(results, result => result);

                if (success)
                {
                    _logger.LogSuccess("System optimization completed successfully");
                }
                else
                {
                    _logger.LogWarning("System optimization completed with some failures");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to optimize system", ex);
                return false;
            }
        }

        private async Task<bool> OptimizeCPU(OptimizationType optimizationType)
        {
            try
            {
                _logger.LogInfo("Optimizing CPU...");

                var script = optimizationType switch
                {
                    OptimizationType.Performance => @"
                        $planName = 'High performance'
                        $powerPlanLine = powercfg /list | Select-String -Pattern $planName
                        if ($powerPlanLine -match '([a-f0-9]{8}-([a-f0-9]{4}-){3}[a-f0-9]{12})') {
                            $guid = $matches[1]
                            powercfg /setactive $guid
                            Write-Host ""Set power plan to High Performance ($guid)""
                        } else {
                            Write-Warning ""Could not find GUID for power plan: $planName""
                        }
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl' -Name 'Win32PrioritySeparation' -Value 26
                    """,
                    OptimizationType.PowerEfficiency => @"
                        $planName = 'Power saver'
                        $powerPlanLine = powercfg /list | Select-String -Pattern $planName
                        if ($powerPlanLine -match '([a-f0-9]{8}-([a-f0-9]{4}-){3}[a-f0-9]{12})') {
                            $guid = $matches[1]
                            powercfg /setactive $guid
                            Write-Host ""Set power plan to Power Saver ($guid)""
                        } else {
                            Write-Warning ""Could not find GUID for power plan: $planName""
                        }
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl' -Name 'Win32PrioritySeparation' -Value 18
                    """,
                    OptimizationType.Balanced => @"
                        $planName = 'Balanced'
                        $powerPlanLine = powercfg /list | Select-String -Pattern $planName
                        if ($powerPlanLine -match '([a-f0-9]{8}-([a-f0-9]{4}-){3}[a-f0-9]{12})') {
                            $guid = $matches[1]
                            powercfg /setactive $guid
                            Write-Host ""Set power plan to Balanced ($guid)""
                        } else {
                            Write-Warning ""Could not find GUID for power plan: $planName""
                        }
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl' -Name 'Win32PrioritySeparation' -Value 2
                    """,
                    OptimizationType.Gaming => @"
                        $planName = 'Ultimate Performance'
                        $powerPlanLine = powercfg /list | Select-String -Pattern $planName
                        if ($powerPlanLine -match '([a-f0-9]{8}-([a-f0-9]{4}-){3}[a-f0-9]{12})') {
                            $guid = $matches[1]
                            powercfg /setactive $guid
                            Write-Host ""Set power plan to Ultimate Performance ($guid)""
                        } else {
                            # Fallback to High Performance if Ultimate is not found
                            $planName = 'High performance'
                            $powerPlanLine = powercfg /list | Select-String -Pattern $planName
                            if ($powerPlanLine -match '([a-f0-9]{8}-([a-f0-9]{4}-){3}[a-f0-9]{12})') {
                                $guid = $matches[1]
                                powercfg /setactive $guid
                                Write-Host ""Set power plan to High Performance (fallback from Ultimate) ($guid)""
                            } else {
                                Write-Warning ""Could not find GUID for Ultimate Performance or High Performance power plan.""
                            }
                        }
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl' -Name 'Win32PrioritySeparation' -Value 28
                        Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile' -Name 'SystemResponsiveness' -Value 0
                        Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games' -Name 'Priority' -Value 6
                        Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games' -Name 'GPU Priority' -Value 8
                    """,
                    _ => throw new ArgumentException($"Unsupported optimization type: {optimizationType}")
                };

                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"CPU optimization failed: {string.Join(", ", result.Errors)}");
                    return false;
                }

                _logger.LogSuccess("CPU optimization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to optimize CPU", ex);
                return false;
            }
        }

        private async Task<bool> OptimizeGPU(OptimizationType optimizationType)
        {
            try
            {
                _logger.LogInfo("Optimizing GPU...");

                var script = optimizationType switch
                {
                    OptimizationType.Performance or OptimizationType.Gaming => @"
                        Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile' -Name 'GraphicsPreferencePolicy' -Value 2
                        Set-ItemProperty -Path 'HKCU:\Software\Microsoft\DirectX\GraphicsSettings' -Name 'SwapEffectUpgradeCache' -Value 1
                        Set-ItemProperty -Path 'HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects' -Name 'VisualFXSetting' -Value 2
                    ",
                    OptimizationType.PowerEfficiency => @"
                        Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile' -Name 'GraphicsPreferencePolicy' -Value 0
                        Set-ItemProperty -Path 'HKCU:\Software\Microsoft\DirectX\GraphicsSettings' -Name 'SwapEffectUpgradeCache' -Value 0
                        Set-ItemProperty -Path 'HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects' -Name 'VisualFXSetting' -Value 3
                    ",
                    OptimizationType.Balanced => @"
                        Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile' -Name 'GraphicsPreferencePolicy' -Value 1
                        Set-ItemProperty -Path 'HKCU:\Software\Microsoft\DirectX\GraphicsSettings' -Name 'SwapEffectUpgradeCache' -Value 1
                        Set-ItemProperty -Path 'HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects' -Name 'VisualFXSetting' -Value 1
                    ",
                    _ => throw new ArgumentException($"Unsupported optimization type: {optimizationType}")
                };

                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"GPU optimization failed: {string.Join(", ", result.Errors)}");
                    return false;
                }

                _logger.LogSuccess("GPU optimization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to optimize GPU", ex);
                return false;
            }
        }

        private async Task<bool> OptimizeMemory(OptimizationType optimizationType)
        {
            try
            {
                _logger.LogInfo("Optimizing memory...");

                var script = optimizationType switch
                {
                    OptimizationType.Performance or OptimizationType.Gaming => @"
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management' -Name 'LargeSystemCache' -Value 1
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management' -Name 'DisablePagingExecutive' -Value 1
                        wmic computersystem set AutomaticManagedPagefile=False
                        wmic pagefileset delete
                        wmic pagefileset create name='C:\pagefile.sys' initialsize=16384 maximumsize=16384
                    ",
                    OptimizationType.PowerEfficiency => @"
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management' -Name 'LargeSystemCache' -Value 0
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management' -Name 'DisablePagingExecutive' -Value 0
                        wmic computersystem set AutomaticManagedPagefile=True
                    ",
                    OptimizationType.Balanced => @"
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management' -Name 'LargeSystemCache' -Value 0
                        Set-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management' -Name 'DisablePagingExecutive' -Value 0
                        wmic computersystem set AutomaticManagedPagefile=False
                        wmic pagefileset delete
                        wmic pagefileset create name='C:\pagefile.sys' initialsize=8192 maximumsize=8192
                    ",
                    _ => throw new ArgumentException($"Unsupported optimization type: {optimizationType}")
                };

                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Memory optimization failed: {string.Join(", ", result.Errors)}");
                    return false;
                }

                _logger.LogSuccess("Memory optimization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to optimize memory", ex);
                return false;
            }
        }

        private async Task<bool> OptimizeStorage(OptimizationType optimizationType)
        {
            try
            {
                _logger.LogInfo("Optimizing storage...");

                var script = @"
                    # Enable TRIM for SSDs
                    fsutil behavior set DisableDeleteNotify 0
                    
                    # Optimize drives
                    Get-PhysicalDisk | ForEach-Object {
                        $diskNumber = $_.DeviceId
                        if ($_.MediaType -eq 'SSD') {
                            defrag /C /O /L /U /V $diskNumber
                        } else {
                            defrag /C /H /U /V $diskNumber
                        }
                    }
                    
                    # Disable SuperFetch/SysMain for SSDs
                    if ((Get-PhysicalDisk | Where-Object MediaType -eq 'SSD').Count -gt 0) {
                        Stop-Service -Name 'SysMain' -Force
                        Set-Service -Name 'SysMain' -StartupType Disabled
                    }
                ";

                var result = await _powerShell.ExecuteScriptAsync(script);
                if (result.HadErrors)
                {
                    _logger.LogError($"Storage optimization failed: {string.Join(", ", result.Errors)}");
                    return false;
                }

                _logger.LogSuccess("Storage optimization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to optimize storage", ex);
                return false;
            }
        }

        private async Task<bool> OptimizeNetwork(OptimizationType optimizationType)
        {
            try
            {
                _logger.LogInfo("Optimizing network...");

                var scriptTemplate = optimizationType switch
                {
                    OptimizationType.Balanced => @"
                        # Generic network optimizations for Balanced
                        # (Add your PowerShell commands here)
                    ",
                    OptimizationType.Performance => @"
                        # Generic network optimizations for Performance
                        # (Add your PowerShell commands here)
                    ",
                    OptimizationType.LowLatency => @"
                        # Generic network optimizations for LowLatency
                        # (Add your PowerShell commands here)
                    ",
                    _ => @"# No network optimization script for this type"
                };

                var result = await _powerShell.ExecuteScriptAsync(scriptTemplate);
                if (result.HadErrors)
                {
                    _logger.LogError($"Failed to optimize network: {string.Join(", ", result.Errors)}");
                    return false;
                }
                _logger.LogSuccess("Network optimized successfully.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error optimizing network", ex);
                return false;
            }
        }

        private void LoadOptimizationSettings()
        {
            try
            {
                _optimizationSettings["CPU"] = new Dictionary<string, object>
                {
                    ["MaxPowerPlan"] = "High performance",
                    ["ProcessPriority"] = "High",
                    ["CoreParking"] = false
                };

                _optimizationSettings["GPU"] = new Dictionary<string, object>
                {
                    ["PowerManagement"] = "Prefer maximum performance",
                    ["TextureFiltering"] = "High performance",
                    ["VSync"] = false
                };

                _optimizationSettings["Memory"] = new Dictionary<string, object>
                {
                    ["PageFileSize"] = "16GB",
                    ["LargeSystemCache"] = true,
                    ["DisablePaging"] = true
                };

                _optimizationSettings["Storage"] = new Dictionary<string, object>
                {
                    ["TrimEnabled"] = true,
                    ["SuperFetchEnabled"] = false,
                    ["DefragSchedule"] = "Weekly"
                };

                _optimizationSettings["Network"] = new Dictionary<string, object>
                {
                    ["ReceiveSideScaling"] = true,
                    ["PowerSaving"] = false,
                    ["QoSEnabled"] = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load optimization settings", ex);
                throw;
            }
        }

        /// <summary>
        /// Applies a set of recommended optimizations based on detected hardware and game list for network QoS.
        /// This is intended to be the primary method for a user-initiated "Smart Optimize" or "Optimize My PC for Gaming".
        /// </summary>
        /// <returns>True if all critical operations succeeded, false otherwise.</returns>
        public async Task<bool> ApplyRecommendedHardwareOptimizationsAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogError("HardwareOptimizationService not initialized. Cannot apply recommended optimizations.");
                throw new InvalidOperationException("HardwareOptimizationService is not initialized.");
            }

            _logger.LogInfo("Starting application of recommended hardware optimizations...");

            HardwareInfo currentHardware = _hardwareDetectionService.GetHardwareInfo();
            if (currentHardware == null)
            {
                _logger.LogError("Failed to detect hardware. Cannot apply hardware-specific optimizations.");
                return false;
            }

            bool allSpecificOptimizationsSucceeded = true;
            int appliedSpecificOptimizationsCount = 0;

            if (_optimizations == null || !_optimizations.Any())
            {
                _logger.LogWarning("No hardware-specific optimizations loaded. Skipping this step.");
            }
            else
            {
                _logger.LogInfo($"Evaluating {_optimizations.Count} loaded hardware-specific optimizations against current hardware...");
                foreach (var opt in _optimizations)
                {
                    bool isApplicableAndRecommended = false;
                    if (opt.IsRecommended)
                    {
                        // Expanded matching logic for all hardware fields
                        if (opt.HardwareType == HardwareType.CPU && currentHardware.CPU != null)
                        {
                            isApplicableAndRecommended = (string.IsNullOrEmpty(opt.Manufacturer) || opt.Manufacturer.Equals(currentHardware.CPU.Manufacturer, StringComparison.OrdinalIgnoreCase)) &&
                                (string.IsNullOrEmpty(opt.ModelPattern) || Regex.IsMatch(currentHardware.CPU.Name, opt.ModelPattern, RegexOptions.IgnoreCase));
                        }
                        else if (opt.HardwareType == HardwareType.GPU && currentHardware.GPU != null)
                        {
                            isApplicableAndRecommended = (string.IsNullOrEmpty(opt.Manufacturer) || opt.Manufacturer.Equals(currentHardware.GPU.Vendor.ToString(), StringComparison.OrdinalIgnoreCase)) &&
                                (string.IsNullOrEmpty(opt.ModelPattern) || Regex.IsMatch(currentHardware.GPU.Name, opt.ModelPattern, RegexOptions.IgnoreCase));
                        }
                        else if (opt.HardwareType == HardwareType.RAM && currentHardware.RAM != null)
                        {
                            isApplicableAndRecommended = (string.IsNullOrEmpty(opt.Manufacturer) || opt.Manufacturer.Equals(currentHardware.RAM.Manufacturer, StringComparison.OrdinalIgnoreCase)) &&
                                (string.IsNullOrEmpty(opt.ModelPattern) || Regex.IsMatch(currentHardware.RAM.Model, opt.ModelPattern, RegexOptions.IgnoreCase));
                        }
                        else if (opt.HardwareType == HardwareType.Storage && currentHardware.Storage != null && currentHardware.Storage.Any())
                        {
                            isApplicableAndRecommended = currentHardware.Storage.Any(s =>
                                (string.IsNullOrEmpty(opt.Manufacturer) || opt.Manufacturer.Equals(s.Manufacturer, StringComparison.OrdinalIgnoreCase)) &&
                                (string.IsNullOrEmpty(opt.ModelPattern) || Regex.IsMatch(s.Model, opt.ModelPattern, RegexOptions.IgnoreCase)));
                        }
                        else if (opt.HardwareType == HardwareType.Motherboard && currentHardware.Motherboard != null)
                        {
                            isApplicableAndRecommended = (string.IsNullOrEmpty(opt.Manufacturer) || opt.Manufacturer.Equals(currentHardware.Motherboard.Manufacturer, StringComparison.OrdinalIgnoreCase)) &&
                                (string.IsNullOrEmpty(opt.ModelPattern) || Regex.IsMatch(currentHardware.Motherboard.Model, opt.ModelPattern, RegexOptions.IgnoreCase));
                        }
                        else if (opt.HardwareType == HardwareType.System)
                        {
                            isApplicableAndRecommended = opt.IsRecommended;
                        }
                    }

                    if (isApplicableAndRecommended)
                    {
                        _logger.LogInfo($"Attempting to apply recommended hardware-specific optimization: {opt.Name}");
                        bool specificOptSuccess = await ApplyOptimizationAsync(opt);
                        if (specificOptSuccess)
                        {
                            appliedSpecificOptimizationsCount++;
                        }
                        else
                        {
                            allSpecificOptimizationsSucceeded = false;
                            _logger.LogWarning($"Failed to apply specific optimization: {opt.Name}. Continuing with others.");
                        }
                    }
                }
                _logger.LogInfo($"Applied {appliedSpecificOptimizationsCount} specific hardware optimizations.");
            }

            _logger.LogInfo("Applying general network optimizations for gaming...");
            bool networkOptSuccess = await OptimizeNetwork(OptimizationType.Gaming);
            if (!networkOptSuccess)
            {
                _logger.LogWarning("General network optimization for gaming failed or had issues.");
            }

            bool overallSuccess = allSpecificOptimizationsSucceeded && networkOptSuccess;

            if (overallSuccess)
            {
                _logger.LogSuccess("Recommended hardware optimizations applied successfully.");
            }
            else
            {
                _logger.LogWarning("Recommended hardware optimizations applied with some failures or warnings.");
            }
            return overallSuccess;
        }

        // Method to load smart tweaks from JSON
        private void LoadSmartTweaks()
        {
            _logger.LogInfo("Loading smart tweaks configuration...");
            if (!File.Exists(_smartTweaksConfigPath))
            {
                _logger.LogWarning($"Smart tweaks configuration file not found at: {_smartTweaksConfigPath}");
                _smartTweaks = new List<SmartTweak>(); // Ensure it's an empty list, not null
                return;
            }

            try
            {
                string jsonContent = File.ReadAllText(_smartTweaksConfigPath);
                _smartTweaks = JsonSerializer.Deserialize<List<SmartTweak>>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true // Helpful if JSON casing differs slightly
                });

                if (_smartTweaks == null) // Deserialize can return null if JSON is just "null"
                {
                    _smartTweaks = new List<SmartTweak>();
                    _logger.LogWarning("Smart tweaks configuration file was empty or invalid. Loaded 0 smart tweaks.");
                }
                else
                {
                    _logger.LogSuccess($"Successfully loaded {_smartTweaks.Count} smart tweaks.");
                }
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError($"Error deserializing smart_tweaks_config.json: {jsonEx.Message}", jsonEx);
                _smartTweaks = new List<SmartTweak>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to load smart tweaks: {ex.Message}", ex);
                _smartTweaks = new List<SmartTweak>();
            }
        }

        public async Task<List<SmartTweak>> GetRecommendedSmartTweaksAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("HardwareOptimizationService not initialized. Cannot get recommended smart tweaks.");
                return new List<SmartTweak>();
            }

            HardwareInfo hardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync();
            if (hardwareInfo == null)
            {
                _logger.LogError("Failed to retrieve hardware information for smart tweak evaluation.");
                return new List<SmartTweak>();
            }

            var recommendedTweaks = new List<SmartTweak>();
            if (_smartTweaks == null || !_smartTweaks.Any())
            {
                _logger.LogInfo("No smart tweaks loaded to evaluate.");
                return recommendedTweaks;
            }

            _logger.LogInfo($"Evaluating {_smartTweaks.Count} smart tweaks against current hardware...");

            foreach (var tweak in _smartTweaks)
            {
                if (EvaluateSmartTweakCondition(tweak, hardwareInfo))
                {
                    tweak.IsCurrentlyApplied = _appliedSmartTweakIds.Contains(tweak.TweakId);
                    recommendedTweaks.Add(tweak);
                }
            }

            _logger.LogInfo($"Found {recommendedTweaks.Count} applicable smart tweaks.");
            return recommendedTweaks;
        }

        private bool EvaluateSmartTweakCondition(SmartTweak tweak, HardwareInfo info)
        {
            if (info == null || string.IsNullOrWhiteSpace(tweak.Condition))
            {
                return false; // Cannot evaluate if no info or no condition
            }

            _logger.LogDebug($"Evaluating condition for tweak '{tweak.TweakId}': {tweak.Condition}");

            // V1: Simple direct evaluation for known conditions. 
            // This will be expanded to a more generic parser.
            // For now, let's hardcode evaluation for the conditions we defined in smart_tweaks_config.json

            try
            {
                // Example direct evaluation for HAGS enable condition:
                // "OperatingSystem.HardwareAcceleratedGPUScheduling == HAGSStatus.Disabled && OperatingSystem.WddmVersion >= WDDMVersion.WDDM_2_7"
                if (tweak.TweakId == "EnableHAGS_IfNeeded")
                {
                    return info.OperatingSystem.HardwareAcceleratedGPUScheduling == HAGSStatus.Disabled &&
                           info.OperatingSystem.WddmVersion >= WDDMVersion.WDDM_2_7;
                }
                // Example direct evaluation for HAGS disable condition:
                // "OperatingSystem.HardwareAcceleratedGPUScheduling == HAGSStatus.Enabled"
                if (tweak.TweakId == "DisableHAGS_IfEnabled")
                {
                    return info.OperatingSystem.HardwareAcceleratedGPUScheduling == HAGSStatus.Enabled;
                }
                // Example direct evaluation for DisableCoreParking condition:
                // "CPU.Cores >= 2"
                if (tweak.TweakId == "DisableCoreParking")
                {
                    return info.CPU.Cores >= 2;
                }
                // Example direct evaluation for EnsureSystemManagedPageFile_LowRam condition:
                // "(OperatingSystem.PageFile.UsageType == PageFileUsageType.Disabled || (OperatingSystem.PageFile.UsageType == PageFileUsageType.Custom && OperatingSystem.PageFile.MaximumSizeMB < 4096)) && RAM.TotalCapacity < 16384"
                if (tweak.TweakId == "EnsureSystemManagedPageFile_LowRam")
                {
                    bool pageFileCondition = info.OperatingSystem.PageFile.UsageType == PageFileUsageType.Disabled ||
                                             (info.OperatingSystem.PageFile.UsageType == PageFileUsageType.Custom && info.OperatingSystem.PageFile.MaximumSizeMB < 4096);
                    bool ramCondition = info.RAM.TotalCapacity < 16384; // Assuming TotalCapacity is in MB
                    return pageFileCondition && ramCondition;
                }

                _logger.LogWarning($"Condition for TweakId '{tweak.TweakId}' not handled by current simple evaluator: {tweak.Condition}");
                return false; // Default to false if condition string is not recognized by this simple parser
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error evaluating condition for TweakId '{tweak.TweakId}' (Condition: {tweak.Condition}): {ex.Message}", ex);
                return false;
            }
        }

        // ADD THESE NEW METHODS:
        public async Task<bool> ApplySmartTweakAsync(SmartTweak tweak)
        {
            if (tweak == null)
            {
                _logger.LogError("Cannot apply null smart tweak.");
                return false;
            }

            _logger.LogInfo($"Attempting to apply smart tweak: {tweak.SettingName} (ID: {tweak.TweakId})");

            if (tweak.RequiresAdmin && !_adminOperationsService.IsAdmin)
            {
                _logger.LogWarning($"Smart tweak '{tweak.SettingName}' requires admin privileges, but not running as admin. Attempting anyway, but may fail.");
                // Optionally, you could explicitly request elevation here or prevent the attempt.
            }

            bool success = false;
            try
            {
                switch (tweak.TweakType?.ToLowerInvariant())
                {
                    case "registry":
                        success = ApplyRegistrySmartTweak(tweak.ApplyAction);
                        break;
                    case "powershell":
                        // Assuming ApplyAction.Script contains the script for PowerShell
                        if (!string.IsNullOrWhiteSpace(tweak.ApplyAction?.Script))
                        {
                            var result = await _powerShell.ExecuteScriptAsync(tweak.ApplyAction.Script);
                            success = !result.HadErrors;
                            if (!success)
                            {
                                _logger.LogError($"PowerShell script for smart tweak '{tweak.SettingName}' failed. Errors: {string.Join("\n", result.Errors)}");
                            }
                        }
                        else
                        {
                            _logger.LogWarning($"PowerShell smart tweak '{tweak.SettingName}' has no script in ApplyAction.");
                        }
                        break;
                    case "informational":
                        _logger.LogInfo($"Smart tweak '{tweak.SettingName}' is informational. No action taken by ApplySmartTweakAsync.");
                        success = true; // Informational tweaks are considered "applied" by displaying them.
                        break;
                    default:
                        _logger.LogWarning($"Unsupported TweakType '{tweak.TweakType}' for smart tweak: {tweak.SettingName}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error applying smart tweak '{tweak.SettingName}': {ex.Message}", ex);
                success = false;
            }

            if (success)
            {
                _logger.LogSuccess($"Successfully applied smart tweak: {tweak.SettingName}");
                _appliedSmartTweakIds.Add(tweak.TweakId); // Track applied tweak
                tweak.IsCurrentlyApplied = true; // Update in-memory object state
                // Optionally raise an event here
                if (tweak.RequiresRestart)
                {
                    _logger.LogInfo("This tweak recommends a system restart to take full effect.");
                    // UI should handle prompting user for restart
                }
            }
            else
            {
                _logger.LogError($"Failed to apply smart tweak: {tweak.SettingName}");
            }
            return success;
        }

        public async Task<bool> RevertSmartTweakAsync(SmartTweak tweak)
        {
            if (tweak == null)
            {
                _logger.LogError("Cannot revert null smart tweak.");
                return false;
            }

            _logger.LogInfo($"Attempting to revert smart tweak to default: {tweak.SettingName} (ID: {tweak.TweakId})");

            if (tweak.RequiresAdmin && !_adminOperationsService.IsAdmin)
            {
                _logger.LogWarning($"Smart tweak revert for '{tweak.SettingName}' requires admin privileges, but not running as admin. Attempting anyway, but may fail.");
            }

            bool success = false;
            try
            {
                switch (tweak.TweakType?.ToLowerInvariant())
                {
                    case "registry":
                        // For registry tweaks, we'll use the default Windows values
                        switch (tweak.TweakId)
                        {
                            case "EnableHAGS_IfNeeded":
                            case "DisableHAGS_IfEnabled":
                                // Default Windows value for HAGS is 0 (disabled)
                                success = ApplyRegistrySmartTweak(new SmartTweakAction
                                {
                                    Path = "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers",
                                    ValueName = "HwSchMode",
                                    ValueData = "0",
                                    ValueKind = "DWORD"
                                });
                                break;
                            case "DisableCoreParking":
                                // Default Windows value for core parking is 100 (enabled)
                                success = ApplyRegistrySmartTweak(new SmartTweakAction
                                {
                                    Path = "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\5d76a2ca-e8c0-402f-a133-2158492d58ad\\dec35c318583",
                                    ValueName = "ValueMax",
                                    ValueData = "100",
                                    ValueKind = "DWORD"
                                });
                                break;
                            default:
                                _logger.LogWarning($"No default value defined for registry tweak: {tweak.TweakId}");
                                success = false;
                                break;
                        }
                        break;
                    case "powershell":
                        // For PowerShell tweaks, we'll use the default Windows settings
                        switch (tweak.TweakId)
                        {
                            case "EnsureSystemManagedPageFile_LowRam":
                                // Default Windows behavior is to let the system manage the page file
                                var result = await _powerShell.ExecuteScriptAsync(
                                    "Get-WmiObject Win32_PageFileSetting | ForEach-Object { $_.Delete() }; " +
                                    "Clear-ItemProperty -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management' -Name PagingFiles -ErrorAction SilentlyContinue -Force; " +
                                    "$SystemDrive = $env:SystemDrive; " +
                                    "Set-ItemProperty -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management' -Name PagingFiles -Value \"${SystemDrive}\\pagefile.sys 0 0\" -Type MultiString -Force");
                                success = !result.HadErrors;
                                if (!success)
                                {
                                    _logger.LogError($"PowerShell script for reverting smart tweak '{tweak.SettingName}' failed. Errors: {string.Join("\n", result.Errors)}");
                                }
                                break;
                            default:
                                _logger.LogWarning($"No default value defined for PowerShell tweak: {tweak.TweakId}");
                                success = false;
                                break;
                        }
                        break;
                    case "informational":
                        _logger.LogInfo($"Smart tweak '{tweak.SettingName}' is informational. No revert action taken by RevertSmartTweakAsync.");
                        success = true;
                        break;
                    default:
                        _logger.LogWarning($"Unsupported TweakType '{tweak.TweakType}' for smart tweak: {tweak.SettingName}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reverting smart tweak '{tweak.SettingName}': {ex.Message}", ex);
                success = false;
            }

            if (success)
            {
                _logger.LogSuccess($"Successfully reverted smart tweak to default: {tweak.SettingName}");
                _appliedSmartTweakIds.Remove(tweak.TweakId); // Remove from applied tweaks
                tweak.IsCurrentlyApplied = false; // Update in-memory object state
                if (tweak.RequiresRestart)
                {
                    _logger.LogInfo("This tweak recommends a system restart to take full effect.");
                }
            }
            else
            {
                _logger.LogError($"Failed to revert smart tweak to default: {tweak.SettingName}");
            }
            return success;
        }

        private bool ApplyRegistrySmartTweak(SmartTweakAction action)
        {
            if (action == null || string.IsNullOrWhiteSpace(action.Path) || string.IsNullOrWhiteSpace(action.ValueName))
            {
                _logger.LogWarning("Registry action is null or has invalid Path/ValueName.");
                return false;
            }

            try
            {
                RegistryValueKind valueKind = RegistryValueKind.Unknown;
                object valueData = action.ValueData; // Keep as string initially

                switch (action.ValueKind?.ToUpperInvariant())
                {
                    case "DWORD":
                        valueKind = RegistryValueKind.DWord;
                        if (int.TryParse(action.ValueData, out int dwordValue))
                            valueData = dwordValue;
                        else {
                            _logger.LogError($"Could not parse ValueData '{action.ValueData}' as DWORD for {action.Path}\\{action.ValueName}");
                            return false;
                        }
                        break;
                    case "QWORD":
                        valueKind = RegistryValueKind.QWord;
                         if (long.TryParse(action.ValueData, out long qwordValue))
                            valueData = qwordValue;
                        else {
                            _logger.LogError($"Could not parse ValueData '{action.ValueData}' as QWORD for {action.Path}\\{action.ValueName}");
                            return false;
                        }
                        break;
                    case "STRING":
                        valueKind = RegistryValueKind.String;
                        break;
                    case "EXPANDSTRING":
                        valueKind = RegistryValueKind.ExpandString;
                        break;
                    case "BINARY":
                        valueKind = RegistryValueKind.Binary;
                        // Assuming ValueData is a hex string e.g., "00FF10AB"
                        // This needs more robust parsing if used.
                        try {
                            valueData = Enumerable.Range(0, action.ValueData.Length)
                                                 .Where(x => x % 2 == 0)
                                                 .Select(x => Convert.ToByte(action.ValueData.Substring(x, 2), 16))
                                                 .ToArray();
                        } catch (Exception ex) {
                            _logger.LogError($"Could not parse ValueData '{action.ValueData}' as BINARY for {action.Path}\\{action.ValueName}: {ex.Message}");
                            return false;
                        }
                        break;
                    case "MULTISTRING":
                        valueKind = RegistryValueKind.MultiString;
                        // Assuming ValueData is a single string that might represent multiple lines, 
                        // or needs to be split if it contains explicit separators like \n that should be converted to a string array.
                        // For simple cases where PowerShell sets it, the string itself might be fine if it's already formatted correctly by PS.
                        // If we need to create an array from a delimited string: valueData = action.ValueData.Split(';');
                        valueData = new string[] { action.ValueData }; // Simple case: treat as a single entry for MultiString array
                        // For the pagefile case: 'C:\pagefile.sys 0 0' is ONE string in the MultiString array.
                        break;
                    default:
                        _logger.LogWarning($"Unsupported RegistryValueKind: {action.ValueKind} for {action.Path}\\{action.ValueName}. Defaulting to String.");
                        valueKind = RegistryValueKind.String;
                        break;
                }

                Registry.SetValue(action.Path, action.ValueName, valueData, valueKind);
                _logger.LogInfo($"Registry value set: {action.Path}\\{action.ValueName} = {action.ValueData} (Kind: {valueKind})");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error applying registry smart tweak action ({action.Path}\\{action.ValueName}): {ex.Message}", ex);
                return false;
            }
        }

        // Helper methods for registry operations
        private void ParseRegistryKey(string fullKeyPath, out RegistryHive hive, out string pathWithoutHive)
        {
            if (string.IsNullOrEmpty(fullKeyPath))
                throw new ArgumentNullException(nameof(fullKeyPath));

            string[] parts = fullKeyPath.Split(new[] { '\\' }, 2, StringSplitOptions.RemoveEmptyEntries);
            string hiveName = parts[0].ToUpperInvariant();
            pathWithoutHive = parts.Length > 1 ? parts[1] : string.Empty;

            switch (hiveName)
            {
                case "HKEY_CLASSES_ROOT":
                case "HKCR":
                    hive = RegistryHive.ClassesRoot;
                    break;
                case "HKEY_CURRENT_CONFIG":
                case "HKCC":
                    hive = RegistryHive.CurrentConfig;
                    break;
                case "HKEY_CURRENT_USER":
                case "HKCU":
                    hive = RegistryHive.CurrentUser;
                    break;
                case "HKEY_LOCAL_MACHINE":
                case "HKLM":
                    hive = RegistryHive.LocalMachine;
                    break;
                case "HKEY_USERS":
                case "HKU":
                    hive = RegistryHive.Users;
                    break;
                default:
                    throw new ArgumentException($"Invalid registry hive: {hiveName} in path {fullKeyPath}");
            }
        }

        private RegistryValueKind ParseRegistryValueKind(string kindStr)
        {
            if (string.IsNullOrEmpty(kindStr)) return RegistryValueKind.Unknown; // Let Registry.SetValue infer or default
            switch (kindStr.ToLowerInvariant())
            {
                case "string": return RegistryValueKind.String;
                case "expandstring": return RegistryValueKind.ExpandString;
                case "binary": return RegistryValueKind.Binary;
                case "dword": return RegistryValueKind.DWord;
                case "qword": return RegistryValueKind.QWord;
                case "multistring": return RegistryValueKind.MultiString;
                case "unknown": return RegistryValueKind.Unknown;
                default:
                    _logger?.Log($"Unknown RegistryValueKind specified: '{kindStr}'. Defaulting to Unknown.", Models.LogLevel.WARNING);
                    return RegistryValueKind.Unknown; // Let Registry.SetValue attempt to infer
            }
        }

        private object ProcessRegistryValue(object value, RegistryValueKind kind, LoggingService logger) // Added logger instance
        {
            if (value == null) return null;

            if (value is JsonElement jsonElement)
            {
                try
                {
                    switch (kind)
                    {
                        case RegistryValueKind.DWord:
                            return jsonElement.ValueKind == JsonValueKind.Number ? jsonElement.GetInt32() : Convert.ToInt32(jsonElement.GetString());
                        case RegistryValueKind.QWord:
                            return jsonElement.ValueKind == JsonValueKind.Number ? jsonElement.GetInt64() : Convert.ToInt64(jsonElement.GetString());
                        case RegistryValueKind.String:
                        case RegistryValueKind.ExpandString:
                        case RegistryValueKind.Unknown: // If unknown, assume string if it's a Json string
                            return jsonElement.ValueKind == JsonValueKind.String ? jsonElement.GetString() : jsonElement.ToString();
                        case RegistryValueKind.MultiString:
                            return jsonElement.ValueKind == JsonValueKind.Array ?
                                   jsonElement.EnumerateArray().Select(e => e.GetString()).ToArray() :
                                   new string[] { jsonElement.GetString() }; // Fallback for single string
                        case RegistryValueKind.Binary:
                            if (jsonElement.ValueKind == JsonValueKind.String)
                                return Convert.FromBase64String(jsonElement.GetString());
                            if (jsonElement.ValueKind == JsonValueKind.Array)
                                return jsonElement.EnumerateArray().Select(e => e.GetByte()).ToArray();
                            logger?.Log($"Cannot convert JsonElement of type {jsonElement.ValueKind} to Binary. Reverting to raw string.", Models.LogLevel.WARNING);
                            return jsonElement.ToString(); // Fallback
                        default:
                            logger?.Log($"Unhandled JsonElement conversion for RegistryValueKind {kind}. Reverting to raw string.", Models.LogLevel.WARNING);
                            return jsonElement.ToString();
                    }
                }
                catch (Exception ex)
                {
                    logger?.Log($"Error processing JsonElement for registry value (Kind: {kind}): {ex.Message}. Value: {jsonElement.GetRawText()}", Models.LogLevel.ERROR, ex);
                    // Fallback to a raw string representation or re-throw, depending on desired strictness
                    return jsonElement.GetRawText();
                }
            }

            // If it's already a CLR type, ensure it's compatible or convert
            if (kind == RegistryValueKind.DWord && !(value is int)) return Convert.ToInt32(value);
            if (kind == RegistryValueKind.QWord && !(value is long)) return Convert.ToInt64(value);
            if (kind == RegistryValueKind.MultiString && value is string valStr) return new string[] { valStr }; // Handle single string for MultiString

            return value;
        }

        private bool IsApplicableAndRecommended(HardwareSpecificOptimization opt, HardwareInfo currentHardware)
        {
            // Not implemented: detailed matching logic
            _logger.LogWarning($"Hardware matching logic for optimization '{opt.Name}' is not yet implemented. Assuming not applicable.");
            return false;
        }

        public static void SetInstance(HardwareOptimizationService instance)
        {
            _instance = instance;
        }
    }

    /// <summary>
    /// Event arguments for optimization events
    /// </summary>
    public class OptimizationEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the optimization
        /// </summary>
        public HardwareSpecificOptimization Optimization { get; }

        /// <summary>
        /// Initializes a new instance of the OptimizationEventArgs class
        /// </summary>
        /// <param name="optimization">The optimization</param>
        public OptimizationEventArgs(HardwareSpecificOptimization optimization)
        {
            Optimization = optimization;
        }
    }

    /// <summary>
    /// Event arguments for power profile events
    /// </summary>
    public class PowerProfileEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the power profile
        /// </summary>
        public PowerManagementProfile Profile { get; }

        /// <summary>
        /// Initializes a new instance of the PowerProfileEventArgs class
        /// </summary>
        /// <param name="profile">The power profile</param>
        public PowerProfileEventArgs(PowerManagementProfile profile)
        {
            Profile = profile;
        }
    }
}




