using System;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for tracking user activities and behavior
    /// </summary>
    public class UserTrackingService
    {
        private static UserTrackingService _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;

        /// <summary>
        /// Gets the singleton instance of the user tracking service
        /// </summary>
        public static UserTrackingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new UserTrackingService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes a new instance of the UserTrackingService class
        /// </summary>
        private UserTrackingService()
        {
            _logger = LoggingService.Instance;
        }

        /// <summary>
        /// Adds a user activity with 3 parameters (matching expected signature)
        /// </summary>
        /// <param name="activity">The activity description</param>
        /// <param name="details">Additional details</param>
        /// <param name="category">The activity category</param>
        public void AddUserActivity(string activity, string details, string category)
        {
            try
            {
                _logger?.Log($"User activity: {activity} - {details} [{category}]", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error adding user activity: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Adds a user activity with 1 parameter (overload)
        /// </summary>
        /// <param name="activity">The activity description</param>
        public void AddUserActivity(string activity)
        {
            AddUserActivity(activity, "", "General");
        }

        /// <summary>
        /// Registers a user login
        /// </summary>
        /// <param name="username">The username</param>
        public void RegisterUserLogin(string username)
        {
            try
            {
                _logger?.Log($"User login registered: {username}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error registering user login: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tracks user login
        /// </summary>
        /// <param name="username">The username</param>
        public void TrackLogin(string username)
        {
            try
            {
                _logger?.Log($"User login: {username}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error tracking login: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tracks user logout
        /// </summary>
        /// <param name="username">The username</param>
        public void TrackLogout(string username)
        {
            try
            {
                _logger?.Log($"User logout: {username}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error tracking logout: {ex.Message}", LogLevel.ERROR);
            }
        }
    }
}
