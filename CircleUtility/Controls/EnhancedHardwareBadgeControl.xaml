<UserControl x:Class="CircleUtility.Controls.EnhancedHardwareBadgeControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="30" d:DesignWidth="30">

    <UserControl.Resources>
        <!-- Badge icon style -->
        <Style x:Key="BadgeIconStyle" TargetType="Path">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Pulse animation -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterGlow"
                                           Storyboard.TargetProperty="Opacity"
                                           Duration="0:0:2">
                <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0.8"/>
                <LinearDoubleKeyFrame KeyTime="0:0:1" Value="0.3"/>
                <LinearDoubleKeyFrame KeyTime="0:0:2" Value="0.8"/>
            </DoubleAnimationUsingKeyFrames>

            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterGlow"
                                           Storyboard.TargetProperty="Width"
                                           Duration="0:0:2">
                <LinearDoubleKeyFrame KeyTime="0:0:0" Value="24"/>
                <LinearDoubleKeyFrame KeyTime="0:0:1" Value="28"/>
                <LinearDoubleKeyFrame KeyTime="0:0:2" Value="24"/>
            </DoubleAnimationUsingKeyFrames>

            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterGlow"
                                           Storyboard.TargetProperty="Height"
                                           Duration="0:0:2">
                <LinearDoubleKeyFrame KeyTime="0:0:0" Value="24"/>
                <LinearDoubleKeyFrame KeyTime="0:0:1" Value="28"/>
                <LinearDoubleKeyFrame KeyTime="0:0:2" Value="24"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>

        <!-- Rotate animation -->
        <Storyboard x:Key="RotateAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="BadgeRotation"
                             Storyboard.TargetProperty="Angle"
                             From="0" To="360" Duration="0:0:4" />
        </Storyboard>

        <!-- New hardware detection animation -->
        <Storyboard x:Key="NewHardwareAnimation">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="BadgeBackground"
                                           Storyboard.TargetProperty="Opacity"
                                           Duration="0:0:1.5">
                <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0"/>
                <LinearDoubleKeyFrame KeyTime="0:0:0.3" Value="1"/>
                <LinearDoubleKeyFrame KeyTime="0:0:0.6" Value="0.5"/>
                <LinearDoubleKeyFrame KeyTime="0:0:0.9" Value="1"/>
                <LinearDoubleKeyFrame KeyTime="0:0:1.2" Value="0.7"/>
                <LinearDoubleKeyFrame KeyTime="0:0:1.5" Value="1"/>
            </DoubleAnimationUsingKeyFrames>

            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="BadgeBackground"
                                           Storyboard.TargetProperty="Width"
                                           Duration="0:0:0.5">
                <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0"/>
                <LinearDoubleKeyFrame KeyTime="0:0:0.5" Value="20"/>
            </DoubleAnimationUsingKeyFrames>

            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="BadgeBackground"
                                           Storyboard.TargetProperty="Height"
                                           Duration="0:0:0.5">
                <LinearDoubleKeyFrame KeyTime="0:0:0" Value="0"/>
                <LinearDoubleKeyFrame KeyTime="0:0:0.5" Value="20"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </UserControl.Resources>

    <Grid x:Name="MainGrid" ToolTip="Detected Hardware Component">
        <!-- Outer glow -->
        <Ellipse x:Name="OuterGlow"
                 Width="24"
                 Height="24"
                 Fill="#20FFFFFF"
                 Opacity="0.5">
            <Ellipse.Effect>
                <BlurEffect Radius="8"/>
            </Ellipse.Effect>
        </Ellipse>

        <!-- Badge background -->
        <Ellipse x:Name="BadgeBackground"
                 Width="20"
                 Height="20"
                 Fill="#00C8FF">
            <Ellipse.RenderTransform>
                <RotateTransform x:Name="BadgeRotation"
                                 CenterX="10"
                                 CenterY="10"
                                 Angle="0"/>
            </Ellipse.RenderTransform>
            <Ellipse.Effect>
                <DropShadowEffect x:Name="GlowEffect"
                                  Color="#00C8FF"
                                  BlurRadius="10"
                                  ShadowDepth="0"
                                  Opacity="0.6"/>
            </Ellipse.Effect>
        </Ellipse>

        <!-- Hardware icon -->
        <Path x:Name="HardwareIcon"
              Style="{StaticResource BadgeIconStyle}"
              Data="M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"
              Fill="White"/>

        <!-- Compatibility score -->
        <Border x:Name="ScoreBadge"
                Width="14"
                Height="14"
                Background="#FF00C853"
                CornerRadius="7"
                HorizontalAlignment="Right"
                VerticalAlignment="Bottom"
                Margin="0,0,-5,-5"
                Visibility="Collapsed">
            <TextBlock x:Name="ScoreText"
                       Text="95"
                       FontSize="8"
                       FontWeight="Bold"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
        </Border>
    </Grid>
</UserControl>
