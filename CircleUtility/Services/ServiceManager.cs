using System;
using System.Collections.Generic;
using System.Diagnostics;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing other services
    /// </summary>
    public class ServiceManager
    {
        private static ServiceManager _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;
        private readonly Dictionary<Type, object> _services;
        private readonly Dictionary<Type, bool> _serviceInitialized;
        private readonly Dictionary<Type, Stopwatch> _serviceInitializationTimes;

        /// <summary>
        /// Gets the singleton instance of the service manager
        /// </summary>
        public static ServiceManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ServiceManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes a new instance of the ServiceManager class
        /// </summary>
        private ServiceManager()
        {
            _logger = LoggingService.Instance;
            _services = new Dictionary<Type, object>();
            _serviceInitialized = new Dictionary<Type, bool>();
            _serviceInitializationTimes = new Dictionary<Type, Stopwatch>();
        }

        /// <summary>
        /// Registers a service
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="service">The service instance</param>
        public void RegisterService<T>(T service) where T : class
        {
            try
            {
                Type serviceType = typeof(T);
                _services[serviceType] = service;
                _serviceInitialized[serviceType] = true;
                _logger.Log($"Service registered: {serviceType.Name}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error registering service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets a service
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>The service instance</returns>
        public T GetService<T>() where T : class
        {
            try
            {
                Type serviceType = typeof(T);
                if (_services.TryGetValue(serviceType, out object service))
                {
                    return (T)service;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting service: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Initializes all services
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool InitializeAllServices()
        {
            try
            {
                _logger.Log("Initializing all services...", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing all services: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }
    }
}
