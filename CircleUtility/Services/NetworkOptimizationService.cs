// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using Microsoft.Win32;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for optimizing network settings for gaming
    /// </summary>
    public class NetworkOptimizationService
    {
        private static NetworkOptimizationService _instance;
        private readonly LoggingService _logger;
        private bool _isTcpOptimized;
        private bool _isQosOptimized;
        private bool _isNetworkThrottlingDisabled;
        private bool _isNagleAlgorithmDisabled;
        private bool _isDnsOptimized;
        private string _currentDnsProvider = "Default";
        private NetworkInterface _primaryNetworkInterface;

        /// <summary>
        /// Initializes a new instance of the NetworkOptimizationService class
        /// </summary>
        private NetworkOptimizationService()
        {
            _logger = LoggingService.Instance;
            DetectNetworkInterfaces();
            CheckCurrentOptimizationStatus();
        }

        /// <summary>
        /// Gets the singleton instance of the network optimization service
        /// </summary>
        public static NetworkOptimizationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new NetworkOptimizationService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether TCP settings are optimized
        /// </summary>
        public bool IsTcpOptimized => _isTcpOptimized;

        /// <summary>
        /// Gets a value indicating whether QoS settings are optimized
        /// </summary>
        public bool IsQosOptimized => _isQosOptimized;

        /// <summary>
        /// Gets a value indicating whether network throttling is disabled
        /// </summary>
        public bool IsNetworkThrottlingDisabled => _isNetworkThrottlingDisabled;

        /// <summary>
        /// Gets a value indicating whether Nagle's algorithm is disabled
        /// </summary>
        public bool IsNagleAlgorithmDisabled => _isNagleAlgorithmDisabled;

        /// <summary>
        /// Gets a value indicating whether DNS settings are optimized
        /// </summary>
        public bool IsDnsOptimized => _isDnsOptimized;

        /// <summary>
        /// Gets the current DNS provider
        /// </summary>
        public string CurrentDnsProvider => _currentDnsProvider;

        /// <summary>
        /// Gets the primary network interface
        /// </summary>
        public NetworkInterface PrimaryNetworkInterface => _primaryNetworkInterface;

        /// <summary>
        /// Detects network interfaces
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DetectNetworkInterfaces()
        {
            try
            {
                _logger.Log("Detecting network interfaces...", LogLevel.INFO);

                NetworkInterface[] interfaces = NetworkInterface.GetAllNetworkInterfaces();

                // Find the primary interface (the one that's up and not loopback or tunnel)
                foreach (NetworkInterface ni in interfaces)
                {
                    if (ni.OperationalStatus == OperationalStatus.Up &&
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                        ni.NetworkInterfaceType != NetworkInterfaceType.Tunnel)
                    {
                        _primaryNetworkInterface = ni;
                        break;
                    }
                }

                if (_primaryNetworkInterface != null)
                {
                    _logger.Log($"Primary network interface detected: {_primaryNetworkInterface.Name} ({_primaryNetworkInterface.Description})", LogLevel.INFO);
                    return true;
                }
                else
                {
                    _logger.Log("No suitable network interface detected", LogLevel.WARNING);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting network interfaces: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes TCP settings for gaming
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeTcpSettings()
        {
            try
            {
                _logger.Log("Optimizing TCP settings...", LogLevel.INFO);

                // Run netsh commands to optimize TCP settings
                RunProcessAsAdmin("netsh", "int tcp set global autotuninglevel=normal");
                RunProcessAsAdmin("netsh", "int tcp set global chimney=enabled");
                RunProcessAsAdmin("netsh", "int tcp set global ecncapability=enabled");
                RunProcessAsAdmin("netsh", "int tcp set global rss=enabled");
                RunProcessAsAdmin("netsh", "int tcp set global netdma=enabled");

                // Set registry values for TCP optimization
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true))
                {
                    if (key != null)
                    {
                        // Set DefaultTTL to 64 (optimal for gaming)
                        key.SetValue("DefaultTTL", 64, RegistryValueKind.DWord);

                        // Enable TCP timestamps and window scaling
                        key.SetValue("Tcp1323Opts", 3, RegistryValueKind.DWord);

                        // Set maximum duplicate ACKs to 2 (faster retransmission)
                        key.SetValue("TcpMaxDupAcks", 2, RegistryValueKind.DWord);

                        // Enable selective ACKs
                        key.SetValue("SackOpts", 1, RegistryValueKind.DWord);
                    }
                }

                _isTcpOptimized = true;
                _logger.Log("TCP settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing TCP settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes QoS settings to prioritize gaming traffic
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeQosSettings()
        {
            try
            {
                _logger.Log("Optimizing QoS settings...", LogLevel.INFO);

                // Set registry values for QoS optimization
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Policies\Microsoft\Windows\Psched", true))
                {
                    if (key != null)
                    {
                        // Reserve 20% of bandwidth for system services
                        key.SetValue("NonBestEffortLimit", 20, RegistryValueKind.DWord);

                        // Enable application priority tagging
                        key.SetValue("ApplicationTag", 1, RegistryValueKind.DWord);
                    }
                }

                // In a real implementation, we would also configure QoS policies for gaming ports
                // This is a simplified version for demonstration

                _isQosOptimized = true;
                _logger.Log("QoS settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing QoS settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables network throttling for better performance
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableNetworkThrottling()
        {
            try
            {
                _logger.Log("Disabling network throttling...", LogLevel.INFO);

                // Set registry values to disable network throttling
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile", true))
                {
                    if (key != null)
                    {
                        // Set network throttling index to maximum (no throttling)
                        key.SetValue("NetworkThrottlingIndex", 0xffffffff, RegistryValueKind.DWord);

                        // Set system responsiveness to gaming mode
                        key.SetValue("SystemResponsiveness", 0, RegistryValueKind.DWord);
                    }
                }

                _isNetworkThrottlingDisabled = true;
                _logger.Log("Network throttling disabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling network throttling: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables Nagle's algorithm for reduced latency
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableNagleAlgorithm()
        {
            try
            {
                _logger.Log("Disabling Nagle's algorithm...", LogLevel.INFO);

                // Set registry values to disable Nagle's algorithm
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces", true))
                {
                    if (key != null)
                    {
                        // Get all network interfaces
                        string[] interfaceKeys = key.GetSubKeyNames();

                        foreach (string interfaceKey in interfaceKeys)
                        {
                            using (RegistryKey interfaceSubKey = key.OpenSubKey(interfaceKey, true))
                            {
                                if (interfaceSubKey != null)
                                {
                                    // Disable Nagle's algorithm
                                    interfaceSubKey.SetValue("TcpAckFrequency", 1, RegistryValueKind.DWord);
                                    interfaceSubKey.SetValue("TCPNoDelay", 1, RegistryValueKind.DWord);
                                }
                            }
                        }
                    }
                }

                _isNagleAlgorithmDisabled = true;
                _logger.Log("Nagle's algorithm disabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling Nagle's algorithm: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes DNS settings for faster lookups
        /// </summary>
        /// <param name="dnsProvider">The DNS provider to use (Default, Google, Cloudflare, OpenDNS)</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeDnsSettings(string dnsProvider = "Cloudflare")
        {
            try
            {
                _logger.Log($"Optimizing DNS settings using {dnsProvider}...", LogLevel.INFO);

                string primaryDns;
                string secondaryDns;

                // Set DNS servers based on provider
                switch (dnsProvider.ToLower())
                {
                    case "google":
                        primaryDns = "*******";
                        secondaryDns = "8.8.4.4";
                        break;
                    case "cloudflare":
                        primaryDns = "1.1.1.1";
                        secondaryDns = "1.0.0.1";
                        break;
                    case "opendns":
                        primaryDns = "**************";
                        secondaryDns = "**************";
                        break;
                    case "default":
                    default:
                        // Use DHCP-assigned DNS servers
                        _isDnsOptimized = false;
                        _currentDnsProvider = "Default";

                        // Reset DNS to DHCP
                        RunProcessAsAdmin("netsh", "interface ip set dns name=\"" + _primaryNetworkInterface.Name + "\" source=dhcp");

                        _logger.Log("DNS settings reset to DHCP defaults", LogLevel.SUCCESS);
                        return true;
                }

                // Set DNS servers
                RunProcessAsAdmin("netsh", "interface ip set dns name=\"" + _primaryNetworkInterface.Name + "\" static " + primaryDns + " primary");
                RunProcessAsAdmin("netsh", "interface ip add dns name=\"" + _primaryNetworkInterface.Name + "\" " + secondaryDns + " index=2");

                // Flush DNS cache
                RunProcessAsAdmin("ipconfig", "/flushdns");

                _isDnsOptimized = true;
                _currentDnsProvider = dnsProvider;
                _logger.Log($"DNS settings optimized using {dnsProvider} successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing DNS settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Measures network latency to a host
        /// </summary>
        /// <param name="host">The host to ping (default: *******)</param>
        /// <param name="count">The number of pings to send (default: 10)</param>
        /// <returns>The network latency metrics</returns>
        public async Task<NetworkLatencyMetrics> MeasureNetworkLatency(string host = "*******", int count = 10)
        {
            try
            {
                _logger.Log($"Measuring network latency to {host}...", LogLevel.INFO);

                NetworkLatencyMetrics metrics = new NetworkLatencyMetrics
                {
                    Host = host,
                    SampleCount = count,
                    Timestamp = DateTime.Now
                };

                Ping ping = new Ping();
                List<long> latencies = new List<long>();
                int packetLoss = 0;

                for (int i = 0; i < count; i++)
                {
                    try
                    {
                        PingReply reply = await ping.SendPingAsync(host, 1000);

                        if (reply.Status == IPStatus.Success)
                        {
                            latencies.Add(reply.RoundtripTime);
                        }
                        else
                        {
                            packetLoss++;
                        }

                        // Small delay between pings
                        await Task.Delay(100);
                    }
                    catch
                    {
                        packetLoss++;
                    }
                }

                // Calculate metrics
                if (latencies.Count > 0)
                {
                    metrics.MinLatency = latencies.Min();
                    metrics.MaxLatency = latencies.Max();
                    metrics.AverageLatency = latencies.Average();

                    // Calculate jitter (standard deviation)
                    double variance = latencies.Average(l => Math.Pow(l - metrics.AverageLatency, 2));
                    metrics.Jitter = Math.Sqrt(variance);

                    // Calculate packet loss percentage
                    metrics.PacketLoss = (double)packetLoss / count * 100;
                }
                else
                {
                    metrics.PacketLoss = 100;
                }

                _logger.Log($"Network latency measured: Avg={metrics.AverageLatency:F2}ms, Min={metrics.MinLatency}ms, Max={metrics.MaxLatency}ms, Jitter={metrics.Jitter:F2}ms, Loss={metrics.PacketLoss:F1}%", LogLevel.SUCCESS);
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error measuring network latency: {ex.Message}", LogLevel.ERROR);
                return new NetworkLatencyMetrics
                {
                    Host = host,
                    SampleCount = count,
                    Timestamp = DateTime.Now,
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// Applies all network optimizations
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool ApplyAllOptimizations()
        {
            try
            {
                _logger.Log("Applying all network optimizations...", LogLevel.INFO);

                bool tcpResult = OptimizeTcpSettings();
                bool qosResult = OptimizeQosSettings();
                bool throttlingResult = DisableNetworkThrottling();
                bool nagleResult = DisableNagleAlgorithm();
                bool dnsResult = OptimizeDnsSettings("Cloudflare");

                bool allSuccessful = tcpResult && qosResult && throttlingResult && nagleResult && dnsResult;

                if (allSuccessful)
                {
                    _logger.Log("All network optimizations applied successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some network optimizations failed to apply", LogLevel.WARNING);
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying network optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts all network optimizations to default settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertAllOptimizations()
        {
            try
            {
                _logger.Log("Reverting all network optimizations...", LogLevel.INFO);

                // Reset TCP settings
                RunProcessAsAdmin("netsh", "int tcp set global autotuninglevel=normal");
                RunProcessAsAdmin("netsh", "int tcp set global chimney=disabled");
                RunProcessAsAdmin("netsh", "int tcp set global ecncapability=disabled");
                RunProcessAsAdmin("netsh", "int tcp set global rss=enabled");
                RunProcessAsAdmin("netsh", "int tcp set global netdma=enabled");

                // Reset registry values
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true))
                {
                    if (key != null)
                    {
                        key.DeleteValue("DefaultTTL", false);
                        key.DeleteValue("Tcp1323Opts", false);
                        key.DeleteValue("TcpMaxDupAcks", false);
                        key.DeleteValue("SackOpts", false);
                    }
                }

                // Reset QoS settings
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Policies\Microsoft\Windows\Psched", true))
                {
                    if (key != null)
                    {
                        key.DeleteValue("NonBestEffortLimit", false);
                        key.DeleteValue("ApplicationTag", false);
                    }
                }

                // Reset network throttling
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile", true))
                {
                    if (key != null)
                    {
                        key.SetValue("NetworkThrottlingIndex", 10, RegistryValueKind.DWord);
                        key.SetValue("SystemResponsiveness", 20, RegistryValueKind.DWord);
                    }
                }

                // Reset Nagle's algorithm
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces", true))
                {
                    if (key != null)
                    {
                        string[] interfaceKeys = key.GetSubKeyNames();

                        foreach (string interfaceKey in interfaceKeys)
                        {
                            using (RegistryKey interfaceSubKey = key.OpenSubKey(interfaceKey, true))
                            {
                                if (interfaceSubKey != null)
                                {
                                    interfaceSubKey.DeleteValue("TcpAckFrequency", false);
                                    interfaceSubKey.DeleteValue("TCPNoDelay", false);
                                }
                            }
                        }
                    }
                }

                // Reset DNS settings to DHCP
                OptimizeDnsSettings("Default");

                _isTcpOptimized = false;
                _isQosOptimized = false;
                _isNetworkThrottlingDisabled = false;
                _isNagleAlgorithmDisabled = false;
                _isDnsOptimized = false;
                _currentDnsProvider = "Default";

                _logger.Log("All network optimizations reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting network optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        private void CheckCurrentOptimizationStatus()
        {
            try
            {
                // Check TCP optimization status
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"))
                {
                    if (key != null)
                    {
                        object defaultTtl = key.GetValue("DefaultTTL");
                        object tcp1323Opts = key.GetValue("Tcp1323Opts");

                        _isTcpOptimized = (defaultTtl != null && (int)defaultTtl == 64) &&
                                         (tcp1323Opts != null && (int)tcp1323Opts == 3);
                    }
                }

                // Check QoS optimization status
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Policies\Microsoft\Windows\Psched"))
                {
                    if (key != null)
                    {
                        object nonBestEffortLimit = key.GetValue("NonBestEffortLimit");
                        object applicationTag = key.GetValue("ApplicationTag");

                        _isQosOptimized = (nonBestEffortLimit != null && (int)nonBestEffortLimit == 20) &&
                                         (applicationTag != null && (int)applicationTag == 1);
                    }
                }

                // Check network throttling status
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile"))
                {
                    if (key != null)
                    {
                        object networkThrottlingIndex = key.GetValue("NetworkThrottlingIndex");

                        _isNetworkThrottlingDisabled = (networkThrottlingIndex != null && Convert.ToInt64(networkThrottlingIndex) == 0xffffffff);
                    }
                }

                // Check Nagle's algorithm status
                _isNagleAlgorithmDisabled = false; // This is harder to check without checking all interfaces

                // Check DNS optimization status
                _isDnsOptimized = false; // This is harder to check without querying the network adapter
                _currentDnsProvider = "Default";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking optimization status: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void RunProcessAsAdmin(string fileName, string arguments)
        {
            try
            {
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = fileName,
                    Arguments = arguments,
                    Verb = "runas", // Run as administrator
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using (Process process = Process.Start(psi))
                {
                    process.WaitForExit();
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();

                    if (!string.IsNullOrEmpty(error))
                    {
                        _logger.Log($"Process error: {error}", LogLevel.ERROR);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running process: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }
    }
}

