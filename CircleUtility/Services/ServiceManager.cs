using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using CircleUtility.Models;
using CircleUtility.Config;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing other services
    /// </summary>
    public class ServiceManager
    {
        private readonly LoggingService _logger;
        private readonly Dictionary<Type, object> _services;
        private readonly Dictionary<Type, bool> _serviceInitialized;
        private readonly Dictionary<Type, Stopwatch> _serviceInitializationTimes;

        /// <summary>
        /// Initializes a new instance of the ServiceManager class
        /// </summary>
        public ServiceManager(bool forDI = false)
        {
            _logger = LoggingService.Instance;
            _services = new Dictionary<Type, object>();
            _serviceInitialized = new Dictionary<Type, bool>();
            _serviceInitializationTimes = new Dictionary<Type, Stopwatch>();
        }

        /// <summary>
        /// Registers a service
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="service">The service instance</param>
        public void RegisterService<T>(T service) where T : class
        {
            try
            {
                Type serviceType = typeof(T);
                _services[serviceType] = service;
                _serviceInitialized[serviceType] = true;
                _logger.Log($"Service registered: {serviceType.Name}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error registering service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets a service
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>The service instance</returns>
        public T GetService<T>() where T : class
        {
            try
            {
                Type serviceType = typeof(T);
                if (_services.TryGetValue(serviceType, out object service))
                {
                    return (T)service;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting service: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Initializes all registered services in the correct order
        /// </summary>
        /// <returns>True if all services initialized successfully, false otherwise</returns>
        public bool InitializeAllServices()
        {
            var stopwatch = Stopwatch.StartNew();
            int successCount = 0;
            int failureCount = 0;
            var initializationOrder = new List<Type>();

            try
            {
                _logger.Log("Starting service initialization...", LogLevel.INFO);

                // Initialize services in the defined order
                foreach (var serviceType in ServiceInitializationConfig.InitializationOrder)
                {
                    if (!_services.TryGetValue(serviceType, out var service))
                    {
                        _logger.Log($"Service not registered: {serviceType.Name}", LogLevel.ERROR);
                        if (ServiceInitializationConfig.CriticalServices.Contains(serviceType))
                        {
                            throw new InvalidOperationException($"Critical service not registered: {serviceType.Name}");
                        }
                        continue;
                    }

                    // Check dependencies
                    if (ServiceInitializationConfig.ServiceDependencies.TryGetValue(serviceType, out var dependencies))
                    {
                        foreach (var dependency in dependencies)
                        {
                            if (!_serviceInitialized.TryGetValue(dependency, out bool initialized) || !initialized)
                            {
                                throw new InvalidOperationException(
                                    $"Service {serviceType.Name} depends on {dependency.Name} which is not initialized");
                            }
                        }
                    }

                    var sw = Stopwatch.StartNew();
                    bool success = false;
                    string serviceName = serviceType.Name;

                    try
                    {
                        _logger.Log($"Initializing service: {serviceName}...", LogLevel.DEBUG);
                        
                        // Special handling for services with Initialize method
                        var initializeMethod = serviceType.GetMethod("Initialize", Type.EmptyTypes);
                        if (initializeMethod != null)
                        {
                            initializeMethod.Invoke(service, null);
                        }
                        
                        success = true;
                        successCount++;
                        initializationOrder.Add(serviceType);
                        _logger.Log($"Successfully initialized {serviceName} in {sw.ElapsedMilliseconds}ms", LogLevel.INFO);
                    }
                    catch (Exception ex)
                    {
                        failureCount++;
                        _logger.Log($"Failed to initialize {serviceName}: {ex.InnerException?.Message ?? ex.Message}", 
                            LogLevel.ERROR, ex);
                        
                        // For critical services, rethrow the exception
                        if (ServiceInitializationConfig.CriticalServices.Contains(serviceType))
                        {
                            throw;
                        }
                    }
                    finally
                    {
                        sw.Stop();
                        _serviceInitializationTimes[serviceType] = sw;
                        _serviceInitialized[serviceType] = success;
                    }
                }

                _logger.Log($"Service initialization completed. Success: {successCount}, Failed: {failureCount}", 
                    failureCount > 0 ? LogLevel.WARNING : LogLevel.SUCCESS);
                
                return failureCount == 0;
            }
            catch (Exception ex)
            {
                _logger.Log("Critical error during service initialization", LogLevel.ERROR, ex);
                throw new ApplicationException("Failed to initialize critical services", ex);
            }
        }
        
        /// <summary>
        /// Disposes all disposable services
        /// </summary>
        public void DisposeServices()
        {
            _logger.Log("Disposing services...", LogLevel.INFO);
            
            // Dispose services in reverse initialization order
            foreach (var service in _services.Values.Reverse())
            {
                if (service is IDisposable disposable)
                {
                    try
                    {
                        disposable.Dispose();
                        _logger.Log($"Disposed service: {service.GetType().Name}", LogLevel.DEBUG);
                    }
                    catch (Exception ex)
                    {
                        _logger.Log($"Error disposing service {service.GetType().Name}: {ex.Message}", LogLevel.ERROR);
                    }
                }
            }
            
            _services.Clear();
            _serviceInitialized.Clear();
            _serviceInitializationTimes.Clear();
            
            _logger.Log("All services disposed", LogLevel.INFO);
        }
    }
}
