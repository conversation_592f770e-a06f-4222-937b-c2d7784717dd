// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing game profiles and optimizations
    /// </summary>
    public class ProfileManagerService
    {
        private static ProfileManagerService _instance;
        private readonly LoggingService _logger;
        private readonly string _profilesDirectory;
        private readonly Dictionary<string, GameProfile> _profiles;

        /// <summary>
        /// Event raised when a profile is applied
        /// </summary>
        public event EventHandler<ProfileAppliedEventArgs> ProfileApplied;

        /// <summary>
        /// Event raised when a profile is created or updated
        /// </summary>
        public event EventHandler<ProfileUpdatedEventArgs> ProfileUpdated;

        private ProfileManagerService()
        {
            _logger = LoggingService.Instance;
            _profilesDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Profiles");
            _profiles = new Dictionary<string, GameProfile>(StringComparer.OrdinalIgnoreCase);
            
            // Ensure profiles directory exists
            if (!Directory.Exists(_profilesDirectory))
            {
                Directory.CreateDirectory(_profilesDirectory);
            }
            
            LoadProfiles();
        }

        /// <summary>
        /// Gets the singleton instance of the profile manager service
        /// </summary>
        public static ProfileManagerService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ProfileManagerService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets all available game profiles
        /// </summary>
        public IEnumerable<GameProfile> Profiles => _profiles.Values;

        /// <summary>
        /// Gets a profile by game name
        /// </summary>
        /// <param name="gameName">The name of the game</param>
        /// <returns>The game profile, or null if not found</returns>
        public GameProfile GetProfile(string gameName)
        {
            if (string.IsNullOrEmpty(gameName))
            {
                return null;
            }

            _profiles.TryGetValue(gameName, out GameProfile profile);
            return profile;
        }

        /// <summary>
        /// Creates or updates a game profile
        /// </summary>
        /// <param name="profile">The game profile</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool SaveProfile(GameProfile profile)
        {
            if (profile == null || string.IsNullOrEmpty(profile.GameName))
            {
                _logger.Log("Cannot save profile with null or empty game name", LogLevel.ERROR);
                return false;
            }

            try
            {
                string filePath = GetProfileFilePath(profile.GameName);
                string json = JsonSerializer.Serialize(profile, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(filePath, json);
                
                _profiles[profile.GameName] = profile;
                
                _logger.Log($"Profile saved: {profile.GameName}", LogLevel.SUCCESS);
                ProfileUpdated?.Invoke(this, new ProfileUpdatedEventArgs(profile));
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Deletes a game profile
        /// </summary>
        /// <param name="gameName">The name of the game</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool DeleteProfile(string gameName)
        {
            if (string.IsNullOrEmpty(gameName))
            {
                return false;
            }

            try
            {
                string filePath = GetProfileFilePath(gameName);
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                
                _profiles.Remove(gameName);
                
                _logger.Log($"Profile deleted: {gameName}", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error deleting profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies a game profile
        /// </summary>
        /// <param name="gameName">The name of the game</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ApplyProfile(string gameName)
        {
            if (string.IsNullOrEmpty(gameName))
            {
                return false;
            }

            try
            {
                if (!_profiles.TryGetValue(gameName, out GameProfile profile))
                {
                    _logger.Log($"Profile not found: {gameName}", LogLevel.ERROR);
                    return false;
                }
                
                // Apply the profile settings
                bool result = ApplyProfileSettings(profile);
                
                if (result)
                {
                    _logger.Log($"Profile applied: {gameName}", LogLevel.SUCCESS);
                    ProfileApplied?.Invoke(this, new ProfileAppliedEventArgs(profile));
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Exports a game profile to a file
        /// </summary>
        /// <param name="gameName">The name of the game</param>
        /// <param name="exportPath">The export file path</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ExportProfile(string gameName, string exportPath)
        {
            if (string.IsNullOrEmpty(gameName) || string.IsNullOrEmpty(exportPath))
            {
                return false;
            }

            try
            {
                if (!_profiles.TryGetValue(gameName, out GameProfile profile))
                {
                    _logger.Log($"Profile not found: {gameName}", LogLevel.ERROR);
                    return false;
                }
                
                string json = JsonSerializer.Serialize(profile, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(exportPath, json);
                
                _logger.Log($"Profile exported: {gameName} to {exportPath}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error exporting profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Imports a game profile from a file
        /// </summary>
        /// <param name="importPath">The import file path</param>
        /// <returns>The imported profile, or null if import failed</returns>
        public GameProfile ImportProfile(string importPath)
        {
            if (string.IsNullOrEmpty(importPath) || !File.Exists(importPath))
            {
                return null;
            }

            try
            {
                string json = File.ReadAllText(importPath);
                GameProfile profile = JsonSerializer.Deserialize<GameProfile>(json);
                
                if (profile != null && !string.IsNullOrEmpty(profile.GameName))
                {
                    SaveProfile(profile);
                    _logger.Log($"Profile imported: {profile.GameName}", LogLevel.SUCCESS);
                    return profile;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error importing profile: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Creates a default profile for a game
        /// </summary>
        /// <param name="gameName">The name of the game</param>
        /// <returns>The created profile</returns>
        public GameProfile CreateDefaultProfile(string gameName)
        {
            if (string.IsNullOrEmpty(gameName))
            {
                return null;
            }

            try
            {
                GameProfile profile = new GameProfile
                {
                    GameName = gameName,
                    CreatedDate = DateTime.Now,
                    LastModifiedDate = DateTime.Now,
                    IsDefault = true,
                    Settings = new Dictionary<string, string>(),
                    LaunchParameters = string.Empty,
                    Description = $"Default optimization profile for {gameName}"
                };
                
                // Add default settings based on game
                AddDefaultSettings(profile);
                
                SaveProfile(profile);
                
                _logger.Log($"Default profile created: {gameName}", LogLevel.INFO);
                return profile;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating default profile: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private void LoadProfiles()
        {
            try
            {
                _profiles.Clear();
                
                string[] profileFiles = Directory.GetFiles(_profilesDirectory, "*.json");
                
                foreach (string file in profileFiles)
                {
                    try
                    {
                        string json = File.ReadAllText(file);
                        GameProfile profile = JsonSerializer.Deserialize<GameProfile>(json);
                        
                        if (profile != null && !string.IsNullOrEmpty(profile.GameName))
                        {
                            _profiles[profile.GameName] = profile;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Log($"Error loading profile file {file}: {ex.Message}", LogLevel.ERROR);
                    }
                }
                
                _logger.Log($"Loaded {_profiles.Count} profiles", LogLevel.INFO);
                
                // Create default profiles for known games if they don't exist
                CreateDefaultProfiles();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading profiles: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void CreateDefaultProfiles()
        {
            string[] defaultGames = new[]
            {
                "Fortnite",
                "Call of Duty: Warzone",
                "Valorant",
                "Counter-Strike 2",
                "Apex Legends",
                "Call of Duty 2025"
            };
            
            foreach (string game in defaultGames)
            {
                if (!_profiles.ContainsKey(game))
                {
                    CreateDefaultProfile(game);
                }
            }
        }

        private string GetProfileFilePath(string gameName)
        {
            // Sanitize the game name for use in a file name
            string sanitizedName = string.Join("_", gameName.Split(Path.GetInvalidFileNameChars()));
            return Path.Combine(_profilesDirectory, $"{sanitizedName}.json");
        }

        private bool ApplyProfileSettings(GameProfile profile)
        {
            try
            {
                // Apply each setting in the profile
                foreach (var setting in profile.Settings)
                {
                    // In a real implementation, this would apply the settings to the game
                    // For now, we'll just log the settings
                    _logger.Log($"Applying setting: {setting.Key} = {setting.Value}", LogLevel.INFO);
                }
                
                // Apply launch parameters if specified
                if (!string.IsNullOrEmpty(profile.LaunchParameters))
                {
                    _logger.Log($"Setting launch parameters: {profile.LaunchParameters}", LogLevel.INFO);
                }
                
                // Update the last applied date
                profile.LastAppliedDate = DateTime.Now;
                SaveProfile(profile);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying profile settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        private void AddDefaultSettings(GameProfile profile)
        {
            // Add game-specific default settings
            switch (profile.GameName.ToLower())
            {
                case "fortnite":
                    profile.Settings["MaxFPS"] = "240";
                    profile.Settings["ViewDistance"] = "Medium";
                    profile.Settings["Shadows"] = "Off";
                    profile.Settings["AntiAliasing"] = "Off";
                    profile.Settings["Textures"] = "Low";
                    profile.Settings["Effects"] = "Low";
                    profile.Settings["PostProcessing"] = "Low";
                    profile.LaunchParameters = "-NOSPLASH -USEALLAVAILABLECORES -NOTEXTURESTREAMING";
                    break;
                    
                case "call of duty: warzone":
                case "call of duty 2025":
                    profile.Settings["RenderResolution"] = "100";
                    profile.Settings["FieldOfView"] = "120";
                    profile.Settings["VSync"] = "Disabled";
                    profile.Settings["TextureResolution"] = "Normal";
                    profile.Settings["ParticleQuality"] = "Low";
                    profile.Settings["BulletImpacts"] = "Disabled";
                    profile.Settings["DirectX"] = "DirectX 12";
                    profile.LaunchParameters = "-d3d11 -noBorder";
                    break;
                    
                case "valorant":
                    profile.Settings["MaxFPS"] = "Unlimited";
                    profile.Settings["MaterialQuality"] = "Low";
                    profile.Settings["TextureQuality"] = "Low";
                    profile.Settings["DetailQuality"] = "Low";
                    profile.Settings["VSync"] = "Off";
                    profile.Settings["AntiAliasing"] = "None";
                    profile.Settings["Anisotropic"] = "1x";
                    profile.LaunchParameters = "--disable-fullscreen-optimizations";
                    break;
                    
                case "counter-strike 2":
                    profile.Settings["GlobalShadowQuality"] = "Very Low";
                    profile.Settings["ModelTextureDetail"] = "Low";
                    profile.Settings["EffectDetail"] = "Low";
                    profile.Settings["ShaderDetail"] = "Low";
                    profile.Settings["Multicore"] = "Enabled";
                    profile.Settings["FXAA"] = "Disabled";
                    profile.Settings["VSync"] = "Disabled";
                    profile.LaunchParameters = "-novid -tickrate 128 -high -nojoy";
                    break;
                    
                case "apex legends":
                    profile.Settings["VideoMemoryScale"] = "0.6";
                    profile.Settings["AntiAliasing"] = "Off";
                    profile.Settings["TextureStreaming"] = "Disabled";
                    profile.Settings["TextureDetail"] = "Low";
                    profile.Settings["EffectDetail"] = "Low";
                    profile.Settings["ImpactMarks"] = "Low";
                    profile.Settings["RagdollPhysics"] = "Low";
                    profile.LaunchParameters = "+fps_max unlimited -preload -novid";
                    break;
                    
                default:
                    // Generic settings for other games
                    profile.Settings["Shadows"] = "Low";
                    profile.Settings["Textures"] = "Medium";
                    profile.Settings["Effects"] = "Low";
                    profile.Settings["VSync"] = "Off";
                    profile.Settings["AntiAliasing"] = "Off";
                    break;
            }
        }
    }

    /// <summary>
    /// Event arguments for profile application
    /// </summary>
    public class ProfileAppliedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the applied profile
        /// </summary>
        public GameProfile Profile { get; }

        /// <summary>
        /// Initializes a new instance of the ProfileAppliedEventArgs class
        /// </summary>
        /// <param name="profile">The applied profile</param>
        public ProfileAppliedEventArgs(GameProfile profile)
        {
            Profile = profile;
        }
    }

    /// <summary>
    /// Event arguments for profile updates
    /// </summary>
    public class ProfileUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the updated profile
        /// </summary>
        public GameProfile Profile { get; }

        /// <summary>
        /// Initializes a new instance of the ProfileUpdatedEventArgs class
        /// </summary>
        /// <param name="profile">The updated profile</param>
        public ProfileUpdatedEventArgs(GameProfile profile)
        {
            Profile = profile;
        }
    }
}

