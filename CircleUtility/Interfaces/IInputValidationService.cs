namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for input validation service
    /// </summary>
    public interface IInputValidationService
    {
        /// <summary>
        /// Validates input data
        /// </summary>
        /// <param name="input">The input to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        bool ValidateInput(string input);

        /// <summary>
        /// Sanitizes input data
        /// </summary>
        /// <param name="input">The input to sanitize</param>
        /// <returns>The sanitized input</returns>
        string SanitizeInput(string input);
    }
}
