// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing application configuration
    /// </summary>
    public class ConfigurationManager
    {
        private static ConfigurationManager _instance;
        private readonly LoggingService _logger;
        private readonly string _configDirectory;
        private readonly string _configFilePath;
        private readonly string _backupDirectory;
        private AppConfiguration _currentConfig;

        /// <summary>
        /// Event raised when configuration is changed
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// Initializes a new instance of the ConfigurationManager class
        /// </summary>
        private ConfigurationManager()
        {
            _logger = LoggingService.Instance;
            _configDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
            _configFilePath = Path.Combine(_configDirectory, "config.json");
            _backupDirectory = Path.Combine(_configDirectory, "Backups");

            // Ensure directories exist
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
            }

            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }

            LoadConfiguration();
        }

        /// <summary>
        /// Gets the singleton instance of the configuration manager
        /// </summary>
        public static ConfigurationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ConfigurationManager();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets the current configuration
        /// </summary>
        public AppConfiguration CurrentConfig => _currentConfig;

        /// <summary>
        /// Loads the configuration from disk
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool LoadConfiguration()
        {
            try
            {
                _logger.Log("Loading configuration...", LogLevel.INFO);

                if (File.Exists(_configFilePath))
                {
                    string json = File.ReadAllText(_configFilePath);
                    _currentConfig = JsonSerializer.Deserialize<AppConfiguration>(json);

                    if (_currentConfig != null)
                    {
                        _logger.Log("Configuration loaded successfully", LogLevel.SUCCESS);
                        return true;
                    }
                }

                // If file doesn't exist or deserialization failed, create default configuration
                _currentConfig = CreateDefaultConfiguration();
                SaveConfiguration();

                _logger.Log("Default configuration created", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading configuration: {ex.Message}", LogLevel.ERROR);
                _currentConfig = CreateDefaultConfiguration();
                return false;
            }
        }

        /// <summary>
        /// Saves the configuration to disk
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool SaveConfiguration()
        {
            try
            {
                _logger.Log("Saving configuration...", LogLevel.INFO);

                string json = JsonSerializer.Serialize(_currentConfig, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_configFilePath, json);

                _logger.Log("Configuration saved successfully", LogLevel.SUCCESS);

                // Raise event
                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(_currentConfig));

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Creates a backup of the current configuration
        /// </summary>
        /// <param name="backupName">The name of the backup</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool CreateBackup(string backupName = null)
        {
            try
            {
                _logger.Log("Creating configuration backup...", LogLevel.INFO);

                // Generate backup name if not provided
                if (string.IsNullOrEmpty(backupName))
                {
                    backupName = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                }

                // Sanitize backup name
                backupName = string.Join("_", backupName.Split(Path.GetInvalidFileNameChars()));

                string backupFilePath = Path.Combine(_backupDirectory, $"{backupName}.json");

                // Save current configuration to backup file
                string json = JsonSerializer.Serialize(_currentConfig, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(backupFilePath, json);

                _logger.Log($"Configuration backup created: {backupName}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating configuration backup: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restores a configuration from a backup
        /// </summary>
        /// <param name="backupName">The name of the backup</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RestoreBackup(string backupName)
        {
            try
            {
                _logger.Log($"Restoring configuration from backup: {backupName}...", LogLevel.INFO);

                string backupFilePath = Path.Combine(_backupDirectory, $"{backupName}.json");

                if (!File.Exists(backupFilePath))
                {
                    _logger.Log($"Backup file not found: {backupName}", LogLevel.ERROR);
                    return false;
                }

                // Create a backup of the current configuration before restoring
                CreateBackup("BeforeRestore");

                // Load configuration from backup file
                string json = File.ReadAllText(backupFilePath);
                _currentConfig = JsonSerializer.Deserialize<AppConfiguration>(json);

                // Save the restored configuration
                SaveConfiguration();

                _logger.Log($"Configuration restored from backup: {backupName}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restoring configuration from backup: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a list of available backups
        /// </summary>
        /// <returns>A list of backup names</returns>
        public List<string> GetAvailableBackups()
        {
            try
            {
                List<string> backups = new List<string>();

                string[] backupFiles = Directory.GetFiles(_backupDirectory, "*.json");

                foreach (string file in backupFiles)
                {
                    string backupName = Path.GetFileNameWithoutExtension(file);
                    backups.Add(backupName);
                }

                return backups;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting available backups: {ex.Message}", LogLevel.ERROR);
                return new List<string>();
            }
        }

        /// <summary>
        /// Resets the configuration to default values
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool ResetConfiguration()
        {
            try
            {
                _logger.Log("Resetting configuration to defaults...", LogLevel.INFO);

                // Create a backup before resetting
                CreateBackup("BeforeReset");

                // Create default configuration
                _currentConfig = CreateDefaultConfiguration();

                // Save the default configuration
                SaveConfiguration();

                _logger.Log("Configuration reset to defaults", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error resetting configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Exports the configuration to a file
        /// </summary>
        /// <param name="filePath">The file path to export to</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ExportConfiguration(string filePath)
        {
            try
            {
                _logger.Log($"Exporting configuration to: {filePath}...", LogLevel.INFO);

                string json = JsonSerializer.Serialize(_currentConfig, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(filePath, json);

                _logger.Log($"Configuration exported to: {filePath}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error exporting configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Imports the configuration from a file
        /// </summary>
        /// <param name="filePath">The file path to import from</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ImportConfiguration(string filePath)
        {
            try
            {
                _logger.Log($"Importing configuration from: {filePath}...", LogLevel.INFO);

                if (!File.Exists(filePath))
                {
                    _logger.Log($"Import file not found: {filePath}", LogLevel.ERROR);
                    return false;
                }

                // Create a backup before importing
                CreateBackup("BeforeImport");

                // Load configuration from import file
                string json = File.ReadAllText(filePath);
                AppConfiguration importedConfig = JsonSerializer.Deserialize<AppConfiguration>(json);

                if (importedConfig != null)
                {
                    _currentConfig = importedConfig;
                    SaveConfiguration();

                    _logger.Log($"Configuration imported from: {filePath}", LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    _logger.Log("Invalid configuration file", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error importing configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Updates a specific configuration setting
        /// </summary>
        /// <param name="section">The configuration section</param>
        /// <param name="key">The setting key</param>
        /// <param name="value">The setting value</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool UpdateSetting(string section, string key, object value)
        {
            try
            {
                _logger.Log($"Updating configuration setting: {section}.{key}...", LogLevel.INFO);

                // Update the setting based on the section
                switch (section.ToLower())
                {
                    case "general":
                        UpdateGeneralSetting(key, value);
                        break;
                    case "interface":
                        UpdateInterfaceSetting(key, value);
                        break;
                    case "performance":
                        UpdatePerformanceSetting(key, value);
                        break;
                    case "logging":
                        UpdateLoggingSetting(key, value);
                        break;
                    case "advanced":
                        UpdateAdvancedSetting(key, value);
                        break;
                    default:
                        _logger.Log($"Unknown configuration section: {section}", LogLevel.ERROR);
                        return false;
                }

                // Save the updated configuration
                SaveConfiguration();

                _logger.Log($"Configuration setting updated: {section}.{key}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating configuration setting: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a specific configuration setting
        /// </summary>
        /// <param name="section">The configuration section</param>
        /// <param name="key">The setting key</param>
        /// <returns>The setting value, or null if not found</returns>
        public object GetSetting(string section, string key)
        {
            try
            {
                // Get the setting based on the section
                switch (section.ToLower())
                {
                    case "general":
                        return GetGeneralSetting(key);
                    case "interface":
                        return GetInterfaceSetting(key);
                    case "performance":
                        return GetPerformanceSetting(key);
                    case "logging":
                        return GetLoggingSetting(key);
                    case "advanced":
                        return GetAdvancedSetting(key);
                    default:
                        _logger.Log($"Unknown configuration section: {section}", LogLevel.ERROR);
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting configuration setting: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private AppConfiguration CreateDefaultConfiguration()
        {
            return new AppConfiguration
            {
                General = new GeneralSettings
                {
                    Username = "User",
                    StartWithWindows = false,
                    CheckForUpdatesAtStartup = true,
                    MinimizeToTray = true,
                    ConfirmBeforeApplyingTweaks = true,
                    ShowTipsAtStartup = true
                },
                Interface = new InterfaceSettings
                {
                    EnableDarkMode = true,
                    AnimationSpeedIndex = 1, // Normal
                    FontSizeIndex = 1, // Medium
                    ShowTooltips = true,
                    ThemeIndex = 0 // Default
                },
                Performance = new PerformanceSettings
                {
                    RefreshIntervalIndex = 1, // 5 seconds
                    EnableHardwareMonitoring = true,
                    EnableBackgroundProcessing = false,
                    EnableAutoOptimization = false,
                    ShowFpsCounter = true,
                    EnableGameDetection = true
                },
                Logging = new LoggingSettings
                {
                    EnableLogging = true,
                    LogLevel = 1, // Info
                    MaxLogFileSize = 10, // 10 MB
                    MaxLogFiles = 5,
                    LogFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "CircleUtility.log")
                },
                Advanced = new AdvancedSettings
                {
                    EnableDebugMode = false,
                    EnableExperimentalFeatures = false,
                    EnableVerboseLogging = false,
                    EnableCrashReporting = true,
                    EnableTelemetry = false,
                    EnableAutoBackup = true,
                    BackupInterval = 7, // 7 days
                    MaxBackups = 10,
                    BackupLocation = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "Backups")
                }
            };
        }

        private void UpdateGeneralSetting(string key, object value)
        {
            switch (key.ToLower())
            {
                case "username":
                    _currentConfig.General.Username = value.ToString();
                    break;
                case "startwithwindows":
                    _currentConfig.General.StartWithWindows = Convert.ToBoolean(value);
                    break;
                case "checkforupdatesatstartup":
                    _currentConfig.General.CheckForUpdatesAtStartup = Convert.ToBoolean(value);
                    break;
                case "minimizetotray":
                    _currentConfig.General.MinimizeToTray = Convert.ToBoolean(value);
                    break;
                case "confirmbeforeapplyingtweaks":
                    _currentConfig.General.ConfirmBeforeApplyingTweaks = Convert.ToBoolean(value);
                    break;
                case "showtipsatstartup":
                    _currentConfig.General.ShowTipsAtStartup = Convert.ToBoolean(value);
                    break;
                case "autoupdate":
                    _currentConfig.General.AutoUpdate = Convert.ToBoolean(value);
                    break;
                case "updatecheckinterval":
                    _currentConfig.General.UpdateCheckInterval = Convert.ToInt32(value);
                    break;
                case "updateserverurl":
                    _currentConfig.General.UpdateServerUrl = value.ToString();
                    break;
                case "lastupdatecheck":
                    _currentConfig.General.LastUpdateCheck = value as DateTime?;
                    break;
                default:
                    _logger.Log($"Unknown general setting: {key}", LogLevel.ERROR);
                    break;
            }
        }

        private void UpdateInterfaceSetting(string key, object value)
        {
            switch (key.ToLower())
            {
                case "enabledarkmode":
                    _currentConfig.Interface.EnableDarkMode = Convert.ToBoolean(value);
                    break;
                case "animationspeedindex":
                    _currentConfig.Interface.AnimationSpeedIndex = Convert.ToInt32(value);
                    break;
                case "fontsizeindex":
                    _currentConfig.Interface.FontSizeIndex = Convert.ToInt32(value);
                    break;
                case "showtooltips":
                    _currentConfig.Interface.ShowTooltips = Convert.ToBoolean(value);
                    break;
                case "themeindex":
                    _currentConfig.Interface.ThemeIndex = Convert.ToInt32(value);
                    break;
                default:
                    _logger.Log($"Unknown interface setting: {key}", LogLevel.ERROR);
                    break;
            }
        }

        private void UpdatePerformanceSetting(string key, object value)
        {
            switch (key.ToLower())
            {
                case "refreshintervalindex":
                    _currentConfig.Performance.RefreshIntervalIndex = Convert.ToInt32(value);
                    break;
                case "enablehardwaremonitoring":
                    _currentConfig.Performance.EnableHardwareMonitoring = Convert.ToBoolean(value);
                    break;
                case "enablebackgroundprocessing":
                    _currentConfig.Performance.EnableBackgroundProcessing = Convert.ToBoolean(value);
                    break;
                case "enableautooptimization":
                    _currentConfig.Performance.EnableAutoOptimization = Convert.ToBoolean(value);
                    break;
                case "showfpscounter":
                    _currentConfig.Performance.ShowFpsCounter = Convert.ToBoolean(value);
                    break;
                case "enablegamedetection":
                    _currentConfig.Performance.EnableGameDetection = Convert.ToBoolean(value);
                    break;
                default:
                    _logger.Log($"Unknown performance setting: {key}", LogLevel.ERROR);
                    break;
            }
        }

        private void UpdateLoggingSetting(string key, object value)
        {
            switch (key.ToLower())
            {
                case "enablelogging":
                    _currentConfig.Logging.EnableLogging = Convert.ToBoolean(value);
                    break;
                case "loglevel":
                    _currentConfig.Logging.LogLevel = Convert.ToInt32(value);
                    break;
                case "maxlogfilesize":
                    _currentConfig.Logging.MaxLogFileSize = Convert.ToInt32(value);
                    break;
                case "maxlogfiles":
                    _currentConfig.Logging.MaxLogFiles = Convert.ToInt32(value);
                    break;
                case "logfilepath":
                    _currentConfig.Logging.LogFilePath = value.ToString();
                    break;
                default:
                    _logger.Log($"Unknown logging setting: {key}", LogLevel.ERROR);
                    break;
            }
        }

        private void UpdateAdvancedSetting(string key, object value)
        {
            switch (key.ToLower())
            {
                case "enabledebugmode":
                    _currentConfig.Advanced.EnableDebugMode = Convert.ToBoolean(value);
                    break;
                case "enableexperimentalfeatures":
                    _currentConfig.Advanced.EnableExperimentalFeatures = Convert.ToBoolean(value);
                    break;
                case "enableverboselogging":
                    _currentConfig.Advanced.EnableVerboseLogging = Convert.ToBoolean(value);
                    break;
                case "enablecrashreporting":
                    _currentConfig.Advanced.EnableCrashReporting = Convert.ToBoolean(value);
                    break;
                case "enabletelemetry":
                    _currentConfig.Advanced.EnableTelemetry = Convert.ToBoolean(value);
                    break;
                case "enableautobackup":
                    _currentConfig.Advanced.EnableAutoBackup = Convert.ToBoolean(value);
                    break;
                case "backupinterval":
                    _currentConfig.Advanced.BackupInterval = Convert.ToInt32(value);
                    break;
                case "maxbackups":
                    _currentConfig.Advanced.MaxBackups = Convert.ToInt32(value);
                    break;
                case "backuplocation":
                    _currentConfig.Advanced.BackupLocation = value.ToString();
                    break;
                default:
                    _logger.Log($"Unknown advanced setting: {key}", LogLevel.ERROR);
                    break;
            }
        }

        private object GetGeneralSetting(string key)
        {
            switch (key.ToLower())
            {
                case "username":
                    return _currentConfig.General.Username;
                case "startwithwindows":
                    return _currentConfig.General.StartWithWindows;
                case "checkforupdatesatstartup":
                    return _currentConfig.General.CheckForUpdatesAtStartup;
                case "minimizetotray":
                    return _currentConfig.General.MinimizeToTray;
                case "confirmbeforeapplyingtweaks":
                    return _currentConfig.General.ConfirmBeforeApplyingTweaks;
                case "showtipsatstartup":
                    return _currentConfig.General.ShowTipsAtStartup;
                case "autoupdate":
                    return _currentConfig.General.AutoUpdate;
                case "updatecheckinterval":
                    return _currentConfig.General.UpdateCheckInterval;
                case "updateserverurl":
                    return _currentConfig.General.UpdateServerUrl;
                case "lastupdatecheck":
                    return _currentConfig.General.LastUpdateCheck;
                default:
                    _logger.Log($"Unknown general setting: {key}", LogLevel.ERROR);
                    return null;
            }
        }

        private object GetInterfaceSetting(string key)
        {
            switch (key.ToLower())
            {
                case "enabledarkmode":
                    return _currentConfig.Interface.EnableDarkMode;
                case "animationspeedindex":
                    return _currentConfig.Interface.AnimationSpeedIndex;
                case "fontsizeindex":
                    return _currentConfig.Interface.FontSizeIndex;
                case "showtooltips":
                    return _currentConfig.Interface.ShowTooltips;
                case "themeindex":
                    return _currentConfig.Interface.ThemeIndex;
                default:
                    _logger.Log($"Unknown interface setting: {key}", LogLevel.ERROR);
                    return null;
            }
        }

        private object GetPerformanceSetting(string key)
        {
            switch (key.ToLower())
            {
                case "refreshintervalindex":
                    return _currentConfig.Performance.RefreshIntervalIndex;
                case "enablehardwaremonitoring":
                    return _currentConfig.Performance.EnableHardwareMonitoring;
                case "enablebackgroundprocessing":
                    return _currentConfig.Performance.EnableBackgroundProcessing;
                case "enableautooptimization":
                    return _currentConfig.Performance.EnableAutoOptimization;
                case "showfpscounter":
                    return _currentConfig.Performance.ShowFpsCounter;
                case "enablegamedetection":
                    return _currentConfig.Performance.EnableGameDetection;
                default:
                    _logger.Log($"Unknown performance setting: {key}", LogLevel.ERROR);
                    return null;
            }
        }

        private object GetLoggingSetting(string key)
        {
            switch (key.ToLower())
            {
                case "enablelogging":
                    return _currentConfig.Logging.EnableLogging;
                case "loglevel":
                    return _currentConfig.Logging.LogLevel;
                case "maxlogfilesize":
                    return _currentConfig.Logging.MaxLogFileSize;
                case "maxlogfiles":
                    return _currentConfig.Logging.MaxLogFiles;
                case "logfilepath":
                    return _currentConfig.Logging.LogFilePath;
                default:
                    _logger.Log($"Unknown logging setting: {key}", LogLevel.ERROR);
                    return null;
            }
        }

        private object GetAdvancedSetting(string key)
        {
            switch (key.ToLower())
            {
                case "enabledebugmode":
                    return _currentConfig.Advanced.EnableDebugMode;
                case "enableexperimentalfeatures":
                    return _currentConfig.Advanced.EnableExperimentalFeatures;
                case "enableverboselogging":
                    return _currentConfig.Advanced.EnableVerboseLogging;
                case "enablecrashreporting":
                    return _currentConfig.Advanced.EnableCrashReporting;
                case "enabletelemetry":
                    return _currentConfig.Advanced.EnableTelemetry;
                case "enableautobackup":
                    return _currentConfig.Advanced.EnableAutoBackup;
                case "backupinterval":
                    return _currentConfig.Advanced.BackupInterval;
                case "maxbackups":
                    return _currentConfig.Advanced.MaxBackups;
                case "backuplocation":
                    return _currentConfig.Advanced.BackupLocation;
                default:
                    _logger.Log($"Unknown advanced setting: {key}", LogLevel.ERROR);
                    return null;
            }
        }
    }

    /// <summary>
    /// Event arguments for configuration changes
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the new configuration
        /// </summary>
        public AppConfiguration NewConfiguration { get; }

        /// <summary>
        /// Initializes a new instance of the ConfigurationChangedEventArgs class
        /// </summary>
        /// <param name="newConfiguration">The new configuration</param>
        public ConfigurationChangedEventArgs(AppConfiguration newConfiguration)
        {
            NewConfiguration = newConfiguration;
        }
    }
}

