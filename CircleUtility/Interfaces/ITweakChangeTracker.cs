using System.Collections.Generic;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for tweak change tracker
    /// </summary>
    public interface ITweakChangeTracker
    {
        /// <summary>
        /// Tracks a tweak change
        /// </summary>
        /// <param name="tweak">The tweak that was changed</param>
        void TrackChange(TweakHistoryEntry tweak);

        /// <summary>
        /// Gets all tracked changes
        /// </summary>
        /// <returns>List of tracked changes</returns>
        List<TweakHistoryEntry> GetTrackedChanges();

        /// <summary>
        /// Clears all tracked changes
        /// </summary>
        void ClearTrackedChanges();
    }
}
