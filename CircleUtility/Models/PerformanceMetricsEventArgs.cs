using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Event arguments for performance metrics events
    /// </summary>
    public class PerformanceMetricsEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the CPU usage percentage
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// Gets or sets the memory usage percentage
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// Gets or sets the disk usage percentage
        /// </summary>
        public double DiskUsage { get; set; }

        /// <summary>
        /// Gets or sets the GPU usage percentage
        /// </summary>
        public double GpuUsage { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the metrics
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Initializes a new instance of the PerformanceMetricsEventArgs class
        /// </summary>
        public PerformanceMetricsEventArgs()
        {
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// Initializes a new instance of the PerformanceMetricsEventArgs class
        /// </summary>
        /// <param name="cpuUsage">CPU usage percentage</param>
        /// <param name="memoryUsage">Memory usage percentage</param>
        /// <param name="diskUsage">Disk usage percentage</param>
        /// <param name="gpuUsage">GPU usage percentage</param>
        public PerformanceMetricsEventArgs(double cpuUsage, double memoryUsage, double diskUsage, double gpuUsage)
        {
            CpuUsage = cpuUsage;
            MemoryUsage = memoryUsage;
            DiskUsage = diskUsage;
            GpuUsage = gpuUsage;
            Timestamp = DateTime.Now;
        }
    }
}
