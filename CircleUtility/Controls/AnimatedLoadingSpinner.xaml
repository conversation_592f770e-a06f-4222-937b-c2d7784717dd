<UserControl x:Class="CircleUtility.Controls.AnimatedLoadingSpinner"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="100"
             d:DesignWidth="100"
             Loaded="UserControl_Loaded">

    <UserControl.Resources>
        <!-- Spinner rotation -->
        <Storyboard x:Key="RotationAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="SpinnerRotation"
                             Storyboard.TargetProperty="Angle"
                             From="0" To="360" Duration="0:0:1.5" />
        </Storyboard>

        <!-- Glow pulsing -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="GlowEffect"
                             Storyboard.TargetProperty="Opacity"
                             From="0.5" To="1.0"
                             Duration="0:0:0.75"
                             AutoReverse="True" />
        </Storyboard>
    </UserControl.Resources>

    <Grid>
        <!-- Outer circle glow ring -->
        <Ellipse x:Name="OuterCircle"
                 Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="3"
                 Fill="Transparent">
            <Ellipse.Effect>
                <DropShadowEffect x:Name="GlowEffect"
                                  Color="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource ColorConverter}}"
                                  BlurRadius="10"
                                  ShadowDepth="0"
                                  Opacity="0.5" />
            </Ellipse.Effect>
        </Ellipse>

        <!-- Rotating spinner segments -->
        <Grid RenderTransformOrigin="0.5,0.5">
            <Grid.RenderTransform>
                <RotateTransform x:Name="SpinnerRotation" Angle="0" />
            </Grid.RenderTransform>

            <!-- Spinner arcs -->
            <Canvas>
                <Path Data="M 50,0 A 50,50 0 0,1 75,6.7" Opacity="1.0"
                      Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      StrokeThickness="5" StrokeStartLineCap="Round" StrokeEndLineCap="Round">
                    <Path.RenderTransform>
                        <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                            Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}" />
                    </Path.RenderTransform>
                </Path>
                <Path Data="M 93.3,25 A 50,50 0 0,1 100,50" Opacity="0.9"
                      Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      StrokeThickness="5" StrokeStartLineCap="Round" StrokeEndLineCap="Round">
                    <Path.RenderTransform>
                        <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                            Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}" />
                    </Path.RenderTransform>
                </Path>
                <Path Data="M 93.3,75 A 50,50 0 0,1 75,93.3" Opacity="0.8"
                      Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      StrokeThickness="5" StrokeStartLineCap="Round" StrokeEndLineCap="Round">
                    <Path.RenderTransform>
                        <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                            Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}" />
                    </Path.RenderTransform>
                </Path>
                <Path Data="M 50,100 A 50,50 0 0,1 25,93.3" Opacity="0.7"
                      Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      StrokeThickness="5" StrokeStartLineCap="Round" StrokeEndLineCap="Round">
                    <Path.RenderTransform>
                        <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                            Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}" />
                    </Path.RenderTransform>
                </Path>
                <Path Data="M 6.7,75 A 50,50 0 0,1 0,50" Opacity="0.6"
                      Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      StrokeThickness="5" StrokeStartLineCap="Round" StrokeEndLineCap="Round">
                    <Path.RenderTransform>
                        <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                            Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}" />
                    </Path.RenderTransform>
                </Path>
                <Path Data="M 6.7,25 A 50,50 0 0,1 25,6.7" Opacity="0.5"
                      Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      StrokeThickness="5" StrokeStartLineCap="Round" StrokeEndLineCap="Round">
                    <Path.RenderTransform>
                        <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                            Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}" />
                    </Path.RenderTransform>
                </Path>
            </Canvas>
        </Grid>

        <!-- Center loading label -->
        <TextBlock x:Name="CenterText"
                   Text="{Binding Text, RelativeSource={RelativeSource AncestorType=UserControl}}"
                   FontFamily="Consolas"
                   FontSize="12"
                   Foreground="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Visibility="{Binding Text, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource StringToVisibilityConverter}}" />
    </Grid>
</UserControl>
