<UserControl x:Class="CircleUtility.Controls.HardwareBadgeControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="20" d:DesignWidth="20">
    
    <UserControl.Resources>
        <Style x:Key="BadgeIconStyle" TargetType="Path">
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <!-- Animation for the glow effect -->
        <Storyboard x:Key="GlowAnimation" RepeatBehavior="Forever">
            <DoubleAnimation 
                Storyboard.TargetName="GlowEffect" 
                Storyboard.TargetProperty="Opacity"
                From="0.4" To="0.8" Duration="0:0:1.5" 
                AutoReverse="True"/>
        </Storyboard>
        
        <!-- Animation for the badge rotation -->
        <Storyboard x:Key="RotateAnimation" RepeatBehavior="Forever">
            <DoubleAnimation 
                Storyboard.TargetName="BadgeRotation" 
                Storyboard.TargetProperty="Angle"
                From="0" To="360" Duration="0:0:10"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Grid x:Name="MainGrid" ToolTip="Detected Hardware Component">
        <Ellipse x:Name="BadgeBackground" 
                 Width="20" 
                 Height="20" 
                 Fill="#00C8FF">
            <Ellipse.RenderTransform>
                <RotateTransform x:Name="BadgeRotation" CenterX="10" CenterY="10" Angle="0"/>
            </Ellipse.RenderTransform>
            <Ellipse.Effect>
                <DropShadowEffect x:Name="GlowEffect" 
                                  Color="#00C8FF" 
                                  BlurRadius="10" 
                                  ShadowDepth="0" 
                                  Opacity="0.6"/>
            </Ellipse.Effect>
        </Ellipse>
        
        <!-- Hardware Icon -->
        <Path x:Name="HardwareIcon" 
              Style="{StaticResource BadgeIconStyle}"
              Data="M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"
              Fill="White"/>
    </Grid>
</UserControl>
