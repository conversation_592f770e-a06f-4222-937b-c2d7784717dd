// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using CircleUtility.Models;
using CircleUtility.Interfaces;

namespace CircleUtility.Views
{
    /// <summary>
    /// Interaction logic for ThermalWarningDialog.xaml
    /// </summary>
    public partial class ThermalWarningDialog : Window, IThermalWarningDialog
    {
        private bool _userChoseToProceed;
        private PowerManagementProfile _profile;

        /// <summary>
        /// Gets a value indicating whether the user chose to proceed
        /// </summary>
        public bool UserChoseToProceed
        {
            get => _userChoseToProceed;
            // No setter - use SetUserChoice method instead
        }

        /// <summary>
        /// Gets or sets the power management profile
        /// </summary>
        public PowerManagementProfile Profile
        {
            get => _profile;
            set
            {
                _profile = value;
                if (_profile != null)
                {
                    UpdateProfileDisplay();
                }
            }
        }

        /// <summary>
        /// Sets the user choice without setting DialogResult (for testing)
        /// </summary>
        /// <param name="proceed">Whether the user chose to proceed</param>
        private void SetUserChoice(bool proceed)
        {
            _userChoseToProceed = proceed;
        }

        /// <summary>
        /// Initializes a new instance of the ThermalWarningDialog class
        /// </summary>
        public ThermalWarningDialog()
        {
            InitializeComponent();
            _userChoseToProceed = false;

            // Enable window dragging
            this.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ButtonState == MouseButtonState.Pressed)
                {
                    this.DragMove();
                }
            };
        }

        /// <summary>
        /// Initializes a new instance of the ThermalWarningDialog class with a power profile
        /// </summary>
        /// <param name="profile">The power profile</param>
        public ThermalWarningDialog(PowerManagementProfile profile) : this()
        {
            Profile = profile;
        }

        /// <summary>
        /// Shows the dialog with a power profile (static method)
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="profile">The power profile</param>
        /// <returns>True if the user chose to proceed, false otherwise</returns>
        public static bool ShowWarning(Window owner, PowerManagementProfile profile)
        {
            var dialog = new ThermalWarningDialog(profile);
            dialog.Owner = owner;
            dialog.ShowDialog();
            return dialog.UserChoseToProceed;
        }

        /// <summary>
        /// Shows the warning dialog with a power profile (interface implementation)
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="profile">The power profile</param>
        /// <returns>True if the user chose to proceed, false otherwise</returns>
        bool IThermalWarningDialog.ShowWarning(Window owner, PowerManagementProfile profile)
        {
            Profile = profile;
            Owner = owner;
            ShowDialog();
            return UserChoseToProceed;
        }

        /// <summary>
        /// Updates the profile display
        /// </summary>
        private void UpdateProfileDisplay()
        {
            if (_profile != null)
            {
                // Update UI elements with profile information
                ProfileNameText.Text = _profile.Name;

                // Additional UI updates can be added here as needed
                // For example, updating thermal impact indicators, etc.
            }
        }

        /// <summary>
        /// Handles the click event for the proceed button
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        public void ProceedButton_Click(object sender, RoutedEventArgs e)
        {
            SetUserChoice(true);
            try
            {
                DialogResult = true;
            }
            catch (InvalidOperationException)
            {
                // This can happen in tests when the window isn't shown as a dialog
            }
            Close();
        }

        /// <summary>
        /// Handles the click event for the cancel button
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        public void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            SetUserChoice(false);
            try
            {
                DialogResult = false;
            }
            catch (InvalidOperationException)
            {
                // This can happen in tests when the window isn't shown as a dialog
            }
            Close();
        }

        /// <summary>
        /// Handles the click event for the close button
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        public void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            SetUserChoice(false);
            try
            {
                DialogResult = false;
            }
            catch (InvalidOperationException)
            {
                // This can happen in tests when the window isn't shown as a dialog
            }
            Close();
        }

        #region IUIComponent Implementation

        /// <summary>
        /// Gets a value indicating whether the component is visible
        /// </summary>
        bool IUIComponent.IsVisible => IsVisible;

        /// <summary>
        /// Initializes the UI component
        /// </summary>
        public void Initialize()
        {
            // Already initialized in constructor
        }

        /// <summary>
        /// Shows the UI component
        /// </summary>
        void IUIComponent.Show()
        {
            Show();
        }

        /// <summary>
        /// Hides the UI component
        /// </summary>
        void IUIComponent.Hide()
        {
            Visibility = Visibility.Hidden;
        }

        #endregion
    }
}
