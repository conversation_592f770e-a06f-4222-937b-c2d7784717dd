// Created by Arsenal on 5-17-25 12:15PM
using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for performance metrics
    /// </summary>
    public class PerformanceMetric
    {
        /// <summary>
        /// Gets or sets the name of the metric
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the category of the metric
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the number of times the metric has been called
        /// </summary>
        public int CallCount { get; set; }

        /// <summary>
        /// Gets or sets the total time spent in the metric
        /// </summary>
        public TimeSpan TotalTime { get; set; }

        /// <summary>
        /// Gets or sets the minimum time spent in the metric
        /// </summary>
        public TimeSpan MinTime { get; set; }

        /// <summary>
        /// Gets or sets the maximum time spent in the metric
        /// </summary>
        public TimeSpan MaxTime { get; set; }

        /// <summary>
        /// Gets or sets the average time spent in the metric
        /// </summary>
        public TimeSpan AverageTime { get; set; }

        /// <summary>
        /// Gets or sets the last time spent in the metric
        /// </summary>
        public TimeSpan LastTime { get; set; }

        /// <summary>
        /// Gets the formatted total time
        /// </summary>
        public string FormattedTotalTime => FormatTimeSpan(TotalTime);

        /// <summary>
        /// Gets the formatted minimum time
        /// </summary>
        public string FormattedMinTime => FormatTimeSpan(MinTime);

        /// <summary>
        /// Gets the formatted maximum time
        /// </summary>
        public string FormattedMaxTime => FormatTimeSpan(MaxTime);

        /// <summary>
        /// Gets the formatted average time
        /// </summary>
        public string FormattedAverageTime => FormatTimeSpan(AverageTime);

        /// <summary>
        /// Gets the formatted last time
        /// </summary>
        public string FormattedLastTime => FormatTimeSpan(LastTime);

        /// <summary>
        /// Formats a TimeSpan for display
        /// </summary>
        /// <param name="timeSpan">The TimeSpan to format</param>
        /// <returns>The formatted TimeSpan</returns>
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalSeconds >= 1)
            {
                return $"{timeSpan.TotalSeconds:F2}s";
            }
            else if (timeSpan.TotalMilliseconds >= 1)
            {
                return $"{timeSpan.TotalMilliseconds:F2}ms";
            }
            else
            {
                return $"{timeSpan.TotalMilliseconds * 1000:F2}μs";
            }
        }
    }

    /// <summary>
    /// Event arguments for performance metrics
    /// </summary>
    public class PerformanceMetricEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the performance metric
        /// </summary>
        public PerformanceMetric Metric { get; }

        /// <summary>
        /// Initializes a new instance of the PerformanceMetricEventArgs class
        /// </summary>
        /// <param name="metric">The performance metric</param>
        public PerformanceMetricEventArgs(PerformanceMetric metric)
        {
            Metric = metric;
        }
    }
}
