using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for revert tweaks service
    /// </summary>
    public interface IRevertTweaksService
    {
        /// <summary>
        /// Event raised when tweaks are reverted
        /// </summary>
        event EventHand<PERSON> TweaksReverted;

        /// <summary>
        /// Gets active tweaks
        /// </summary>
        /// <returns>List of active tweaks</returns>
        List<TweakHistoryEntry> GetActiveTweaks();

        /// <summary>
        /// Reverts all tweaks
        /// </summary>
        /// <returns>True if successful</returns>
        Task<bool> RevertAllTweaksAsync();

        /// <summary>
        /// Reverts tweaks by category
        /// </summary>
        /// <param name="tweakType">The type of tweaks to revert</param>
        /// <returns>True if successful</returns>
        Task<bool> RevertTweaksByCategoryAsync(OptimizationType tweakType);
    }
}
