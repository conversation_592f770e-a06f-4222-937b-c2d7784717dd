using CircleUtility.Commands;
using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using CircleUtility.Helpers;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the game profiles view
    /// </summary>
    public class GameProfilesViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTracking;
        private GameProfile _selectedProfile;

        /// <summary>
        /// Initializes a new instance of the GameProfilesViewModel class
        /// </summary>
        public GameProfilesViewModel()
        {
            _logger = LoggingService.Instance;
            _userTracking = UserTrackingService.Instance;

            // Initialize commands
            ApplyProfileCommand = new RelayCommand<GameProfile>(ApplyProfile);
            RequestGameCommand = new RelayCommand(RequestGame);

            // Initialize game profiles
            InitializeGameProfiles();

            _logger.Log("GameProfilesViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of game profiles
        /// </summary>
        public ObservableCollection<GameProfile> GameProfiles { get; } = new ObservableCollection<GameProfile>();

        /// <summary>
        /// Gets or sets the selected game profile
        /// </summary>
        public GameProfile SelectedProfile
        {
            get => _selectedProfile;
            set => SetProperty(ref _selectedProfile, value);
        }

        /// <summary>
        /// Gets the apply profile command
        /// </summary>
        public ICommand ApplyProfileCommand { get; }

        /// <summary>
        /// Gets the request game command
        /// </summary>
        public ICommand RequestGameCommand { get; }

        /// <summary>
        /// Initializes the game profiles
        /// </summary>
        private void InitializeGameProfiles()
        {
            // Fortnite
            GameProfiles.Add(new GameProfile
            {
                Title = "Fortnite 2025",
                AsciiArt = @"
    /\__/\
   /      \
  |  |/\|  |
  |  /  \  |
   \      /
    \____/
",
                Description = "2025 Pro Config: Ultra-competitive settings with DX12 optimizations, DLSS 3.5 integration, and zero input delay profile. Includes custom shader tweaks for enemy visibility through builds.",
                ApplyAction = () => ApplyFortniteProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Call of Duty 2025
            GameProfiles.Add(new GameProfile
            {
                Title = "Call of Duty 2025",
                AsciiArt = @"
   _____  ____  _____    ___   ___  ___  ___
  / ____||  _ \|  __ \  |__ \ / _ \|__ \|__ \
 | |     | |_) | |  | |    ) | | | |  ) |  ) |
 | |     |  _ <| |  | |   / /| | | | / /  / /
 | |____ | |_) | |__| |  / /_| |_| |/ /_ / /_
  \_____||____/|_____/  |____|\___/|____|____|
",
                Description = "2025 Pro Series Config: Next-gen AI-enhanced aim assist calibration, quantum rendering pipeline optimization, and neural-haptic feedback integration. Features advanced visibility through smoke and flash effects with custom FOV optimization for maximum target acquisition.",
                ApplyAction = () => ApplyCOD2025Profile(),
                AccentColor = Color.FromRgb(255, 50, 50)
            });

            // Call of Duty: Warzone 3
            GameProfiles.Add(new GameProfile
            {
                Title = "Call of Duty: Warzone 3",
                AsciiArt = @"
     _____
    / ____|
   | |
   | |
   | |____
    \_____|
",
                Description = "2025 Tournament Config: Optimized for 360Hz displays with AI-enhanced visibility, FSR 3.0 upscaling, and custom memory management for zero stuttering. Used by CDL pros for maximum competitive edge.",
                ApplyAction = () => ApplyWarzoneProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Apex Legends Next
            GameProfiles.Add(new GameProfile
            {
                Title = "Apex Legends Next",
                AsciiArt = @"
      /\
     /  \
    / /\ \
   / ____ \
  /_/    \_\
",
                Description = "2025 Predator Config: Advanced movement optimization with custom prediction algorithms, reduced muzzle flash, enhanced enemy highlighting, and optimized netcode settings for superior hit registration.",
                ApplyAction = () => ApplyApexProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Counter-Strike 2 Pro
            GameProfiles.Add(new GameProfile
            {
                Title = "Counter-Strike 2 Pro",
                AsciiArt = @"
    _____
   / ____|
  | |
  | |
  | |____
   \_____|
",
                Description = "2025 FaceIT Config: Sub-tick optimization for perfect spray patterns, custom shader settings for maximum visibility, and advanced audio positioning. Includes pro-level viewmodel and crosshair settings.",
                ApplyAction = () => ApplyCS2Profile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Valorant Ultra
            GameProfiles.Add(new GameProfile
            {
                Title = "Valorant Ultra",
                AsciiArt = @"
  __      __
  \ \    / /
   \ \  / /
    \ \/ /
     \  /
      \/
",
                Description = "2025 Radiant Config: Zero-latency input profile with custom anti-aliasing implementation, optimized ability visibility, and enhanced enemy outline technology. Used by top Valorant Champions Tour players.",
                ApplyAction = () => ApplyValorantProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Overwatch 3
            GameProfiles.Add(new GameProfile
            {
                Title = "Overwatch 3",
                AsciiArt = @"
    ____
   / __ \
  | |  | |
  | |  | |
  | |__| |
   \____/
",
                Description = "2025 OWL Config: Advanced ability effect reduction, custom hero outline system, and optimized netcode settings. Includes dynamic resolution scaling for perfect 360FPS lock with zero input lag.",
                ApplyAction = () => ApplyOverwatchProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Rainbow Six: Evolution
            GameProfiles.Add(new GameProfile
            {
                Title = "Rainbow Six: Evolution",
                AsciiArt = @"
   _____
  |  __ \
  | |__) |
  |  _  /
  | | \ \
  |_|  \_\
",
                Description = "2025 Champion Config: Advanced destruction visibility settings, custom operator outline system, and recoil control optimization. Includes specialized audio EQ for perfect footstep detection.",
                ApplyAction = () => ApplyRainbowSixProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Battlefield Nexus
            GameProfiles.Add(new GameProfile
            {
                Title = "Battlefield Nexus",
                AsciiArt = @"
   ____
  |  _ \
  | |_) |
  |  _ <
  | |_) |
  |____/
",
                Description = "2025 Competitive Config: Advanced terrain rendering optimization, custom particle effect reduction, and specialized audio settings for perfect positional awareness in large-scale battles.",
                ApplyAction = () => ApplyBattlefieldProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });

            // Halo Infinite Pro
            GameProfiles.Add(new GameProfile
            {
                Title = "Halo Infinite Pro",
                AsciiArt = @"
   _    _
  | |  | |
  | |__| |
  |  __  |
  | |  | |
  |_|  |_|
",
                Description = "2025 HCS Config: Optimized shield visibility, weapon recoil patterns, and advanced enemy highlighting. Includes custom netcode settings for perfect registration on 360Hz displays.",
                ApplyAction = () => ApplyHaloProfile(),
                AccentColor = Color.FromRgb(0, 200, 255)
            });
        }

        /// <summary>
        /// Applies a game profile
        /// </summary>
        /// <param name="profile">The profile to apply</param>
        private void ApplyProfile(GameProfile profile)
        {
            if (profile == null)
                return;

            _logger.Log($"Applying game profile: {profile.Title}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Game Profile", $"Applied {profile.Title} profile");

            // Execute the profile's action
            profile.ApplyAction?.Invoke();
        }

        /// <summary>
        /// Requests a new game profile
        /// </summary>
        private void RequestGame()
        {
            _logger.Log("Game profile request submitted", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Game Profile", "Requested a new game profile");

            // Show a message to the user (this would be handled by the view)
        }

        // Game profile application methods
        private void ApplyFortniteProfile()
        {
            _logger.Log("Applying Fortnite 2025 profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyCOD2025Profile()
        {
            _logger.Log("Applying Call of Duty 2025 profile", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Game Profiles", "Applied Call of Duty 2025 profile");

            // Show success message
            MessageBox.Show(
                "Call of Duty 2025 profile applied successfully!\n\n" +
                "• Quantum rendering pipeline optimized\n" +
                "• Neural-haptic feedback calibrated\n" +
                "• AI-enhanced aim assist configured\n" +
                "• Advanced visibility settings applied\n" +
                "• Custom FOV optimization enabled\n\n" +
                "Launch the game to experience maximum competitive advantage.",
                "Profile Applied",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void ApplyWarzoneProfile()
        {
            _logger.Log("Applying Warzone 3 profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyApexProfile()
        {
            _logger.Log("Applying Apex Legends Next profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyCS2Profile()
        {
            _logger.Log("Applying Counter-Strike 2 Pro profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyValorantProfile()
        {
            _logger.Log("Applying Valorant Ultra profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyOverwatchProfile()
        {
            _logger.Log("Applying Overwatch 3 profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyRainbowSixProfile()
        {
            _logger.Log("Applying Rainbow Six: Evolution profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyBattlefieldProfile()
        {
            _logger.Log("Applying Battlefield Nexus profile", LogLevel.INFO);
            // Implementation would go here
        }

        private void ApplyHaloProfile()
        {
            _logger.Log("Applying Halo Infinite Pro profile", LogLevel.INFO);
            // Implementation would go here
        }
    }
}


