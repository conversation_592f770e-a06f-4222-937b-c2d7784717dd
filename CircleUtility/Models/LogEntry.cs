using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a log entry
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// Gets or sets the timestamp of the log entry
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the log message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the log level
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// Gets or sets the exception associated with this log entry
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// Gets or sets the thread ID
        /// </summary>
        public int ThreadId { get; set; }

        /// <summary>
        /// Gets or sets the source of the log entry
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Initializes a new instance of the LogEntry class
        /// </summary>
        public LogEntry()
        {
            Timestamp = DateTime.Now;
            ThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
        }
    }

    /// <summary>
    /// Event args for log entry events
    /// </summary>
    public class LogEntryEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the log entry
        /// </summary>
        public LogEntry LogEntry { get; }

        /// <summary>
        /// Initializes a new instance of the LogEntryEventArgs class
        /// </summary>
        /// <param name="logEntry">The log entry</param>
        public LogEntryEventArgs(LogEntry logEntry)
        {
            LogEntry = logEntry ?? throw new ArgumentNullException(nameof(logEntry));
        }
    }
}
