<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Primary Colors -->
    <Color x:Key="PrimaryBlue">#FF00C8FF</Color>
    <Color x:Key="PrimaryDarkBlue">#FF001428</Color>
    <Color x:Key="PrimaryLightBlue">#FF00DCFF</Color>
    
    <!-- Background Colors -->
    <Color x:Key="BackgroundBlack">#FF000000</Color>
    <Color x:Key="BackgroundDarkGray">#FF0A141E</Color>
    <Color x:Key="BackgroundMediumGray">#FF050A0F</Color>
    <Color x:Key="BackgroundLightGray">#FF222222</Color>
    
    <!-- Border Colors -->
    <Color x:Key="BorderBlue">#FF00B4F0</Color>
    <Color x:Key="BorderLightBlue">#FF00C8FF</Color>
    <Color x:Key="BorderGray">#FF555555</Color>
    
    <!-- Text Colors -->
    <Color x:Key="TextWhite">#FFFFFFFF</Color>
    <Color x:Key="TextLightGray">#FFCCCCCC</Color>
    <Color x:Key="TextBlue">#FF00C8FF</Color>
    <Color x:Key="TextLightBlue">#FF00DCFF</Color>
    
    <!-- Status Colors -->
    <Color x:Key="SuccessGreen">#FF00FF00</Color>
    <Color x:Key="WarningYellow">#FFFFFF00</Color>
    <Color x:Key="ErrorRed">#FFFF0000</Color>
    
    <!-- Hover/Interaction Colors -->
    <Color x:Key="HoverBlue">#FF002040</Color>
    <Color x:Key="PressedBlue">#FF003050</Color>
    <Color x:Key="HoverGray">#FF444444</Color>
    <Color x:Key="PressedGray">#FF222222</Color>
    
    <!-- Brushes for easier use -->
    <SolidColorBrush x:Key="PrimaryBlueBrush" Color="{StaticResource PrimaryBlue}"/>
    <SolidColorBrush x:Key="PrimaryDarkBlueBrush" Color="{StaticResource PrimaryDarkBlue}"/>
    <SolidColorBrush x:Key="PrimaryLightBlueBrush" Color="{StaticResource PrimaryLightBlue}"/>
    
    <SolidColorBrush x:Key="BackgroundBlackBrush" Color="{StaticResource BackgroundBlack}"/>
    <SolidColorBrush x:Key="BackgroundDarkGrayBrush" Color="{StaticResource BackgroundDarkGray}"/>
    <SolidColorBrush x:Key="BackgroundMediumGrayBrush" Color="{StaticResource BackgroundMediumGray}"/>
    <SolidColorBrush x:Key="BackgroundLightGrayBrush" Color="{StaticResource BackgroundLightGray}"/>
    
    <SolidColorBrush x:Key="BorderBlueBrush" Color="{StaticResource BorderBlue}"/>
    <SolidColorBrush x:Key="BorderLightBlueBrush" Color="{StaticResource BorderLightBlue}"/>
    <SolidColorBrush x:Key="BorderGrayBrush" Color="{StaticResource BorderGray}"/>
    
    <SolidColorBrush x:Key="TextWhiteBrush" Color="{StaticResource TextWhite}"/>
    <SolidColorBrush x:Key="TextLightGrayBrush" Color="{StaticResource TextLightGray}"/>
    <SolidColorBrush x:Key="TextBlueBrush" Color="{StaticResource TextBlue}"/>
    <SolidColorBrush x:Key="TextLightBlueBrush" Color="{StaticResource TextLightBlue}"/>
    
    <SolidColorBrush x:Key="SuccessGreenBrush" Color="{StaticResource SuccessGreen}"/>
    <SolidColorBrush x:Key="WarningYellowBrush" Color="{StaticResource WarningYellow}"/>
    <SolidColorBrush x:Key="ErrorRedBrush" Color="{StaticResource ErrorRed}"/>
    
    <SolidColorBrush x:Key="HoverBlueBrush" Color="{StaticResource HoverBlue}"/>
    <SolidColorBrush x:Key="PressedBlueBrush" Color="{StaticResource PressedBlue}"/>
    <SolidColorBrush x:Key="HoverGrayBrush" Color="{StaticResource HoverGray}"/>
    <SolidColorBrush x:Key="PressedGrayBrush" Color="{StaticResource PressedGray}"/>

</ResourceDictionary>
