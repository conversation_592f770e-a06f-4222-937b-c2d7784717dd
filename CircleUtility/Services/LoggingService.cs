using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CircleUtility.Models;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for logging application events
    /// </summary>
    public class LoggingService : ILogger, IDisposable
    {
        private static LoggingService _instance;
        private static readonly object _lock = new object();
        private readonly string _logDirectory;
        private readonly string _logFilePath;
        private readonly List<LogEntry> _logEntries;
        private readonly SemaphoreSlim _logLock;
        private LogLevel _logLevel;

        /// <summary>
        /// Event raised when a new log entry is added
        /// </summary>
        public event EventHandler<LogEntryEventArgs> LogEntryAdded;

        /// <summary>
        /// Gets the singleton instance of the logging service
        /// </summary>
        public static LoggingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LoggingService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes a new instance of the LoggingService class
        /// </summary>
        private LoggingService()
        {
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            _logFilePath = Path.Combine(_logDirectory, "CircleUtility.log");
            _logEntries = new List<LogEntry>();
            _logLock = new SemaphoreSlim(1, 1);
            _logLevel = LogLevel.INFO;

            // Ensure log directory exists
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }
        }

        /// <summary>
        /// Gets all log entries
        /// </summary>
        public IReadOnlyList<LogEntry> LogEntries => _logEntries;

        /// <summary>
        /// Gets or sets the log level
        /// </summary>
        public LogLevel LogLevel
        {
            get => _logLevel;
            set => _logLevel = value;
        }

        /// <summary>
        /// Logs a message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="level">The log level</param>
        /// <param name="exception">The exception, if any</param>
        public void Log(string message, LogLevel level, Exception exception = null)
        {
            if (level < _logLevel) return;

            var entry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Message = message,
                Level = level,
                Exception = exception
            };

            lock (_logEntries)
            {
                _logEntries.Add(entry);
                if (_logEntries.Count > 1000)
                {
                    _logEntries.RemoveRange(0, 100);
                }
            }

            // Log to console
            Console.WriteLine($"[{entry.Timestamp:HH:mm:ss}] [{entry.Level}] {entry.Message}");

            // Raise event
            LogEntryAdded?.Invoke(this, new LogEntryEventArgs(entry));
        }

        /// <summary>
        /// Logs a message asynchronously
        /// </summary>
        public async Task LogAsync(string message, LogLevel level, Exception exception = null)
        {
            await Task.Run(() => Log(message, level, exception));
        }

        /// <summary>
        /// Clears all log entries
        /// </summary>
        public void ClearLogs()
        {
            lock (_logEntries)
            {
                _logEntries.Clear();
            }
        }


        /// <summary>
        /// Logs a message (ILogger interface implementation)
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="level">The log level</param>
        public void Log(string message, LogLevel level)
        {
            Log(message, level, null);
        }

        /// <summary>
        /// Disposes the logging service
        /// </summary>
        public void Dispose()
        {
            try
            {
                _logLock?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error disposing logging service: {ex.Message}");
            }
        }
    }
}

