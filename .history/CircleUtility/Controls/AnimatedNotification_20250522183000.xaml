<UserControl x:Class="CircleUtility.Controls.AnimatedNotification"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="80" d:DesignWidth="300"
             Loaded="UserControl_Loaded">

    <UserControl.Resources>
        <!-- Animations are created programmatically in code-behind -->
    </UserControl.Resources>

    <Border x:Name="MainBorder"
           Background="#FF050A0F"
           BorderThickness="1"
           CornerRadius="5"
           Opacity="0"
           Margin="0,-80,0,0">
        <Border.BorderBrush>
            <SolidColorBrush x:Name="BorderBrush" Color="#FF00C8FF"/>
        </Border.BorderBrush>
        <Border.Effect>
            <DropShadowEffect x:Name="GlowEffect"
                             Color="#FF00C8FF"
                             BlurRadius="10"
                             ShadowDepth="0"
                             Opacity="0.5"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="3"/>
            </Grid.RowDefinitions>

            <Grid Margin="15,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Icon -->
                <Path x:Name="NotificationIcon"
                     Width="20"
                     Height="20"
                     Fill="#FF00C8FF"
                     Stretch="Uniform"
                     VerticalAlignment="Center"
                     Margin="0,0,10,0"/>

                <!-- Content -->
                <StackPanel Grid.Column="1">
                    <TextBlock x:Name="TitleText"
                              Text="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}"
                              FontFamily="Consolas"
                              FontSize="14"
                              FontWeight="Bold"
                              Foreground="#FF00C8FF"/>

                    <TextBlock x:Name="MessageText"
                              Text="{Binding Message, RelativeSource={RelativeSource AncestorType=UserControl}}"
                              FontFamily="Consolas"
                              FontSize="12"
                              Foreground="White"
                              TextWrapping="Wrap"
                              Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Close button -->
                <Button Grid.Column="2"
                       Content="✕"
                       FontFamily="Consolas"
                       FontSize="12"
                       Foreground="White"
                       Background="Transparent"
                       BorderThickness="0"
                       Padding="5"
                       VerticalAlignment="Top"
                       HorizontalAlignment="Right"
                       Cursor="Hand"
                       Click="CloseButton_Click"/>
            </Grid>

            <!-- Progress bar -->
            <Border Grid.Row="1"
                   x:Name="ProgressBar"
                   Background="#FF00C8FF"
                   HorizontalAlignment="Left"
                   Height="3"/>
        </Grid>
    </Border>
</UserControl>
