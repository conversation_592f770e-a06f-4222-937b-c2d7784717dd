using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace CircleUtility.Controls
{
    public partial class ApplyRevertButtonPair : UserControl
    {
        public ApplyRevertButtonPair()
        {
            InitializeComponent();
        }

        // =======================
        // Dependency Properties
        // =======================

        public static readonly DependencyProperty ApplyButtonTextProperty =
            DependencyProperty.Register(nameof(ApplyButtonText), typeof(string), typeof(ApplyRevertButtonPair),
                new PropertyMetadata("APPLY", (d, e) =>
                {
                    if (d is ApplyRevertButtonPair ctrl && e.NewValue is string text)
                        ctrl.ApplyButton.ButtonText = text;
                }));

        public static readonly DependencyProperty RevertButtonTextProperty =
            DependencyProperty.Register(nameof(RevertButtonText), typeof(string), typeof(ApplyRevertButtonPair),
                new PropertyMetadata("REVERT", (d, e) =>
                {
                    if (d is ApplyRevertButtonPair ctrl && e.NewValue is string text)
                        ctrl.RevertButton.ButtonText = text;
                }));

        public static readonly DependencyProperty ApplyCommandProperty =
            DependencyProperty.Register(nameof(ApplyCommand), typeof(ICommand), typeof(ApplyRevertButtonPair),
                new PropertyMetadata(null, (d, e) =>
                {
                    if (d is ApplyRevertButtonPair ctrl && e.NewValue is ICommand cmd)
                        ctrl.ApplyButton.Command = cmd;
                }));

        public static readonly DependencyProperty RevertCommandProperty =
            DependencyProperty.Register(nameof(RevertCommand), typeof(ICommand), typeof(ApplyRevertButtonPair),
                new PropertyMetadata(null, (d, e) =>
                {
                    if (d is ApplyRevertButtonPair ctrl && e.NewValue is ICommand cmd)
                        ctrl.RevertButton.Command = cmd;
                }));

        public static readonly DependencyProperty ApplyCommandParameterProperty =
            DependencyProperty.Register(nameof(ApplyCommandParameter), typeof(object), typeof(ApplyRevertButtonPair),
                new PropertyMetadata(null, (d, e) =>
                {
                    if (d is ApplyRevertButtonPair ctrl)
                        ctrl.ApplyButton.CommandParameter = e.NewValue;
                }));

        public static readonly DependencyProperty RevertCommandParameterProperty =
            DependencyProperty.Register(nameof(RevertCommandParameter), typeof(object), typeof(ApplyRevertButtonPair),
                new PropertyMetadata(null, (d, e) =>
                {
                    if (d is ApplyRevertButtonPair ctrl)
                        ctrl.RevertButton.CommandParameter = e.NewValue;
                }));

        public static readonly DependencyProperty IsRevertEnabledProperty =
            DependencyProperty.Register(nameof(IsRevertEnabled), typeof(bool), typeof(ApplyRevertButtonPair),
                new PropertyMetadata(true, (d, e) =>
                {
                    if (d is ApplyRevertButtonPair ctrl && e.NewValue is bool isEnabled)
                        ctrl.RevertButton.IsEnabled = isEnabled;
                }));

        // =======================
        // CLR Wrappers
        // =======================

        public string ApplyButtonText
        {
            get => (string)GetValue(ApplyButtonTextProperty);
            set => SetValue(ApplyButtonTextProperty, value);
        }

        public string RevertButtonText
        {
            get => (string)GetValue(RevertButtonTextProperty);
            set => SetValue(RevertButtonTextProperty, value);
        }

        public ICommand ApplyCommand
        {
            get => (ICommand)GetValue(ApplyCommandProperty);
            set => SetValue(ApplyCommandProperty, value);
        }

        public ICommand RevertCommand
        {
            get => (ICommand)GetValue(RevertCommandProperty);
            set => SetValue(RevertCommandProperty, value);
        }

        public object ApplyCommandParameter
        {
            get => GetValue(ApplyCommandParameterProperty);
            set => SetValue(ApplyCommandParameterProperty, value);
        }

        public object RevertCommandParameter
        {
            get => GetValue(RevertCommandParameterProperty);
            set => SetValue(RevertCommandParameterProperty, value);
        }

        public bool IsRevertEnabled
        {
            get => (bool)GetValue(IsRevertEnabledProperty);
            set => SetValue(IsRevertEnabledProperty, value);
        }

        // =======================
        // Routed Events
        // =======================

        public event RoutedEventHandler ApplyClicked;
        public event RoutedEventHandler RevertClicked;

        private void ApplyButton_Click(object sender, RoutedEventArgs e) => ApplyClicked?.Invoke(this, e);
        private void RevertButton_Click(object sender, RoutedEventArgs e) => RevertClicked?.Invoke(this, e);

        // =======================
        // Public Methods
        // =======================

        public void ShowApplySuccess()
        {
            ApplyButton.ShowSuccess();
            IsRevertEnabled = true;
        }

        public void ShowRevertSuccess()
        {
            RevertButton.ShowSuccess();
            IsRevertEnabled = false;
        }

        public void ShowApplySuccessAndReset(int resetDelayMs = 1500)
        {
            ApplyButton.ShowSuccessAndReset(resetDelayMs);
            IsRevertEnabled = true;
        }

        public void ShowRevertSuccessAndReset(int resetDelayMs = 1500)
        {
            RevertButton.ShowSuccessAndReset(resetDelayMs);
            IsRevertEnabled = false;
        }

        public void Reset()
        {
            ApplyButton.Reset();
            RevertButton.Reset();
        }
    }
}
