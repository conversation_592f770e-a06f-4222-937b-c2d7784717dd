using System;
using System.Threading.Tasks;
using System.Windows.Input;
using CircleUtility.Services;
using CircleUtility.Views;
using CircleUtility.Models;

namespace CircleUtility.ViewModels
{
    public class UpdateViewModel : BaseViewModel
    {
        private readonly UpdateService _updateService;
        private bool _isUpdateAvailable;
        private bool _isUpdateInProgress;
        private string _updateStatus;
        private CircleUtility.Services.UpdateInfo _latestUpdate;
        private UpdateProgressWindow _progressWindow;

        public bool IsUpdateAvailable
        {
            get => _isUpdateAvailable;
            set => SetProperty(ref _isUpdateAvailable, value);
        }

        public bool IsUpdateInProgress
        {
            get => _isUpdateInProgress;
            set => SetProperty(ref _isUpdateInProgress, value);
        }

        public string UpdateStatus
        {
            get => _updateStatus;
            set => SetProperty(ref _updateStatus, value);
        }

        public CircleUtility.Services.UpdateInfo LatestUpdate
        {
            get => _latestUpdate;
            set => SetProperty(ref _latestUpdate, value);
        }

        public ICommand CheckForUpdatesCommand { get; }
        public ICommand InstallUpdateCommand { get; }

        public UpdateViewModel()
        {
            _updateService = UpdateService.Instance;

            CheckForUpdatesCommand = new RelayCommand(async () => await CheckForUpdatesAsync());
            InstallUpdateCommand = new RelayCommand(async () => await InstallUpdateAsync(), () => IsUpdateAvailable && !IsUpdateInProgress);

            // Subscribe to update service events
            _updateService.UpdateAvailable += OnUpdateAvailable;
            _updateService.UpdateProgress += OnUpdateProgress;
            _updateService.UpdateError += OnUpdateError;
            _updateService.UpdateInstalled += OnUpdateInstalled;
        }

        private async Task CheckForUpdatesAsync()
        {
            try
            {
                IsUpdateInProgress = true;
                UpdateStatus = "Checking for updates...";
                await _updateService.CheckForUpdatesAsync();
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Log($"Error checking for updates: {ex.Message}", LogLevel.ERROR);
                UpdateStatus = "Error checking for updates";
            }
            finally
            {
                IsUpdateInProgress = false;
            }
        }

        private async Task InstallUpdateAsync()
        {
            try
            {
                IsUpdateInProgress = true;
                _progressWindow = new UpdateProgressWindow();
                _progressWindow.Show();

                await _updateService.DownloadAndInstallUpdateAsync(LatestUpdate.DownloadUrl);
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Log($"Error installing update: {ex.Message}", LogLevel.ERROR);
                _progressWindow?.SetError($"Error installing update: {ex.Message}");
            }
        }

        private void OnUpdateAvailable(object sender, CircleUtility.Services.UpdateInfo updateInfo)
        {
            LatestUpdate = updateInfo;
            IsUpdateAvailable = true;
            UpdateStatus = $"Update available: {updateInfo.Version}";
        }

        private void OnUpdateProgress(object sender, string progress)
        {
            _progressWindow?.UpdateProgress(progress, 0);
        }

        private void OnUpdateError(object sender, Exception ex)
        {
            LoggingService.Instance.Log($"Update error: {ex.Message}", LogLevel.ERROR);
            _progressWindow?.SetError(ex.Message);
            IsUpdateInProgress = false;
        }

        private void OnUpdateInstalled(object sender, UpdateHistory history)
        {
            LoggingService.Instance.Log($"Update installed successfully: {history.Version}", LogLevel.SUCCESS);
            _progressWindow?.Close();
            IsUpdateInProgress = false;
            IsUpdateAvailable = false;
            UpdateStatus = "Update installed successfully";
        }
    }
} 