using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Media;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using CircleUtility.Models;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing notifications
    /// </summary>
    public class NotificationService : INotificationService
    {
        private static NotificationService _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of the NotificationService
        /// </summary>
        public static NotificationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new NotificationService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private NotificationService()
        {
            // Initialize service
        }
        private readonly ILoggerService _logger;
        private readonly ObservableCollection<SystemNotification> _notifications;
        private readonly NotificationSettings _settings;
        private readonly DispatcherTimer _cleanupTimer;
        private readonly string _notificationsFilePath;
        private int _unreadCount;
        private object _notificationPanel;

        /// <summary>
        /// Initializes a new instance of the NotificationService class
        /// </summary>
        /// <param name="logger">The logger</param>
        private NotificationService(ILoggerService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _notifications = new ObservableCollection<SystemNotification>();
            _settings = new NotificationSettings();
            _unreadCount = 0;

            // Set up the notifications file path
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CircleUtility");

            // Create the directory if it doesn't exist
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _notificationsFilePath = Path.Combine(appDataPath, "notifications.json");

            // Set up the cleanup timer
            _cleanupTimer = new DispatcherTimer();
            _cleanupTimer.Interval = TimeSpan.FromHours(1);
            _cleanupTimer.Tick += CleanupExpiredNotifications;
            _cleanupTimer.Start();

            // Load notifications from file
            LoadNotifications();
        }

        /// <summary>
        /// Gets the notifications
        /// </summary>
        public ObservableCollection<SystemNotification> Notifications => _notifications;

        /// <summary>
        /// Gets the settings
        /// </summary>
        public NotificationSettings Settings => _settings;

        /// <summary>
        /// Gets the unread count
        /// </summary>
        public int UnreadCount => _unreadCount;

        /// <summary>
        /// Gets a value indicating whether there are any notifications
        /// </summary>
        public bool HasNotifications => _notifications.Count > 0;

        /// <summary>
        /// Gets a value indicating whether there are any unread notifications
        /// </summary>
        public bool HasUnreadNotifications => _unreadCount > 0;

        /// <summary>
        /// Adds a notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="severity">The severity</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        public SystemNotification AddNotification(
            string title,
            string message,
            NotificationSeverity severity = NotificationSeverity.Info,
            string category = "System",
            System.Windows.Input.ICommand action = null,
            string actionText = null)
        {
            var notification = new SystemNotification
            {
                Title = title,
                Message = message,
                Severity = severity,
                Category = category,
                Action = action,
                ActionText = actionText,
                Timestamp = DateTime.Now
            };

            return AddNotification(notification);
        }

        /// <summary>
        /// Adds a notification
        /// </summary>
        /// <param name="notification">The notification</param>
        /// <returns>The notification</returns>
        public SystemNotification AddNotification(SystemNotification notification)
        {
            if (notification == null)
            {
                throw new ArgumentNullException(nameof(notification));
            }

            // Check if the notification should be shown based on settings
            if (!_settings.ShouldShowNotification(notification.Severity) ||
                !_settings.ShouldShowCategory(notification.Category))
            {
                return notification;
            }

            // Add the notification to the collection
            Application.Current.Dispatcher.Invoke(() =>
            {
                _notifications.Insert(0, notification);

                if (!notification.IsRead)
                {
                    _unreadCount++;
                }

                // Play sound effect if enabled
                if (_settings.EnableSoundEffects)
                {
                    string soundFile = _settings.GetSoundEffect(notification.Severity);
                    if (!string.IsNullOrEmpty(soundFile))
                    {
                        PlaySound(soundFile);
                    }
                }

                // Limit the number of notifications
                while (_notifications.Count > _settings.MaxNotificationHistory)
                {
                    var oldNotification = _notifications.Last();
                    if (!oldNotification.IsRead)
                    {
                        _unreadCount--;
                    }

                    _notifications.Remove(oldNotification);
                }
            });

            // Save notifications to file
            if (_settings.SaveNotificationsToFile)
            {
                SaveNotifications();
            }

            _logger.Log($"Added notification: {notification.Title}", Models.LogLevel.INFO);

            // Raise the notification added event
            RaiseNotificationAddedEvent(notification);

            return notification;
        }

        /// <summary>
        /// Marks all notifications as read
        /// </summary>
        public void MarkAllAsRead()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                foreach (var notification in _notifications)
                {
                    notification.MarkAsRead();
                }

                _unreadCount = 0;
            });

            // Save notifications to file
            if (_settings.SaveNotificationsToFile)
            {
                SaveNotifications();
            }

            _logger.Log("Marked all notifications as read", Models.LogLevel.INFO);
        }

        /// <summary>
        /// Clears all notifications
        /// </summary>
        public void ClearAll()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                _notifications.Clear();
                _unreadCount = 0;
            });

            // Save notifications to file
            if (_settings.SaveNotificationsToFile)
            {
                SaveNotifications();
            }

            _logger.Log("Cleared all notifications", Models.LogLevel.INFO);
        }

        /// <summary>
        /// Dismisses a notification
        /// </summary>
        /// <param name="notification">The notification</param>
        public void DismissNotification(SystemNotification notification)
        {
            if (notification == null)
            {
                throw new ArgumentNullException(nameof(notification));
            }

            Application.Current.Dispatcher.Invoke(() =>
            {
                if (!notification.IsRead)
                {
                    _unreadCount--;
                }

                _notifications.Remove(notification);
            });

            // Save notifications to file
            if (_settings.SaveNotificationsToFile)
            {
                SaveNotifications();
            }

            _logger.Log($"Dismissed notification: {notification.Title}", Models.LogLevel.INFO);
        }

        /// <summary>
        /// Toggles the read status of a notification
        /// </summary>
        /// <param name="notification">The notification</param>
        public void ToggleReadStatus(SystemNotification notification)
        {
            if (notification == null)
            {
                throw new ArgumentNullException(nameof(notification));
            }

            Application.Current.Dispatcher.Invoke(() =>
            {
                if (notification.IsRead)
                {
                    notification.MarkAsUnread();
                    _unreadCount++;
                }
                else
                {
                    notification.MarkAsRead();
                    _unreadCount--;
                }
            });

            // Save notifications to file
            if (_settings.SaveNotificationsToFile)
            {
                SaveNotifications();
            }

            _logger.Log($"Toggled read status of notification: {notification.Title}", Models.LogLevel.INFO);
        }

        /// <summary>
        /// Loads notifications from file
        /// </summary>
        private void LoadNotifications()
        {
            try
            {
                if (!File.Exists(_notificationsFilePath))
                {
                    return;
                }

                string json = File.ReadAllText(_notificationsFilePath);
                var notifications = Newtonsoft.Json.JsonConvert.DeserializeObject<List<SystemNotification>>(json);

                if (notifications != null)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        _notifications.Clear();
                        _unreadCount = 0;

                        foreach (var notification in notifications)
                        {
                            _notifications.Add(notification);

                            if (!notification.IsRead)
                            {
                                _unreadCount++;
                            }
                        }
                    });
                }

                _logger.Log("Loaded notifications from file", Models.LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading notifications: {ex.Message}", Models.LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Saves notifications to file
        /// </summary>
        private void SaveNotifications()
        {
            try
            {
                var notifications = _notifications.ToList();
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(notifications, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(_notificationsFilePath, json);

                _logger.Log("Saved notifications to file", Models.LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving notifications: {ex.Message}", Models.LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Cleans up expired notifications
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void CleanupExpiredNotifications(object sender, EventArgs e)
        {
            try
            {
                var now = DateTime.Now;
                var expiredNotifications = _notifications
                    .Where(n => n.ExpirationDate is DateTime expirationDate && expirationDate < now)
                    .ToList();

                if (expiredNotifications.Count > 0)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        foreach (var notification in expiredNotifications)
                        {
                            if (!notification.IsRead)
                            {
                                _unreadCount--;
                            }

                            _notifications.Remove(notification);
                        }
                    });

                    // Save notifications to file
                    if (_settings.SaveNotificationsToFile)
                    {
                        SaveNotifications();
                    }

                    _logger.Log($"Cleaned up {expiredNotifications.Count} expired notifications", Models.LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error cleaning up expired notifications: {ex.Message}", Models.LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Plays a sound
        /// </summary>
        /// <param name="soundFile">The sound file</param>
        private void PlaySound(string soundFile)
        {
            try
            {
                string soundPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", "Sounds", soundFile);

                if (File.Exists(soundPath))
                {
                    var player = new SoundPlayer(soundPath);
                    player.Play();
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error playing sound: {ex.Message}", Models.LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Initializes the notification service
        /// </summary>
        public void Initialize()
        {
            _logger.Log("Notification service initialized", Models.LogLevel.INFO);
        }

        /// <summary>
        /// Initializes the notification service with settings
        /// </summary>
        /// <param name="settings">The notification settings</param>
        public void Initialize(NotificationSettings settings)
        {
            if (settings != null)
            {
                // Apply settings
                _settings.EnableSoundEffects = settings.EnableSoundEffects;
                _settings.MaxNotificationHistory = settings.MaxNotificationHistory;
                _settings.SaveNotificationsToFile = settings.SaveNotificationsToFile;
                _settings.EnableInfoNotifications = settings.EnableInfoNotifications;
                _settings.EnableSuccessNotifications = settings.EnableSuccessNotifications;
                _settings.EnableWarningNotifications = settings.EnableWarningNotifications;
                _settings.EnableErrorNotifications = settings.EnableErrorNotifications;
            }

            _logger.Log("Notification service initialized with settings", Models.LogLevel.INFO);
        }

        /// <summary>
        /// Initializes the notification service with a notification panel
        /// </summary>
        /// <param name="notificationPanel">The notification panel</param>
        public void Initialize(object notificationPanel)
        {
            // Store the notification panel for later use
            _notificationPanel = notificationPanel;

            _logger.Log("Notification service initialized with notification panel", Models.LogLevel.INFO);
        }

        /// <summary>
        /// Adds an info notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        public SystemNotification AddInfoNotification(string title, string message, string category = "System", System.Windows.Input.ICommand action = null, string actionText = null)
        {
            return AddNotification(title, message, NotificationSeverity.Info, category, action, actionText);
        }

        /// <summary>
        /// Adds a success notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        public SystemNotification AddSuccessNotification(string title, string message, string category = "System", System.Windows.Input.ICommand action = null, string actionText = null)
        {
            return AddNotification(title, message, NotificationSeverity.Success, category, action, actionText);
        }

        /// <summary>
        /// Adds a warning notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        public SystemNotification AddWarningNotification(string title, string message, string category = "System", System.Windows.Input.ICommand action = null, string actionText = null)
        {
            return AddNotification(title, message, NotificationSeverity.Warning, category, action, actionText);
        }

        /// <summary>
        /// Adds an error notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        public SystemNotification AddErrorNotification(string title, string message, string category = "System", System.Windows.Input.ICommand action = null, string actionText = null)
        {
            return AddNotification(title, message, NotificationSeverity.Error, category, action, actionText);
        }

        /// <summary>
        /// Gets the notification history
        /// </summary>
        public IEnumerable<SystemNotification> NotificationHistory => _notifications;

#nullable enable
        /// <summary>
        /// Event that is raised when a notification is added
        /// </summary>
        public event EventHandler<SystemNotification>? NotificationAdded;
#nullable restore

        // Ensure the event is used somewhere to avoid the warning
        private void RaiseNotificationAddedEvent(SystemNotification notification)
        {
            NotificationAdded?.Invoke(this, notification);
        }

        /// <summary>
        /// Event that is raised when a notification is read
        /// </summary>
        public event EventHandler<SystemNotification> NotificationRead;

        /// <summary>
        /// Event that is raised when the unread count changes
        /// </summary>
        public event EventHandler<int> UnreadCountChanged;

        /// <summary>
        /// Marks a notification as read
        /// </summary>
        /// <param name="notification">The notification</param>
        public void MarkAsRead(SystemNotification notification)
        {
            if (notification == null)
            {
                throw new ArgumentNullException(nameof(notification));
            }

            if (!notification.IsRead)
            {
                notification.MarkAsRead();
                _unreadCount--;
                NotificationRead?.Invoke(this, notification);
                UnreadCountChanged?.Invoke(this, _unreadCount);
            }
        }

        /// <summary>
        /// Marks a notification as unread
        /// </summary>
        /// <param name="notification">The notification</param>
        public void MarkAsUnread(SystemNotification notification)
        {
            if (notification == null)
            {
                throw new ArgumentNullException(nameof(notification));
            }

            if (notification.IsRead)
            {
                notification.MarkAsUnread();
                _unreadCount++;
                UnreadCountChanged?.Invoke(this, _unreadCount);
            }
        }
    }
}


