// Created by Arsenal on 5-17-25 12:15PM
using System;
using CircleUtility.Models;
using System.Collections.Generic;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for application configuration
    /// </summary>
    public class AppConfiguration
    {
        /// <summary>
        /// Gets or sets the general settings
        /// </summary>
        public GeneralSettings General { get; set; } = new GeneralSettings();

        /// <summary>
        /// Gets or sets the interface settings
        /// </summary>
        public InterfaceSettings Interface { get; set; } = new InterfaceSettings();

        /// <summary>
        /// Gets or sets the performance settings
        /// </summary>
        public PerformanceSettings Performance { get; set; } = new PerformanceSettings();

        /// <summary>
        /// Gets or sets the logging settings
        /// </summary>
        public LoggingSettings Logging { get; set; } = new LoggingSettings();

        /// <summary>
        /// Gets or sets the advanced settings
        /// </summary>
        public AdvancedSettings Advanced { get; set; } = new AdvancedSettings();

        /// <summary>
        /// Gets or sets the version of the configuration
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// Gets or sets the last modified date
        /// </summary>
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Model for general settings
    /// </summary>
    public class GeneralSettings
    {
        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username { get; set; } = "User";

        /// <summary>
        /// Gets or sets a value indicating whether to start with Windows
        /// </summary>
        public bool StartWithWindows { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to check for updates at startup
        /// </summary>
        public bool CheckForUpdatesAtStartup { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to minimize to tray
        /// </summary>
        public bool MinimizeToTray { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to confirm before applying tweaks
        /// </summary>
        public bool ConfirmBeforeApplyingTweaks { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to show tips at startup
        /// </summary>
        public bool ShowTipsAtStartup { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to automatically update the application
        /// </summary>
        public bool AutoUpdate { get; set; } = true;

        /// <summary>
        /// Gets or sets the update check interval (0 = never, 1 = daily, 2 = weekly, 3 = monthly)
        /// </summary>
        public int UpdateCheckInterval { get; set; } = 1;

        /// <summary>
        /// Gets or sets the update server URL
        /// </summary>
        public string UpdateServerUrl { get; set; } = "https://updates.circleutility.com";

        /// <summary>
        /// Gets or sets the last update check date
        /// </summary>
        public DateTime? LastUpdateCheck { get; set; }

        /// <summary>
        /// Gets or sets the last login date
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// Gets or sets the security level
        /// </summary>
        public int SecurityLevel { get; set; } = 1;

        /// <summary>
        /// Gets or sets the custom tips
        /// </summary>
        public List<string> CustomTips { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the recent activities
        /// </summary>
        public List<string> RecentActivities { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the favorite tweaks
        /// </summary>
        public List<string> FavoriteTweaks { get; set; } = new List<string>();

        /// <summary>
        /// Gets the formatted last login date
        /// </summary>
        public string FormattedLastLogin => LastLogin?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the security level description
        /// </summary>
        public string SecurityLevelDescription
        {
            get
            {
                switch (SecurityLevel)
                {
                    case 1:
                        return "Basic";
                    case 2:
                        return "Advanced";
                    case 3:
                        return "Expert";
                    default:
                        return "Unknown";
                }
            }
        }
    }

    /// <summary>
    /// Model for interface settings
    /// </summary>
    public class InterfaceSettings
    {
        /// <summary>
        /// Gets or sets a value indicating whether dark mode is enabled
        /// </summary>
        public bool EnableDarkMode { get; set; } = true;

        /// <summary>
        /// Gets or sets the animation speed index
        /// </summary>
        public int AnimationSpeedIndex { get; set; } = 1; // 0: Slow, 1: Normal, 2: Fast

        /// <summary>
        /// Gets or sets the font size index
        /// </summary>
        public int FontSizeIndex { get; set; } = 1; // 0: Small, 1: Medium, 2: Large

        /// <summary>
        /// Gets or sets a value indicating whether to show tooltips
        /// </summary>
        public bool ShowTooltips { get; set; } = true;

        /// <summary>
        /// Gets or sets the theme index
        /// </summary>
        public int ThemeIndex { get; set; } = 0; // 0: Default, 1: Blue, 2: Green, 3: Red, 4: Purple

        /// <summary>
        /// Gets or sets a value indicating whether to show animations
        /// </summary>
        public bool ShowAnimations { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to show status bar
        /// </summary>
        public bool ShowStatusBar { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to show menu icons
        /// </summary>
        public bool ShowMenuIcons { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to use compact mode
        /// </summary>
        public bool UseCompactMode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to use hardware acceleration
        /// </summary>
        public bool UseHardwareAcceleration { get; set; } = true;

        /// <summary>
        /// Gets or sets the custom accent color
        /// </summary>
        public string CustomAccentColor { get; set; } = "#00C8FF";

        /// <summary>
        /// Gets the animation speed description
        /// </summary>
        public string AnimationSpeedDescription
        {
            get
            {
                switch (AnimationSpeedIndex)
                {
                    case 0:
                        return "Slow";
                    case 1:
                        return "Normal";
                    case 2:
                        return "Fast";
                    default:
                        return "Normal";
                }
            }
        }

        /// <summary>
        /// Gets the font size description
        /// </summary>
        public string FontSizeDescription
        {
            get
            {
                switch (FontSizeIndex)
                {
                    case 0:
                        return "Small";
                    case 1:
                        return "Medium";
                    case 2:
                        return "Large";
                    default:
                        return "Medium";
                }
            }
        }

        /// <summary>
        /// Gets the theme description
        /// </summary>
        public string ThemeDescription
        {
            get
            {
                switch (ThemeIndex)
                {
                    case 0:
                        return "Default";
                    case 1:
                        return "Blue";
                    case 2:
                        return "Green";
                    case 3:
                        return "Red";
                    case 4:
                        return "Purple";
                    default:
                        return "Default";
                }
            }
        }
    }

    /// <summary>
    /// Model for performance settings
    /// </summary>
    public class PerformanceSettings
    {
        /// <summary>
        /// Gets or sets the refresh interval index
        /// </summary>
        public int RefreshIntervalIndex { get; set; } = 1; // 0: 1 second, 1: 5 seconds, 2: 10 seconds, 3: 30 seconds

        /// <summary>
        /// Gets or sets a value indicating whether hardware monitoring is enabled
        /// </summary>
        public bool EnableHardwareMonitoring { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether background processing is enabled
        /// </summary>
        public bool EnableBackgroundProcessing { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether auto optimization is enabled
        /// </summary>
        public bool EnableAutoOptimization { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to show FPS counter
        /// </summary>
        public bool ShowFpsCounter { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether game detection is enabled
        /// </summary>
        public bool EnableGameDetection { get; set; } = true;

        /// <summary>
        /// Gets or sets the CPU priority index
        /// </summary>
        public int CpuPriorityIndex { get; set; } = 1; // 0: Low, 1: Normal, 2: High

        /// <summary>
        /// Gets or sets the memory optimization level
        /// </summary>
        public int MemoryOptimizationLevel { get; set; } = 1; // 0: None, 1: Normal, 2: Aggressive

        /// <summary>
        /// Gets or sets the disk optimization level
        /// </summary>
        public int DiskOptimizationLevel { get; set; } = 1; // 0: None, 1: Normal, 2: Aggressive

        /// <summary>
        /// Gets or sets the network optimization level
        /// </summary>
        public int NetworkOptimizationLevel { get; set; } = 1; // 0: None, 1: Normal, 2: Aggressive

        /// <summary>
        /// Gets or sets the auto optimization interval in minutes
        /// </summary>
        public int AutoOptimizationInterval { get; set; } = 60;

        /// <summary>
        /// Gets the refresh interval description
        /// </summary>
        public string RefreshIntervalDescription
        {
            get
            {
                switch (RefreshIntervalIndex)
                {
                    case 0:
                        return "1 second";
                    case 1:
                        return "5 seconds";
                    case 2:
                        return "10 seconds";
                    case 3:
                        return "30 seconds";
                    default:
                        return "5 seconds";
                }
            }
        }

        /// <summary>
        /// Gets the refresh interval in milliseconds
        /// </summary>
        public int RefreshIntervalMs
        {
            get
            {
                switch (RefreshIntervalIndex)
                {
                    case 0:
                        return 1000;
                    case 1:
                        return 5000;
                    case 2:
                        return 10000;
                    case 3:
                        return 30000;
                    default:
                        return 5000;
                }
            }
        }

        /// <summary>
        /// Gets the CPU priority description
        /// </summary>
        public string CpuPriorityDescription
        {
            get
            {
                switch (CpuPriorityIndex)
                {
                    case 0:
                        return "Low";
                    case 1:
                        return "Normal";
                    case 2:
                        return "High";
                    default:
                        return "Normal";
                }
            }
        }

        /// <summary>
        /// Gets the memory optimization description
        /// </summary>
        public string MemoryOptimizationDescription
        {
            get
            {
                switch (MemoryOptimizationLevel)
                {
                    case 0:
                        return "None";
                    case 1:
                        return "Normal";
                    case 2:
                        return "Aggressive";
                    default:
                        return "Normal";
                }
            }
        }
    }

    /// <summary>
    /// Model for logging settings
    /// </summary>
    public class LoggingSettings
    {
        /// <summary>
        /// Gets or sets a value indicating whether logging is enabled
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// Gets or sets the log level
        /// </summary>
        public LogLevel LogLevel { get; set; } = LogLevel.INFO;

        /// <summary>
        /// Gets or sets the maximum log file size in MB
        /// </summary>
        public int MaxLogFileSize { get; set; } = 10;

        /// <summary>
        /// Gets or sets the maximum number of log files
        /// </summary>
        public int MaxLogFiles { get; set; } = 5;

        /// <summary>
        /// Gets or sets the log file path
        /// </summary>
        public string LogFilePath { get; set; } = "Logs/CircleUtility.log";

        /// <summary>
        /// Gets or sets a value indicating whether to log to console
        /// </summary>
        public bool LogToConsole { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to log to file
        /// </summary>
        public bool LogToFile { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to include timestamps in logs
        /// </summary>
        public bool IncludeTimestamps { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to include log levels in logs
        /// </summary>
        public bool IncludeLogLevels { get; set; } = true;

        /// <summary>
        /// Gets or sets the log rotation interval in days
        /// </summary>
        public int LogRotationInterval { get; set; } = 7;

        /// <summary>
        /// Gets the log level description
        /// </summary>
        public string LogLevelDescription
        {
            get
            {
                return LogLevel.ToString();
            }
        }
    }

    /// <summary>
    /// Model for advanced settings
    /// </summary>
    public class AdvancedSettings
    {
        /// <summary>
        /// Gets or sets a value indicating whether debug mode is enabled
        /// </summary>
        public bool EnableDebugMode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether experimental features are enabled
        /// </summary>
        public bool EnableExperimentalFeatures { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether verbose logging is enabled
        /// </summary>
        public bool EnableVerboseLogging { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether crash reporting is enabled
        /// </summary>
        public bool EnableCrashReporting { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether telemetry is enabled
        /// </summary>
        public bool EnableTelemetry { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether auto backup is enabled
        /// </summary>
        public bool EnableAutoBackup { get; set; } = true;

        /// <summary>
        /// Gets or sets the backup interval in days
        /// </summary>
        public int BackupInterval { get; set; } = 7;

        /// <summary>
        /// Gets or sets the maximum number of backups
        /// </summary>
        public int MaxBackups { get; set; } = 10;

        /// <summary>
        /// Gets or sets the backup location
        /// </summary>
        public string BackupLocation { get; set; } = "Config/Backups";

        /// <summary>
        /// Gets or sets a value indicating whether to use custom DNS
        /// </summary>
        public bool UseCustomDns { get; set; }

        /// <summary>
        /// Gets or sets the primary DNS server
        /// </summary>
        public string PrimaryDns { get; set; } = "1.1.1.1";

        /// <summary>
        /// Gets or sets the secondary DNS server
        /// </summary>
        public string SecondaryDns { get; set; } = "1.0.0.1";

        /// <summary>
        /// Gets or sets a value indicating whether to use custom TCP settings
        /// </summary>
        public bool UseCustomTcpSettings { get; set; }

        /// <summary>
        /// Gets or sets the TCP receive window size
        /// </summary>
        public int TcpReceiveWindow { get; set; } = 65536;

        /// <summary>
        /// Gets or sets the TCP send window size
        /// </summary>
        public int TcpSendWindow { get; set; } = 65536;

        /// <summary>
        /// Gets or sets a value indicating whether to disable Nagle's algorithm
        /// </summary>
        public bool DisableNagleAlgorithm { get; set; } = true;
    }
}

