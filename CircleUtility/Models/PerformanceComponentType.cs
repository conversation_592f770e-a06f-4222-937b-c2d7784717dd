namespace CircleUtility.Models
{
    /// <summary>
    /// Represents the type of component for performance optimization
    /// </summary>
    public enum PerformanceComponentType
    {
        /// <summary>
        /// System-wide performance
        /// </summary>
        System,
        
        /// <summary>
        /// CPU performance
        /// </summary>
        CPU,
        
        /// <summary>
        /// GPU performance
        /// </summary>
        GPU,
        
        /// <summary>
        /// Memory performance
        /// </summary>
        Memory,
        
        /// <summary>
        /// Storage performance
        /// </summary>
        Storage,
        
        /// <summary>
        /// Network performance
        /// </summary>
        Network,
        
        /// <summary>
        /// Input device performance
        /// </summary>
        Input
    }
}
