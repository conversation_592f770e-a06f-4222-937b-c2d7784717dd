using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for handling two-factor authentication
    /// </summary>
    public class TwoFactorAuthService
    {
        private static TwoFactorAuthService _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;
        private readonly Dictionary<string, string> _pendingCodes;
        private readonly Random _random;

        /// <summary>
        /// Gets the singleton instance of the TwoFactorAuthService
        /// </summary>
        public static TwoFactorAuthService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TwoFactorAuthService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes a new instance of the TwoFactorAuthService class
        /// </summary>
        private TwoFactorAuthService()
        {
            _logger = LoggingService.Instance;
            _pendingCodes = new Dictionary<string, string>();
            _random = new Random();
        }

        /// <summary>
        /// Generates a verification code for the specified user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="twoFactorType">The two-factor authentication type</param>
        /// <returns>The verification code</returns>
        public string GenerateVerificationCode(string username, TwoFactorType twoFactorType)
        {
            try
            {
                // Generate a 6-digit code
                string code = _random.Next(100000, 999999).ToString();

                // Store the code (update if exists, add if new)
                _pendingCodes[username] = code;

                // Log the action
                _logger.Log($"Generated verification code for user: {username}", LogLevel.INFO);

                return code;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error generating verification code: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Sends a verification code to the user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="code">The verification code</param>
        /// <param name="twoFactorType">The two-factor authentication type</param>
        /// <returns>True if the code was sent successfully, false otherwise</returns>
        public async Task<bool> SendVerificationCodeAsync(string username, string code, TwoFactorType twoFactorType)
        {
            try
            {
                // Get the user
                var user = await GetUserAsync(username);
                if (user == null)
                {
                    _logger.Log($"Error sending verification code: User not found: {username}", LogLevel.ERROR);
                    return false;
                }

                // Cast user to UserDetails type
                if (user is DiscordUser userDetails)
                {
                    // Send the code based on the two-factor type
                    switch (twoFactorType)
                    {
                        case TwoFactorType.Email:
                            await SendEmailVerificationAsync(userDetails.Email, code);
                            _logger.Log($"Sent verification code to email: {userDetails.Email}", LogLevel.INFO);
                            break;

                        case TwoFactorType.SMS:
                            await SendSmsVerificationAsync(userDetails.PhoneNumber, code);
                            _logger.Log($"Sent verification code to phone: {userDetails.PhoneNumber}", LogLevel.INFO);
                            break;

                        case TwoFactorType.Authenticator:
                            _logger.Log($"Authenticator app should be used for verification", LogLevel.INFO);
                            break;

                        default:
                            _logger.Log($"Unsupported two-factor type: {twoFactorType}", LogLevel.ERROR);
                            return false;
                    }

                    return true;
                }
                else
                {
                    _logger.Log($"Error sending verification code: User object is not of expected type", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sending verification code: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets user details by username
        /// </summary>
        /// <param name="username">The username</param>
        /// <returns>The user details or null if not found</returns>
        private async Task<object> GetUserAsync(string username)
        {
            // Not implemented: actual user lookup logic
            _logger.LogWarning($"User lookup for '{username}' is not yet implemented. Returning null.");
            await Task.Delay(100); // Simulate async operation
            return null;
        }

        /// <summary>
        /// Sends verification code via email
        /// </summary>
        /// <param name="email">The email address</param>
        /// <param name="code">The verification code</param>
        private async Task SendEmailVerificationAsync(string email, string code)
        {
            // Not implemented: actual email sending logic
            _logger.LogWarning($"Email verification for '{email}' is not yet implemented. No email sent.");
            await Task.Delay(500); // Simulate email sending delay
        }

        /// <summary>
        /// Sends verification code via SMS
        /// </summary>
        /// <param name="phoneNumber">The phone number</param>
        /// <param name="code">The verification code</param>
        private async Task SendSmsVerificationAsync(string phoneNumber, string code)
        {
            // Not implemented: actual SMS sending logic
            _logger.LogWarning($"SMS verification for '{phoneNumber}' is not yet implemented. No SMS sent.");
            await Task.Delay(500); // Simulate SMS sending delay
        }

        /// <summary>
        /// Verifies a verification code
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="code">The verification code</param>
        /// <returns>True if the code is valid, false otherwise</returns>
        public bool VerifyCode(string username, string code)
        {
            try
            {
                // Check if the user has a pending code
                if (!_pendingCodes.TryGetValue(username, out string storedCode))
                {
                    _logger.Log($"Error verifying code: No pending code for user: {username}", LogLevel.ERROR);
                    return false;
                }

                // Verify the code
                bool isValid = storedCode == code;

                // Remove the code if it's valid
                if (isValid)
                {
                    _pendingCodes.Remove(username);
                    _logger.Log($"Verification code verified for user: {username}", LogLevel.INFO);
                }
                else
                {
                    _logger.Log($"Invalid verification code for user: {username}", LogLevel.WARNING);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error verifying code: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Generates recovery codes for the specified user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="count">The number of recovery codes to generate</param>
        /// <returns>The recovery codes</returns>
        public string[] GenerateRecoveryCodes(string username, int count = 10)
        {
            try
            {
                // Generate recovery codes
                string[] recoveryCodes = new string[count];

                using (var rng = RandomNumberGenerator.Create())
                {
                    for (int i = 0; i < count; i++)
                    {
                        // Generate a random 10-character code
                        byte[] buffer = new byte[8]; // Increased buffer size for better randomness
                        rng.GetBytes(buffer);
                        recoveryCodes[i] = Convert.ToBase64String(buffer).Replace("+", "").Replace("/", "").Replace("=", "").Substring(0, 10);
                    }
                }

                // Log the action
                _logger.Log($"Generated {count} recovery codes for user: {username}", LogLevel.INFO);

                return recoveryCodes;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error generating recovery codes: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }
    }
}