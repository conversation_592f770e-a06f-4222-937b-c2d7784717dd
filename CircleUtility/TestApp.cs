using System;
using CircleUtility.Models;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.ViewModels;
using CircleUtility.Services;

namespace CircleUtility
{
    public class TestApp
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("Starting test application");

                // Initialize services
                InitializeServices();

                // Create application
                Application app = new Application();

                // Create a simple window with buttons
                Console.WriteLine("Creating test window");
                Window testWindow = new Window
                {
                    Title = "Menu Test",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                // Create a stack panel for buttons
                StackPanel panel = new StackPanel();
                testWindow.Content = panel;

                // Add buttons for each view
                AddButton(panel, "Dashboard", () => CreateViewModel("Dashboard"));
                AddButton(panel, "System Tweaks", () => CreateViewModel("System Tweaks"));
                AddButton(panel, "Game Profiles", () => CreateViewModel("Game Profiles"));
                AddButton(panel, "Input Delay", () => CreateViewModel("Input Delay"));
                AddButton(panel, "Controller Tweaks", () => CreateViewModel("Controller Tweaks"));
                AddButton(panel, "Benchmark", () => CreateViewModel("Benchmark"));
                AddButton(panel, "Tools", () => CreateViewModel("Tools"));
                AddButton(panel, "Debloat", () => CreateViewModel("Debloat"));
                AddButton(panel, "Settings", () => CreateViewModel("Settings"));

                // Show the window
                Console.WriteLine("Showing test window");
                testWindow.Show();

                // Run the application
                Console.WriteLine("Running application");
                app.Run();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                MessageBox.Show($"Error: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private static void InitializeServices()
        {
            try
            {
                Console.WriteLine("Initializing services");

                // Initialize logging service
                var logger = LoggingService.Instance;
                logger.Log("Test application started", LogLevel.INFO);

                // Initialize user tracking service
                var userTracking = UserTrackingService.Instance;

                // Initialize Discord service
                var discord = DiscordService.Instance;

                logger.Log("Services initialized", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing services: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private static void AddButton(StackPanel panel, string text, Action action)
        {
            Button button = new Button
            {
                Content = text,
                Margin = new Thickness(10),
                Padding = new Thickness(5)
            };

            button.Click += (sender, e) =>
            {
                try
                {
                    Console.WriteLine($"Button clicked: {text}");
                    action();
                    Console.WriteLine($"Action completed for: {text}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in button click: {ex.Message}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                    MessageBox.Show($"Error: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            };

            panel.Children.Add(button);
        }

        private static void CreateViewModel(string viewName)
        {
            Console.WriteLine($"Creating view model: {viewName}");

            try
            {
                // Create service provider
                var serviceProvider = CreateServiceProvider();

                // Get required services
                var hardwareDetectionService = serviceProvider.GetRequiredService<IHardwareDetectionService>();
                var hardwareOptimizationService = serviceProvider.GetRequiredService<IHardwareOptimizationService>();
                var hardwareCompatibilityService = serviceProvider.GetRequiredService<IHardwareCompatibilityService>();
                var performanceMonitoringService = serviceProvider.GetRequiredService<IPerformanceMonitoringService>();
                var benchmarkingService = serviceProvider.GetRequiredService<IBenchmarkingService>();

                object viewModel = null;

                switch (viewName)
                {
                    case "Dashboard":
                        viewModel = new DashboardViewModel(
                            hardwareDetectionService,
                            performanceMonitoringService);
                        break;
                    case "System Tweaks":
                        viewModel = new SystemTweaksViewModel();
                        break;
                    case "Game Profiles":
                        viewModel = new GameProfilesViewModel();
                        break;
                    case "Input Delay":
                        viewModel = new InputDelayViewModel();
                        break;
                    case "Controller Tweaks":
                        viewModel = new ControllerTweaksViewModel();
                        break;
                    case "Benchmark":
                        viewModel = new BenchmarkViewModel();
                        break;
                    case "Tools":
                        viewModel = new ToolsViewModel();
                        break;
                    case "Debloat":
                        viewModel = new DebloatViewModel();
                        break;
                    case "Settings":
                        viewModel = new SettingsViewModel();
                        break;
                }

                Console.WriteLine($"Created view model: {viewModel?.GetType().Name}");
                MessageBox.Show($"Successfully created {viewName} view model", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating view model: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error creating view model: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private static ServiceProvider CreateServiceProvider()
        {
            Console.WriteLine("Creating service provider");

            try
            {
                // Create a service collection
                var services = new ServiceCollection();

                // Register all services using standardized extension method
                Console.WriteLine("Registering all CircleUtility services");
                services.AddCircleUtilityServices(
                    includeUIServices: false,  // No UI services for console test app
                    includeSecurityServices: false);  // No security services for test

                // Build the service provider
                Console.WriteLine("Building service provider");
                var serviceProvider = services.BuildServiceProvider();

                // Initialize all services
                Console.WriteLine("Initializing all services");
                serviceProvider.InitializeAllServices();

                Console.WriteLine("Service provider created");

                return serviceProvider;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating service provider: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}


