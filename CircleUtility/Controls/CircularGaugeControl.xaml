<UserControl x:Class="CircleUtility.Controls.CircularGaugeControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="200">
    
    <UserControl.Resources>
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="GlowEllipse">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="0.6"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0.3"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="0.6"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
        
        <Storyboard x:Key="ValueChangeAnimation" Duration="0:0:0.3">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)" 
                           Duration="0:0:0.3" 
                           DecelerationRatio="0.7"
                           AccelerationRatio="0.3"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Grid>
        <Viewbox>
            <Grid Width="200" Height="200">
                <!-- Background Circle -->
                <Ellipse x:Name="BackgroundCircle" Width="180" Height="180" Fill="#1E1E1E" Stroke="#333333" StrokeThickness="1"/>
                
                <!-- Glow Effect -->
                <Ellipse x:Name="GlowEllipse" Width="200" Height="200" Opacity="0.6">
                    <Ellipse.Fill>
                        <RadialGradientBrush>
                            <GradientStop Color="#00000000" Offset="0.7"/>
                            <GradientStop Color="#4000C8FF" Offset="1"/>
                        </RadialGradientBrush>
                    </Ellipse.Fill>
                </Ellipse>
                
                <!-- Gauge Arc -->
                <Path x:Name="GaugeArc" Stroke="#00C8FF" StrokeThickness="12" StrokeStartLineCap="Round" StrokeEndLineCap="Round"
                      StrokeDashCap="Round" StrokeLineJoin="Round" Stretch="None" 
                      RenderTransformOrigin="0.5,0.5">
                    <Path.RenderTransform>
                        <RotateTransform Angle="-90"/>
                    </Path.RenderTransform>
                </Path>
                
                <!-- Center Circle -->
                <Ellipse Width="120" Height="120" Fill="#252525" Stroke="#333333" StrokeThickness="1"/>
                
                <!-- Value Display -->
                <Viewbox>
                    <StackPanel>
                        <TextBlock x:Name="ValueText" Text="0%" Foreground="White" FontWeight="Bold" 
                                 TextAlignment="Center" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock x:Name="TitleText" Text="Gauge" Foreground="#AAAAAA" FontSize="12" 
                                 TextAlignment="Center" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Viewbox>
            </Grid>
        </Viewbox>
    </Grid>
</UserControl>
