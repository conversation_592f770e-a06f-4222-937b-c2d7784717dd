<UserControl x:Class="CircleUtility.Controls.RevertConfirmationDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="600">
    
    <UserControl.Resources>
        <Style x:Key="DialogTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
        
        <Style x:Key="DialogMessageStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="ChangeItemStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF050A0F"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="CornerRadius" Value="3"/>
        </Style>
        
        <Style x:Key="ChangeNameStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
        
        <Style x:Key="ChangeDescriptionStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#FFC0C0C0"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        
        <Style x:Key="DialogButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5,0"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF002A57" TargetName="border"/>
                                <Setter Property="BorderBrush" Value="#FF00E1FF" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF003C7A" TargetName="border"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource DialogButtonStyle}">
            <Setter Property="Background" Value="#FF1A1A1A"/>
            <Setter Property="BorderBrush" Value="#FF808080"/>
        </Style>
    </UserControl.Resources>
    
    <Border Background="#FF0A141E" 
            BorderBrush="#FF00C8FF" 
            BorderThickness="1" 
            Padding="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Dialog Title -->
            <TextBlock Grid.Row="0" 
                       x:Name="DialogTitle" 
                       Text="Confirm Revert" 
                       Style="{StaticResource DialogTitleStyle}"/>
            
            <!-- Dialog Message -->
            <TextBlock Grid.Row="1" 
                       x:Name="DialogMessage" 
                       Text="Are you sure you want to revert the following changes? This action will restore your system to its previous state." 
                       Style="{StaticResource DialogMessageStyle}"/>
            
            <!-- Changes List -->
            <ScrollViewer Grid.Row="2" 
                          VerticalScrollBarVisibility="Auto" 
                          MaxHeight="250" 
                          Margin="0,0,0,20">
                <StackPanel x:Name="ChangesPanel">
                    <!-- Changes will be added here dynamically -->
                </StackPanel>
            </ScrollViewer>
            
            <!-- Buttons -->
            <StackPanel Grid.Row="3" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right">
                <Button x:Name="RevertButton" 
                        Content="REVERT" 
                        Style="{StaticResource DialogButtonStyle}" 
                        Click="RevertButton_Click"/>
                <Button x:Name="CancelButton" 
                        Content="CANCEL" 
                        Style="{StaticResource CancelButtonStyle}" 
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
