using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;
using System.ComponentModel;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for SystemHealthIndicator.xaml
    /// </summary>
    public partial class SystemHealthIndicator : UserControl
    {
        private readonly Storyboard _pulseAnimation;
        private readonly Storyboard _scoreChangeAnimation;

        // Dependency Properties
        public static readonly DependencyProperty ScoreProperty =
            DependencyProperty.Register(nameof(Score), typeof(int), typeof(SystemHealthIndicator),
                new PropertyMetadata(0, OnScoreChanged));

        public static readonly DependencyProperty IsAnimatingProperty =
            DependencyProperty.Register(nameof(IsAnimating), typeof(bool), typeof(SystemHealthIndicator),
                new PropertyMetadata(true, OnIsAnimatingChanged));

        /// <summary>
        /// Initializes a new instance of the SystemHealthIndicator class
        /// </summary>
        public SystemHealthIndicator()
        {
            InitializeComponent();

            // Get animations from resources
            _pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            _scoreChangeAnimation = (Storyboard)FindResource("ScoreChangeAnimation");

            // Start pulse animation
            _pulseAnimation.Begin(this, true);
        }

        /// <summary>
        /// Gets or sets the health score (0-100)
        /// </summary>
        public int Score
        {
            get => (int)GetValue(ScoreProperty);
            set => SetValue(ScoreProperty, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the indicator is animating
        /// </summary>
        public bool IsAnimating
        {
            get => (bool)GetValue(IsAnimatingProperty);
            set => SetValue(IsAnimatingProperty, value);
        }

        /// <summary>
        /// Called when the Score property changes
        /// </summary>
        private static void OnScoreChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SystemHealthIndicator indicator)
            {
                int oldScore = (int)e.OldValue;
                int newScore = Math.Max(0, Math.Min(100, (int)e.NewValue));

                // Update the UI if the score actually changed
                if (oldScore != newScore)
                {
                    indicator.UpdateUI(oldScore);
                }
            }
        }

        /// <summary>
        /// Called when the IsAnimating property changes
        /// </summary>
        private static void OnIsAnimatingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SystemHealthIndicator indicator)
            {
                bool isAnimating = (bool)e.NewValue;

                if (isAnimating)
                {
                    indicator._pulseAnimation.Begin(indicator, true);
                }
                else
                {
                    indicator._pulseAnimation.Stop(indicator);
                }
            }
        }

        /// <summary>
        /// Updates the UI based on the current score
        /// </summary>
        private void UpdateUI(int oldScore)
        {
            // Update score text
            ScoreTextBlock.Text = Score.ToString();

            // Update progress arc
            UpdateProgressArc();

            // Update colors based on score
            UpdateColors();

            // Update status text
            UpdateStatusText();

            // Play score change animation if not in design mode
            if (!DesignerProperties.GetIsInDesignMode(this) && oldScore != Score)
            {
                _scoreChangeAnimation.Begin(this);
            }
        }

        /// <summary>
        /// Updates the progress arc based on the current score
        /// </summary>
        private void UpdateProgressArc()
        {
            // Calculate the angle based on the score (0-360 degrees)
            double angle = (Score / 100.0) * 360.0;

            // Create the arc geometry
            double radius = 55; // Slightly smaller than the outer circle
            Point center = new Point(60, 60);

            // Convert angle to radians
            double startAngle = -90 * (Math.PI / 180); // Start at the top (12 o'clock position)
            double endAngle = (angle - 90) * (Math.PI / 180);

            // Calculate start and end points
            Point startPoint = new Point(
                center.X + (radius * Math.Cos(startAngle)),
                center.Y + (radius * Math.Sin(startAngle)));

            Point endPoint = new Point(
                center.X + (radius * Math.Cos(endAngle)),
                center.Y + (radius * Math.Sin(endAngle)));

            // Create the arc path
            bool isLargeArc = angle > 180;

            var arcSegment = new ArcSegment(
                endPoint,
                new Size(radius, radius),
                0,
                isLargeArc,
                SweepDirection.Clockwise,
                true);

            var pathFigure = new PathFigure(startPoint, new[] { arcSegment }, false);
            var pathGeometry = new PathGeometry(new[] { pathFigure });

            // Set the path data
            ProgressArc.Data = pathGeometry;
        }

        /// <summary>
        /// Updates the colors based on the current score
        /// </summary>
        private void UpdateColors()
        {
            Color color;

            if (Score >= 80)
            {
                // Green for good health
                color = Color.FromRgb(0, 200, 83); // #00C853
            }
            else if (Score >= 60)
            {
                // Blue for moderate health
                color = Color.FromRgb(0, 200, 255); // #00C8FF
            }
            else if (Score >= 40)
            {
                // Yellow for caution
                color = Color.FromRgb(255, 193, 7); // #FFC107
            }
            else if (Score >= 20)
            {
                // Orange for warning
                color = Color.FromRgb(255, 152, 0); // #FF9800
            }
            else
            {
                // Red for critical
                color = Color.FromRgb(255, 0, 0); // #FF0000
            }

            // Update colors
            var brush = new SolidColorBrush(color);
            OuterCircle.Stroke = brush;
            GlowCircle.Stroke = brush;
            ProgressArc.Stroke = brush;
            ScoreTextBlock.Foreground = brush;
            GlowEffect.Color = color;
        }

        /// <summary>
        /// Updates the status text based on the current score
        /// </summary>
        private void UpdateStatusText()
        {
            if (Score >= 80)
            {
                StatusTextBlock.Text = "EXCELLENT";
                StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(0, 200, 83)); // Green
            }
            else if (Score >= 60)
            {
                StatusTextBlock.Text = "GOOD";
                StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(0, 200, 255)); // Blue
            }
            else if (Score >= 40)
            {
                StatusTextBlock.Text = "FAIR";
                StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(255, 193, 7)); // Yellow
            }
            else if (Score >= 20)
            {
                StatusTextBlock.Text = "POOR";
                StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
            }
            else
            {
                StatusTextBlock.Text = "CRITICAL";
                StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(255, 0, 0)); // Red
            }
        }

        /// <summary>
        /// Handles the control being loaded
        /// </summary>
        protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
        {
            base.OnRenderSizeChanged(sizeInfo);
            UpdateProgressArc();
        }
    }
}
