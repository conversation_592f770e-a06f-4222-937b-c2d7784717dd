using CircleUtility.Commands;
using System;
using CircleUtility.Models;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Helpers;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the tools view
    /// </summary>
    public class ToolsViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTracking;
        private string _selectedTool;

        /// <summary>
        /// Initializes a new instance of the ToolsViewModel class
        /// </summary>
        public ToolsViewModel()
        {
            _logger = LoggingService.Instance;
            _userTracking = UserTrackingService.Instance;
            
            // Initialize commands
            RunToolCommand = new RelayCommand(RunTool);
            
            // Initialize tools
            InitializeTools();
            
            // Set default selected tool
            SelectedTool = Tools[0];
            
            _logger.Log("ToolsViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of tools
        /// </summary>
        public ObservableCollection<string> Tools { get; } = new ObservableCollection<string>();

        /// <summary>
        /// Gets or sets the selected tool
        /// </summary>
        public string SelectedTool
        {
            get => _selectedTool;
            set => SetProperty(ref _selectedTool, value);
        }

        /// <summary>
        /// Gets the run tool command
        /// </summary>
        public ICommand RunToolCommand { get; }

        /// <summary>
        /// Initializes the tools
        /// </summary>
        private void InitializeTools()
        {
            Tools.Add("Disk Cleanup");
            Tools.Add("Registry Cleaner");
            Tools.Add("Driver Updater");
            Tools.Add("Network Optimizer");
            Tools.Add("Startup Manager");
            Tools.Add("Process Priority Manager");
            Tools.Add("Memory Optimizer");
            Tools.Add("Temp File Cleaner");
            Tools.Add("System File Checker");
            Tools.Add("DirectX Diagnostic");
            Tools.Add("GPU Driver Cleaner");
            Tools.Add("Windows Update Reset");
        }

        /// <summary>
        /// Runs the selected tool
        /// </summary>
        private void RunTool()
        {
            if (string.IsNullOrEmpty(SelectedTool))
                return;

            _logger.Log($"Running tool: {SelectedTool}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Tool", $"Ran {SelectedTool}");
            
            // Run the selected tool
            switch (SelectedTool)
            {
                case "Disk Cleanup":
                    RunDiskCleanup();
                    break;
                case "Registry Cleaner":
                    RunRegistryCleaner();
                    break;
                case "Driver Updater":
                    RunDriverUpdater();
                    break;
                case "Network Optimizer":
                    RunNetworkOptimizer();
                    break;
                case "Startup Manager":
                    RunStartupManager();
                    break;
                case "Process Priority Manager":
                    RunProcessPriorityManager();
                    break;
                case "Memory Optimizer":
                    RunMemoryOptimizer();
                    break;
                case "Temp File Cleaner":
                    RunTempFileCleaner();
                    break;
                case "System File Checker":
                    RunSystemFileChecker();
                    break;
                case "DirectX Diagnostic":
                    RunDirectXDiagnostic();
                    break;
                case "GPU Driver Cleaner":
                    RunGpuDriverCleaner();
                    break;
                case "Windows Update Reset":
                    RunWindowsUpdateReset();
                    break;
                default:
                    _logger.Log($"Unknown tool: {SelectedTool}", LogLevel.WARNING);
                    break;
            }
        }

        // Tool implementation methods (these would be implemented in a full version)
        private void RunDiskCleanup() { }
        private void RunRegistryCleaner() { }
        private void RunDriverUpdater() { }
        private void RunNetworkOptimizer() { }
        private void RunStartupManager() { }
        private void RunProcessPriorityManager() { }
        private void RunMemoryOptimizer() { }
        private void RunTempFileCleaner() { }
        private void RunSystemFileChecker() { }
        private void RunDirectXDiagnostic() { }
        private void RunGpuDriverCleaner() { }
        private void RunWindowsUpdateReset() { }
    }
}


