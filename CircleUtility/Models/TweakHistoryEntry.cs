using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a tweak history entry
    /// </summary>
    public class TweakHistoryEntry
    {
        /// <summary>
        /// Gets or sets the type of optimization
        /// </summary>
        public OptimizationType Type { get; set; }

        /// <summary>
        /// Gets or sets the name of the tweak
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the date the tweak was applied
        /// </summary>
        public DateTime AppliedDate { get; set; }

        /// <summary>
        /// Gets or sets the date the tweak was reverted (if applicable)
        /// </summary>
        public DateTime? RevertedDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the tweak is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets additional data for the tweak (if applicable)
        /// </summary>
        public string AdditionalData { get; set; }
    }

    /// <summary>
    /// Represents the type of optimization
    /// </summary>
    public enum OptimizationType
    {
        /// <summary>
        /// Hardware optimization
        /// </summary>
        Optimization,

        /// <summary>
        /// Power profile
        /// </summary>
        PowerProfile,

        /// <summary>
        /// System tweak
        /// </summary>
        SystemTweak,

        /// <summary>
        /// Network tweak
        /// </summary>
        NetworkTweak,

        /// <summary>
        /// Registry tweak
        /// </summary>
        RegistryTweak,

        /// <summary>
        /// File system tweak
        /// </summary>
        FileSystemTweak,

        /// <summary>
        /// Service configuration
        /// </summary>
        ServiceConfig,

        /// <summary>
        /// Other tweak
        /// </summary>
        Other
    }
}
