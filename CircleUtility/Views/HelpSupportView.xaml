<UserControl x:Class="CircleUtility.Views.HelpSupportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="400" d:DesignWidth="600">
    <Grid Background="#0A141E">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Help &amp; Support"
                   FontFamily="Consolas"
                   FontSize="22"
                   FontWeight="Bold"
                   Foreground="#00C8FF"
                   Margin="0,0,0,20"/>
        <StackPanel Grid.Row="1" Orientation="Vertical">
            <TextBlock Text="We value your feedback and are here to help! Please describe your issue or suggestion below."
                       Foreground="White"
                       FontSize="14"
                       Margin="0,0,0,8"/>
            <TextBox x:Name="FeedbackTextBox"
                     MinHeight="100"
                     MaxHeight="200"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     Background="#16202A"
                     Foreground="White"
                     BorderBrush="#00C8FF"
                     BorderThickness="1"
                     Padding="8"
                     FontSize="14"/>
            <CheckBox x:Name="AnonymousCheckBox"
                      Content="Send anonymously"
                      Foreground="White"
                      FontSize="13"
                      Margin="0,8,0,0"/>
        </StackPanel>
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Send Feedback"
                    x:Name="SendFeedbackButton"
                    Width="140"
                    Height="38"
                    Background="#00C8FF"
                    Foreground="Black"
                    FontWeight="Bold"
                    FontSize="15"
                    BorderThickness="0"
                    Cursor="Hand"
                    Margin="0,0,0,0"/>
        </StackPanel>
    </Grid>
</UserControl> 