using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for CircularGaugeControl.xaml
    /// </summary>
    public partial class CircularGaugeControl : UserControl
    {
        private Storyboard _valueChangeAnimation;
        private Storyboard _pulseAnimation;
        private double _value;
        private double _minValue = 0;
        private double _maxValue = 100;
        private string _title = "Gauge";
        private string _format = "{0}%";
        private bool _isAnimating = true;
        private Brush _gaugeColor = new SolidColorBrush(Color.FromRgb(0, 200, 255)); // #00C8FF

        public CircularGaugeControl()
        {
            InitializeComponent();

            _valueChangeAnimation = (Storyboard)TryFindResource("ValueChangeAnimation");
            _pulseAnimation = (Storyboard)TryFindResource("PulseAnimation");

            if (_isAnimating && _pulseAnimation != null)
                _pulseAnimation.Begin(this, true);

            Value = 0;
            UpdateUI();
        }

        public double Value
        {
            get => _value;
            set
            {
                double newValue = Math.Max(_minValue, Math.Min(_maxValue, value));
                if (Math.Abs(_value - newValue) > 0.01)
                {
                    _value = newValue;
                    UpdateUI();
                }
            }
        }

        public double MinValue
        {
            get => _minValue;
            set
            {
                _minValue = value;
                Value = _value; // Re-clamp current value
            }
        }

        public double MaxValue
        {
            get => _maxValue;
            set
            {
                _maxValue = value;
                Value = _value; // Re-clamp current value
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                if (TitleText != null)
                    TitleText.Text = _title;
            }
        }

        public string Format
        {
            get => _format;
            set
            {
                _format = value;
                UpdateValueText();
            }
        }

        public bool IsAnimating
        {
            get => _isAnimating;
            set
            {
                _isAnimating = value;
                if (_pulseAnimation == null) return;

                if (_isAnimating)
                    _pulseAnimation.Begin(this, true);
                else
                    _pulseAnimation.Stop(this);
            }
        }

        public Brush GaugeColor
        {
            get => _gaugeColor;
            set
            {
                _gaugeColor = value;
                GaugeArc.Stroke = value;
                BackgroundCircle.Stroke = value;
                TitleText.Foreground = value;

                if (value is SolidColorBrush solidColorBrush && GlowEllipse.Fill is RadialGradientBrush radialGradientBrush)
                {
                    if (radialGradientBrush.GradientStops.Count > 1)
                    {
                        // Assuming the second gradient stop is the one to change for the glow effect
                        radialGradientBrush.GradientStops[1] = new GradientStop(Color.FromArgb(0x40, solidColorBrush.Color.R, solidColorBrush.Color.G, solidColorBrush.Color.B), 1);
                    }
                }
            }
        }

        private void UpdateUI()
        {
            double normalized = (_maxValue - _minValue) > 0 ? (_value - _minValue) / (_maxValue - _minValue) : 0;
            double valueAngle = -225 + (normalized * 270);

            UpdateValueText();

            if (_valueChangeAnimation != null)
            {
                var anim = _valueChangeAnimation.Children[0] as DoubleAnimation;
                if (anim != null)
                    anim.To = valueAngle;

                _valueChangeAnimation.Begin(this, true);
            }
            else
            {
                // GaugeArc.EndAngle = valueAngle; // Path does not have EndAngle, animation handles update
            }

            UpdateColors();
        }

        private void UpdateValueText()
        {
            if (ValueText != null)
                ValueText.Text = string.Format(_format, Math.Round(_value));
        }

        private void UpdateColors()
        {
            double normalized = (_maxValue - _minValue) > 0 ? (_value - _minValue) / (_maxValue - _minValue) : 0;

            Color color = normalized switch
            {
                <= 0.4 => Color.FromRgb(0, 200, 83),     // Green
                <= 0.7 => Color.FromRgb(0, 200, 255),    // Blue
                <= 0.85 => Color.FromRgb(255, 193, 7),   // Yellow
                _ => Color.FromRgb(255, 0, 0),           // Red
            };

            GaugeColor = new SolidColorBrush(color);
        }
    }
}
