using System;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Base interface for all dialog windows
    /// </summary>
    public interface IDialog : IUIComponent
    {
        /// <summary>
        /// Shows the dialog and waits for user interaction
        /// </summary>
        /// <returns>The dialog result</returns>
        bool? ShowDialog();

        /// <summary>
        /// Closes the dialog
        /// </summary>
        void Close();

        /// <summary>
        /// Gets or sets the dialog result
        /// </summary>
        bool? DialogResult { get; set; }
    }
}
