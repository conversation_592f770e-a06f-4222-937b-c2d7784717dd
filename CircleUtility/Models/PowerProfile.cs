// Created by Arsenal on 5-17-25 12:15PM
using System.Collections.Generic;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a power profile
    /// </summary>
    public class PowerProfile
    {
        /// <summary>
        /// Gets or sets the name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the power mode
        /// </summary>
        public PowerMode PowerMode { get; set; }

        /// <summary>
        /// Gets or sets the thermal impact (0-100)
        /// </summary>
        public int ThermalImpact { get; set; }

        /// <summary>
        /// Gets or sets the performance impact (0-100)
        /// </summary>
        public int PerformanceImpact { get; set; }

        /// <summary>
        /// Gets or sets the battery impact (0-100)
        /// </summary>
        public int BatteryImpact { get; set; }

        /// <summary>
        /// Gets or sets the fan curve points
        /// </summary>
        public List<FanCurvePoint> FanCurvePoints { get; set; }

        /// <summary>
        /// Gets or sets the power limits
        /// </summary>
        public PowerLimits PowerLimits { get; set; }
    }

    /// <summary>
    /// Represents power limits
    /// </summary>
    public class PowerLimits
    {
        /// <summary>
        /// Gets or sets the CPU power limit (watts)
        /// </summary>
        public int CpuPowerLimit { get; set; }

        /// <summary>
        /// Gets or sets the GPU power limit (watts)
        /// </summary>
        public int GpuPowerLimit { get; set; }
    }

    /// <summary>
    /// Represents a power mode
    /// </summary>
    public enum PowerMode
    {
        /// <summary>
        /// Balanced mode
        /// </summary>
        Balanced,

        /// <summary>
        /// Performance mode
        /// </summary>
        Performance,

        /// <summary>
        /// Power saver mode
        /// </summary>
        PowerSaver,

        /// <summary>
        /// Ultra performance mode
        /// </summary>
        UltraPerformance,

        /// <summary>
        /// Custom mode
        /// </summary>
        Custom
    }
}
