<UserControl x:Class="CircleUtility.Controls.CompatibilityStatusControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="24" d:DesignWidth="24">
    
    <UserControl.Resources>
        <Style x:Key="CompatibilityIconStyle" TargetType="Path">
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>
    
    <Grid x:Name="MainGrid" ToolTip="Compatibility status unknown">
        <Ellipse x:Name="StatusBackground" 
                 Width="20" 
                 Height="20" 
                 Fill="#555555">
            <Ellipse.Effect>
                <DropShadowEffect Color="#555555" 
                                  BlurRadius="8" 
                                  ShadowDepth="0" 
                                  Opacity="0.5"/>
            </Ellipse.Effect>
        </Ellipse>
        
        <!-- Compatible Icon (Checkmark) -->
        <Path x:Name="CompatibleIcon" 
              Style="{StaticResource CompatibilityIconStyle}"
              Data="M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"
              Fill="White"
              Visibility="Collapsed"/>
        
        <!-- Incompatible Icon (X) -->
        <Path x:Name="IncompatibleIcon" 
              Style="{StaticResource CompatibilityIconStyle}"
              Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41z"
              Fill="White"
              Visibility="Collapsed"/>
        
        <!-- Warning Icon (!) -->
        <Path x:Name="WarningIcon" 
              Style="{StaticResource CompatibilityIconStyle}"
              Data="M12,2L1,21h22L12,2z M12,18c-0.55,0-1-0.45-1-1s0.45-1,1-1s1,0.45,1,1S12.55,18,12,18z M13,14h-2v-6h2V14z"
              Fill="White"
              Visibility="Collapsed"/>
        
        <!-- Unknown Icon (?) -->
        <Path x:Name="UnknownIcon" 
              Style="{StaticResource CompatibilityIconStyle}"
              Data="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,19h-2v-2h2V19z M15.07,11.25l-0.9,0.92C13.45,12.9,13,13.5,13,15h-2v-0.5c0-1.1,0.45-2.1,1.17-2.83l1.24-1.26c0.37-0.36,0.59-0.86,0.59-1.41c0-1.1-0.9-2-2-2s-2,0.9-2,2H8c0-2.21,1.79-4,4-4s4,1.79,4,4C16,9.67,15.65,10.6,15.07,11.25z"
              Fill="White"
              Visibility="Visible"/>
    </Grid>
</UserControl>
