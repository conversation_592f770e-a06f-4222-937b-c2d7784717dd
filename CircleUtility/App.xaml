<Application x:Class="CircleUtility.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:helpers="clr-namespace:CircleUtility.Helpers"
             xmlns:converters="clr-namespace:CircleUtility.Converters"
             xmlns:views="clr-namespace:CircleUtility.Views"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- Centralized Resource Dictionary -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/ResourceDictionary.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
