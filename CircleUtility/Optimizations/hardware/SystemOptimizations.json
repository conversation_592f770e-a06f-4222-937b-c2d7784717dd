[{"Name": "Timer Resolution Optimization", "Description": "Sets the Windows timer resolution to 0.5ms for reduced input latency and smoother gameplay.", "Category": "System Latency Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 2, "Command": "bcdedit.exe", "Arguments": "/set useplatformtick yes", "PerformanceImpact": 60, "StabilityImpact": 95, "PowerConsumptionImpact": 70, "ThermalImpact": 50, "InputLatencyImpact": 90, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Windows uses a system timer for various operations, including input processing. By default, this timer has a resolution of 15.6ms, which can introduce input latency. This optimization reduces the timer resolution to 0.5ms, which can significantly reduce input latency in games.", "RevertCommand": "bcdedit.exe", "RevertArguments": "/deletevalue useplatformtick"}, {"Name": "Network Throttling Index Optimization", "Description": "Disables Windows network throttling to improve network responsiveness in online games.", "Category": "Network Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile", "RegistryValueName": "NetworkThrottlingIndex", "RegistryValue": 4294967295, "RegistryValueKind": "DWord", "PerformanceImpact": 55, "StabilityImpact": 95, "PowerConsumptionImpact": 55, "ThermalImpact": 50, "InputLatencyImpact": 70, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Windows limits the amount of network traffic that can be processed by the system to prevent network activity from impacting system responsiveness. While this is beneficial for general use, it can limit network performance in games. This optimization disables this throttling to improve network responsiveness in online games.", "RevertRegistryValue": 10}, {"Name": "HPET Optimization", "Description": "Disables the High Precision Event Timer (HPET) for reduced input latency in games.", "Category": "System Latency Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 2, "Command": "bcdedit.exe", "Arguments": "/set useplatformclock false", "PerformanceImpact": 65, "StabilityImpact": 90, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 85, "Risk": 1, "WarningMessage": "Disabling HPET can cause stability issues in some systems. If you experience crashes or other issues after applying this optimization, revert it immediately.", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "HPET is a hardware timer used by Windows for various timing operations. While it's more accurate than other timers, it can introduce latency in some systems. This optimization disables HPET, which can reduce input latency in games. However, it can cause stability issues in some systems, so it's disabled by default.", "RevertCommand": "bcdedit.exe", "RevertArguments": "/set useplatformclock true"}, {"Name": "Game Mode Optimization", "Description": "Optimizes Windows Game Mode settings for better gaming performance and reduced background interference.", "Category": "System Performance Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_CURRENT_USER\\Software\\Microsoft\\GameBar", "RegistryValueName": "AllowAutoGameMode", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 60, "StabilityImpact": 100, "PowerConsumptionImpact": 55, "ThermalImpact": 50, "InputLatencyImpact": 65, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": false, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Windows Game Mode prioritizes system resources for games, reduces background activity, and prevents Windows Update from performing driver installations and rebooting your PC while you're playing a game. This optimization ensures Game Mode is enabled and properly configured for optimal gaming performance.", "RevertRegistryValue": 0}, {"Name": "Memory Management Optimization", "Description": "Optimizes Windows memory management settings for gaming by prioritizing foreground applications and reducing background memory usage.", "Category": "System Performance Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "RegistryValueName": "LargeSystemCache", "RegistryValue": 0, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 95, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 70, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization configures Windows memory management to prioritize foreground applications (like games) over background processes. It disables the large system cache, which is beneficial for servers but can reduce performance in gaming scenarios.", "RevertRegistryValue": 1}, {"Name": "Process Scheduling Optimization", "Description": "Optimizes Windows process scheduling for gaming by increasing the priority of foreground applications.", "Category": "System Performance Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile", "RegistryValueName": "SystemResponsiveness", "RegistryValue": 0, "RegistryValueKind": "DWord", "PerformanceImpact": 70, "StabilityImpact": 90, "PowerConsumptionImpact": 55, "ThermalImpact": 50, "InputLatencyImpact": 75, "Risk": 1, "WarningMessage": "This optimization prioritizes foreground applications (like games) over background processes. While this improves gaming performance, it may cause background applications to be less responsive.", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Windows normally reserves 20% of CPU resources for background processes. This optimization reduces that to 0%, allowing games to use the full CPU resources. This can significantly improve performance in CPU-limited games, but may cause background applications to be less responsive.", "RevertRegistryValue": 20}, {"Name": "Full-Screen Optimization", "Description": "Optimizes Windows full-screen optimizations for better performance and reduced input latency in games.", "Category": "System Performance Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_CURRENT_USER\\System\\GameConfigStore", "RegistryValueName": "GameDVR_DXGIHonorFSEWindowsCompatible", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 60, "StabilityImpact": 95, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 80, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": false, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Windows 10 and 11 include a feature called 'Full-screen Optimizations' that runs exclusive full-screen games in a borderless windowed mode with optimizations. While this improves compatibility and allows for faster Alt+Tab switching, it can introduce input latency in some games. This optimization configures these settings for optimal gaming performance.", "RevertRegistryValue": 0}, {"Name": "Input Delay Reduction", "Description": "Reduces input delay by optimizing mouse and keyboard input processing in Windows.", "Category": "Input Latency Optimization", "HardwareType": 6, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 4, "RegistryKey": "HKEY_CURRENT_USER\\Control Panel\\Mouse", "RegistryValueName": "MouseSensitivity", "RegistryValue": "10", "RegistryValueKind": "String", "Command": "reg.exe", "Arguments": "add \"HKEY_CURRENT_USER\\Control Panel\\Accessibility\\MouseKeys\" /v \"Flags\" /t REG_SZ /d \"0\" /f", "PerformanceImpact": 50, "StabilityImpact": 100, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 85, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": false, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization configures various Windows settings to reduce input latency for mouse and keyboard. It disables mouse acceleration, optimizes mouse sensitivity, and disables various accessibility features that can introduce input latency.", "RevertRegistryValue": "10", "RevertCommand": "reg.exe", "RevertArguments": "add \"HKEY_CURRENT_USER\\Control Panel\\Accessibility\\MouseKeys\" /v \"Flags\" /t REG_SZ /d \"62\" /f"}]