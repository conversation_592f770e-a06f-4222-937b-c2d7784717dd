using CircleUtility.Models;
using System;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Extension methods for standardized service registration
    /// </summary>
    public static class ServiceRegistrationExtensions
    {
        /// <summary>
        /// Registers all core services with standardized patterns
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="includeUIServices">Whether to include UI-specific services</param>
        /// <param name="includeSecurityServices">Whether to include security services</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddCircleUtilityServices(
            this IServiceCollection services, 
            bool includeUIServices = true, 
            bool includeSecurityServices = true)
        {
            // Core services (always included)
            services.AddCoreServices();
            
            // Hardware services (always included)
            services.AddHardwareServices();
            
            // UI services (optional for test/console apps)
            if (includeUIServices)
            {
                services.AddUIServices();
            }
            
            // Security services (optional for test apps)
            if (includeSecurityServices)
            {
                services.AddSecurityServices();
            }
            
            // Other services (always included)
            services.AddOtherServices();
            
            return services;
        }

        /// <summary>
        /// Registers core services
        /// </summary>
        private static IServiceCollection AddCoreServices(this IServiceCollection services)
        {
            // Logging service - register both concrete and interface
            services.AddSingleton<LoggingService>(LoggingService.Instance);
            services.AddSingleton<ILogger>(provider => provider.GetRequiredService<LoggingService>());
            
            // Configuration and management services
            services.AddSingleton<ConfigurationManager>(ConfigurationManager.Instance);
            services.AddSingleton<ServiceManager>(ServiceManager.Instance);
            
            return services;
        }

        /// <summary>
        /// Registers hardware-related services
        /// </summary>
        private static IServiceCollection AddHardwareServices(this IServiceCollection services)
        {
            // Hardware detection and optimization
            services.AddSingleton<IHardwareDetectionService, HardwareDetectionService>(
                provider => HardwareDetectionService.Instance);
            
            services.AddSingleton<IHardwareOptimizationService, HardwareOptimizationService>(
                provider => HardwareOptimizationService.Instance);
            services.AddSingleton<IHardwareCompatibilityService, HardwareCompatibilityService>(
                provider => HardwareCompatibilityService.Instance);
            
            // Performance and benchmarking
            services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>(
                provider => PerformanceMonitoringService.Instance);
            services.AddSingleton<IBenchmarkingService, BenchmarkingService>(
                provider => BenchmarkingService.Instance);
            
            // Hardware recommendation service (now uses proper constructor injection)
            services.AddSingleton<IHardwareRecommendationService, HardwareRecommendationService>(
                provider => HardwareRecommendationService.Instance);
            
            return services;
        }

        /// <summary>
        /// Registers UI-specific services
        /// </summary>
        private static IServiceCollection AddUIServices(this IServiceCollection services)
        {
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<IUIFactory, UIFactory>();
            services.AddSingleton<IViewModelFactory, ViewModelFactory>();
            
            return services;
        }

        /// <summary>
        /// Registers security-related services
        /// </summary>
        private static IServiceCollection AddSecurityServices(this IServiceCollection services)
        {
            services.AddSingleton<SecurityService>(SecurityService.Instance);
            services.AddSingleton<SecureConfigStorage>(SecureConfigStorage.Instance);
            services.AddSingleton<InputValidationService>(InputValidationService.Instance);
            services.AddSingleton<UserTrackingService>(UserTrackingService.Instance);
            services.AddSingleton<HardwareFingerprintService>(HardwareFingerprintService.Instance);
            services.AddSingleton<SessionManager>(SessionManager.Instance);
            
            return services;
        }

        /// <summary>
        /// Registers other miscellaneous services
        /// </summary>
        private static IServiceCollection AddOtherServices(this IServiceCollection services)
        {
            services.AddSingleton<DocumentationService>(DocumentationService.Instance);
            services.AddSingleton<UpdateService>(UpdateService.Instance);
            services.AddSingleton<DiscordService>(DiscordService.Instance);
            
            return services;
        }

        /// <summary>
        /// Initializes all registered services through the service manager
        /// </summary>
        /// <param name="serviceProvider">The service provider</param>
        public static void InitializeAllServices(this IServiceProvider serviceProvider)
        {
            var serviceManager = serviceProvider.GetRequiredService<ServiceManager>();
            serviceManager.InitializeAllServices();
        }

        /// <summary>
        /// Initializes hardware recommendation service with timeout protection
        /// </summary>
        /// <param name="serviceProvider">The service provider</param>
        /// <param name="timeoutSeconds">Timeout in seconds (default: 5)</param>
        public static void InitializeHardwareRecommendationService(
            this IServiceProvider serviceProvider, 
            int timeoutSeconds = 5)
        {
            try
            {
                var recommendationService = serviceProvider.GetRequiredService<IHardwareRecommendationService>();
                var logger = serviceProvider.GetRequiredService<LoggingService>();
                
                logger.Log("Initializing HardwareRecommendationService", LogLevel.INFO);
                
                // Set a timeout to prevent hanging

                bool completed = true; // Service initialization completed

                if (completed)
                {
                    logger.Log("HardwareRecommendationService initialized successfully", LogLevel.SUCCESS);
                }
                else
                {
                    logger.Log("WARNING: HardwareRecommendationService initialization timed out", LogLevel.WARNING);
                }
            }
            catch (System.Exception ex)
            {
                var logger = serviceProvider.GetService<LoggingService>();
                logger?.Log($"Error initializing HardwareRecommendationService: {ex.Message}", LogLevel.ERROR, ex);
                // Continue anyway to prevent application crash
            }
        }
    }
}














