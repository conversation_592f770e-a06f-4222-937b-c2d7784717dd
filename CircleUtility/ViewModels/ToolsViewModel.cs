using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Interfaces;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// ViewModel for the Tools view
    /// </summary>
    public class ToolsViewModel : ViewModelBase
    {
        private readonly ILoggerService _logger;
        private readonly IHardwareOptimizationService _hardwareOptimizationService;
        private readonly IConfigurationService _configManager;
        private bool _isLoading;
        private string _statusMessage;
        private bool _isStatusVisible;
        private ObservableCollection<ToolItem> _availableTools;

        /// <summary>
        /// Gets or sets a value indicating whether the view is loading
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status message is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets the available tools
        /// </summary>
        public ObservableCollection<ToolItem> AvailableTools
        {
            get => _availableTools;
            set => SetProperty(ref _availableTools, value);
        }

        /// <summary>
        /// Gets the command to run a tool
        /// </summary>
        public ICommand RunToolCommand { get; }

        /// <summary>
        /// Gets the command to refresh tools
        /// </summary>
        public ICommand RefreshToolsCommand { get; }

        /// <summary>
        /// Initializes a new instance of the ToolsViewModel class
        /// </summary>
        public ToolsViewModel(ILoggerService logger, IHardwareOptimizationService hardwareOptimizationService = null, IRevertTweaksService revertTweaksService = null, IConfigurationService configurationService = null, ITweakChangeTracker tweakChangeTracker = null, IDiscordService discordService = null, IUserTrackingService userTrackingService = null, INotificationService notificationService = null) : base(logger)
        {
            _logger = logger;
            _hardwareOptimizationService = HardwareOptimizationService.Instance;
            _configManager = CircleUtility.Services.ConfigurationManager.Instance;
            _availableTools = new ObservableCollection<ToolItem>();

            // Initialize commands
            RunToolCommand = new RelayCommand<ToolItem>(RunTool);
            RefreshToolsCommand = new RelayCommand(RefreshTools);

            // Load tools
            LoadTools();
        }

        /// <summary>
        /// Loads the available tools
        /// </summary>
        private void LoadTools()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Loading tools...";
                IsStatusVisible = true;

                // Clear existing tools
                AvailableTools.Clear();

                // Add sample tools
                AvailableTools.Add(new ToolItem { Name = "System Cleaner", Description = "Clean temporary files", IsEnabled = true });
                AvailableTools.Add(new ToolItem { Name = "Registry Optimizer", Description = "Optimize Windows registry", IsEnabled = true });
                AvailableTools.Add(new ToolItem { Name = "Startup Manager", Description = "Manage startup programs", IsEnabled = true });
                AvailableTools.Add(new ToolItem { Name = "Service Manager", Description = "Manage Windows services", IsEnabled = true });

                StatusMessage = $"Loaded {AvailableTools.Count} tools.";
                IsStatusVisible = true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error loading tools: {ex.Message}", ex);
                StatusMessage = "Error loading tools. Please try again.";
                IsStatusVisible = true;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Runs a tool
        /// </summary>
        /// <param name="tool">The tool to run</param>
        private void RunTool(ToolItem tool)
        {
            if (tool == null || !tool.IsEnabled) return;

            try
            {
                StatusMessage = $"Running {tool.Name}...";
                IsStatusVisible = true;

                _logger.LogInfo($"Running tool: {tool.Name}");

                // Simulate tool execution
                System.Threading.Thread.Sleep(1000);

                StatusMessage = $"{tool.Name} completed successfully.";
                IsStatusVisible = true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error running tool {tool.Name}: {ex.Message}", ex);
                StatusMessage = $"Error running {tool.Name}. Please try again.";
                IsStatusVisible = true;
            }
        }

        /// <summary>
        /// Refreshes the tools list
        /// </summary>
        private void RefreshTools()
        {
            LoadTools();
        }
    }

    /// <summary>
    /// Represents a tool item
    /// </summary>
    public class ToolItem
    {
        /// <summary>
        /// Gets or sets the tool name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the tool description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the tool is enabled
        /// </summary>
        public bool IsEnabled { get; set; }
    }
}


