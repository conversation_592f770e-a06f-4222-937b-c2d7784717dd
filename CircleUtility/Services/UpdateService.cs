// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for checking and applying updates
    /// </summary>
    public class UpdateService
    {
        private static UpdateService _instance;
        private readonly LoggingService _logger;
        private readonly string _updateDirectory;
        private readonly string _backupDirectory;
        private readonly string _updateServerUrl;
        private readonly HttpClient _httpClient;
        private UpdateInfo _latestUpdate;
        private bool _isUpdateAvailable;
        private bool _isUpdateInProgress;

        /// <summary>
        /// Event raised when an update is available
        /// </summary>
        public event EventHandler<UpdateAvailableEventArgs> UpdateAvailable;

        /// <summary>
        /// Event raised when an update is downloaded
        /// </summary>
        public event EventHandler<UpdateDownloadedEventArgs> UpdateDownloaded;

        /// <summary>
        /// Event raised when an update is installed
        /// </summary>
        public event EventHandler<UpdateInstalledEventArgs> UpdateInstalled;

        /// <summary>
        /// Event raised when an update progress changes
        /// </summary>
        public event EventHandler<UpdateProgressEventArgs> UpdateProgress;

        /// <summary>
        /// Initializes a new instance of the UpdateService class
        /// </summary>
        private UpdateService()
        {
            _logger = LoggingService.Instance;
            _updateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Updates");
            _backupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
            _updateServerUrl = "https://circleutil.com/api/updates";
            _httpClient = new HttpClient();
            _isUpdateAvailable = false;
            _isUpdateInProgress = false;
            
            // Ensure directories exist
            if (!Directory.Exists(_updateDirectory))
            {
                Directory.CreateDirectory(_updateDirectory);
            }
            
            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }
        }

        /// <summary>
        /// Gets the singleton instance of the update service
        /// </summary>
        public static UpdateService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new UpdateService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether an update is available
        /// </summary>
        public bool IsUpdateAvailable => _isUpdateAvailable;

        /// <summary>
        /// Gets a value indicating whether an update is in progress
        /// </summary>
        public bool IsUpdateInProgress => _isUpdateInProgress;

        /// <summary>
        /// Gets the latest update information
        /// </summary>
        public UpdateInfo LatestUpdate => _latestUpdate;

        /// <summary>
        /// Gets the current version of the application
        /// </summary>
        public Version CurrentVersion
        {
            get
            {
                try
                {
                    return Assembly.GetExecutingAssembly().GetName().Version;
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error getting current version: {ex.Message}", LogLevel.ERROR);
                    return new Version(1, 0, 0, 0);
                }
            }
        }

        /// <summary>
        /// Checks for updates
        /// </summary>
        /// <returns>True if an update is available, false otherwise</returns>
        public async Task<bool> CheckForUpdatesAsync()
        {
            try
            {
                _logger.Log("Checking for updates...", LogLevel.INFO);
                
                // In a real implementation, this would make an HTTP request to the update server
                // For now, we'll simulate an update check
                
                // Simulate network delay
                await Task.Delay(1000);
                
                // Get current version
                Version currentVersion = CurrentVersion;
                
                // Simulate latest version (one version higher than current)
                Version latestVersion = new Version(
                    currentVersion.Major,
                    currentVersion.Minor,
                    currentVersion.Build,
                    currentVersion.Revision + 1);
                
                // Create update info
                _latestUpdate = new UpdateInfo
                {
                    Version = latestVersion.ToString(),
                    ReleaseDate = DateTime.Now,
                    DownloadUrl = $"{_updateServerUrl}/download/{latestVersion}",
                    ReleaseNotes = "- Improved performance\n- Fixed bugs\n- Added new features",
                    FileSize = 15360, // 15 MB
                    IsMandatory = false,
                    MinimumRequiredVersion = "1.0.0.0",
                    PublishedBy = "Circle Utility Team"
                };
                
                // Check if update is available
                _isUpdateAvailable = latestVersion > currentVersion;
                
                if (_isUpdateAvailable)
                {
                    _logger.Log($"Update available: {_latestUpdate.Version}", LogLevel.INFO);
                    
                    // Raise event
                    UpdateAvailable?.Invoke(this, new UpdateAvailableEventArgs(_latestUpdate));
                }
                else
                {
                    _logger.Log("No updates available", LogLevel.INFO);
                }
                
                return _isUpdateAvailable;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking for updates: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Downloads an update
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> DownloadUpdateAsync()
        {
            try
            {
                if (!_isUpdateAvailable)
                {
                    _logger.Log("No update available to download", LogLevel.WARNING);
                    return false;
                }
                
                if (_isUpdateInProgress)
                {
                    _logger.Log("Update already in progress", LogLevel.WARNING);
                    return false;
                }
                
                _logger.Log($"Downloading update {_latestUpdate.Version}...", LogLevel.INFO);
                _isUpdateInProgress = true;
                
                // In a real implementation, this would download the update from the server
                // For now, we'll simulate a download
                
                string updateFilePath = Path.Combine(_updateDirectory, $"CircleUtility_{_latestUpdate.Version}.zip");
                
                // Simulate download progress
                for (int i = 0; i <= 100; i += 10)
                {
                    // Raise progress event
                    UpdateProgress?.Invoke(this, new UpdateProgressEventArgs(i, "Downloading update..."));
                    
                    // Simulate download delay
                    await Task.Delay(300);
                }
                
                // Create empty file to simulate downloaded update
                using (FileStream fs = File.Create(updateFilePath))
                {
                    // Just create the file
                }
                
                _logger.Log($"Update {_latestUpdate.Version} downloaded successfully", LogLevel.SUCCESS);
                
                // Raise event
                UpdateDownloaded?.Invoke(this, new UpdateDownloadedEventArgs(_latestUpdate, updateFilePath));
                
                _isUpdateInProgress = false;
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error downloading update: {ex.Message}", LogLevel.ERROR);
                _isUpdateInProgress = false;
                return false;
            }
        }

        /// <summary>
        /// Installs an update
        /// </summary>
        /// <param name="updateFilePath">The path to the update file</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> InstallUpdateAsync(string updateFilePath)
        {
            try
            {
                if (!File.Exists(updateFilePath))
                {
                    _logger.Log($"Update file not found: {updateFilePath}", LogLevel.ERROR);
                    return false;
                }
                
                if (_isUpdateInProgress)
                {
                    _logger.Log("Update already in progress", LogLevel.WARNING);
                    return false;
                }
                
                _logger.Log($"Installing update from {updateFilePath}...", LogLevel.INFO);
                _isUpdateInProgress = true;
                
                // In a real implementation, this would extract the update and replace files
                // For now, we'll simulate an installation
                
                // Simulate installation progress
                for (int i = 0; i <= 100; i += 20)
                {
                    // Raise progress event
                    UpdateProgress?.Invoke(this, new UpdateProgressEventArgs(i, "Installing update..."));
                    
                    // Simulate installation delay
                    await Task.Delay(500);
                }
                
                _logger.Log($"Update {_latestUpdate.Version} installed successfully", LogLevel.SUCCESS);
                
                // Raise event
                UpdateInstalled?.Invoke(this, new UpdateInstalledEventArgs(_latestUpdate));
                
                _isUpdateInProgress = false;
                _isUpdateAvailable = false;
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error installing update: {ex.Message}", LogLevel.ERROR);
                _isUpdateInProgress = false;
                return false;
            }
        }

        /// <summary>
        /// Creates a backup before updating
        /// </summary>
        /// <returns>The backup file path, or null if backup failed</returns>
        public async Task<string> CreateBackupAsync()
        {
            try
            {
                _logger.Log("Creating backup before update...", LogLevel.INFO);
                
                // In a real implementation, this would create a backup of the application files
                // For now, we'll simulate a backup
                
                string backupFilePath = Path.Combine(_backupDirectory, $"CircleUtility_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip");
                
                // Simulate backup progress
                for (int i = 0; i <= 100; i += 25)
                {
                    // Raise progress event
                    UpdateProgress?.Invoke(this, new UpdateProgressEventArgs(i, "Creating backup..."));
                    
                    // Simulate backup delay
                    await Task.Delay(200);
                }
                
                // Create empty file to simulate backup
                using (FileStream fs = File.Create(backupFilePath))
                {
                    // Just create the file
                }
                
                _logger.Log($"Backup created successfully: {backupFilePath}", LogLevel.SUCCESS);
                return backupFilePath;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating backup: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Restores a backup
        /// </summary>
        /// <param name="backupFilePath">The path to the backup file</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RestoreBackupAsync(string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    _logger.Log($"Backup file not found: {backupFilePath}", LogLevel.ERROR);
                    return false;
                }
                
                _logger.Log($"Restoring backup from {backupFilePath}...", LogLevel.INFO);
                
                // In a real implementation, this would restore the application files from the backup
                // For now, we'll simulate a restoration
                
                // Simulate restoration progress
                for (int i = 0; i <= 100; i += 20)
                {
                    // Raise progress event
                    UpdateProgress?.Invoke(this, new UpdateProgressEventArgs(i, "Restoring backup..."));
                    
                    // Simulate restoration delay
                    await Task.Delay(300);
                }
                
                _logger.Log("Backup restored successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restoring backup: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restarts the application to apply updates
        /// </summary>
        public void RestartApplication()
        {
            try
            {
                _logger.Log("Restarting application to apply updates...", LogLevel.INFO);
                
                // Get the executable path
                string exePath = Process.GetCurrentProcess().MainModule.FileName;
                
                // Start the application
                Process.Start(exePath);
                
                // Exit the current process
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restarting application: {ex.Message}", LogLevel.ERROR);
            }
        }
    }

    /// <summary>
    /// Event arguments for update available events
    /// </summary>
    public class UpdateAvailableEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the update information
        /// </summary>
        public UpdateInfo Update { get; }

        /// <summary>
        /// Initializes a new instance of the UpdateAvailableEventArgs class
        /// </summary>
        /// <param name="update">The update information</param>
        public UpdateAvailableEventArgs(UpdateInfo update)
        {
            Update = update;
        }
    }

    /// <summary>
    /// Event arguments for update downloaded events
    /// </summary>
    public class UpdateDownloadedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the update information
        /// </summary>
        public UpdateInfo Update { get; }

        /// <summary>
        /// Gets the update file path
        /// </summary>
        public string UpdateFilePath { get; }

        /// <summary>
        /// Initializes a new instance of the UpdateDownloadedEventArgs class
        /// </summary>
        /// <param name="update">The update information</param>
        /// <param name="updateFilePath">The update file path</param>
        public UpdateDownloadedEventArgs(UpdateInfo update, string updateFilePath)
        {
            Update = update;
            UpdateFilePath = updateFilePath;
        }
    }

    /// <summary>
    /// Event arguments for update installed events
    /// </summary>
    public class UpdateInstalledEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the update information
        /// </summary>
        public UpdateInfo Update { get; }

        /// <summary>
        /// Initializes a new instance of the UpdateInstalledEventArgs class
        /// </summary>
        /// <param name="update">The update information</param>
        public UpdateInstalledEventArgs(UpdateInfo update)
        {
            Update = update;
        }
    }

    /// <summary>
    /// Event arguments for update progress events
    /// </summary>
    public class UpdateProgressEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the progress percentage
        /// </summary>
        public int ProgressPercentage { get; }

        /// <summary>
        /// Gets the status message
        /// </summary>
        public string StatusMessage { get; }

        /// <summary>
        /// Initializes a new instance of the UpdateProgressEventArgs class
        /// </summary>
        /// <param name="progressPercentage">The progress percentage</param>
        /// <param name="statusMessage">The status message</param>
        public UpdateProgressEventArgs(int progressPercentage, string statusMessage)
        {
            ProgressPercentage = progressPercentage;
            StatusMessage = statusMessage;
        }
    }
}

