using System;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json;
using System.IO;
using CircleUtility.Services;

namespace CircleUtility.UtilityApi
{
    public static class UtilityApi
    {
        public static async Task<string> GetStatusAsync(EnhancedDiscordUserService userService, string version)
        {
            await userService.LoadUsersFromFileAsync();
            new
            {
                status = "TheCircleUtility is online!",
                userCount = users.Count,
                activeUsers = users.Where(u => u.IsActive).Count(),
                adminCount = users.Where(u => u.IsAdmin).Count(),
                version = version,
                time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
            return JsonSerializer.Serialize(result);
        }

        public static async Task<string> VerifyAsync(string body)
        {
            await Task.Yield();
            try
            {
                JsonSerializer.Deserialize<VerifyRequest>(body);
                bool verified = req != null && req.code == "1234";
                return JsonSerializer.Serialize(new { verified });
            }
            catch
            {
                return JsonSerializer.Serialize(new { verified = false });
            }
        }

        private class VerifyRequest
        {
            public string discordId { get; set; }
            public string code { get; set; }
        }
    }
} 
