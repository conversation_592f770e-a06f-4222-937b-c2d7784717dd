using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for logging service
    /// </summary>
    public interface ILoggerService
    {
        /// <summary>
        /// Event raised when a new log entry is added
        /// </summary>
        event EventHandler<LogEntryEventArgs> LogEntryAdded;

        /// <summary>
        /// Gets all log entries
        /// </summary>
        IReadOnlyList<LogEntry> LogEntries { get; }

        /// <summary>
        /// Gets or sets the log level
        /// </summary>
        LogLevel LogLevel { get; set; }

        /// <summary>
        /// Logs a message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="level">The log level</param>
        /// <param name="exception">The exception, if any</param>
        void Log(string message, LogLevel level, Exception exception = null);

        /// <summary>
        /// Logs a message asynchronously
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="level">The log level</param>
        /// <param name="exception">The exception, if any</param>
        Task LogAsync(string message, LogLevel level, Exception exception = null);

        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">The message to log</param>
        void LogInfo(string message);

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">The error message to log</param>
        void LogError(string message);

        /// <summary>
        /// Logs an error message with exception
        /// </summary>
        /// <param name="message">The error message to log</param>
        /// <param name="exception">The exception to log</param>
        void LogError(string message, Exception exception);

        /// <summary>
        /// Clears all log entries
        /// </summary>
        void ClearLogs();
    }
}
