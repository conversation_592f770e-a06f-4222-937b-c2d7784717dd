<UserControl x:Class="CircleUtility.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="Black">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="Settings"
                   FontFamily="Consolas"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20"/>

        <!-- Settings content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- General settings -->
                <Border Background="#FF050A0F"
                        BorderThickness="1"
                        BorderBrush="#FF00C8FF"
                        Padding="15"
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Text="General Settings"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>

                        <!-- Username -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Username:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <TextBox Grid.Column="1"
                                     Text="{Binding Username}"
                                     Background="#FF0A141E"
                                     Foreground="#FF00C8FF"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Height="30"
                                     Padding="5,0"
                                     VerticalContentAlignment="Center"/>
                        </Grid>

                        <!-- Start with Windows -->
                        <Grid Grid.Row="2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Start with Windows:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding StartWithWindows}"
                                      VerticalAlignment="Center"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Interface settings -->
                <Border Background="#FF050A0F"
                        BorderThickness="1"
                        BorderBrush="#FF00C8FF"
                        Padding="15"
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Text="Interface Settings"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>

                        <!-- Dark mode -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Dark Mode:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableDarkMode}"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Animation Speed -->
                        <Grid Grid.Row="2" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Animation Speed:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <ComboBox Grid.Column="1"
                                      SelectedIndex="{Binding AnimationSpeedIndex}"
                                      Width="150"
                                      HorizontalAlignment="Left"
                                      Background="#FF0A141E"
                                      Foreground="#FF00C8FF"
                                      BorderBrush="#FF00C8FF"
                                      BorderThickness="1">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                                </ComboBox.Resources>
                                <ComboBoxItem Content="Off"/>
                                <ComboBoxItem Content="Slow"/>
                                <ComboBoxItem Content="Normal"/>
                                <ComboBoxItem Content="Fast"/>
                                <ComboBoxItem Content="Very Fast"/>
                            </ComboBox>
                        </Grid>

                        <!-- Font Size -->
                        <Grid Grid.Row="3" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Font Size:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <ComboBox Grid.Column="1"
                                      SelectedIndex="{Binding FontSizeIndex}"
                                      Width="150"
                                      HorizontalAlignment="Left"
                                      Background="#FF0A141E"
                                      Foreground="#FF00C8FF"
                                      BorderBrush="#FF00C8FF"
                                      BorderThickness="1">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                                </ComboBox.Resources>
                                <ComboBoxItem Content="Small"/>
                                <ComboBoxItem Content="Medium"/>
                                <ComboBoxItem Content="Large"/>
                            </ComboBox>
                        </Grid>

                        <!-- Show Tooltips -->
                        <Grid Grid.Row="4" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Show Tooltips:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding ShowTooltips}"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Theme Selection -->
                        <Grid Grid.Row="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Theme:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <ComboBox Grid.Column="1"
                                      SelectedIndex="{Binding ThemeIndex}"
                                      Width="150"
                                      HorizontalAlignment="Left"
                                      Background="#FF0A141E"
                                      Foreground="#FF00C8FF"
                                      BorderBrush="#FF00C8FF"
                                      BorderThickness="1">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                                </ComboBox.Resources>
                                <ComboBoxItem Content="Blue (Default)"/>
                                <ComboBoxItem Content="Red"/>
                                <ComboBoxItem Content="Green"/>
                                <ComboBoxItem Content="Purple"/>
                                <ComboBoxItem Content="Orange"/>
                            </ComboBox>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Performance settings -->
                <Border Background="#FF050A0F"
                        BorderThickness="1"
                        BorderBrush="#FF00C8FF"
                        Padding="15"
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Text="Performance Settings"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>

                        <!-- Auto-Refresh Interval -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Auto-Refresh Interval:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <ComboBox Grid.Column="1"
                                      SelectedIndex="{Binding RefreshIntervalIndex}"
                                      Width="150"
                                      HorizontalAlignment="Left"
                                      Background="#FF0A141E"
                                      Foreground="#FF00C8FF"
                                      BorderBrush="#FF00C8FF"
                                      BorderThickness="1">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                                </ComboBox.Resources>
                                <ComboBoxItem Content="Off"/>
                                <ComboBoxItem Content="5 seconds"/>
                                <ComboBoxItem Content="10 seconds"/>
                                <ComboBoxItem Content="30 seconds"/>
                                <ComboBoxItem Content="1 minute"/>
                            </ComboBox>
                        </Grid>

                        <!-- Hardware Monitoring -->
                        <Grid Grid.Row="2" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Hardware Monitoring:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableHardwareMonitoring}"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Background Processing -->
                        <Grid Grid.Row="3">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Background Processing:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableBackgroundProcessing}"
                                      VerticalAlignment="Center"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Logging settings -->
                <Border Background="#FF050A0F"
                        BorderThickness="1"
                        BorderBrush="#FF00C8FF"
                        Padding="15"
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Text="Logging Settings"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>

                        <!-- Enable logging -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Enable Logging:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableLogging}"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Log Level -->
                        <Grid Grid.Row="2" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Log Level:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <ComboBox Grid.Column="1"
                                      SelectedIndex="{Binding LogLevelIndex}"
                                      Width="150"
                                      HorizontalAlignment="Left"
                                      Background="#FF0A141E"
                                      Foreground="#FF00C8FF"
                                      BorderBrush="#FF00C8FF"
                                      BorderThickness="1"
                                      IsEnabled="{Binding EnableLogging}">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                                </ComboBox.Resources>
                                <ComboBoxItem Content="Error"/>
                                <ComboBoxItem Content="Warning"/>
                                <ComboBoxItem Content="Info"/>
                                <ComboBoxItem Content="Debug"/>
                                <ComboBoxItem Content="Verbose"/>
                            </ComboBox>
                        </Grid>

                        <!-- Log File Location -->
                        <Grid Grid.Row="3" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Log File Location:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <TextBox Grid.Column="1"
                                     Text="{Binding LogFileLocation}"
                                     Background="#FF0A141E"
                                     Foreground="#FF00C8FF"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Height="30"
                                     Padding="5,0"
                                     VerticalContentAlignment="Center"
                                     IsEnabled="{Binding EnableLogging}"/>

                            <Button Grid.Column="2"
                                    Content="Browse"
                                    Width="80"
                                    Height="30"
                                    Margin="10,0,0,0"
                                    Background="#FF001428"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"
                                    Command="{Binding BrowseLogFileCommand}"
                                    IsEnabled="{Binding EnableLogging}"/>
                        </Grid>

                        <!-- Enable user tracking -->
                        <Grid Grid.Row="4">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="User Activity Tracking:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableUserTracking}"
                                      VerticalAlignment="Center"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Security settings -->
                <Border Background="#FF050A0F"
                        BorderThickness="1"
                        BorderBrush="#FF00C8FF"
                        Padding="15"
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Text="Security Settings"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>

                        <!-- Enable Security Features -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Enable Security Features:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableSecurityFeatures}"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Security Level -->
                        <Grid Grid.Row="2" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Security Level:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <ComboBox Grid.Column="1"
                                      SelectedIndex="{Binding SecurityLevelIndex}"
                                      Width="150"
                                      HorizontalAlignment="Left"
                                      Background="#FF0A141E"
                                      Foreground="#FF00C8FF"
                                      BorderBrush="#FF00C8FF"
                                      BorderThickness="1"
                                      IsEnabled="{Binding EnableSecurityFeatures}">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                                </ComboBox.Resources>
                                <ComboBoxItem Content="1 - Basic"/>
                                <ComboBoxItem Content="2 - Advanced"/>
                                <ComboBoxItem Content="3 - Expert"/>
                            </ComboBox>
                        </Grid>

                        <!-- Advanced Security Settings -->
                        <Grid Grid.Row="3" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Advanced Security:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <Button Grid.Column="1"
                                    Content="Open Security Settings"
                                    Width="180"
                                    Height="30"
                                    HorizontalAlignment="Left"
                                    Background="#FF001428"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"
                                    Command="{Binding OpenSecuritySettingsCommand}"
                                    IsEnabled="{Binding EnableSecurityFeatures}"/>
                        </Grid>

                        <!-- Security Settings View -->
                        <ContentControl Grid.Row="4"
                                        Content="{Binding SecuritySettingsView}"
                                        Visibility="Collapsed"/>
                    </Grid>
                </Border>

                <!-- Advanced settings -->
                <Border Background="#FF050A0F"
                        BorderThickness="1"
                        BorderBrush="#FF00C8FF"
                        Padding="15"
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Text="Advanced Settings"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>

                        <!-- Developer Mode -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Developer Mode:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableDevMode}"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Auto-Update -->
                        <Grid Grid.Row="2" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Auto-Update:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <CheckBox Grid.Column="1"
                                      IsChecked="{Binding EnableAutoUpdate}"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Reset All Settings -->
                        <Button Grid.Row="3"
                                Content="Reset All Settings"
                                Command="{Binding ResetSettingsCommand}"
                                Width="180"
                                Height="30"
                                HorizontalAlignment="Left"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FFFF3232"
                                BorderThickness="1"/>
                    </Grid>
                </Border>

                <!-- Backup & Restore -->
                <Border Background="#FF050A0F"
                        BorderThickness="1"
                        BorderBrush="#FF00C8FF"
                        Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Text="Backup and Restore"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>

                        <!-- Backup Location -->
                        <Grid Grid.Row="1" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Text="Backup Location:"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF00C8FF"
                                       VerticalAlignment="Center"/>

                            <TextBox Grid.Column="1"
                                     Text="{Binding BackupLocation}"
                                     Background="#FF0A141E"
                                     Foreground="#FF00C8FF"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Height="30"
                                     Padding="5,0"
                                     VerticalContentAlignment="Center"/>

                            <Button Grid.Column="2"
                                    Content="Browse"
                                    Width="80"
                                    Height="30"
                                    Margin="10,0,0,0"
                                    Background="#FF001428"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"
                                    Command="{Binding BrowseBackupLocationCommand}"/>
                        </Grid>

                        <!-- Backup/Restore Buttons -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal">
                            <Button Content="Create Backup"
                                    Command="{Binding CreateBackupCommand}"
                                    Width="150"
                                    Height="30"
                                    Margin="0,0,10,0"
                                    Background="#FF001428"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"/>

                            <Button Content="Restore from Backup"
                                    Command="{Binding RestoreBackupCommand}"
                                    Width="180"
                                    Height="30"
                                    Background="#FF001428"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Action buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Reset Settings"
                    Command="{Binding ResetSettingsCommand}"
                    Width="120"
                    Height="40"
                    Margin="0,0,10,0"
                    Background="#FF001428"
                    Foreground="White"
                    BorderBrush="#FFFF3232"
                    BorderThickness="1"/>

            <Button Content="Save Settings"
                    Command="{Binding SaveSettingsCommand}"
                    Width="120"
                    Height="40"
                    Background="#FF001428"
                    Foreground="White"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"/>
        </StackPanel>
    </Grid>
</UserControl>
