using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    public interface IRevertTweaksService
    {
        bool IsInitialized { get; }
        event EventHandler<TweaksRevertedEventArgs> TweaksReverted;
        List<TweakHistoryEntry> GetActiveTweaks();
        List<TweakHistoryEntry> GetAllTweaks();
        Task<bool> RevertAllTweaksAsync();
        Task<bool> RevertTweaksByCategoryAsync(OptimizationType tweakType);
    }
} 