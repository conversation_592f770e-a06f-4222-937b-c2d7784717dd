using System;
using System.IO;
using System.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Services;
using CircleUtility.ViewModels;
using CircleUtility.Models;
using CircleUtility.Interfaces;

namespace CircleUtility
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public static IServiceProvider ServiceProvider { get; private set; }
        private IConfiguration _configuration;
        private readonly string _logDirectory;
        private bool _isInitialized;
        private CentralConfigService _configService;
        public static CentralConfigService ConfigService { get; private set; }

        /// <summary>
        /// Initializes a new instance of the App class
        /// </summary>
        public App()
        {
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            Directory.CreateDirectory(_logDirectory);

            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            DispatcherUnhandledException += App_DispatcherUnhandledException;
        }

        /// <summary>
        /// Handles application startup
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The startup event args</param>
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                base.OnStartup(e);
                InitializeApplication();
            }
            catch (Exception ex)
            {
                HandleStartupError(ex);
            }
        }

        private void InitializeApplication()
        {
            if (_isInitialized) return;

            try
            {
                // Validate critical directories
                string[] requiredDirs = { _logDirectory, Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config") };
                foreach (var dir in requiredDirs)
                {
                    if (!Directory.Exists(dir))
                    {
                        try { Directory.CreateDirectory(dir); }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"Failed to create required directory: {dir}\nError: {ex.Message}", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                            Shutdown(1);
                            return;
                        }
                    }
                    // Check write permission
                    try
                    {
                        string testFile = Path.Combine(dir, "__test_write.txt");
                        File.WriteAllText(testFile, "test");
                        File.Delete(testFile);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"No write permission for directory: {dir}\nError: {ex.Message}", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        Shutdown(1);
                        return;
                    }
                }

                // Validate critical config file
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                if (!File.Exists(configPath))
                {
                    MessageBox.Show($"Critical config file missing: {configPath}\nPlease reinstall or repair the application.", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown(1);
                    return;
                }

                // Validate required config values (e.g., Discord webhook URLs)
                var configJson = File.ReadAllText(configPath);
                if (configJson.Contains("YOUR_COMMANDS_WEBHOOK_URL_HERE") || configJson.Contains("YOUR_REPORTS_WEBHOOK_URL_HERE"))
                {
                    MessageBox.Show("Discord webhook URLs are not configured in appsettings.json. Please update the configuration before running the app.", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown(1);
                    return;
                }

                // Initialize configuration
                _configuration = new ConfigurationBuilder()
                    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Configure services
                var services = new ServiceCollection();
                ConfigureServices(services);

                // Build service provider
                ServiceProvider = services.BuildServiceProvider();

                // Resolve all critical services via DI
                var logger = ServiceProvider.GetRequiredService<ILoggerService>();
                var configManager = ServiceProvider.GetRequiredService<IConfigurationService>();
                var exceptionHandler = ServiceProvider.GetRequiredService<IExceptionHandlingService>();
                var securityService = ServiceProvider.GetRequiredService<ISecurityService>();
                var serviceManager = ServiceProvider.GetRequiredService<ServiceManager>();
                // Register services with ServiceManager if needed
                serviceManager.RegisterService(logger);
                serviceManager.RegisterService(configManager);
                serviceManager.RegisterService(exceptionHandler);
                serviceManager.RegisterService(securityService);
                // Initialize all services
                if (!serviceManager.InitializeAllServices())
                {
                    throw new ApplicationException("Failed to initialize all required services");
                }

                // Initialize the central config service
                _configService = new CentralConfigService(
                    "****************************************",
                    "AquaknowsJava",
                    "TheCircleUtility"
                );
                ConfigService = _configService;
                
                // Handle configuration updates
                _configService.ConfigUpdated += (s, config) =>
                {
                    // Update user access and permissions
                    Dispatcher.Invoke(() =>
                    {
                        // Refresh any open views that need updating
                        foreach (Window window in Windows)
                        {
                            if (window.DataContext is IConfigAware configAware)
                            {
                                configAware.OnConfigUpdated(config);
                            }
                        }
                    });
                };

                // Initialize update service
                UpdateService.Initialize(
                    "****************************************",
                    "AquaknowsJava",
                    "TheCircleUtility"
                );
                var updateService = UpdateService.Instance;

                // Handle update events
                updateService.UpdateAvailable += (s, updateInfo) =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        var result = MessageBox.Show(
                            $"A new version ({updateInfo.Version}) is available!\n\n" +
                            $"Release Notes:\n{updateInfo.ReleaseNotes}\n\n" +
                            "Would you like to update now?",
                            "Update Available",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Information);

                        if (result == MessageBoxResult.Yes)
                        {
                            _ = updateService.DownloadAndInstallUpdateAsync(updateInfo.DownloadUrl);
                        }
                    });
                };

                updateService.UpdateProgress += (s, message) =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        // You can show a progress window here if needed
                        logger.LogInfo($"Update progress: {message}");
                    });
                };

                updateService.UpdateError += (s, ex) =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show(
                            $"Error during update: {ex.Message}",
                            "Update Error",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    });
                };

                // Check for updates periodically
                var updateTimer = new System.Timers.Timer(TimeSpan.FromHours(1).TotalMilliseconds);
                updateTimer.Elapsed += async (s, e) => await updateService.CheckForUpdatesAsync();
                updateTimer.Start();

                // Initial update check
                _ = updateService.CheckForUpdatesAsync();

                // Show main window
                var mainWindow = ServiceProvider.GetRequiredService<MainWindow>();
                mainWindow.Show();

                _isInitialized = true;
                logger.LogInfo("Application initialized successfully");
            }
            catch (Exception ex)
            {
                HandleStartupError(ex);
            }
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Add configuration
            services.AddSingleton(_configuration);

            // Add logging
            services.AddSingleton<ILoggerService, LoggingService>();

            // Add core services
            services.AddSingleton<IConfigurationService, CircleUtility.Services.ConfigurationManager>();
            services.AddSingleton<IExceptionHandlingService, ExceptionHandlingService>();
            services.AddSingleton<ISecurityService, SecurityService>();
            services.AddSingleton<IUserTrackingService, UserTrackingService>();
            services.AddSingleton<IInputValidationService, InputValidationService>();
            services.AddSingleton<IWindowService, WindowLoadingManager>();
            services.AddSingleton<IPowerShellScriptHandler, PowerShellScriptHandler>();

            // Add feature services
            services.AddSingleton<IHardwareDetectionService>(provider => HardwareDetectionService.Instance);
            services.AddSingleton<IHardwareOptimizationService, HardwareOptimizationService>();
            services.AddSingleton<IHardwareCompatibilityService, HardwareCompatibilityService>();
            services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
            services.AddSingleton<IBenchmarkingService, BenchmarkingService>();
            services.AddSingleton<IHardwareRecommendationService, HardwareRecommendationService>();
            services.AddSingleton<IRevertTweaksService, RevertTweaksService>();
            services.AddSingleton<ITweakChangeTracker, TweakChangeTracker>();
            services.AddSingleton<IDocumentationService, DocumentationService>();
            services.AddSingleton<INotificationService, NotificationService>();
            services.AddSingleton<IDiscordService, DiscordService>();
            services.AddSingleton<IEnhancedDiscordUserService, EnhancedDiscordUserService>();
            services.AddSingleton<IDiscordBotService, DiscordBotService>();
            services.AddSingleton<ISystemOptimizationService, SystemOptimizationService>();
            services.AddSingleton<IHardwareDetectionBadgeService, HardwareDetectionBadgeService>();

            // Add ViewModels
            services.AddSingleton<DashboardViewModel>();
            services.AddSingleton<SettingsViewModel>();
            services.AddSingleton<HelpSupportViewModel>();
            services.AddSingleton<PerformanceOptimizerViewModel>();
            services.AddSingleton<SmartRecommendationsViewModel>();

            // Add UI services
            services.AddSingleton<WindowLoadingManager>();
            services.AddSingleton<InputValidationService>();

            // Add hardware interfaces
            services.AddSingleton<IHardwareDetectionService>(provider => provider.GetRequiredService<HardwareDetectionService>());
            services.AddSingleton<IHardwareOptimizationService>(provider => provider.GetRequiredService<HardwareOptimizationService>());

            // Add hardware compatibility interface
            services.AddSingleton<IHardwareCompatibilityService>(provider => HardwareCompatibilityService.Instance);

            // Add performance monitoring interface
            services.AddSingleton<IPerformanceMonitoringService>(provider => PerformanceMonitoringService.Instance);

            // Add benchmarking interface
            services.AddSingleton<IBenchmarkingService>(provider => BenchmarkingService.Instance);

            // Add hardware recommendation interface
            services.AddSingleton<IHardwareRecommendationService>(provider => HardwareRecommendationService.Instance);

            // Add Discord service interface
            services.AddSingleton<IDiscordService>(provider => DiscordService.Instance);

            // Add configuration service interface
            services.AddSingleton<IConfigurationService>(provider => CircleUtility.Services.CircleUtility.Services.CircleUtility.Services.ConfigurationManager.Instance);

            // Add security service interface
            services.AddSingleton<ISecurityService>(provider => SecurityService.Instance);

            // Add window service interface
            services.AddSingleton<IWindowService>(provider => WindowLoadingManager.Instance);

            // Add windows
            services.AddTransient<MainWindow>();
            services.AddTransient<WelcomeScreen>();
        }

        private void HandleStartupError(Exception ex)
        {
            try
            {
                var errorMessage = $"Critical error during startup: {ex.Message}\n\n" +
                                 $"Details: {ex}\n\n" +
                                 "The application will now close.";

                File.WriteAllText(
                    Path.Combine(_logDirectory, $"startup_error_{DateTime.Now:yyyyMMdd_HHmmss}.log"),
                    errorMessage);

                MessageBox.Show(
                    errorMessage,
                    "Startup Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                Shutdown(1);
            }
            catch
            {
                MessageBox.Show(
                    "A critical error occurred during startup and the error could not be logged.",
                    "Critical Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var ex = e.ExceptionObject as Exception;
                LoggingService.Instance?.LogCritical("Unhandled domain exception", ex);
                HandleFatalError(ex);
            }
            catch
            {
                HandleFatalError(null);
            }
        }

        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                LoggingService.Instance?.LogError("Unhandled dispatcher exception", e.Exception);
                e.Handled = true;
                MessageBox.Show(
                    $"An error occurred: {e.Exception.Message}\n\nThe error has been logged.",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            catch
            {
                HandleFatalError(e.Exception);
            }
        }

        private void HandleFatalError(Exception ex)
        {
            try
            {
                var errorMessage = ex != null
                    ? $"Fatal error: {ex.Message}\n\nDetails: {ex}"
                    : "A fatal error occurred.";

                File.WriteAllText(
                    Path.Combine(_logDirectory, $"fatal_error_{DateTime.Now:yyyyMMdd_HHmmss}.log"),
                    errorMessage);

                MessageBox.Show(
                    errorMessage,
                    "Fatal Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                Shutdown(1);
            }
        }

        /// <summary>
        /// Handles application exit
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The exit event args</param>
        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                var logger = ServiceProvider.GetService<ILoggerService>();
                logger?.LogInfo("Application shutting down...");
                var serviceManager = ServiceProvider.GetService<ServiceManager>();
                serviceManager?.DisposeServices();
                (ServiceProvider as IDisposable)?.Dispose();
                _configService?.Dispose();
            }
            catch (Exception ex)
            {
                File.WriteAllText(
                    Path.Combine(_logDirectory, $"shutdown_error_{DateTime.Now:yyyyMMdd_HHmmss}.log"),
                    $"Error during shutdown: {ex}");
            }
            finally
            {
                base.OnExit(e);
            }
        }
    }

    // Interface for views that need to react to config changes
    public interface IConfigAware
    {
        void OnConfigUpdated(CentralConfig config);
    }
}










