using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the benchmark view
    /// </summary>
    public class BenchmarkViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTracking;
        private string _selectedBenchmark;
        private bool _isBenchmarkRunning;
        private string _benchmarkStatus;
        private double _benchmarkProgress;

        /// <summary>
        /// Initializes a new instance of the BenchmarkViewModel class
        /// </summary>
        public BenchmarkViewModel()
        {
            _logger = LoggingService.Instance;
            _userTracking = UserTrackingService.Instance;

            // Initialize commands
            RunBenchmarkCommand = new RelayCommand(RunBenchmark, () => !IsBenchmarkRunning);
            CancelBenchmarkCommand = new RelayCommand(CancelBenchmark, () => IsBenchmarkRunning);
            ClearResultsCommand = new RelayCommand(ClearResults);
            ExportResultsCommand = new RelayCommand(ExportResults);

            // Initialize benchmark options
            InitializeBenchmarkOptions();

            // Set default values
            SelectedBenchmark = BenchmarkOptions[0];
            BenchmarkStatus = "Ready to run benchmark";
            BenchmarkProgress = 0;

            _logger.Log("BenchmarkViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of benchmark options
        /// </summary>
        public ObservableCollection<string> BenchmarkOptions { get; } = new ObservableCollection<string>();

        /// <summary>
        /// Gets the collection of benchmark results
        /// </summary>
        public ObservableCollection<CircleUtility.Models.BenchmarkResult> BenchmarkResults { get; } = new ObservableCollection<CircleUtility.Models.BenchmarkResult>();

        /// <summary>
        /// Gets or sets the selected benchmark
        /// </summary>
        public string SelectedBenchmark
        {
            get => _selectedBenchmark;
            set => SetProperty(ref _selectedBenchmark, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether a benchmark is running
        /// </summary>
        public bool IsBenchmarkRunning
        {
            get => _isBenchmarkRunning;
            set
            {
                if (SetProperty(ref _isBenchmarkRunning, value))
                {
                    // Refresh command can execute status
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the benchmark status
        /// </summary>
        public string BenchmarkStatus
        {
            get => _benchmarkStatus;
            set => SetProperty(ref _benchmarkStatus, value);
        }

        /// <summary>
        /// Gets or sets the benchmark progress
        /// </summary>
        public double BenchmarkProgress
        {
            get => _benchmarkProgress;
            set => SetProperty(ref _benchmarkProgress, value);
        }

        /// <summary>
        /// Gets a value indicating whether there are benchmark results
        /// </summary>
        public bool HasBenchmarkResults => BenchmarkResults.Count > 0;

        /// <summary>
        /// Gets the run benchmark command
        /// </summary>
        public ICommand RunBenchmarkCommand { get; }

        /// <summary>
        /// Gets the cancel benchmark command
        /// </summary>
        public ICommand CancelBenchmarkCommand { get; }

        /// <summary>
        /// Gets the clear results command
        /// </summary>
        public ICommand ClearResultsCommand { get; }

        /// <summary>
        /// Gets the export results command
        /// </summary>
        public ICommand ExportResultsCommand { get; }

        /// <summary>
        /// Initializes the benchmark options
        /// </summary>
        private void InitializeBenchmarkOptions()
        {
            BenchmarkOptions.Add("CPU Performance");
            BenchmarkOptions.Add("Memory Performance");
            BenchmarkOptions.Add("Disk Performance");
            BenchmarkOptions.Add("GPU Performance");
            BenchmarkOptions.Add("Network Latency");
            BenchmarkOptions.Add("Input Latency");
            BenchmarkOptions.Add("System Responsiveness");
            BenchmarkOptions.Add("Full System Benchmark");
        }

        /// <summary>
        /// Runs the selected benchmark
        /// </summary>
        private async void RunBenchmark()
        {
            if (string.IsNullOrEmpty(SelectedBenchmark))
                return;

            _logger.Log($"Running benchmark: {SelectedBenchmark}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Benchmark", $"Started {SelectedBenchmark} benchmark");

            IsBenchmarkRunning = true;
            BenchmarkStatus = $"Running {SelectedBenchmark} benchmark...";
            BenchmarkProgress = 0;

            try
            {
                // Simulate benchmark progress
                for (int i = 0; i <= 100; i += 5)
                {
                    BenchmarkProgress = i;
                    BenchmarkStatus = $"Running {SelectedBenchmark} benchmark... {i}%";

                    // Simulate benchmark work
                    await System.Threading.Tasks.Task.Delay(200);

                    // Check if benchmark was cancelled
                    if (!IsBenchmarkRunning)
                    {
                        BenchmarkStatus = "Benchmark cancelled";
                        return;
                    }
                }

                // Create a benchmark result
                var result = new CircleUtility.Models.BenchmarkResult
                {
                    Name = SelectedBenchmark,
                    Score = new Random().Next(5000, 15000) / 100.0, // Random score between 50 and 150
                    Unit = GetBenchmarkUnit(SelectedBenchmark),
                    DateTime = DateTime.Now,
                    SystemConfig = "Windows 10, Intel i7, 16GB RAM, NVIDIA RTX 3070",
                    Notes = "Benchmark completed successfully"
                };

                // Add the result
                BenchmarkResults.Add(result);

                // Notify that HasBenchmarkResults has changed
                OnPropertyChanged(nameof(HasBenchmarkResults));

                BenchmarkStatus = $"{SelectedBenchmark} benchmark completed successfully";
                _logger.Log($"Benchmark completed: {SelectedBenchmark}, Score: {result.Score} {result.Unit}", LogLevel.SUCCESS);
                _userTracking.AddUserActivity("User", "Benchmark", $"Completed {SelectedBenchmark} benchmark with score {result.Score} {result.Unit}");
            }
            catch (Exception ex)
            {
                BenchmarkStatus = $"Error running benchmark: {ex.Message}";
                _logger.Log($"Error running benchmark: {ex.Message}", LogLevel.ERROR);
            }
            finally
            {
                IsBenchmarkRunning = false;
            }
        }

        /// <summary>
        /// Cancels the running benchmark
        /// </summary>
        private void CancelBenchmark()
        {
            _logger.Log("Cancelling benchmark", LogLevel.INFO);
            IsBenchmarkRunning = false;
        }

        /// <summary>
        /// Clears the benchmark results
        /// </summary>
        private void ClearResults()
        {
            _logger.Log("Clearing benchmark results", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Benchmark", "Cleared benchmark results");

            BenchmarkResults.Clear();
            BenchmarkStatus = "Benchmark results cleared";

            // Notify that HasBenchmarkResults has changed
            OnPropertyChanged(nameof(HasBenchmarkResults));
        }

        /// <summary>
        /// Exports the benchmark results to a text file on the desktop
        /// </summary>
        private void ExportResults()
        {
            try
            {
                _logger.Log("Exporting benchmark results", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "Benchmark", "Exported benchmark results");

                // Check if there are any results to export
                if (BenchmarkResults.Count == 0)
                {
                    BenchmarkStatus = "No benchmark results to export";
                    _logger.Log("No benchmark results to export", LogLevel.WARNING);
                    return;
                }

                // Get the desktop path
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);

                // Create a unique filename with timestamp
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName = $"CircleUtility_Benchmark_Results_{timestamp}.txt";
                string filePath = System.IO.Path.Combine(desktopPath, fileName);

                // Create the file content
                using (System.IO.StreamWriter writer = new System.IO.StreamWriter(filePath))
                {
                    // Write header
                    writer.WriteLine("=======================================================");
                    writer.WriteLine("           THE CIRCLE UTILITY BENCHMARK RESULTS        ");
                    writer.WriteLine("=======================================================");
                    writer.WriteLine($"Exported on: {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                    writer.WriteLine();

                    // Write system information
                    writer.WriteLine("SYSTEM INFORMATION");
                    writer.WriteLine("-------------------------------------------------------");
                    writer.WriteLine(GetSystemInfo());
                    writer.WriteLine();

                    // Write benchmark results
                    writer.WriteLine("BENCHMARK RESULTS");
                    writer.WriteLine("-------------------------------------------------------");
                    writer.WriteLine(string.Format("{0,-20} {1,-10} {2,-10} {3,-20} {4,-30}",
                        "Benchmark", "Score", "Unit", "Date/Time", "Notes"));
                    writer.WriteLine(string.Format("{0,-20} {1,-10} {2,-10} {3,-20} {4,-30}",
                        "--------------------", "----------", "----------", "--------------------", "------------------------------"));

                    foreach (var result in BenchmarkResults)
                    {
                        writer.WriteLine(string.Format("{0,-20} {1,-10:F2} {2,-10} {3,-20} {4,-30}",
                            result.Name,
                            result.Score,
                            result.Unit,
                            result.DateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                            result.Notes));
                    }

                    writer.WriteLine();
                    writer.WriteLine("=======================================================");
                    writer.WriteLine("                  END OF REPORT                        ");
                    writer.WriteLine("=======================================================");
                }

                // Update status
                BenchmarkStatus = $"Benchmark results exported to {fileName} on your desktop";
                _logger.Log($"Benchmark results exported to {filePath}", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                BenchmarkStatus = $"Error exporting benchmark results: {ex.Message}";
                _logger.Log($"Error exporting benchmark results: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets system information for the benchmark report
        /// </summary>
        /// <returns>A string containing system information</returns>
        private string GetSystemInfo()
        {
            try
            {
                // Get basic system information
                string osVersion = Environment.OSVersion.ToString();
                string processorCount = Environment.ProcessorCount.ToString();
                string machineName = Environment.MachineName;
                string systemDirectory = Environment.SystemDirectory;
                string dotNetVersion = Environment.Version.ToString();

                // Format the information
                return $"OS Version: {osVersion}\n" +
                       $"Processor Count: {processorCount}\n" +
                       $"Machine Name: {machineName}\n" +
                       $"System Directory: {systemDirectory}\n" +
                       $".NET Version: {dotNetVersion}";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting system information: {ex.Message}", LogLevel.ERROR);
                return "System information not available";
            }
        }

        /// <summary>
        /// Gets the unit for a benchmark
        /// </summary>
        /// <param name="benchmarkName">The name of the benchmark</param>
        /// <returns>The unit for the benchmark</returns>
        private string GetBenchmarkUnit(string benchmarkName)
        {
            switch (benchmarkName)
            {
                case "CPU Performance":
                    return "MIPS";
                case "Memory Performance":
                    return "MB/s";
                case "Disk Performance":
                    return "MB/s";
                case "GPU Performance":
                    return "FPS";
                case "Network Latency":
                    return "ms";
                case "Input Latency":
                    return "ms";
                case "System Responsiveness":
                    return "score";
                case "Full System Benchmark":
                    return "score";
                default:
                    return "points";
            }
        }
    }
}


