[{"Name": "AMD Anti-Lag Optimization", "Description": "Enables AMD Anti-Lag for reduced input latency in games. This technology reduces the time between your mouse/keyboard input and the corresponding action on screen.", "Category": "GPU Latency Optimization", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "KMD_EnableAntiLag", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 50, "StabilityImpact": 95, "PowerConsumptionImpact": 60, "ThermalImpact": 55, "InputLatencyImpact": 90, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "AMD Anti-Lag works by limiting the number of frames queued by the CPU, similar to NVIDIA's Low Latency mode. It's particularly effective in GPU-bound scenarios and can reduce input lag by up to 30% in supported games.", "RevertRegistryValue": 0}, {"Name": "AMD Performance Power State", "Description": "Sets the AMD GPU power state to 'Performance' for optimal gaming performance and reduced input latency.", "Category": "GPU Power Management", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "PowerState", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 75, "StabilityImpact": 95, "PowerConsumptionImpact": 80, "ThermalImpact": 75, "InputLatencyImpact": 80, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization ensures your GPU always runs at its highest performance state, which reduces input latency and provides more consistent frame rates. The downside is slightly higher power consumption and heat output.", "RevertRegistryValue": 0}, {"Name": "AMD Texture Filtering Quality Optimization", "Description": "Sets texture filtering quality to 'Performance' for better FPS at the cost of slightly reduced texture quality.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "TextureFilteringQuality", "RegistryValue": 2, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 100, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 60, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization reduces the quality of texture filtering to improve performance. The visual difference is minimal in most games, but the performance gain can be significant, especially at higher resolutions.", "RevertRegistryValue": 0}, {"Name": "AMD Tessellation Optimization", "Description": "Sets tessellation mode to 'AMD Optimized' to improve performance in games that use heavy tessellation.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "TessellationMode", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 70, "StabilityImpact": 100, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 55, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "AMD Optimized tessellation can significantly improve performance in games that use heavy tessellation, with minimal impact on visual quality. This is particularly beneficial for older AMD GPUs.", "RevertRegistryValue": 0}, {"Name": "AMD Smart Access Memory Optimization", "Description": "Optimizes Smart Access Memory (SAM) settings for compatible AMD GPUs and CPUs to improve performance in supported games.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "EnableSAM", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 95, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 55, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Smart Access Memory (AMD's implementation of Resizable BAR) allows the CPU to access the full GPU memory at once, rather than in 256MB chunks. This can improve performance in supported games. This optimization only works if you have a compatible AMD CPU and GPU, and it's enabled in BIOS.", "RevertRegistryValue": 0}, {"Name": "AMD RDNA 2 Power Tuning", "Description": "Optimizes power settings for AMD RDNA 2 GPUs (RX 6000 series) to balance performance and efficiency.", "Category": "GPU Power Management", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": "RX 6.*", "SpecificModels": ["RX 6500", "RX 6500 XT", "RX 6600", "RX 6600 XT", "RX 6650 XT", "RX 6700", "RX 6700 XT", "RX 6750 XT", "RX 6800", "RX 6800 XT", "RX 6900", "RX 6900 XT", "RX 6950 XT"], "Method": 2, "Command": "amdpowerplay.exe", "Arguments": "--power-limit=103", "PerformanceImpact": 70, "StabilityImpact": 85, "PowerConsumptionImpact": 75, "ThermalImpact": 75, "InputLatencyImpact": 65, "Risk": 1, "WarningMessage": "This optimization slightly increases power consumption and heat output. It's designed specifically for RX 6000 series GPUs and uses RDNA 2 architecture-specific optimizations.", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization is specifically designed for the RDNA 2 architecture in RX 6000 series GPUs. It increases the power limit to 103% of the default value, which allows the GPU to maintain higher clock speeds for longer periods.", "RevertCommand": "amdpowerplay.exe", "RevertArguments": "--power-limit=100"}, {"Name": "AMD RDNA 3 Optimized Power Management", "Description": "Optimizes power management for AMD RDNA 3 GPUs (RX 7000 series) to balance performance and efficiency.", "Category": "GPU Power Management", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": "RX 7.*", "SpecificModels": ["RX 7600", "RX 7700", "RX 7700 XT", "RX 7800", "RX 7800 XT", "RX 7900", "RX 7900 XT", "RX 7900 XTX"], "Method": 4, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "RDNA3PowerMode", "RegistryValue": 2, "RegistryValueKind": "DWord", "Command": "amdpowerplay.exe", "Arguments": "--power-limit=102", "PerformanceImpact": 75, "StabilityImpact": 90, "PowerConsumptionImpact": 70, "ThermalImpact": 70, "InputLatencyImpact": 70, "Risk": 1, "WarningMessage": "This optimization slightly increases power consumption and heat output. It's designed specifically for RX 7000 series GPUs and uses RDNA 3 architecture-specific optimizations.", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization is specifically designed for the RDNA 3 architecture in RX 7000 series GPUs. It balances performance and efficiency better than the generic 'Performance' setting.", "RevertRegistryValue": 0, "RevertCommand": "amdpowerplay.exe", "RevertArguments": "--power-limit=100"}, {"Name": "AMD Infinity Cache Optimization", "Description": "Optimizes Infinity Cache settings for AMD RDNA 2 and RDNA 3 GPUs to improve performance and reduce latency.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "AMD", "ModelPattern": "RX [67].*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "InfinityCacheOptimization", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 95, "PowerConsumptionImpact": 55, "ThermalImpact": 55, "InputLatencyImpact": 70, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Infinity Cache is a high-speed cache in RDNA 2 and RDNA 3 GPUs that reduces memory latency and improves performance. This optimization ensures it's properly configured for gaming workloads.", "RevertRegistryValue": 0}]