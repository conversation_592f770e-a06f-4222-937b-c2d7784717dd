// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for handling background processing
    /// </summary>
    public class BackgroundProcessingService : IDisposable
    {
        private static BackgroundProcessingService _instance;
        private readonly LoggingService _logger;
        private readonly ConcurrentDictionary<string, BackgroundTask> _tasks;
        private readonly ConcurrentDictionary<string, CancellationTokenSource> _cancellationTokens;
        private readonly SemaphoreSlim _semaphore;
        private readonly int _maxConcurrentTasks;

        // Task queues by priority
        private readonly ConcurrentQueue<string> _criticalPriorityQueue;
        private readonly ConcurrentQueue<string> _highPriorityQueue;
        private readonly ConcurrentQueue<string> _normalPriorityQueue;
        private readonly ConcurrentQueue<string> _lowPriorityQueue;

        // Task scheduler
        private readonly Timer _taskSchedulerTimer;
        private readonly TimeSpan _taskSchedulerInterval = TimeSpan.FromMilliseconds(500);
        private readonly object _schedulerLock = new object();
        private bool _isSchedulerRunning;

        /// <summary>
        /// Event raised when a task is started
        /// </summary>
        public event EventHandler<BackgroundTaskEventArgs> TaskStarted;

        /// <summary>
        /// Event raised when a task is completed
        /// </summary>
        public event EventHandler<BackgroundTaskEventArgs> TaskCompleted;

        /// <summary>
        /// Event raised when a task fails
        /// </summary>
        public event EventHandler<BackgroundTaskEventArgs> TaskFailed;

        /// <summary>
        /// Event raised when a task is cancelled
        /// </summary>
        public event EventHandler<BackgroundTaskEventArgs> TaskCancelled;

        /// <summary>
        /// Event raised when a task's progress is updated
        /// </summary>
        public event EventHandler<BackgroundTaskProgressEventArgs> TaskProgressUpdated;

        /// <summary>
        /// Initializes a new instance of the BackgroundProcessingService class
        /// </summary>
        private BackgroundProcessingService()
        {
            _logger = LoggingService.Instance;
            _tasks = new ConcurrentDictionary<string, BackgroundTask>();
            _cancellationTokens = new ConcurrentDictionary<string, CancellationTokenSource>();
            _maxConcurrentTasks = Environment.ProcessorCount;
            _semaphore = new SemaphoreSlim(_maxConcurrentTasks, _maxConcurrentTasks);

            // Initialize priority queues
            _criticalPriorityQueue = new ConcurrentQueue<string>();
            _highPriorityQueue = new ConcurrentQueue<string>();
            _normalPriorityQueue = new ConcurrentQueue<string>();
            _lowPriorityQueue = new ConcurrentQueue<string>();

            // Initialize task scheduler
            _isSchedulerRunning = false;
            _taskSchedulerTimer = new Timer(ProcessTaskQueues, null, _taskSchedulerInterval, _taskSchedulerInterval);

            _logger.Log("Background processing service initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Processes task queues based on priority
        /// </summary>
        private void ProcessTaskQueues(object state)
        {
            // Prevent concurrent execution
            if (_isSchedulerRunning)
            {
                return;
            }

            lock (_schedulerLock)
            {
                if (_isSchedulerRunning)
                {
                    return;
                }

                _isSchedulerRunning = true;

                try
                {
                    // Process queues in priority order
                    ProcessQueue(_criticalPriorityQueue);
                    ProcessQueue(_highPriorityQueue);
                    ProcessQueue(_normalPriorityQueue);
                    ProcessQueue(_lowPriorityQueue);
                }
                finally
                {
                    _isSchedulerRunning = false;
                }
            }
        }

        /// <summary>
        /// Processes a single task queue
        /// </summary>
        private void ProcessQueue(ConcurrentQueue<string> queue)
        {
            // Check if we can acquire a semaphore slot
            if (_semaphore.CurrentCount == 0)
            {
                return;
            }

            // Try to dequeue a task
            if (queue.TryDequeue(out string taskId))
            {
                if (_tasks.TryGetValue(taskId, out BackgroundTask task) &&
                    _cancellationTokens.TryGetValue(taskId, out CancellationTokenSource cancellationTokenSource))
                {
                    // Start the task
                    Task.Run(async () =>
                    {
                        try
                        {
                            // Wait for a semaphore slot
                            await _semaphore.WaitAsync(cancellationTokenSource.Token);

                            // Update task status
                            task.Status = BackgroundTaskStatus.Running;
                            task.StartDate = DateTime.Now;
                            OnTaskStarted(task);

                            // Create progress reporter
                            Progress<double> progress = new Progress<double>(p =>
                            {
                                task.Progress = p;
                                OnTaskProgressUpdated(task, p);
                            });

                            // Execute the task action
                            await task.Action(cancellationTokenSource.Token, progress);

                            // Update task status
                            task.Status = BackgroundTaskStatus.Completed;
                            task.EndDate = DateTime.Now;
                            task.Progress = 100;
                            OnTaskCompleted(task);
                        }
                        catch (OperationCanceledException)
                        {
                            // Task was cancelled
                            task.Status = BackgroundTaskStatus.Cancelled;
                            task.EndDate = DateTime.Now;
                            OnTaskCancelled(task);
                        }
                        catch (Exception ex)
                        {
                            // Task failed
                            task.Status = BackgroundTaskStatus.Failed;
                            task.EndDate = DateTime.Now;
                            task.ErrorMessage = ex.Message;
                            OnTaskFailed(task);
                        }
                        finally
                        {
                            // Release the semaphore
                            _semaphore.Release();
                        }
                    });
                }
            }
        }

        /// <summary>
        /// Gets the singleton instance of the background processing service
        /// </summary>
        public static BackgroundProcessingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new BackgroundProcessingService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets all background tasks
        /// </summary>
        public IEnumerable<BackgroundTask> Tasks => _tasks.Values;

        /// <summary>
        /// Gets the number of active tasks
        /// </summary>
        public int ActiveTaskCount => _tasks.Count;

        /// <summary>
        /// Gets the maximum number of concurrent tasks
        /// </summary>
        public int MaxConcurrentTasks => _maxConcurrentTasks;

        /// <summary>
        /// Starts a new background task
        /// </summary>
        /// <param name="name">The task name</param>
        /// <param name="description">The task description</param>
        /// <param name="action">The action to perform</param>
        /// <param name="priority">The task priority</param>
        /// <param name="cpuUsageLimit">CPU usage limit (0-100, 0 means no limit)</param>
        /// <param name="memoryUsageLimit">Memory usage limit in MB (0 means no limit)</param>
        /// <returns>The task ID</returns>
        public string StartTask(
            string name,
            string description,
            Func<CancellationToken, IProgress<double>, Task> action,
            TaskPriority priority = TaskPriority.Normal,
            int cpuUsageLimit = 0,
            int memoryUsageLimit = 0)
        {
            try
            {
                string taskId = Guid.NewGuid().ToString();
                CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();

                BackgroundTask task = new BackgroundTask
                {
                    Id = taskId,
                    Name = name,
                    Description = description,
                    Status = BackgroundTaskStatus.Queued,
                    Priority = priority,
                    CreatedDate = DateTime.Now,
                    Progress = 0,
                    Action = action,
                    CpuUsageLimit = cpuUsageLimit,
                    MemoryUsageLimit = memoryUsageLimit
                };

                _tasks[taskId] = task;
                _cancellationTokens[taskId] = cancellationTokenSource;

                // Add task to appropriate queue based on priority
                switch (priority)
                {
                    case TaskPriority.Critical:
                        _criticalPriorityQueue.Enqueue(taskId);
                        _logger.Log($"Critical priority task queued: {name}", LogLevel.INFO);
                        break;
                    case TaskPriority.High:
                        _highPriorityQueue.Enqueue(taskId);
                        _logger.Log($"High priority task queued: {name}", LogLevel.INFO);
                        break;
                    case TaskPriority.Normal:
                        _normalPriorityQueue.Enqueue(taskId);
                        _logger.Log($"Normal priority task queued: {name}", LogLevel.INFO);
                        break;
                    case TaskPriority.Low:
                        _lowPriorityQueue.Enqueue(taskId);
                        _logger.Log($"Low priority task queued: {name}", LogLevel.INFO);
                        break;
                }

                return taskId;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error starting background task: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Cancels a background task
        /// </summary>
        /// <param name="taskId">The task ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool CancelTask(string taskId)
        {
            try
            {
                if (_cancellationTokens.TryGetValue(taskId, out CancellationTokenSource cancellationTokenSource))
                {
                    cancellationTokenSource.Cancel();
                    _logger.Log($"Background task cancelled: {taskId}", LogLevel.INFO);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error cancelling background task: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a background task
        /// </summary>
        /// <param name="taskId">The task ID</param>
        /// <returns>The background task, or null if not found</returns>
        public BackgroundTask GetTask(string taskId)
        {
            _tasks.TryGetValue(taskId, out BackgroundTask task);
            return task;
        }

        /// <summary>
        /// Removes a completed, cancelled, or failed task
        /// </summary>
        /// <param name="taskId">The task ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RemoveTask(string taskId)
        {
            try
            {
                if (_tasks.TryGetValue(taskId, out BackgroundTask task))
                {
                    if (task.Status == BackgroundTaskStatus.Completed || task.Status == BackgroundTaskStatus.Cancelled || task.Status == BackgroundTaskStatus.Failed)
                    {
                        _tasks.TryRemove(taskId, out _);
                        _logger.Log($"Background task removed: {taskId}", LogLevel.INFO);
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error removing background task: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Cancels all background tasks
        /// </summary>
        public void CancelAllTasks()
        {
            try
            {
                foreach (var taskId in _cancellationTokens.Keys)
                {
                    CancelTask(taskId);
                }
                _logger.Log("All background tasks cancelled", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error cancelling all background tasks: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Raises the TaskStarted event
        /// </summary>
        /// <param name="task">The background task</param>
        private void OnTaskStarted(BackgroundTask task)
        {
            TaskStarted?.Invoke(this, new BackgroundTaskEventArgs(task));
        }

        /// <summary>
        /// Raises the TaskCompleted event
        /// </summary>
        /// <param name="task">The background task</param>
        private void OnTaskCompleted(BackgroundTask task)
        {
            TaskCompleted?.Invoke(this, new BackgroundTaskEventArgs(task));
        }

        /// <summary>
        /// Raises the TaskFailed event
        /// </summary>
        /// <param name="task">The background task</param>
        private void OnTaskFailed(BackgroundTask task)
        {
            TaskFailed?.Invoke(this, new BackgroundTaskEventArgs(task));
        }

        /// <summary>
        /// Raises the TaskCancelled event
        /// </summary>
        /// <param name="task">The background task</param>
        private void OnTaskCancelled(BackgroundTask task)
        {
            TaskCancelled?.Invoke(this, new BackgroundTaskEventArgs(task));
        }

        /// <summary>
        /// Raises the TaskProgressUpdated event
        /// </summary>
        /// <param name="task">The background task</param>
        /// <param name="progress">The progress value</param>
        private void OnTaskProgressUpdated(BackgroundTask task, double progress)
        {
            TaskProgressUpdated?.Invoke(this, new BackgroundTaskProgressEventArgs(task, progress));
        }

        /// <summary>
        /// Disposes the background processing service
        /// </summary>
        public void Dispose()
        {
            try
            {
                // Stop the task scheduler timer
                _taskSchedulerTimer?.Dispose();

                // Cancel all running tasks
                CancelAllTasks();

                // Release the semaphore
                _semaphore?.Dispose();

                _logger.Log("Background processing service disposed", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disposing background processing service: {ex.Message}", LogLevel.ERROR);
            }
        }
    }

    /// <summary>
    /// Model for background tasks
    /// </summary>
    public class BackgroundTask
    {
        /// <summary>
        /// Gets or sets the task ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the task name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the task description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the task status
        /// </summary>
        public BackgroundTaskStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the task priority
        /// </summary>
        public TaskPriority Priority { get; set; }

        /// <summary>
        /// Gets or sets the task progress (0-100)
        /// </summary>
        public double Progress { get; set; }

        /// <summary>
        /// Gets or sets the task created date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the task start date
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Gets or sets the task end date
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Gets or sets the task error message
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the task action
        /// </summary>
        public Func<CancellationToken, IProgress<double>, Task> Action { get; set; }

        /// <summary>
        /// Gets or sets the CPU usage limit (0-100, 0 means no limit)
        /// </summary>
        public int CpuUsageLimit { get; set; }

        /// <summary>
        /// Gets or sets the memory usage limit in MB (0 means no limit)
        /// </summary>
        public int MemoryUsageLimit { get; set; }

        /// <summary>
        /// Gets the task duration
        /// </summary>
        public TimeSpan Duration
        {
            get
            {
                if (StartDate == null)
                {
                    return TimeSpan.Zero;
                }

                if (EndDate == null)
                {
                    return DateTime.Now - StartDate.Value;
                }

                return EndDate.Value - StartDate.Value;
            }
        }

        /// <summary>
        /// Gets the formatted task duration
        /// </summary>
        public string FormattedDuration
        {
            get
            {
                TimeSpan duration = Duration;
                if (duration.TotalHours >= 1)
                {
                    return $"{duration.Hours}h {duration.Minutes}m {duration.Seconds}s";
                }
                else if (duration.TotalMinutes >= 1)
                {
                    return $"{duration.Minutes}m {duration.Seconds}s";
                }
                else
                {
                    return $"{duration.Seconds}s";
                }
            }
        }
    }

    /// <summary>
    /// Enum for task priority
    /// </summary>
    public enum TaskPriority
    {
        /// <summary>
        /// Low priority
        /// </summary>
        Low,

        /// <summary>
        /// Normal priority
        /// </summary>
        Normal,

        /// <summary>
        /// High priority
        /// </summary>
        High,

        /// <summary>
        /// Critical priority
        /// </summary>
        Critical
    }

    /// <summary>
    /// Enum for background task status
    /// </summary>
    public enum BackgroundTaskStatus
    {
        /// <summary>
        /// Task is queued
        /// </summary>
        Queued,

        /// <summary>
        /// Task is running
        /// </summary>
        Running,

        /// <summary>
        /// Task is completed
        /// </summary>
        Completed,

        /// <summary>
        /// Task is cancelled
        /// </summary>
        Cancelled,

        /// <summary>
        /// Task has failed
        /// </summary>
        Failed
    }

    /// <summary>
    /// Event arguments for background tasks
    /// </summary>
    public class BackgroundTaskEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the background task
        /// </summary>
        public BackgroundTask Task { get; }

        /// <summary>
        /// Initializes a new instance of the BackgroundTaskEventArgs class
        /// </summary>
        /// <param name="task">The background task</param>
        public BackgroundTaskEventArgs(BackgroundTask task)
        {
            Task = task;
        }
    }

    /// <summary>
    /// Event arguments for background task progress
    /// </summary>
    public class BackgroundTaskProgressEventArgs : BackgroundTaskEventArgs
    {
        /// <summary>
        /// Gets the progress value
        /// </summary>
        public double Progress { get; }

        /// <summary>
        /// Initializes a new instance of the BackgroundTaskProgressEventArgs class
        /// </summary>
        /// <param name="task">The background task</param>
        /// <param name="progress">The progress value</param>
        public BackgroundTaskProgressEventArgs(BackgroundTask task, double progress)
            : base(task)
        {
            Progress = progress;
        }
    }
}
