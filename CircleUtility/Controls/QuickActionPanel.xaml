<UserControl x:Class="CircleUtility.Controls.QuickActionPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="600">

    <UserControl.Resources>
        <!-- Animation for button hover -->
        <Storyboard x:Key="ButtonHoverAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)"
                             From="5" To="15" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.Opacity)"
                             From="0.5" To="0.8" Duration="0:0:0.2"/>
        </Storyboard>

        <!-- Animation for button leave -->
        <Storyboard x:Key="ButtonLeaveAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)"
                             From="15" To="5" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.Opacity)"
                             From="0.8" To="0.5" Duration="0:0:0.2"/>
        </Storyboard>

        <!-- Animation for button click -->
        <Storyboard x:Key="ButtonClickAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                             From="0.95" To="1.0" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                             From="0.95" To="1.0" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Style for quick action buttons -->
        <Style x:Key="QuickActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF050A0F"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <ScaleTransform ScaleX="1" ScaleY="1"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="5" ShadowDepth="0" Color="#FF00C8FF" Opacity="0.5"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="Quick Actions"
                   FontFamily="Consolas"
                   FontSize="16"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,10"/>

        <!-- Quick Action Buttons -->
        <WrapPanel Grid.Row="1" Orientation="Horizontal">
            <!-- Tweak System Button -->
            <Button x:Name="TweakSystemButton"
                    Content="Tweak System"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="TweakSystemButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Apply all input delay tweaks based on your hardware">
                <Button.Resources>
                    <Storyboard x:Key="TweakSystemClickAnimation" Storyboard.TargetName="TweakSystemButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>

            <!-- REVERT ALL TWEAKS Button (Highlighted) -->
            <Button x:Name="RevertTweaksButton"
                    Content="REVERT ALL TWEAKS"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="RevertTweaksButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Revert all optimizations to Windows defaults"
                    BorderBrush="#FFFF5050"
                    Foreground="#FFFF5050">
                <Button.Resources>
                    <Storyboard x:Key="RevertTweaksClickAnimation" Storyboard.TargetName="RevertTweaksButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>

            <!-- Optimize Input Button -->
            <Button x:Name="OptimizeInputButton"
                    Content="Optimize Input"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="OptimizeInputButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Reduce input lag and optimize mouse/keyboard settings">
                <Button.Resources>
                    <Storyboard x:Key="OptimizeInputClickAnimation" Storyboard.TargetName="OptimizeInputButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>

            <!-- Optimize GPU Button -->
            <Button x:Name="OptimizeGpuButton"
                    Content="Optimize GPU"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="OptimizeGpuButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Apply GPU-specific optimizations">
                <Button.Resources>
                    <Storyboard x:Key="OptimizeGpuClickAnimation" Storyboard.TargetName="OptimizeGpuButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>

            <!-- Optimize Network Button -->
            <Button x:Name="OptimizeNetworkButton"
                    Content="Optimize Network"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="OptimizeNetworkButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Apply network optimizations for better connectivity">
                <Button.Resources>
                    <Storyboard x:Key="OptimizeNetworkClickAnimation" Storyboard.TargetName="OptimizeNetworkButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>

            <!-- Clean System Button -->
            <Button x:Name="CleanSystemButton"
                    Content="Clean System"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="CleanSystemButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Clean temporary files and optimize system storage">
                <Button.Resources>
                    <Storyboard x:Key="CleanSystemClickAnimation" Storyboard.TargetName="CleanSystemButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>

            <!-- Optimize Performance Button -->
            <Button x:Name="OptimizePerformanceButton"
                    Content="Optimize Performance"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="OptimizePerformanceButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Apply performance optimizations">
                <Button.Resources>
                    <Storyboard x:Key="OptimizePerformanceClickAnimation" Storyboard.TargetName="OptimizePerformanceButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>

            <!-- Test Notifications Button -->
            <Button x:Name="TestNotificationsButton"
                    Content="Test Notifications"
                    Style="{StaticResource QuickActionButtonStyle}"
                    Click="TestNotificationsButton_Click"
                    MouseEnter="Button_MouseEnter"
                    MouseLeave="Button_MouseLeave"
                    ToolTip="Show test notifications of each type"
                    BorderBrush="#FF00FFFF"
                    Foreground="#FF00FFFF">
                <Button.Resources>
                    <Storyboard x:Key="TestNotificationsClickAnimation" Storyboard.TargetName="TestNotificationsButton">
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.95" To="1.0" Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <ElasticEase EasingMode="EaseOut" Oscillations="1" Springiness="5"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </Button.Resources>
            </Button>
        </WrapPanel>
    </Grid>
</UserControl>
