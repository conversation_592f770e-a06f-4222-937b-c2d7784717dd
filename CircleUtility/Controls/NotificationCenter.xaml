<UserControl x:Class="CircleUtility.Controls.NotificationCenter"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:CircleUtility.Controls"
             xmlns:models="clr-namespace:CircleUtility.Models"
             xmlns:viewmodels="clr-namespace:CircleUtility.ViewModels"
             xmlns:views="clr-namespace:CircleUtility.Views"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="300">

    <UserControl.Resources>
        <!-- Notification item template -->
        <DataTemplate x:Key="NotificationTemplate">
            <Border Background="#FF050A0F"
                    BorderThickness="1"
                    BorderBrush="{Binding SeverityColor}"
                    Margin="0,0,0,5"
                    Padding="10"
                    Opacity="{Binding IsRead, Converter={StaticResource BoolToOpacityConverter}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Severity Icon -->
                    <TextBlock Grid.Row="0" Grid.Column="0" Grid.RowSpan="2"
                               Text="{Binding SeverityIcon}"
                               FontSize="24"
                               Foreground="{Binding SeverityColor}"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>

                    <!-- Title -->
                    <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="{Binding Title}"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   FontWeight="Bold"
                                   Foreground="White"/>

                        <!-- Category Badge -->
                        <Border Background="{Binding SeverityColor}"
                                CornerRadius="3"
                                Padding="3,0"
                                Margin="5,0,0,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding CategoryIcon}"
                                           FontSize="10"
                                           Foreground="White"
                                           VerticalAlignment="Center"
                                           Margin="0,0,3,0"/>
                                <TextBlock Text="{Binding Category}"
                                           FontFamily="Consolas"
                                           FontSize="9"
                                           Foreground="White"/>
                            </StackPanel>
                        </Border>

                        <!-- Unread Indicator -->
                        <Ellipse Width="8"
                                 Height="8"
                                 Fill="#FF00C8FF"
                                 Margin="5,0,0,0"
                                 Visibility="{Binding IsRead, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=true}"/>
                    </StackPanel>

                    <!-- Message -->
                    <TextBlock Grid.Row="1" Grid.Column="1"
                               Text="{Binding Message}"
                               FontFamily="Consolas"
                               FontSize="11"
                               Foreground="White"
                               TextWrapping="Wrap"/>

                    <!-- Timestamp -->
                    <TextBlock Grid.Row="0" Grid.Column="2"
                               Text="{Binding RelativeTime}"
                               FontFamily="Consolas"
                               FontSize="10"
                               Foreground="#FF808080"
                               HorizontalAlignment="Right"
                               ToolTip="{Binding FullFormattedTimestamp}"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="1" Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                        <!-- Mark as Read/Unread Button -->
                        <Button Content="{Binding IsRead, Converter={StaticResource BoolToReadStatusConverter}}"
                                Background="Transparent"
                                BorderThickness="0"
                                Foreground="#FF808080"
                                FontSize="10"
                                Padding="5,0"
                                Margin="0,0,5,0"
                                Command="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:NotificationCenter}}, Path=ToggleReadStatusCommand}"
                                CommandParameter="{Binding}"/>

                        <!-- Dismiss Button -->
                        <Button Content="✕"
                                Background="Transparent"
                                BorderThickness="0"
                                Foreground="White"
                                FontSize="10"
                                Padding="5,0"
                                Command="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:NotificationCenter}}, Path=DismissNotificationCommand}"
                                CommandParameter="{Binding}"/>
                    </StackPanel>

                    <!-- Action Button (if available) -->
                    <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"
                            Content="{Binding ActionText}"
                            Command="{Binding Action}"
                            Visibility="{Binding HasAction, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,5,0,0"
                            Height="25"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="{Binding SeverityColor}"
                            BorderThickness="1"/>

                    <!-- Multiple Actions (if available) -->
                    <ItemsControl Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="3"
                                  ItemsSource="{Binding Actions}"
                                  Visibility="{Binding HasActions, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Margin="0,5,0,0">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Button Content="{Binding Text}"
                                        Command="{Binding Command}"
                                        CommandParameter="{Binding CommandParameter}"
                                        ToolTip="{Binding Tooltip}"
                                        Margin="0,0,0,5"
                                        Height="25"
                                        Background="{Binding IsPrimary, Converter={StaticResource BoolToPrimaryButtonBrushConverter}}"
                                        Foreground="White"
                                        BorderBrush="#FF00C8FF"
                                        BorderThickness="1"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </Grid>
            </Border>
        </DataTemplate>

        <!-- Boolean to Visibility Converter -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Bool to Opacity Converter -->
        <local:BoolToOpacityConverter x:Key="BoolToOpacityConverter"/>

        <!-- Bool to Read Status Converter -->
        <local:BoolToReadStatusConverter x:Key="BoolToReadStatusConverter"/>

        <!-- Bool to Primary Button Brush Converter -->
        <local:BoolToPrimaryButtonBrushConverter x:Key="BoolToPrimaryButtonBrushConverter"/>

        <!-- Empty notifications template -->
        <DataTemplate x:Key="EmptyNotificationsTemplate">
            <Grid Height="100">
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="✓"
                               FontSize="24"
                               Foreground="#FF00C8FF"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="No notifications"
                               FontFamily="Consolas"
                               FontSize="12"
                               Foreground="#FF808080"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>
        </DataTemplate>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Title with Badge -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="NOTIFICATIONS"
                           FontFamily="Consolas"
                           FontSize="14"
                           FontWeight="Bold"
                           Foreground="#FF00C8FF"/>

                <!-- Notification Badge -->
                <local:NotificationBadge x:Name="NotificationBadge"
                                         Count="{Binding UnreadCount}"
                                         Margin="5,0,0,0"
                                         Width="16"
                                         Height="16"
                                         VerticalAlignment="Top"
                                         Visibility="{Binding HasUnreadNotifications, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>

            <!-- Filter Button -->
            <Button Grid.Column="1"
                    Content="FILTER"
                    Command="{Binding ShowFilterMenuCommand}"
                    Height="20"
                    Padding="5,0"
                    Background="#FF001428"
                    Foreground="White"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"
                    Margin="0,0,5,0"/>

            <!-- Mark All Read Button -->
            <Button Grid.Column="2"
                    Content="MARK ALL READ"
                    Command="{Binding MarkAllAsReadCommand}"
                    Height="20"
                    Padding="5,0"
                    Background="#FF001428"
                    Foreground="White"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"
                    Margin="0,0,5,0"
                    Visibility="{Binding HasUnreadNotifications, Converter={StaticResource BooleanToVisibilityConverter}}"/>

            <!-- Clear All Button -->
            <Button Grid.Column="3"
                    Content="CLEAR ALL"
                    Command="{Binding ClearAllNotificationsCommand}"
                    Height="20"
                    Padding="5,0"
                    Background="#FF001428"
                    Foreground="White"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"
                    Visibility="{Binding HasNotifications, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>

        <!-- Notifications List -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Notifications}"
                          ItemTemplate="{StaticResource NotificationTemplate}">
                <ItemsControl.Template>
                    <ControlTemplate TargetType="ItemsControl">
                        <Border BorderThickness="0">
                            <Grid>
                                <!-- Empty state -->
                                <ContentControl Content="{x:Null}"
                                                ContentTemplate="{StaticResource EmptyNotificationsTemplate}"
                                                Visibility="{Binding HasNotifications, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=true}"/>

                                <!-- Items presenter -->
                                <ItemsPresenter Visibility="{Binding HasNotifications, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </ItemsControl.Template>
            </ItemsControl>
        </ScrollViewer>

        <!-- Footer -->
        <Grid Grid.Row="2" Margin="0,5,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Settings Button -->
            <Button Grid.Column="0"
                    Content="SETTINGS"
                    Command="{Binding ShowSettingsCommand}"
                    Height="20"
                    Padding="5,0"
                    Background="#FF001428"
                    Foreground="White"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"/>

            <!-- Notification Count -->
            <TextBlock Grid.Column="2"
                       Text="{Binding NotificationCountText}"
                       FontFamily="Consolas"
                       FontSize="10"
                       Foreground="#FF808080"
                       HorizontalAlignment="Right"/>
        </Grid>
    </Grid>
</UserControl>
