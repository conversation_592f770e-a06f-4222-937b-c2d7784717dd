<UserControl x:Class="CircleUtility.Controls.AnimatedButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Name="AnimatedButtonRoot"
             Height="60" Width="200">
    <UserControl.Resources>
        <!-- Hover Glow -->
        <Storyboard x:Key="GlowAnimation">
            <ColorAnimation Storyboard.TargetName="GlowBorder" 
                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                            To="#FF00FF" Duration="0:0:0.3" />
        </Storyboard>

        <!-- Unhover -->
        <Storyboard x:Key="UnglowAnimation">
            <ColorAnimation Storyboard.TargetName="GlowBorder" 
                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                            To="Transparent" Duration="0:0:0.3" />
        </Storyboard>

        <!-- Click Feedback -->
        <Storyboard x:Key="ClickAnimation">
            <DoubleAnimation Storyboard.TargetName="MainButton" 
                             Storyboard.TargetProperty="RenderTransform.ScaleX" 
                             To="0.95" Duration="0:0:0.1" AutoReverse="True" />
            <DoubleAnimation Storyboard.TargetName="MainButton" 
                             Storyboard.TargetProperty="RenderTransform.ScaleY" 
                             To="0.95" Duration="0:0:0.1" AutoReverse="True" />
        </Storyboard>

        <!-- Success -->
        <Storyboard x:Key="SuccessAnimation">
            <ColorAnimation Storyboard.TargetName="GlowBorder" 
                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                            To="LimeGreen" Duration="0:0:0.4" />
        </Storyboard>

        <!-- Reset -->
        <Storyboard x:Key="ResetAnimation">
            <ColorAnimation Storyboard.TargetName="GlowBorder" 
                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                            To="Transparent" Duration="0:0:0.4" />
        </Storyboard>
    </UserControl.Resources>

    <Grid>
        <Border x:Name="GlowBorder" Background="Transparent" CornerRadius="8" />

        <Button x:Name="MainButton"
                Content="{Binding ButtonText, ElementName=AnimatedButtonRoot}"
                Command="{Binding Command, ElementName=AnimatedButtonRoot}"
                CommandParameter="{Binding CommandParameter, ElementName=AnimatedButtonRoot}"
                Click="MainButton_Click"
                MouseEnter="MainButton_MouseEnter"
                MouseLeave="MainButton_MouseLeave"
                Padding="12,6"
                Background="#222"
                Foreground="White"
                FontSize="16"
                FontWeight="Bold"
                BorderThickness="0"
                Cursor="Hand"
                RenderTransformOrigin="0.5,0.5">
            <Button.RenderTransform>
                <ScaleTransform ScaleX="1" ScaleY="1" />
            </Button.RenderTransform>
        </Button>
    </Grid>
</UserControl>
