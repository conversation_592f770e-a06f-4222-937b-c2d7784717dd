// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Threading.Tasks;
using Microsoft.Win32;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing and optimizing GPU settings
    /// </summary>
    public class GPUManagementService
    {
        private static GPUManagementService _instance;
        private readonly LoggingService _logger;
        private GPUInfo _primaryGPU;
        private List<GPUInfo> _allGPUs;
        private bool _isNvidiaPresent;
        private bool _isAmdPresent;
        private bool _isIntelPresent;
        private bool _isOptimized;

        /// <summary>
        /// Initializes a new instance of the GPUManagementService class
        /// </summary>
        private GPUManagementService()
        {
            _logger = LoggingService.Instance;
            _allGPUs = new List<GPUInfo>();
            DetectGPUs();
        }

        /// <summary>
        /// Gets the singleton instance of the GPU management service
        /// </summary>
        public static GPUManagementService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new GPUManagementService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets the primary GPU
        /// </summary>
        public GPUInfo PrimaryGPU => _primaryGPU;

        /// <summary>
        /// Gets all detected GPUs
        /// </summary>
        public IReadOnlyList<GPUInfo> AllGPUs => _allGPUs;

        /// <summary>
        /// Gets a value indicating whether an NVIDIA GPU is present
        /// </summary>
        public bool IsNvidiaPresent => _isNvidiaPresent;

        /// <summary>
        /// Gets a value indicating whether an AMD GPU is present
        /// </summary>
        public bool IsAmdPresent => _isAmdPresent;

        /// <summary>
        /// Gets a value indicating whether an Intel GPU is present
        /// </summary>
        public bool IsIntelPresent => _isIntelPresent;

        /// <summary>
        /// Gets a value indicating whether GPU settings are optimized
        /// </summary>
        public bool IsOptimized => _isOptimized;

        /// <summary>
        /// Detects all GPUs in the system
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DetectGPUs()
        {
            try
            {
                _logger.Log("Detecting GPUs...", LogLevel.INFO);
                _allGPUs.Clear();
                _isNvidiaPresent = false;
                _isAmdPresent = false;
                _isIntelPresent = false;

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        GPUInfo gpu = new GPUInfo
                        {
                            Name = obj["Name"]?.ToString() ?? "Unknown GPU",
                            DriverVersion = obj["DriverVersion"]?.ToString() ?? "Unknown",
                            VideoMemory = Convert.ToInt64(obj["AdapterRAM"] ?? 0) / (1024 * 1024), // Convert to MB
                            CurrentResolution = $"{obj["CurrentHorizontalResolution"] ?? 0} x {obj["CurrentVerticalResolution"] ?? 0}",
                            RefreshRate = Convert.ToInt32(obj["CurrentRefreshRate"] ?? 0),
                            DeviceID = obj["DeviceID"]?.ToString() ?? "Unknown",
                            Status = obj["Status"]?.ToString() ?? "Unknown"
                        };

                        // Determine GPU vendor
                        string name = gpu.Name.ToLower();
                        if (name.Contains("nvidia"))
                        {
                            gpu.Vendor = GPUVendor.NVIDIA;
                            _isNvidiaPresent = true;
                        }
                        else if (name.Contains("amd") || name.Contains("radeon") || name.Contains("ati"))
                        {
                            gpu.Vendor = GPUVendor.AMD;
                            _isAmdPresent = true;
                        }
                        else if (name.Contains("intel"))
                        {
                            gpu.Vendor = GPUVendor.Intel;
                            _isIntelPresent = true;
                        }
                        else
                        {
                            gpu.Vendor = GPUVendor.Other;
                        }

                        _allGPUs.Add(gpu);
                    }
                }

                // Set primary GPU (first dedicated GPU, or first GPU if no dedicated GPU is found)
                _primaryGPU = _allGPUs.Find(g => g.Vendor == GPUVendor.NVIDIA || g.Vendor == GPUVendor.AMD);
                if (_primaryGPU == null && _allGPUs.Count > 0)
                {
                    _primaryGPU = _allGPUs[0];
                }

                _logger.Log($"Detected {_allGPUs.Count} GPUs", LogLevel.INFO);
                foreach (var gpu in _allGPUs)
                {
                    _logger.Log($"GPU: {gpu.Name}, {gpu.VideoMemory} MB, {gpu.Vendor}", LogLevel.INFO);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting GPUs: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes NVIDIA GPU settings for gaming
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeNvidiaGPU()
        {
            if (!_isNvidiaPresent)
            {
                _logger.Log("No NVIDIA GPU detected", LogLevel.WARNING);
                return false;
            }

            try
            {
                _logger.Log("Optimizing NVIDIA GPU settings...", LogLevel.INFO);

                // Set registry values for NVIDIA control panel
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\NVIDIA Corporation\Global\NvCplApi\Profiles", true))
                {
                    if (key != null)
                    {
                        // These are simplified examples - real implementation would be more complex
                        // Power management mode - Prefer maximum performance
                        SetNvidiaRegistrySetting(key, "PowerMizerMode", 1);
                        
                        // Preferred refresh rate - Highest available
                        SetNvidiaRegistrySetting(key, "RefreshRateOverride", 1);
                        
                        // Threaded optimization - On
                        SetNvidiaRegistrySetting(key, "ThreadedOptimization", 1);
                        
                        // Vertical sync - Off
                        SetNvidiaRegistrySetting(key, "VSyncMode", 0);
                        
                        // Low latency mode - Ultra
                        SetNvidiaRegistrySetting(key, "LowLatencyMode", 2);
                        
                        // Texture filtering quality - Performance
                        SetNvidiaRegistrySetting(key, "TextureFilteringQuality", 0);
                        
                        // Shader cache - On
                        SetNvidiaRegistrySetting(key, "ShaderCache", 1);
                    }
                }

                // In a real implementation, we would also use NVAPI or similar
                _isOptimized = true;
                _logger.Log("NVIDIA GPU settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing NVIDIA GPU settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes AMD GPU settings for gaming
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeAmdGPU()
        {
            if (!_isAmdPresent)
            {
                _logger.Log("No AMD GPU detected", LogLevel.WARNING);
                return false;
            }

            try
            {
                _logger.Log("Optimizing AMD GPU settings...", LogLevel.INFO);

                // Set registry values for AMD settings
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\AMD\CN", true))
                {
                    if (key != null)
                    {
                        // These are simplified examples - real implementation would be more complex
                        // Anti-aliasing mode - Off
                        SetAmdRegistrySetting(key, "AntiAlias", 0);
                        
                        // Anisotropic filtering mode - Performance
                        SetAmdRegistrySetting(key, "AnisotropicMode", 0);
                        
                        // Texture filtering quality - Performance
                        SetAmdRegistrySetting(key, "TextureFilterQuality", 0);
                        
                        // Surface format optimization - On
                        SetAmdRegistrySetting(key, "SurfaceFormatOptimization", 1);
                        
                        // Tessellation mode - Override application settings
                        SetAmdRegistrySetting(key, "TessellationMode", 2);
                        
                        // Maximum tessellation level - 8x
                        SetAmdRegistrySetting(key, "TessellationLevel", 8);
                        
                        // Shader cache - On
                        SetAmdRegistrySetting(key, "ShaderCache", 1);
                    }
                }

                // In a real implementation, we would also use ADL or similar
                _isOptimized = true;
                _logger.Log("AMD GPU settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing AMD GPU settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes Intel GPU settings for gaming
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeIntelGPU()
        {
            if (!_isIntelPresent)
            {
                _logger.Log("No Intel GPU detected", LogLevel.WARNING);
                return false;
            }

            try
            {
                _logger.Log("Optimizing Intel GPU settings...", LogLevel.INFO);

                // Set registry values for Intel settings
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Intel\MediaSDK", true))
                {
                    if (key != null)
                    {
                        // These are simplified examples - real implementation would be more complex
                        // Enable hardware acceleration
                        SetIntelRegistrySetting(key, "EnableHWAcceleration", 1);
                    }
                }

                // In a real implementation, we would also use Intel Graphics Command Center API or similar
                _isOptimized = true;
                _logger.Log("Intel GPU settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing Intel GPU settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes GPU settings for the primary GPU
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizePrimaryGPU()
        {
            if (_primaryGPU == null)
            {
                _logger.Log("No primary GPU detected", LogLevel.WARNING);
                return false;
            }

            try
            {
                _logger.Log($"Optimizing primary GPU ({_primaryGPU.Name})...", LogLevel.INFO);

                bool result = false;
                switch (_primaryGPU.Vendor)
                {
                    case GPUVendor.NVIDIA:
                        result = OptimizeNvidiaGPU();
                        break;
                    case GPUVendor.AMD:
                        result = OptimizeAmdGPU();
                        break;
                    case GPUVendor.Intel:
                        result = OptimizeIntelGPU();
                        break;
                    default:
                        _logger.Log("Unsupported GPU vendor", LogLevel.WARNING);
                        return false;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing primary GPU: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes Windows graphics settings for gaming
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeWindowsGraphicsSettings()
        {
            try
            {
                _logger.Log("Optimizing Windows graphics settings...", LogLevel.INFO);

                // Disable fullscreen optimizations
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"System\GameConfigStore", true))
                {
                    if (key != null)
                    {
                        key.SetValue("GameDVR_DXGIHonorFSEWindowsCompatible", 1, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehavior", 2, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehaviorMode", 2, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_HonorUserFSEBehaviorMode", 1, RegistryValueKind.DWord);
                    }
                }

                // Disable Game DVR
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\GameDVR", true))
                {
                    if (key != null)
                    {
                        key.SetValue("AppCaptureEnabled", 0, RegistryValueKind.DWord);
                    }
                }

                // Set visual effects for performance
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects", true))
                {
                    if (key != null)
                    {
                        key.SetValue("VisualFXSetting", 2, RegistryValueKind.DWord);
                    }
                }

                _logger.Log("Windows graphics settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing Windows graphics settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes shader cache settings for better performance
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeShaderCache()
        {
            try
            {
                _logger.Log("Optimizing shader cache settings...", LogLevel.INFO);

                // Enable shader cache for NVIDIA
                if (_isNvidiaPresent)
                {
                    using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\NVIDIA Corporation\Global\NvCplApi\Profiles", true))
                    {
                        if (key != null)
                        {
                            SetNvidiaRegistrySetting(key, "ShaderCache", 1);
                        }
                    }
                }

                // Enable shader cache for AMD
                if (_isAmdPresent)
                {
                    using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\AMD\CN", true))
                    {
                        if (key != null)
                        {
                            SetAmdRegistrySetting(key, "ShaderCache", 1);
                        }
                    }
                }

                _logger.Log("Shader cache settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing shader cache settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies all GPU optimizations
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool ApplyAllOptimizations()
        {
            try
            {
                _logger.Log("Applying all GPU optimizations...", LogLevel.INFO);

                bool primaryResult = OptimizePrimaryGPU();
                bool windowsResult = OptimizeWindowsGraphicsSettings();
                bool shaderResult = OptimizeShaderCache();

                bool allSuccessful = primaryResult && windowsResult && shaderResult;

                if (allSuccessful)
                {
                    _isOptimized = true;
                    _logger.Log("All GPU optimizations applied successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some GPU optimizations failed to apply", LogLevel.WARNING);
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying GPU optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts all GPU optimizations to default settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertAllOptimizations()
        {
            try
            {
                _logger.Log("Reverting all GPU optimizations...", LogLevel.INFO);

                // Revert NVIDIA settings
                if (_isNvidiaPresent)
                {
                    using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\NVIDIA Corporation\Global\NvCplApi\Profiles", true))
                    {
                        if (key != null)
                        {
                            // Reset to default values
                            SetNvidiaRegistrySetting(key, "PowerMizerMode", 0);
                            SetNvidiaRegistrySetting(key, "RefreshRateOverride", 0);
                            SetNvidiaRegistrySetting(key, "ThreadedOptimization", 0);
                            SetNvidiaRegistrySetting(key, "VSyncMode", 1);
                            SetNvidiaRegistrySetting(key, "LowLatencyMode", 0);
                            SetNvidiaRegistrySetting(key, "TextureFilteringQuality", 1);
                            SetNvidiaRegistrySetting(key, "ShaderCache", 1);
                        }
                    }
                }

                // Revert AMD settings
                if (_isAmdPresent)
                {
                    using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\AMD\CN", true))
                    {
                        if (key != null)
                        {
                            // Reset to default values
                            SetAmdRegistrySetting(key, "AntiAlias", 1);
                            SetAmdRegistrySetting(key, "AnisotropicMode", 1);
                            SetAmdRegistrySetting(key, "TextureFilterQuality", 1);
                            SetAmdRegistrySetting(key, "SurfaceFormatOptimization", 0);
                            SetAmdRegistrySetting(key, "TessellationMode", 0);
                            SetAmdRegistrySetting(key, "TessellationLevel", 64);
                            SetAmdRegistrySetting(key, "ShaderCache", 1);
                        }
                    }
                }

                // Revert Windows graphics settings
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"System\GameConfigStore", true))
                {
                    if (key != null)
                    {
                        key.SetValue("GameDVR_DXGIHonorFSEWindowsCompatible", 0, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehavior", 0, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehaviorMode", 0, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_HonorUserFSEBehaviorMode", 0, RegistryValueKind.DWord);
                    }
                }

                _isOptimized = false;
                _logger.Log("All GPU optimizations reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting GPU optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets GPU temperature and usage information
        /// </summary>
        /// <returns>A dictionary with GPU metrics</returns>
        public async Task<Dictionary<string, double>> GetGPUMetrics()
        {
            try
            {
                _logger.Log("Getting GPU metrics...", LogLevel.INFO);

                // In a real implementation, this would use hardware-specific APIs
                // For now, we'll simulate metrics
                await Task.Delay(500); // Simulate API call

                Dictionary<string, double> metrics = new Dictionary<string, double>();
                Random random = new Random();

                metrics["Temperature"] = 55 + random.Next(0, 20); // 55-75°C
                metrics["Usage"] = random.Next(5, 95); // 5-95%
                metrics["MemoryUsage"] = random.Next(10, 90); // 10-90%
                metrics["FanSpeed"] = random.Next(30, 80); // 30-80%
                metrics["PowerUsage"] = 50 + random.Next(0, 100); // 50-150W
                metrics["CoreClock"] = 1500 + random.Next(0, 500); // 1500-2000MHz
                metrics["MemoryClock"] = 7000 + random.Next(0, 2000); // 7000-9000MHz

                _logger.Log("GPU metrics retrieved successfully", LogLevel.SUCCESS);
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting GPU metrics: {ex.Message}", LogLevel.ERROR);
                return new Dictionary<string, double>();
            }
        }

        private void SetNvidiaRegistrySetting(RegistryKey key, string settingName, int value)
        {
            // In a real implementation, this would set the actual NVIDIA registry values
            // This is a simplified version for demonstration
            key.SetValue(settingName, value, RegistryValueKind.DWord);
        }

        private void SetAmdRegistrySetting(RegistryKey key, string settingName, int value)
        {
            // In a real implementation, this would set the actual AMD registry values
            // This is a simplified version for demonstration
            key.SetValue(settingName, value, RegistryValueKind.DWord);
        }

        private void SetIntelRegistrySetting(RegistryKey key, string settingName, int value)
        {
            // In a real implementation, this would set the actual Intel registry values
            // This is a simplified version for demonstration
            key.SetValue(settingName, value, RegistryValueKind.DWord);
        }
    }
}

