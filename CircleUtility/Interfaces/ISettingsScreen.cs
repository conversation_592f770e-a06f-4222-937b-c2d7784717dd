using System;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Event arguments for settings changed events
    /// </summary>
    public class SettingsChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the old settings
        /// </summary>
        public AppSettings OldSettings { get; }

        /// <summary>
        /// Gets the new settings
        /// </summary>
        public AppSettings NewSettings { get; }

        /// <summary>
        /// Initializes a new instance of the SettingsChangedEventArgs class
        /// </summary>
        /// <param name="oldSettings">The old settings</param>
        /// <param name="newSettings">The new settings</param>
        public SettingsChangedEventArgs(AppSettings oldSettings, AppSettings newSettings)
        {
            OldSettings = oldSettings;
            NewSettings = newSettings;
        }
    }

    /// <summary>
    /// Interface for settings screen
    /// </summary>
    public interface ISettingsScreen : IScreen
    {
        /// <summary>
        /// Event raised when settings are changed
        /// </summary>
        event EventHandler<SettingsChangedEventArgs> SettingsChanged;

        /// <summary>
        /// Loads settings into the UI
        /// </summary>
        /// <param name="settings">The settings to load</param>
        void LoadSettings(AppSettings settings);

        /// <summary>
        /// Gets the current settings from the UI
        /// </summary>
        /// <returns>The current settings</returns>
        AppSettings GetCurrentSettings();

        /// <summary>
        /// Applies the current settings
        /// </summary>
        void ApplySettings();

        /// <summary>
        /// Resets settings to defaults
        /// </summary>
        void ResetToDefaults();
    }
}
