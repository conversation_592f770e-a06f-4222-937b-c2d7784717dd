using System.Windows;

namespace CircleUtility.Views
{
    public partial class UpdateProgressWindow : Window
    {
        public string ProgressMessage
        {
            get => (string)GetValue(ProgressMessageProperty);
            set => SetValue(ProgressMessageProperty, value);
        }

        public static readonly DependencyProperty ProgressMessageProperty =
            DependencyProperty.Register(nameof(ProgressMessage), typeof(string), typeof(UpdateProgressWindow), new PropertyMetadata(string.Empty));

        public double ProgressValue
        {
            get => (double)GetValue(ProgressValueProperty);
            set => SetValue(ProgressValueProperty, value);
        }

        public static readonly DependencyProperty ProgressValueProperty =
            DependencyProperty.Register(nameof(ProgressValue), typeof(double), typeof(UpdateProgressWindow), new PropertyMetadata(0.0));

        public UpdateProgressWindow()
        {
            InitializeComponent();
            DataContext = this;
        }

        public void UpdateProgress(string status, int progress)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = status;
                ProgressBar.Value = progress;
                ProgressText.Text = $"{progress}%";
            });
        }

        public void UpdateDetails(string details)
        {
            Dispatcher.Invoke(() =>
            {
                DetailsText.Text = details;
            });
        }

        public void SetError(string errorMessage)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = "Error";
                DetailsText.Text = errorMessage;
                ProgressBar.Foreground = System.Windows.Media.Brushes.Red;
            });
        }
    }
} 