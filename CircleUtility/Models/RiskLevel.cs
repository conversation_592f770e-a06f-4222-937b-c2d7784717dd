namespace CircleUtility.Models
{
    /// <summary>
    /// Represents the risk level of an operation
    /// </summary>
    public enum RiskLevel
    {
        /// <summary>
        /// No risk
        /// </summary>
        None = 0,

        /// <summary>
        /// Low risk
        /// </summary>
        Low = 1,

        /// <summary>
        /// Medium risk
        /// </summary>
        Medium = 2,

        /// <summary>
        /// High risk
        /// </summary>
        High = 3,

        /// <summary>
        /// Critical risk
        /// </summary>
        Critical = 4,

        /// <summary>
        /// Safe operation
        /// </summary>
        Safe = 5,

        /// <summary>
        /// Very high risk
        /// </summary>
        VeryHigh = 6
    }
}

