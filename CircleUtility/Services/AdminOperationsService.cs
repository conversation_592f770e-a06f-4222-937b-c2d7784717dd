// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Diagnostics;
using System.IO;
using System.Security.Principal;
using System.Threading.Tasks;
using System.Windows;
using CircleUtility.Models;
using Microsoft.Win32;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for handling administrative operations
    /// </summary>
    public class AdminOperationsService
    {
        private static AdminOperationsService _instance;
        private readonly LoggingService _logger;
        // Security service removed

        /// <summary>
        /// Initializes a new instance of the AdminOperationsService class
        /// </summary>
        private AdminOperationsService()
        {
            _logger = LoggingService.Instance;
            // Security service removed
        }

        /// <summary>
        /// Gets the singleton instance of the admin operations service
        /// </summary>
        public static AdminOperationsService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new AdminOperationsService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Checks if the current process is running with administrator privileges
        /// </summary>
        /// <returns>True if running as administrator, false otherwise</returns>
        public bool IsRunningAsAdmin()
        {
            try
            {
                WindowsIdentity identity = WindowsIdentity.GetCurrent();
                WindowsPrincipal principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking admin privileges: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restarts the application with administrator privileges
        /// </summary>
        /// <param name="commandLineArgs">Optional command line arguments to pass to the new process</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RestartAsAdmin(string commandLineArgs = "")
        {
            try
            {
                // Check if already running as admin
                if (IsRunningAsAdmin())
                {
                    return true;
                }

                // Get the executable path
                string exePath = Process.GetCurrentProcess().MainModule.FileName;

                // Create process start info
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    Arguments = commandLineArgs,
                    UseShellExecute = true,
                    Verb = "runas" // Run as administrator
                };

                // Start the process
                Process.Start(startInfo);

                // Exit the current process
                Environment.Exit(0);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restarting as admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Executes a function that requires administrator privileges
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="showPrompt">Whether to show a UAC prompt if not running as admin</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ExecuteAsAdmin(Action action, bool showPrompt = true)
        {
            try
            {
                // Check if running as admin
                if (IsRunningAsAdmin())
                {
                    // Execute the action
                    action();
                    return true;
                }
                else if (showPrompt)
                {
                    // Show UAC prompt
                    MessageBoxResult result = MessageBox.Show(
                        "This operation requires administrator privileges. Do you want to restart the application as administrator?",
                        "Administrator Privileges Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // Restart as admin
                        return RestartAsAdmin();
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error executing as admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Executes a function that requires administrator privileges asynchronously
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="showPrompt">Whether to show a UAC prompt if not running as admin</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public async Task<bool> ExecuteAsAdminAsync(Func<Task> action, bool showPrompt = true)
        {
            try
            {
                // Check if running as admin
                if (IsRunningAsAdmin())
                {
                    // Execute the action
                    await action();
                    return true;
                }
                else if (showPrompt)
                {
                    // Show UAC prompt
                    MessageBoxResult result = MessageBox.Show(
                        "This operation requires administrator privileges. Do you want to restart the application as administrator?",
                        "Administrator Privileges Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // Restart as admin
                        return RestartAsAdmin();
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error executing as admin asynchronously: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets whether the current process is running with administrator privileges (for compatibility with other code)
        /// </summary>
        public bool IsAdmin => IsRunningAsAdmin();
    }
}

