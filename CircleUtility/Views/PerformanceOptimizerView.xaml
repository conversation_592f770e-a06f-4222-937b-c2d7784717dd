<UserControl x:Class="CircleUtility.Views.PerformanceOptimizerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Views"
             xmlns:controls="clr-namespace:CircleUtility.Controls"
             xmlns:converters="clr-namespace:CircleUtility.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             Background="Black">
    
    <UserControl.Resources>
        <converters:SliderValueToWidthConverter x:Key="SliderValueToWidthConverter"/>
        
        <!-- Glowing section header style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Section container style -->
        <Style x:Key="SectionContainerStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF050A0F"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <!-- Apply button style -->
        <Style x:Key="ApplyButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- Outer glow effect -->
                            <Border x:Name="GlowBorder"
                                    Background="#3300C8FF"
                                    CornerRadius="3"
                                    Margin="-2"
                                    Opacity="0">
                                <Border.Effect>
                                    <BlurEffect Radius="10"/>
                                </Border.Effect>
                            </Border>
                            
                            <!-- Button background -->
                            <Border x:Name="ButtonBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="2">
                                
                                <!-- Button content -->
                                <ContentPresenter HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        
                        <!-- Visual states -->
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="GlowBorder" Property="Opacity" Value="1"/>
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF002A4E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF003A6B"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5"/>
                                <Setter Property="Foreground" Value="#FF666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <TextBlock Grid.Row="0"
                       Text="Performance Optimizer"
                       FontFamily="Consolas"
                       FontSize="24"
                       FontWeight="Bold"
                       Foreground="#FF00C8FF"
                       Margin="0,0,0,20">
                <TextBlock.Effect>
                    <DropShadowEffect Color="#FF00C8FF" BlurRadius="15" ShadowDepth="0" Opacity="0.7"/>
                </TextBlock.Effect>
            </TextBlock>
            
            <!-- Description -->
            <TextBlock Grid.Row="1"
                       Text="Optimize your system performance with interactive sliders. Adjust each component individually or use the preset profiles for quick optimization."
                       FontFamily="Consolas"
                       FontSize="12"
                       Foreground="White"
                       TextWrapping="Wrap"
                       Margin="0,0,0,20"/>
            
            <!-- System Performance Section -->
            <Border Grid.Row="2"
                    Style="{StaticResource SectionContainerStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Section Header -->
                    <TextBlock Grid.Row="0"
                               Text="System Performance"
                               Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <!-- Performance Slider -->
                    <controls:AnimatedPerformanceSlider Grid.Row="1"
                                                       x:Name="SystemPerformanceSlider"
                                                       Title="System Performance"
                                                       ComponentType="System"
                                                       Value="50"
                                                       Margin="0,10,0,0"/>
                </Grid>
            </Border>
            
            <!-- Component Performance Section -->
            <Border Grid.Row="3"
                    Style="{StaticResource SectionContainerStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Section Header -->
                    <TextBlock Grid.Row="0"
                               Text="Component Performance"
                               Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <!-- CPU Performance Slider -->
                    <controls:AnimatedPerformanceSlider Grid.Row="1"
                                                       x:Name="CpuPerformanceSlider"
                                                       Title="CPU Performance"
                                                       ComponentType="CPU"
                                                       Value="50"
                                                       Margin="0,10,0,15"/>
                    
                    <!-- GPU Performance Slider -->
                    <controls:AnimatedPerformanceSlider Grid.Row="2"
                                                       x:Name="GpuPerformanceSlider"
                                                       Title="GPU Performance"
                                                       ComponentType="GPU"
                                                       Value="75"
                                                       Margin="0,0,0,15"/>
                    
                    <!-- Memory Performance Slider -->
                    <controls:AnimatedPerformanceSlider Grid.Row="3"
                                                       x:Name="MemoryPerformanceSlider"
                                                       Title="Memory Performance"
                                                       ComponentType="Memory"
                                                       Value="50"
                                                       Margin="0,0,0,15"/>
                    
                    <!-- Storage Performance Slider -->
                    <controls:AnimatedPerformanceSlider Grid.Row="4"
                                                       x:Name="StoragePerformanceSlider"
                                                       Title="Storage Performance"
                                                       ComponentType="Storage"
                                                       Value="25"
                                                       Margin="0,0,0,0"/>
                </Grid>
            </Border>
            
            <!-- Apply Button Section -->
            <Border Grid.Row="4"
                    Style="{StaticResource SectionContainerStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Description -->
                    <TextBlock Grid.Column="0"
                               Text="Apply all performance settings to optimize your system according to the selected profiles. This will adjust power plans, process priorities, and hardware settings."
                               FontFamily="Consolas"
                               FontSize="12"
                               Foreground="White"
                               TextWrapping="Wrap"
                               VerticalAlignment="Center"/>
                    
                    <!-- Apply Button -->
                    <Button Grid.Column="1"
                            x:Name="ApplyButton"
                            Content="APPLY SETTINGS"
                            Style="{StaticResource ApplyButtonStyle}"
                            Width="180"
                            Height="40"
                            Margin="20,0,0,0"
                            Click="ApplyButton_Click"/>
                </Grid>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
