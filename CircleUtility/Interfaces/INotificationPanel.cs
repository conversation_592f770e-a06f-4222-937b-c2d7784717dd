using System;
using System.Collections.Generic;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for notification panel
    /// </summary>
    public interface INotificationPanel : IUIComponent
    {
        /// <summary>
        /// Adds a notification to the panel
        /// </summary>
        /// <param name="notification">The notification to add</param>
        void AddNotification(SystemNotification notification);

        /// <summary>
        /// Clears all notifications
        /// </summary>
        void ClearNotifications();

        /// <summary>
        /// Gets the number of unread notifications
        /// </summary>
        int UnreadCount { get; }

        /// <summary>
        /// Marks all notifications as read
        /// </summary>
        void MarkAllAsRead();

        /// <summary>
        /// Gets all notifications
        /// </summary>
        IEnumerable<SystemNotification> Notifications { get; }

        /// <summary>
        /// Dismisses a notification
        /// </summary>
        /// <param name="notification">The notification to dismiss</param>
        void DismissNotification(SystemNotification notification);

        /// <summary>
        /// Event raised when a notification is added
        /// </summary>
        event EventHandler<SystemNotification> NotificationAdded;

        /// <summary>
        /// Event raised when a notification is dismissed
        /// </summary>
        event EventHandler<SystemNotification> NotificationDismissed;
    }
}
