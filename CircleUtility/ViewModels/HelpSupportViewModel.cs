using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Interfaces;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// ViewModel for the Help and Support view
    /// </summary>
    public class HelpSupportViewModel : ViewModelBase
    {
        private readonly ILoggerService _logger;
        private readonly IDocumentationService _documentationService;
        private bool _isLoading;
        private string _statusMessage;
        private bool _isStatusVisible;
        private ObservableCollection<HelpItem> _helpItems;
        private string _searchText;

        /// <summary>
        /// Gets or sets a value indicating whether the view is loading
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status message is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets the help items
        /// </summary>
        public ObservableCollection<HelpItem> HelpItems
        {
            get => _helpItems;
            set => SetProperty(ref _helpItems, value);
        }

        /// <summary>
        /// Gets or sets the search text
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// Gets the command to search help items
        /// </summary>
        public ICommand SearchCommand { get; }

        /// <summary>
        /// Gets the command to open a help item
        /// </summary>
        public ICommand OpenHelpItemCommand { get; }

        /// <summary>
        /// Gets the command to refresh help items
        /// </summary>
        public ICommand RefreshCommand { get; }
        private string _feedbackText;
        private bool _isAnonymous;

        /// <summary>
        /// Gets or sets the feedback text
        /// </summary>
        public string FeedbackText
        {
            get => _feedbackText;
            set => SetProperty(ref _feedbackText, value);
        }

        /// <summary>
        /// Gets or sets whether feedback is anonymous
        /// </summary>
        public bool IsAnonymous
        {
            get => _isAnonymous;
            set => SetProperty(ref _isAnonymous, value);
        }

        /// <summary>
        /// Gets the command to send feedback
        /// </summary>
        public ICommand SendFeedbackCommand { get; }

        /// <summary>
        /// Initializes a new instance of the HelpSupportViewModel class
        /// </summary>
        public HelpSupportViewModel(ILoggerService logger, IHardwareOptimizationService hardwareOptimizationService = null, IRevertTweaksService revertTweaksService = null, IConfigurationService configurationService = null, ITweakChangeTracker tweakChangeTracker = null, IDiscordService discordService = null, IUserTrackingService userTrackingService = null, INotificationService notificationService = null) : base(logger)
        {
            _logger = logger;
            _documentationService = DocumentationService.Instance;
            _helpItems = new ObservableCollection<HelpItem>();

            // Initialize commands
            SearchCommand = new RelayCommand(SearchHelpItems);
            OpenHelpItemCommand = new RelayCommand<HelpItem>(OpenHelpItem);
            RefreshCommand = new RelayCommand(LoadHelpItems);
            SendFeedbackCommand = new RelayCommand(SendFeedback);

            // Load help items
            LoadHelpItems();
        }

        /// <summary>
        /// Loads the help items
        /// </summary>
        private void LoadHelpItems()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Loading help items...";
                IsStatusVisible = true;

                // Clear existing items
                HelpItems.Clear();

                // Add sample help items
                HelpItems.Add(new HelpItem { Title = "Getting Started", Description = "Learn how to use CircleUtility", Category = "General" });
                HelpItems.Add(new HelpItem { Title = "Hardware Optimization", Description = "Optimize your hardware settings", Category = "Performance" });
                HelpItems.Add(new HelpItem { Title = "Troubleshooting", Description = "Common issues and solutions", Category = "Support" });
                HelpItems.Add(new HelpItem { Title = "FAQ", Description = "Frequently asked questions", Category = "General" });

                StatusMessage = $"Loaded {HelpItems.Count} help items.";
                IsStatusVisible = true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error loading help items: {ex.Message}", ex);
                StatusMessage = "Error loading help items. Please try again.";
                IsStatusVisible = true;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Searches help items
        /// </summary>
        private void SearchHelpItems()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    LoadHelpItems();
                    return;
                }

                StatusMessage = $"Searching for '{SearchText}'...";
                IsStatusVisible = true;

                // Simple search implementation
                var filteredItems = new ObservableCollection<HelpItem>();
                foreach (var item in HelpItems)
                {
                    if (item.Title.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        item.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
                    {
                        filteredItems.Add(item);
                    }
                }

                HelpItems.Clear();
                foreach (var item in filteredItems)
                {
                    HelpItems.Add(item);
                }

                StatusMessage = $"Found {HelpItems.Count} matching items.";
                IsStatusVisible = true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error searching help items: {ex.Message}", ex);
                StatusMessage = "Error searching help items. Please try again.";
                IsStatusVisible = true;
            }
        }

        /// <summary>
        /// Opens a help item
        /// </summary>
        /// <param name="helpItem">The help item to open</param>
        private void OpenHelpItem(HelpItem helpItem)
        {
            if (helpItem == null) return;

            try
            {
                _logger.LogInfo($"Opening help item: {helpItem.Title}");
                StatusMessage = $"Opening {helpItem.Title}...";
                IsStatusVisible = true;

                // Simulate opening help item
                System.Threading.Thread.Sleep(500);

                StatusMessage = $"Opened {helpItem.Title}.";
                IsStatusVisible = true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error opening help item {helpItem.Title}: {ex.Message}", ex);
                StatusMessage = $"Error opening {helpItem.Title}. Please try again.";
                IsStatusVisible = true;
            }
        }
    }

    /// <summary>
    /// Represents a help item
    /// </summary>
    public class HelpItem
    {
        /// <summary>
        /// Gets or sets the help item title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the help item description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the help item category
        /// </summary>
        public string Category { get; set; }
    }


        /// <summary>
        /// Sends feedback
        /// </summary>
        private void SendFeedback()
        {
            // Send feedback implementation
        }
    }
