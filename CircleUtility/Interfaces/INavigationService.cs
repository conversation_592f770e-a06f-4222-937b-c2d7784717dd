using System;
using System.Collections.Generic;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Event arguments for navigation events
    /// </summary>
    public class NavigationEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the source screen
        /// </summary>
        public IScreen SourceScreen { get; }

        /// <summary>
        /// Gets the target screen
        /// </summary>
        public IScreen TargetScreen { get; }

        /// <summary>
        /// Gets the navigation parameters
        /// </summary>
        public Dictionary<string, object> Parameters { get; }

        /// <summary>
        /// Initializes a new instance of the NavigationEventArgs class
        /// </summary>
        /// <param name="sourceScreen">The source screen</param>
        /// <param name="targetScreen">The target screen</param>
        /// <param name="parameters">The navigation parameters</param>
        public NavigationEventArgs(IScreen sourceScreen, IScreen targetScreen, Dictionary<string, object> parameters = null)
        {
            SourceScreen = sourceScreen;
            TargetScreen = targetScreen;
            Parameters = parameters ?? new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// Interface for navigation services
    /// </summary>
    public interface INavigationService
    {
        /// <summary>
        /// Navigates to a screen
        /// </summary>
        /// <typeparam name="T">The screen type</typeparam>
        /// <param name="parameters">The navigation parameters</param>
        void NavigateTo<T>(Dictionary<string, object> parameters = null) where T : IScreen;

        /// <summary>
        /// Navigates to a screen
        /// </summary>
        /// <param name="screenType">The screen type</param>
        /// <param name="parameters">The navigation parameters</param>
        void NavigateTo(Type screenType, Dictionary<string, object> parameters = null);

        /// <summary>
        /// Navigates back to the previous screen
        /// </summary>
        void NavigateBack();

        /// <summary>
        /// Gets the current screen
        /// </summary>
        IScreen CurrentScreen { get; }

        /// <summary>
        /// Gets the navigation history
        /// </summary>
        IReadOnlyList<IScreen> NavigationHistory { get; }

        /// <summary>
        /// Clears the navigation history
        /// </summary>
        void ClearHistory();

        /// <summary>
        /// Event raised when navigation occurs
        /// </summary>
        event EventHandler<NavigationEventArgs> Navigated;

        /// <summary>
        /// Event raised before navigation occurs
        /// </summary>
        event EventHandler<NavigationEventArgs> Navigating;

        /// <summary>
        /// Gets a value indicating whether navigation back is possible
        /// </summary>
        bool CanNavigateBack { get; }
    }
}
