using CircleUtility.Commands;
//Created by Arsenal on 5-17-25 12:15PM
using System.Windows.Input;
using CircleUtility.Helpers;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for a menu item
    /// </summary>
    public class MenuItemViewModel : ViewModelBase
    {
        private string _displayName;
        private string _viewName;
        private bool _isSelected;

        /// <summary>
        /// Initializes a new instance of the MenuItemViewModel class
        /// </summary>
        /// <param name="displayName">The display name of the menu item</param>
        /// <param name="viewName">The name of the view to navigate to</param>
        /// <param name="navigateCommand">The command to execute when the menu item is selected</param>
        public MenuItemViewModel(string displayName, string viewName, ICommand navigateCommand)
        {
            DisplayName = displayName;
            ViewName = viewName;
            IsSelected = false;
            Command = new RelayCommand(() => navigateCommand.Execute(ViewName));
        }

        /// <summary>
        /// Gets or sets the display name of the menu item
        /// </summary>
        public string DisplayName
        {
            get => _displayName;
            set => SetProperty(ref _displayName, value);
        }

        /// <summary>
        /// Gets or sets the name of the view to navigate to
        /// </summary>
        public string ViewName
        {
            get => _viewName;
            set => SetProperty(ref _viewName, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the menu item is selected
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// Gets the command to execute when the menu item is selected
        /// </summary>
        public ICommand Command { get; }
    }
}

