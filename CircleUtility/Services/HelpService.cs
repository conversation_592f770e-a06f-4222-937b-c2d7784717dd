// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for providing in-app help and tooltips
    /// </summary>
    public class HelpService
    {
        private static HelpService _instance;
        private readonly LoggingService _logger;
        private readonly DocumentationService _documentationService;
        private readonly Dictionary<string, string> _tooltips;
        private readonly string _helpDirectory;
        private readonly string _tooltipsFilePath;

        /// <summary>
        /// Initializes a new instance of the HelpService class
        /// </summary>
        private HelpService()
        {
            _logger = LoggingService.Instance;
            _documentationService = DocumentationService.Instance;
            _helpDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Help");
            _tooltipsFilePath = Path.Combine(_helpDirectory, "tooltips.json");
            _tooltips = new Dictionary<string, string>();

            // Ensure help directory exists
            if (!Directory.Exists(_helpDirectory))
            {
                Directory.CreateDirectory(_helpDirectory);
            }

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Gets the singleton instance of the help service
        /// </summary>
        public static HelpService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new HelpService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes the help service
        /// </summary>
        private void Initialize()
        {
            try
            {
                // Load tooltips
                LoadTooltips();

                // Create default tooltips if none exist
                if (_tooltips.Count == 0)
                {
                    CreateDefaultTooltips();
                }

                _logger.Log("Help service initialized", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing help service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Loads tooltips from the tooltips file
        /// </summary>
        private void LoadTooltips()
        {
            try
            {
                if (File.Exists(_tooltipsFilePath))
                {
                    string json = File.ReadAllText(_tooltipsFilePath);
                    Dictionary<string, string> tooltips = JsonSerializer.Deserialize<Dictionary<string, string>>(json);

                    if (tooltips != null)
                    {
                        _tooltips.Clear();
                        foreach (var tooltip in tooltips)
                        {
                            _tooltips.Add(tooltip.Key, tooltip.Value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading tooltips: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Creates default tooltips
        /// </summary>
        private void CreateDefaultTooltips()
        {
            try
            {
                // Add default tooltips for main menu
                _tooltips.Add("MainMenu_Dashboard", "View system performance and status");
                _tooltips.Add("MainMenu_QuickActions", "Quick actions for common optimizations");
                _tooltips.Add("MainMenu_GameProfiles", "Optimize your system for specific games");
                _tooltips.Add("MainMenu_SystemTweaks", "Advanced system tweaks for performance");
                _tooltips.Add("MainMenu_NetworkTweaks", "Optimize your network for gaming");
                _tooltips.Add("MainMenu_GPUTweaks", "Optimize your GPU for gaming");
                _tooltips.Add("MainMenu_CPUTweaks", "Optimize your CPU for gaming");
                _tooltips.Add("MainMenu_ControllerTweaks", "Optimize your controller for gaming");
                _tooltips.Add("MainMenu_Settings", "Configure application settings");
                _tooltips.Add("MainMenu_Help", "Get help and documentation");

                // Add default tooltips for quick actions
                _tooltips.Add("QuickActions_ReduceInputDelay", "Reduce input delay for gaming");
                _tooltips.Add("QuickActions_OptimizeForGaming", "Apply all gaming optimizations");
                _tooltips.Add("QuickActions_CleanupSystem", "Clean up temporary files and optimize disk");
                _tooltips.Add("QuickActions_BoostNetwork", "Optimize network for lower latency");
                _tooltips.Add("QuickActions_RevertAll", "Revert all applied optimizations");

                // Add default tooltips for settings
                _tooltips.Add("Settings_General", "General application settings");
                _tooltips.Add("Settings_Appearance", "Customize application appearance");
                _tooltips.Add("Settings_Security", "Security and authentication settings");
                _tooltips.Add("Settings_Advanced", "Advanced configuration options");
                _tooltips.Add("Settings_Logging", "Configure logging and diagnostics");

                // Save tooltips
                SaveTooltips();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating default tooltips: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Saves tooltips to the tooltips file
        /// </summary>
        private void SaveTooltips()
        {
            try
            {
                string json = JsonSerializer.Serialize(_tooltips, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_tooltipsFilePath, json);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving tooltips: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets a tooltip for a control
        /// </summary>
        /// <param name="controlName">The name of the control</param>
        /// <returns>The tooltip text, or null if not found</returns>
        public string GetTooltip(string controlName)
        {
            if (_tooltips.TryGetValue(controlName, out string tooltip))
            {
                return tooltip;
            }
            return null;
        }

        /// <summary>
        /// Applies tooltips to controls in a window
        /// </summary>
        /// <param name="window">The window</param>
        public void ApplyTooltips(Window window)
        {
            try
            {
                // Apply tooltips to all controls in the window
                ApplyTooltipsToControls(window);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying tooltips: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Applies tooltips to controls
        /// </summary>
        /// <param name="parent">The parent element</param>
        private void ApplyTooltipsToControls(DependencyObject parent)
        {
            int childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);
                if (child is FrameworkElement element && !string.IsNullOrEmpty(element.Name))
                {
                    string tooltip = GetTooltip(element.Name);
                    if (!string.IsNullOrEmpty(tooltip))
                    {
                        element.ToolTip = tooltip;
                    }
                }

                // Recursively apply tooltips to children
                ApplyTooltipsToControls(child);
            }
        }

        /// <summary>
        /// Shows help for a specific topic
        /// </summary>
        /// <param name="topicKey">The topic key</param>
        /// <returns>The help content, or null if not found</returns>
        public DocumentationItem ShowHelp(string topicKey)
        {
            try
            {
                return _documentationService.GetDocumentationItem(topicKey);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing help: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Shows context-sensitive help for a control
        /// </summary>
        /// <param name="controlName">The name of the control</param>
        /// <returns>The help content, or null if not found</returns>
        public string ShowContextHelp(string controlName)
        {
            try
            {
                // First check tooltips
                string tooltip = GetTooltip(controlName);
                if (!string.IsNullOrEmpty(tooltip))
                {
                    return tooltip;
                }

                // Then check documentation
                DocumentationItem item = _documentationService.GetDocumentationItem(controlName);
                if (item != null)
                {
                    return item.Content;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing context help: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }
    }
}
