// Created by Arsenal on 5-17-25 12:15PM
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Interface for hardware optimization service
    /// </summary>
    public interface IHardwareOptimizationService
    {
        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Gets the list of available optimizations
        /// </summary>
        List<HardwareSpecificOptimization> Optimizations { get; }

        /// <summary>
        /// Gets the list of available power profiles
        /// </summary>
        List<PowerManagementProfile> PowerProfiles { get; }

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task InitializeAsync();

        /// <summary>
        /// Applies an optimization
        /// </summary>
        /// <param name="optimization">The optimization to apply</param>
        /// <param name="skipCompatibilityCheck">Whether to skip the compatibility check</param>
        /// <returns>Whether the optimization was applied successfully</returns>
        Task<bool> ApplyOptimizationAsync(HardwareSpecificOptimization optimization, bool skipCompatibilityCheck = false);

        /// <summary>
        /// Reverts an optimization
        /// </summary>
        /// <param name="optimization">The optimization to revert</param>
        /// <returns>Whether the optimization was reverted successfully</returns>
        Task<bool> RevertOptimizationAsync(HardwareSpecificOptimization optimization);

        /// <summary>
        /// Applies a power profile
        /// </summary>
        /// <param name="profile">The power profile to apply</param>
        /// <param name="skipCompatibilityCheck">Whether to skip the compatibility check</param>
        /// <returns>Whether the power profile was applied successfully</returns>
        Task<bool> ApplyPowerProfileAsync(PowerManagementProfile profile, bool skipCompatibilityCheck = false);

        /// <summary>
        /// Reverts a power profile
        /// </summary>
        /// <param name="profile">The power profile to revert</param>
        /// <returns>Whether the power profile was reverted successfully</returns>
        Task<bool> RevertPowerProfileAsync(PowerManagementProfile profile);

        /// <summary>
        /// Gets optimizations for the current system
        /// </summary>
        /// <returns>The optimizations for the current system</returns>
        IEnumerable<HardwareSpecificOptimization> GetOptimizationsForCurrentSystem();

        /// <summary>
        /// Gets power profiles for the current system
        /// </summary>
        /// <returns>The power profiles for the current system</returns>
        IEnumerable<PowerManagementProfile> GetPowerProfilesForCurrentSystem();

        /// <summary>
        /// Gets optimizations for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer (optional)</param>
        /// <returns>The list of optimizations</returns>
        List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType, string manufacturer = null);

        /// <summary>
        /// Applies an optimization by name
        /// </summary>
        /// <param name="optimizationName">The name of the optimization to apply</param>
        /// <returns>True if successful, false otherwise</returns>
        bool ApplyOptimization(string optimizationName);

        /// <summary>
        /// Reverts an optimization by name
        /// </summary>
        /// <param name="optimizationName">The name of the optimization to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        bool RevertOptimization(string optimizationName);

        /// <summary>
        /// Applies a power profile by name
        /// </summary>
        /// <param name="profileName">The name of the profile to apply</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> ApplyPowerProfileAsync(string profileName);

        /// <summary>
        /// Reverts a power profile by name
        /// </summary>
        /// <param name="profileName">The name of the profile to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> RevertPowerProfileAsync(string profileName);

        /// <summary>
        /// Gets optimizations for a specific hardware type and category
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="category">The category (e.g., "Recommended" or "All")</param>
        /// <returns>The list of optimizations</returns>
        List<HardwareSpecificOptimization> GetOptimizationsForCategory(HardwareType hardwareType, string manufacturer, string category);

        /// <summary>
        /// Gets all applied optimizations
        /// </summary>
        /// <returns>The list of applied optimizations</returns>
        List<HardwareSpecificOptimization> GetAppliedOptimizations();
    }
}
