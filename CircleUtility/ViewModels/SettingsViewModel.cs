using System;
using CircleUtility.Models;
using System.Threading.Tasks;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Services;
using CircleUtility.Views;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the settings view
    /// </summary>
    public class SettingsViewModel : ViewModelBase
    {
        private readonly IUserTrackingService _userTracking;
        private readonly IDiscordService _discord;
        // Security service removed
        private bool _enableLogging;
        private bool _enableUserTracking;
        private bool _enableDevMode;
        private bool _enableDarkMode;
        private bool _enableAutoUpdate;
        private bool _enableHardwareMonitoring;
        private bool _enableBackgroundProcessing;
        private bool _showTooltips;
        private bool _enableSecurityFeatures;
        private int _animationSpeedIndex;
        private int _fontSizeIndex;
        private int _logLevelIndex;
        private int _refreshIntervalIndex;
        private int _securityLevelIndex;
        private string _logFileLocation;
        private string _backupLocation;
        private string _username;

        /// <summary>
        /// Initializes a new instance of the SettingsViewModel class
        /// </summary>        private readonly ILoggerService _logger;


        public SettingsViewModel(ILoggerService logger, IDiscordService discordService, IUserTrackingService userTrackingService) : base(logger)
        {
            _userTracking = userTrackingService;
            _discord = discordService;
            // Security service removed

            // Initialize security settings view

            // Initialize commands
            SaveSettingsCommand = new RelayCommand(SaveSettings);
            ResetSettingsCommand = new RelayCommand(ResetSettings);
            BrowseLogFileCommand = new RelayCommand(BrowseLogFile);
            BrowseBackupLocationCommand = new RelayCommand(BrowseBackupLocation);
            CreateBackupCommand = new RelayCommand(CreateBackup);
            RestoreBackupCommand = new RelayCommand(RestoreBackup);
            OpenSecuritySettingsCommand = new RelayCommand(OpenSecuritySettings);

            // Initialize settings with default values
            EnableLogging = true;
            EnableUserTracking = true;
            EnableDevMode = false;
            EnableDarkMode = true;
            EnableAutoUpdate = true;
            EnableHardwareMonitoring = true;
            EnableBackgroundProcessing = false;
            ShowTooltips = true;
            EnableSecurityFeatures = true;
            AnimationSpeedIndex = 3; // Fast
            FontSizeIndex = 1; // Medium
            LogLevelIndex = 2; // Info
            RefreshIntervalIndex = 1; // 5 seconds
            SecurityLevelIndex = 1; // Basic
            LogFileLocation = @"C:\Logs\CircleUtility.log";
            BackupLocation = @"C:\Backup\CircleUtility";
            Username = "User";

            _logger.Log("SettingsViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets or sets a value indicating whether auto-update is enabled
        /// </summary>
        public bool EnableAutoUpdate
        {
            get => _enableAutoUpdate;
            set => SetProperty(ref _enableAutoUpdate, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether hardware monitoring is enabled
        /// </summary>
        public bool EnableHardwareMonitoring
        {
            get => _enableHardwareMonitoring;
            set => SetProperty(ref _enableHardwareMonitoring, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether background processing is enabled
        /// </summary>
        public bool EnableBackgroundProcessing
        {
            get => _enableBackgroundProcessing;
            set => SetProperty(ref _enableBackgroundProcessing, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether tooltips are shown
        /// </summary>
        public bool ShowTooltips
        {
            get => _showTooltips;
            set => SetProperty(ref _showTooltips, value);
        }

        /// <summary>
        /// Gets or sets the animation speed index
        /// </summary>
        public int AnimationSpeedIndex
        {
            get => _animationSpeedIndex;
            set => SetProperty(ref _animationSpeedIndex, value);
        }

        /// <summary>
        /// Gets or sets the font size index
        /// </summary>
        public int FontSizeIndex
        {
            get => _fontSizeIndex;
            set => SetProperty(ref _fontSizeIndex, value);
        }

        /// <summary>
        /// Gets or sets the log level index
        /// </summary>
        public int LogLevelIndex
        {
            get => _logLevelIndex;
            set => SetProperty(ref _logLevelIndex, value);
        }

        /// <summary>
        /// Gets or sets the refresh interval index
        /// </summary>
        public int RefreshIntervalIndex
        {
            get => _refreshIntervalIndex;
            set => SetProperty(ref _refreshIntervalIndex, value);
        }

        /// <summary>
        /// Gets or sets the log file location
        /// </summary>
        public string LogFileLocation
        {
            get => _logFileLocation;
            set => SetProperty(ref _logFileLocation, value);
        }

        /// <summary>
        /// Gets or sets the backup location
        /// </summary>
        public string BackupLocation
        {
            get => _backupLocation;
            set => SetProperty(ref _backupLocation, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether logging is enabled
        /// </summary>
        public bool EnableLogging
        {
            get => _enableLogging;
            set => SetProperty(ref _enableLogging, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether user tracking is enabled
        /// </summary>
        public bool EnableUserTracking
        {
            get => _enableUserTracking;
            set => SetProperty(ref _enableUserTracking, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether dev mode is enabled
        /// </summary>
        public bool EnableDevMode
        {
            get => _enableDevMode;
            set => SetProperty(ref _enableDevMode, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether dark mode is enabled
        /// </summary>
        public bool EnableDarkMode
        {
            get => _enableDarkMode;
            set => SetProperty(ref _enableDarkMode, value);
        }

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value);
        }

        /// <summary>
        /// Gets the save settings command
        /// </summary>
        public ICommand SaveSettingsCommand { get; }

        /// <summary>
        /// Gets the reset settings command
        /// </summary>
        public ICommand ResetSettingsCommand { get; }

        /// <summary>
        /// Gets the browse log file command
        /// </summary>
        public ICommand BrowseLogFileCommand { get; }

        /// <summary>
        /// Gets the browse backup location command
        /// </summary>
        public ICommand BrowseBackupLocationCommand { get; }

        /// <summary>
        /// Gets the create backup command
        /// </summary>
        public ICommand CreateBackupCommand { get; }

        /// <summary>
        /// Gets the restore backup command
        /// </summary>
        public ICommand RestoreBackupCommand { get; }

        /// <summary>
        /// Gets the open security settings command
        /// </summary>
        public ICommand OpenSecuritySettingsCommand { get; }

        /// <summary>
        /// Gets or sets a value indicating whether security features are enabled
        /// </summary>
        public bool EnableSecurityFeatures
        {
            get => _enableSecurityFeatures;
            set => SetProperty(ref _enableSecurityFeatures, value);
        }

        /// <summary>
        /// Gets or sets the security level index
        /// </summary>
        public int SecurityLevelIndex
        {
            get => _securityLevelIndex;
            set => SetProperty(ref _securityLevelIndex, value);
        }

        /// <summary>
        /// Saves the settings
        /// </summary>        private readonly ILoggerService _logger;


        private void SaveSettings()
        {
            _logger.Log("Saving settings", LogLevel.INFO);
            _userTracking.AddUserActivity(Username, "Settings", "Saved settings");

            try
            {
                // Save settings
                // In a real implementation, this would save to a configuration file or database

                _logger.Log("Settings saved successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving settings: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Resets the settings to default values
        /// </summary>        private readonly ILoggerService _logger;


        private void ResetSettings()
        {
            _logger.Log("Resetting settings to defaults", LogLevel.INFO);
            _userTracking.AddUserActivity(Username, "Settings", "Reset settings to defaults");

            // Reset settings to default values
            EnableLogging = true;
            EnableUserTracking = true;
            EnableDevMode = false;
            EnableDarkMode = true;
            EnableAutoUpdate = true;
            EnableHardwareMonitoring = true;
            EnableBackgroundProcessing = false;
            ShowTooltips = true;
            EnableSecurityFeatures = true;
            AnimationSpeedIndex = 3; // Fast
            FontSizeIndex = 1; // Medium
            LogLevelIndex = 2; // Info
            RefreshIntervalIndex = 1; // 5 seconds
            SecurityLevelIndex = 1; // Basic
            LogFileLocation = @"C:\Logs\CircleUtility.log";
            BackupLocation = @"C:\Backup\CircleUtility";

            _logger.Log("Settings reset to defaults", LogLevel.SUCCESS);
        }

        /// <summary>
        /// Opens a dialog to browse for a log file location
        /// </summary>        private readonly ILoggerService _logger;


        private void BrowseLogFile()
        {
            _logger.Log("Browsing for log file location", LogLevel.INFO);

            try
            {
                // In a real implementation, this would open a file dialog
                // For now, we'll just set a sample path
                LogFileLocation = @"C:\Logs\CircleUtility.log";

                _logger.Log("Log file location selected", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error browsing for log file location: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Opens a dialog to browse for a backup location
        /// </summary>        private readonly ILoggerService _logger;


        private void BrowseBackupLocation()
        {
            _logger.Log("Browsing for backup location", LogLevel.INFO);

            try
            {
                // In a real implementation, this would open a folder dialog
                // For now, we'll just set a sample path
                BackupLocation = @"C:\Backup\CircleUtility";

                _logger.Log("Backup location selected", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error browsing for backup location: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Creates a backup of the settings
        /// </summary>        private readonly ILoggerService _logger;


        private void CreateBackup()
        {
            _logger.Log("Creating backup", LogLevel.INFO);

            try
            {
                // In a real implementation, this would create a backup file
                // For now, we'll just log a success message

                _logger.Log("Backup created successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating backup: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Restores settings from a backup
        /// </summary>        private readonly ILoggerService _logger;


        private void RestoreBackup()
        {
            _logger.Log("Restoring from backup", LogLevel.INFO);

            try
            {
                // In a real implementation, this would restore settings from a backup file
                // For now, we'll just log a success message

                _logger.Log("Settings restored successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restoring from backup: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Opens the security settings dialog
        /// </summary>        private readonly ILoggerService _logger;


        private void OpenSecuritySettings()
        {
            _logger.Log("Opening security settings", LogLevel.INFO);

            try
            {
                // Create and show the security dialog
                // SecurityDialog securityDialog = new SecurityDialog(); // Commented out

                // Set owner to the main window
                // Security dialog removed

                // Show dialog
                bool? result = false; // Security dialog removed
                _logger.LogWarning("SecurityDialog is not implemented. Skipping dialog display."); // Added warning

                if (result == true)
                {
                    _logger.Log("Security settings dialog closed with OK result", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Security settings dialog closed with Cancel result", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error opening security settings: {ex.Message}", LogLevel.ERROR);
            }
        }
    }
}








