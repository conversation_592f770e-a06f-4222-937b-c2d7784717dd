using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace CircleUtility.Views
{
    /// <summary>
    /// Converts a string value to a visibility based on equality with a parameter
    /// </summary>
    public class StringEqualityToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts a string value to a visibility based on equality with a parameter
        /// </summary>
        /// <param name="value">The string value to convert</param>
        /// <param name="targetType">The type of the target property</param>
        /// <param name="parameter">The string to compare with</param>
        /// <param name="culture">The culture information</param>
        /// <returns>Visible if the strings are equal, Collapsed otherwise</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
            {
                return Visibility.Collapsed;
            }

            string stringValue = value.ToString();
            string parameterValue = parameter.ToString();

            return string.Equals(stringValue, parameterValue, StringComparison.OrdinalIgnoreCase) 
                ? Visibility.Visible 
                : Visibility.Collapsed;
        }

        /// <summary>
        /// Converts a visibility back to a string value (not implemented)
        /// </summary>
        /// <param name="value">The visibility to convert</param>
        /// <param name="targetType">The type of the target property</param>
        /// <param name="parameter">The converter parameter</param>
        /// <param name="culture">The culture information</param>
        /// <returns>A string value</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
