using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedButton.xaml
    /// </summary>
    public partial class AnimatedButton : UserControl
    {
        private readonly Storyboard _glowAnimation;
        private readonly Storyboard _unglowAnimation;
        private readonly Storyboard _clickAnimation;
        private readonly Storyboard _successAnimation;
        private readonly Storyboard _resetAnimation;

        /// <summary>
        /// Gets or sets the button text.
        /// </summary>
        public string ButtonText
        {
            get => (string)GetValue(ButtonTextProperty);
            set => SetValue(ButtonTextProperty, value);
        }

        /// <summary>
        /// Dependency property for <see cref="ButtonText"/>.
        /// </summary>
        public static readonly DependencyProperty ButtonTextProperty =
            DependencyProperty.Register(nameof(ButtonText), typeof(string), typeof(AnimatedButton), new PropertyMetadata("Button"));

        /// <summary>
        /// Gets or sets the command to execute when the button is clicked.
        /// </summary>
        public ICommand Command
        {
            get => (ICommand)GetValue(CommandProperty);
            set => SetValue(CommandProperty, value);
        }

        /// <summary>
        /// Dependency property for <see cref="Command"/>.
        /// </summary>
        public static readonly DependencyProperty CommandProperty =
            DependencyProperty.Register(nameof(Command), typeof(ICommand), typeof(AnimatedButton), new PropertyMetadata(null));

        /// <summary>
        /// Gets or sets the command parameter.
        /// </summary>
        public object CommandParameter
        {
            get => GetValue(CommandParameterProperty);
            set => SetValue(CommandParameterProperty, value);
        }

        /// <summary>
        /// Dependency property for <see cref="CommandParameter"/>.
        /// </summary>
        public static readonly DependencyProperty CommandParameterProperty =
            DependencyProperty.Register(nameof(CommandParameter), typeof(object), typeof(AnimatedButton), new PropertyMetadata(null));

        /// <summary>
        /// Event raised when the button is clicked.
        /// </summary>
        public event RoutedEventHandler Click;

        /// <summary>
        /// Initializes a new instance of the <see cref="AnimatedButton"/> class.
        /// </summary>
        public AnimatedButton()
        {
            InitializeComponent();

            _glowAnimation = TryFindResource("GlowAnimation") as Storyboard;
            _unglowAnimation = TryFindResource("UnglowAnimation") as Storyboard;
            _clickAnimation = TryFindResource("ClickAnimation") as Storyboard;
            _successAnimation = TryFindResource("SuccessAnimation") as Storyboard;
            _resetAnimation = TryFindResource("ResetAnimation") as Storyboard;
        }

        private void MainButton_MouseEnter(object sender, MouseEventArgs e)
        {
            _glowAnimation?.Begin(this);
        }

        private void MainButton_MouseLeave(object sender, MouseEventArgs e)
        {
            _unglowAnimation?.Begin(this);
        }

        private void MainButton_Click(object sender, RoutedEventArgs e)
        {
            _clickAnimation?.Begin(this);

            if (Command != null && Command.CanExecute(CommandParameter))
            {
                Command.Execute(CommandParameter);
            }

            Click?.Invoke(this, e);
        }

        /// <summary>
        /// Plays the success animation.
        /// </summary>
        public void ShowSuccess()
        {
            _successAnimation?.Begin(this);
        }

        /// <summary>
        /// Resets the button to its default visual state.
        /// </summary>
        public void Reset()
        {
            _resetAnimation?.Begin(this);
        }

        /// <summary>
        /// Plays the success animation and resets after a short delay.
        /// </summary>
        /// <param name="resetDelayMs">Delay in milliseconds before resetting.</param>
        public void ShowSuccessAndReset(int resetDelayMs = 1500)
        {
            ShowSuccess();

            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(resetDelayMs)
            };

            timer.Tick += (s, e) =>
            {
                Reset();
                timer.Stop();
            };

            timer.Start();
        }
    }
}
