// Created by Arsenal on 5-17-25 12:15PM
namespace CircleUtility.Models
{
    /// <summary>
    /// Represents the compatibility of hardware with an optimization
    /// </summary>
    public class HardwareCompatibility
    {
        /// <summary>
        /// Gets or sets a value indicating whether the hardware is compatible
        /// </summary>
        public bool IsCompatible { get; set; }

        /// <summary>
        /// Gets or sets the incompatibility reason
        /// </summary>
        public string IncompatibilityReason { get; set; }

        /// <summary>
        /// Gets or sets the compatibility score (0-100)
        /// </summary>
        public int CompatibilityScore { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the compatibility was verified
        /// </summary>
        public bool IsVerified { get; set; }

        /// <summary>
        /// Initializes a new instance of the HardwareCompatibility class
        /// </summary>
        public HardwareCompatibility()
        {
            IsCompatible = true;
            IncompatibilityReason = string.Empty;
            CompatibilityScore = 100;
            IsVerified = false;
        }

        /// <summary>
        /// Initializes a new instance of the HardwareCompatibility class
        /// </summary>
        /// <param name="isCompatible">Whether the hardware is compatible</param>
        /// <param name="reason">The incompatibility reason</param>
        /// <param name="score">The compatibility score</param>
        public HardwareCompatibility(bool isCompatible, string reason, int score)
        {
            IsCompatible = isCompatible;
            IncompatibilityReason = reason;
            CompatibilityScore = score;
            IsVerified = true;
        }
    }
}
