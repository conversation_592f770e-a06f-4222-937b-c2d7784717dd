using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using System.Windows.Threading;
using CircleUtility.Helpers;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the Admin Control Panel
    /// </summary>
    public class AdminViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTracking;
        private readonly HardwareFingerprintService _hardwareFingerprintService;
        private readonly SecurityService _securityService;
        private ObservableCollection<UserActivity> _userActivities;
        private ObservableCollection<LogEntryModel> _logEntries;
        private ObservableCollection<UserProfile> _userProfiles;
        private ObservableCollection<HardwareProfile> _hardwareProfiles;
        private ObservableCollection<SystemMetric> _systemMetrics;
        private ObservableCollection<UserSecurityInfo> _searchResults;
        private ObservableCollection<UserPunishment> _punishmentHistory;
        private string _selectedTab;
        private UserProfile _selectedUser;
        private UserSecurityInfo _selectedSecurityUser;
        private string _searchText;
        private bool _searchByHardwareId;
        private bool _searchByIp;
        private bool _includeDisabled;
        private bool _includeAdmins;
        private string _punishmentReason;
        private PunishmentType _selectedPunishmentType;
        private DateTime? _punishmentExpirationDate;
        private bool _isLoading;
        private string _statusMessage;
        private bool _isStatusVisible;
        private bool _isStatusSuccess;
        private DispatcherTimer _refreshTimer;
        private int _refreshInterval;
        private int _refreshIntervalIndex;
        private bool _autoStartWithWindows;
        private bool _autoUpdate;
        private int _updateCheckInterval;
        private string _updateServerUrl;
        private bool _requireLogin;
        private int _sessionTimeout;

        /// <summary>
        /// Initializes a new instance of the AdminViewModel class
        /// </summary>
        public AdminViewModel()
        {
            _logger = LoggingService.Instance;
            _userTracking = UserTrackingService.Instance;
            _hardwareFingerprintService = HardwareFingerprintService.Instance;
            _securityService = SecurityService.Instance;

            // Initialize collections
            UserActivities = new ObservableCollection<UserActivity>();
            LogEntries = new ObservableCollection<LogEntryModel>();
            UserProfiles = new ObservableCollection<UserProfile>();
            HardwareProfiles = new ObservableCollection<HardwareProfile>();
            SystemMetrics = new ObservableCollection<SystemMetric>();

            // Initialize commands
            RefreshDataCommand = new RelayCommand(RefreshData);
            AddUserCommand = new RelayCommand(AddUser);
            EditUserCommand = new RelayCommand(EditUser, () => SelectedUser != null);
            DeleteUserCommand = new RelayCommand(DeleteUser, () => SelectedUser != null);
            LockUserCommand = new RelayCommand(LockUser, () => SelectedUser != null && !SelectedUser.IsLocked);
            UnlockUserCommand = new RelayCommand(UnlockUser, () => SelectedUser != null && SelectedUser.IsLocked);
            LockHardwareCommand = new RelayCommand(LockHardware, () => SelectedUser != null);
            ViewUsersByIpCommand = new RelayCommand(ViewUsersByIp, () => SelectedUser != null && !string.IsNullOrEmpty(SelectedUser.LastIpAddress));

            // New commands for enhanced admin functionality
            SearchUsersCommand = new RelayCommand(SearchUsers);
            ClearSearchCommand = new RelayCommand(ClearSearch);
            PromoteToAdminCommand = new RelayCommand(PromoteToAdmin, () => SelectedSecurityUser != null && !SelectedSecurityUser.IsAdmin);
            AddHardwareIdCommand = new RelayCommand(AddHardwareId, () => SelectedSecurityUser != null);
            AddIpAddressCommand = new RelayCommand(AddIpAddress, () => SelectedSecurityUser != null);
            ViewPunishmentHistoryCommand = new RelayCommand(ViewPunishmentHistory, () => SelectedSecurityUser != null);
            ApplyPunishmentCommand = new RelayCommand(ApplyPunishment, () => SelectedSecurityUser != null && !string.IsNullOrEmpty(PunishmentReason));
            RevokePunishmentCommand = new RelayCommand<string>(RevokePunishment);
            ViewUsersByHardwareIdCommand = new RelayCommand(ViewUsersByHardwareId, () => SelectedSecurityUser != null && !string.IsNullOrEmpty(SelectedSecurityUser.HardwareId));
            ViewUsersByHardwareCommand = new RelayCommand(ViewUsersByHardware, () => SelectedUser != null && !string.IsNullOrEmpty(SelectedUser.HardwareFingerprint));
            ClearLogsCommand = new RelayCommand(ClearLogs);
            ExportLogsCommand = new RelayCommand(ExportLogs);
            SearchCommand = new RelayCommand(PerformSearch);
            SwitchTabCommand = new RelayCommand<string>(SwitchTab);

            // Configuration tab commands
            SaveConfigCommand = new RelayCommand(SaveConfig);
            ResetConfigCommand = new RelayCommand(ResetConfig);

            // Updates tab commands
            CheckForUpdatesCommand = new RelayCommand(CheckForUpdates);
            UploadUpdateCommand = new RelayCommand(UploadUpdate);
            ManageVersionsCommand = new RelayCommand(ManageVersions);
            CreateReleaseCommand = new RelayCommand(CreateRelease);
            ViewReleaseNotesCommand = new RelayCommand(ViewReleaseNotes);

            // Initialize properties
            SelectedTab = "Users";
            IsLoading = false;
            StatusMessage = "";
            IsStatusVisible = false;
            RefreshIntervalIndex = 3; // 30 seconds
            RefreshInterval = 30; // 30 seconds

            // Initialize configuration properties
            AutoStartWithWindows = false;
            AutoUpdate = true;
            UpdateCheckInterval = 1; // Daily
            UpdateServerUrl = "https://updates.circleutility.com";
            RequireLogin = true;
            SessionTimeout = 30; // 30 minutes

            // Load initial data
            LoadInitialData();

            // Set up refresh timer
            SetupRefreshTimer();

            _logger.Log("AdminViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets or sets the collection of user activities
        /// </summary>
        public ObservableCollection<UserActivity> UserActivities
        {
            get => _userActivities;
            set => SetProperty(ref _userActivities, value);
        }

        /// <summary>
        /// Gets or sets the collection of log entries
        /// </summary>
        public ObservableCollection<LogEntryModel> LogEntries
        {
            get => _logEntries;
            set => SetProperty(ref _logEntries, value);
        }

        /// <summary>
        /// Gets or sets the collection of user profiles
        /// </summary>
        public ObservableCollection<UserProfile> UserProfiles
        {
            get => _userProfiles;
            set => SetProperty(ref _userProfiles, value);
        }

        /// <summary>
        /// Gets or sets the collection of system metrics
        /// </summary>
        public ObservableCollection<SystemMetric> SystemMetrics
        {
            get => _systemMetrics;
            set => SetProperty(ref _systemMetrics, value);
        }

        /// <summary>
        /// Gets or sets the collection of hardware profiles
        /// </summary>
        public ObservableCollection<HardwareProfile> HardwareProfiles
        {
            get => _hardwareProfiles;
            set => SetProperty(ref _hardwareProfiles, value);
        }

        /// <summary>
        /// Gets or sets the selected tab
        /// </summary>
        public string SelectedTab
        {
            get => _selectedTab;
            set
            {
                if (SetProperty(ref _selectedTab, value))
                {
                    // Load data for the selected tab
                    LoadTabData(value);
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected user
        /// </summary>
        public UserProfile SelectedUser
        {
            get => _selectedUser;
            set
            {
                if (SetProperty(ref _selectedUser, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected security user
        /// </summary>
        public UserSecurityInfo SelectedSecurityUser
        {
            get => _selectedSecurityUser;
            set
            {
                if (SetProperty(ref _selectedSecurityUser, value))
                {
                    CommandManager.InvalidateRequerySuggested();

                    // Load punishment history if available
                    if (value != null && value.PunishmentHistory != null)
                    {
                        PunishmentHistory = new ObservableCollection<UserPunishment>(value.PunishmentHistory);
                    }
                    else
                    {
                        PunishmentHistory = new ObservableCollection<UserPunishment>();
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets the search text
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to search by hardware ID
        /// </summary>
        public bool SearchByHardwareId
        {
            get => _searchByHardwareId;
            set => SetProperty(ref _searchByHardwareId, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to search by IP address
        /// </summary>
        public bool SearchByIp
        {
            get => _searchByIp;
            set => SetProperty(ref _searchByIp, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to include disabled users in search results
        /// </summary>
        public bool IncludeDisabled
        {
            get => _includeDisabled;
            set => SetProperty(ref _includeDisabled, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to include admin users in search results
        /// </summary>
        public bool IncludeAdmins
        {
            get => _includeAdmins;
            set => SetProperty(ref _includeAdmins, value);
        }

        /// <summary>
        /// Gets or sets the punishment reason
        /// </summary>
        public string PunishmentReason
        {
            get => _punishmentReason;
            set
            {
                if (SetProperty(ref _punishmentReason, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected punishment type
        /// </summary>
        public PunishmentType SelectedPunishmentType
        {
            get => _selectedPunishmentType;
            set => SetProperty(ref _selectedPunishmentType, value);
        }

        /// <summary>
        /// Gets or sets the punishment expiration date
        /// </summary>
        public DateTime? PunishmentExpirationDate
        {
            get => _punishmentExpirationDate;
            set => SetProperty(ref _punishmentExpirationDate, value);
        }

        /// <summary>
        /// Gets or sets the search results
        /// </summary>
        public ObservableCollection<UserSecurityInfo> SearchResults
        {
            get => _searchResults ?? (_searchResults = new ObservableCollection<UserSecurityInfo>());
            set => SetProperty(ref _searchResults, value);
        }

        /// <summary>
        /// Gets or sets the punishment history
        /// </summary>
        public ObservableCollection<UserPunishment> PunishmentHistory
        {
            get => _punishmentHistory ?? (_punishmentHistory = new ObservableCollection<UserPunishment>());
            set => SetProperty(ref _punishmentHistory, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether data is loading
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is a success
        /// </summary>
        public bool IsStatusSuccess
        {
            get => _isStatusSuccess;
            set => SetProperty(ref _isStatusSuccess, value);
        }

        /// <summary>
        /// Gets or sets the refresh interval in seconds
        /// </summary>
        public int RefreshInterval
        {
            get => _refreshInterval;
            set
            {
                if (SetProperty(ref _refreshInterval, value))
                {
                    UpdateRefreshTimer();
                }
            }
        }

        /// <summary>
        /// Gets or sets the refresh interval index for the ComboBox
        /// </summary>
        public int RefreshIntervalIndex
        {
            get => _refreshIntervalIndex;
            set
            {
                if (SetProperty(ref _refreshIntervalIndex, value))
                {
                    // Convert index to actual refresh interval in seconds
                    switch (value)
                    {
                        case 0: // Off
                            RefreshInterval = 0;
                            break;
                        case 1: // 5 seconds
                            RefreshInterval = 5;
                            break;
                        case 2: // 10 seconds
                            RefreshInterval = 10;
                            break;
                        case 3: // 30 seconds
                            RefreshInterval = 30;
                            break;
                        case 4: // 1 minute
                            RefreshInterval = 60;
                            break;
                        default:
                            RefreshInterval = 30; // Default to 30 seconds
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether to auto-start with Windows
        /// </summary>
        public bool AutoStartWithWindows
        {
            get => _autoStartWithWindows;
            set => SetProperty(ref _autoStartWithWindows, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to automatically update the application
        /// </summary>
        public bool AutoUpdate
        {
            get => _autoUpdate;
            set => SetProperty(ref _autoUpdate, value);
        }

        /// <summary>
        /// Gets or sets the update check interval
        /// </summary>
        public int UpdateCheckInterval
        {
            get => _updateCheckInterval;
            set => SetProperty(ref _updateCheckInterval, value);
        }

        /// <summary>
        /// Gets or sets the update server URL
        /// </summary>
        public string UpdateServerUrl
        {
            get => _updateServerUrl;
            set => SetProperty(ref _updateServerUrl, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to require login
        /// </summary>
        public bool RequireLogin
        {
            get => _requireLogin;
            set => SetProperty(ref _requireLogin, value);
        }

        /// <summary>
        /// Gets or sets the session timeout in minutes
        /// </summary>
        public int SessionTimeout
        {
            get => _sessionTimeout;
            set => SetProperty(ref _sessionTimeout, value);
        }

        /// <summary>
        /// Gets the refresh data command
        /// </summary>
        public ICommand RefreshDataCommand { get; }

        /// <summary>
        /// Gets the add user command
        /// </summary>
        public ICommand AddUserCommand { get; }

        /// <summary>
        /// Gets the edit user command
        /// </summary>
        public ICommand EditUserCommand { get; }

        /// <summary>
        /// Gets the delete user command
        /// </summary>
        public ICommand DeleteUserCommand { get; }

        /// <summary>
        /// Gets the lock user command
        /// </summary>
        public ICommand LockUserCommand { get; }

        /// <summary>
        /// Gets the unlock user command
        /// </summary>
        public ICommand UnlockUserCommand { get; }

        /// <summary>
        /// Gets the lock hardware command
        /// </summary>
        public ICommand LockHardwareCommand { get; }

        /// <summary>
        /// Gets the view users by IP command
        /// </summary>
        public ICommand ViewUsersByIpCommand { get; }

        /// <summary>
        /// Gets the search users command
        /// </summary>
        public ICommand SearchUsersCommand { get; }

        /// <summary>
        /// Gets the clear search command
        /// </summary>
        public ICommand ClearSearchCommand { get; }

        /// <summary>
        /// Gets the promote to admin command
        /// </summary>
        public ICommand PromoteToAdminCommand { get; }

        /// <summary>
        /// Gets the add hardware ID command
        /// </summary>
        public ICommand AddHardwareIdCommand { get; }

        /// <summary>
        /// Gets the add IP address command
        /// </summary>
        public ICommand AddIpAddressCommand { get; }

        /// <summary>
        /// Gets the view punishment history command
        /// </summary>
        public ICommand ViewPunishmentHistoryCommand { get; }

        /// <summary>
        /// Gets the apply punishment command
        /// </summary>
        public ICommand ApplyPunishmentCommand { get; }

        /// <summary>
        /// Gets the revoke punishment command
        /// </summary>
        public ICommand RevokePunishmentCommand { get; }

        /// <summary>
        /// Gets the view users by hardware ID command
        /// </summary>
        public ICommand ViewUsersByHardwareIdCommand { get; }

        /// <summary>
        /// Gets the view users by hardware command
        /// </summary>
        public ICommand ViewUsersByHardwareCommand { get; }

        /// <summary>
        /// Gets the clear logs command
        /// </summary>
        public ICommand ClearLogsCommand { get; }

        /// <summary>
        /// Gets the export logs command
        /// </summary>
        public ICommand ExportLogsCommand { get; }

        /// <summary>
        /// Gets the search command
        /// </summary>
        public ICommand SearchCommand { get; }

        /// <summary>
        /// Gets the switch tab command
        /// </summary>
        public ICommand SwitchTabCommand { get; }

        /// <summary>
        /// Gets the save config command
        /// </summary>
        public ICommand SaveConfigCommand { get; }

        /// <summary>
        /// Gets the reset config command
        /// </summary>
        public ICommand ResetConfigCommand { get; }

        /// <summary>
        /// Gets the check for updates command
        /// </summary>
        public ICommand CheckForUpdatesCommand { get; }

        /// <summary>
        /// Gets the upload update command
        /// </summary>
        public ICommand UploadUpdateCommand { get; }

        /// <summary>
        /// Gets the manage versions command
        /// </summary>
        public ICommand ManageVersionsCommand { get; }

        /// <summary>
        /// Gets the create release command
        /// </summary>
        public ICommand CreateReleaseCommand { get; }

        /// <summary>
        /// Gets the view release notes command
        /// </summary>
        public ICommand ViewReleaseNotesCommand { get; }
    }
}
