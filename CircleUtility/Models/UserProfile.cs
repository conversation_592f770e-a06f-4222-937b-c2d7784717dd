using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a user profile
    /// </summary>
    public class UserProfile
    {
        /// <summary>
        /// Gets or sets the user ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is an admin
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// Gets or sets the last login date
        /// </summary>
        public DateTime LastLogin { get; set; }

        /// <summary>
        /// Gets or sets the first login date
        /// </summary>
        public DateTime FirstLogin { get; set; }

        /// <summary>
        /// Gets or sets the login count
        /// </summary>
        public int LoginCount { get; set; }

        /// <summary>
        /// Gets or sets the IP addresses
        /// </summary>
        public List<string> IpAddresses { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the last IP address
        /// </summary>
        public string LastIpAddress { get; set; }

        /// <summary>
        /// Gets or sets the hardware fingerprint
        /// </summary>
        public string HardwareFingerprint { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is locked
        /// </summary>
        public bool IsLocked { get; set; }

        /// <summary>
        /// Gets or sets the lock reason
        /// </summary>
        public string LockReason { get; set; }

        /// <summary>
        /// Gets or sets the lock date
        /// </summary>
        public DateTime? LockDate { get; set; }

        /// <summary>
        /// Gets or sets the email address
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets the phone number
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether two-factor authentication is enabled
        /// </summary>
        public bool IsTwoFactorEnabled { get; set; }

        /// <summary>
        /// Gets or sets the two-factor authentication type
        /// </summary>
        public TwoFactorType TwoFactorType { get; set; }

        /// <summary>
        /// Gets or sets the notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets the formatted last login date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastLogin => LastLogin.ToString("MMM dd, yyyy HH:mm:ss");

        /// <summary>
        /// Gets the formatted first login date
        /// </summary>
        [JsonIgnore]
        public string FormattedFirstLogin => FirstLogin.ToString("MMM dd, yyyy HH:mm:ss");

        /// <summary>
        /// Gets the formatted lock date
        /// </summary>
        [JsonIgnore]
        public string FormattedLockDate => LockDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "N/A";

        /// <summary>
        /// Gets the status description
        /// </summary>
        [JsonIgnore]
        public string StatusDescription => IsLocked ? "Locked" : (IsAdmin ? "Admin" : "Active");

        /// <summary>
        /// Gets the status color
        /// </summary>
        [JsonIgnore]
        public string StatusColor => IsLocked ? "#FF0000" : (IsAdmin ? "#FFFF00" : "#00FF00");

        /// <summary>
        /// Gets the IP addresses as a comma-separated string
        /// </summary>
        [JsonIgnore]
        public string IpAddressesString => string.Join(", ", IpAddresses);
    }
}
