using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.Views
{
    public partial class EnhancedUserManagementView : UserControl
    {
        private readonly EnhancedDiscordUserService _discordUserService;
        private ObservableCollection<DiscordUser> _regularUsers;
        private ObservableCollection<DiscordUser> _adminUsers;
        private bool _isRegularUserMode = true;

        public EnhancedUserManagementView(EnhancedDiscordUserService discordUserService)
        {
            InitializeComponent();
            _discordUserService = discordUserService;
            _regularUsers = new ObservableCollection<DiscordUser>();
            _adminUsers = new ObservableCollection<DiscordUser>();

            // Bind the collections to the UI
            RegularUsersList.ItemsSource = _regularUsers;
            AdminUsersList.ItemsSource = _adminUsers;

            // Load users on initialization
            Loaded += async (s, e) => await LoadUsersAsync();
        }

        private async Task LoadUsersAsync()
        {
            try
            {
                var allUsers = await _discordUserService.LoadUsersFromFileAsync();
                
                // Clear existing collections
                _regularUsers.Clear();
                _adminUsers.Clear();

                // Separate users by role
                foreach (var user in allUsers)
                {
                    if (user.IsAdmin)
                    {
                        _adminUsers.Add(user);
                    }
                    else
                    {
                        _regularUsers.Add(user);
                    }
                }

                ShowStatusMessage($"Loaded {allUsers.Count} users ({_regularUsers.Count} regular, {_adminUsers.Count} admin)", Colors.Green);
            }
            catch (Exception ex)
            {
                ShowStatusMessage($"Error loading users: {ex.Message}", Colors.Red);
            }
        }

        private void RegularUserToggle_Click(object sender, RoutedEventArgs e)
        {
            if (RegularUserToggle.IsChecked == true)
            {
                _isRegularUserMode = true;
                AdminUserToggle.IsChecked = false;
                UpdateToggleUI();
            }
            else
            {
                RegularUserToggle.IsChecked = true; // Keep it checked
            }
        }

        private void AdminUserToggle_Click(object sender, RoutedEventArgs e)
        {
            if (AdminUserToggle.IsChecked == true)
            {
                _isRegularUserMode = false;
                RegularUserToggle.IsChecked = false;
                UpdateToggleUI();
            }
            else
            {
                AdminUserToggle.IsChecked = true; // Keep it checked
            }
        }

        private void UpdateToggleUI()
        {
            // Update the form placeholder text or styling based on mode
            if (_isRegularUserMode)
            {
                NewUsernameInput.ToolTip = "Enter username for regular user";
                NewDisplayNameInput.ToolTip = "Display name for regular user";
                CreateUserButton.Content = "Create Regular User";
            }
            else
            {
                NewUsernameInput.ToolTip = "Enter username for admin user";
                NewDisplayNameInput.ToolTip = "Display name for admin user";
                CreateUserButton.Content = "Create Admin User";
            }
        }

        private async void CreateUserButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                string username = NewUsernameInput.Text?.Trim();
                string password = NewPasswordInput.Password?.Trim();
                string displayName = NewDisplayNameInput.Text?.Trim();

                if (string.IsNullOrEmpty(username))
                {
                    ShowStatusMessage("Username is required", Colors.Red);
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowStatusMessage("Password is required", Colors.Red);
                    return;
                }

                if (string.IsNullOrEmpty(displayName))
                {
                    displayName = username; // Use username as display name if not provided
                }

                // Check if username already exists
                var existingUsers = await _discordUserService.LoadUsersFromFileAsync();
                if (existingUsers.Any(u => string.Equals(u.Username, username, StringComparison.OrdinalIgnoreCase)))
                {
                    ShowStatusMessage("Username already exists", Colors.Red);
                    return;
                }

                // Create new user
                var newUser = new DiscordUser
                {
                    Username = username,
                    Password = password,
                    DisplayName = displayName,
                    Role = _isRegularUserMode ? "User" : "Admin",
                    IsActive = true,
                    DiscordId = $"{username}_{DateTime.Now.Ticks}",
                    JoinDate = DateTime.Now,
                    Email = $"{username}@discord.local"
                };

                // Add to appropriate collection
                if (newUser.IsAdmin)
                {
                    _adminUsers.Add(newUser);
                }
                else
                {
                    _regularUsers.Add(newUser);
                }

                // Save to file
                var allUsers = _regularUsers.Concat(_adminUsers).ToList();
                await SaveUsersAsync(allUsers);

                // Clear form
                ClearForm();

                ShowStatusMessage($"Successfully created {newUser.Role.ToLower()} account: {username}", Colors.Green);
            }
            catch (Exception ex)
            {
                ShowStatusMessage($"Error creating user: {ex.Message}", Colors.Red);
            }
        }

        private async Task SaveUsersAsync(System.Collections.Generic.List<DiscordUser> users)
        {
            try
            {
                // For now, we'll save to the local file
                // In the future, this could also sync to Discord
                var json = System.Text.Json.JsonSerializer.Serialize(users, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                var localUsersFile = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "CircleUtility", "discord_users.json");

                await System.IO.File.WriteAllTextAsync(localUsersFile, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save users: {ex.Message}");
            }
        }

        private void ClearForm()
        {
            NewUsernameInput.Text = string.Empty;
            NewPasswordInput.Password = string.Empty;
            NewDisplayNameInput.Text = string.Empty;
        }

        private void ShowStatusMessage(string message, Color color)
        {
            StatusMessage.Text = message;
            StatusMessage.Foreground = new SolidColorBrush(color);
            StatusMessage.Visibility = Visibility.Visible;

            // Auto-hide after 5 seconds
            var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(5) };
            timer.Tick += (s, e) =>
            {
                StatusMessage.Visibility = Visibility.Collapsed;
                timer.Stop();
            };
            timer.Start();
        }

        /// <summary>
        /// Refreshes the user lists from the data source
        /// </summary>
        public async Task RefreshUsersAsync()
        {
            await LoadUsersAsync();
        }

        /// <summary>
        /// Gets the total number of users
        /// </summary>
        public int TotalUserCount => _regularUsers.Count + _adminUsers.Count;

        /// <summary>
        /// Gets the number of regular users
        /// </summary>
        public int RegularUserCount => _regularUsers.Count;

        /// <summary>
        /// Gets the number of admin users
        /// </summary>
        public int AdminUserCount => _adminUsers.Count;
    }
}


