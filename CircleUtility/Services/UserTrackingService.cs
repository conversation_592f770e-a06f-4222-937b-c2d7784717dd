using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using CircleUtility.Models;
using System.Linq;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for tracking user activities and behavior
    /// </summary>
    public class UserTrackingService : IUserTrackingService
    {
        private readonly LoggingService _logger;
        private readonly string _userLoginFile;
        private readonly string _userDataDirectory;
        private readonly object _lockObject = new object();

        /// <summary>
        /// Initializes a new instance of the UserTrackingService class
        /// </summary>
        public UserTrackingService(LoggingService logger)
        {
            _logger = logger;
            _userLoginFile = Path.Combine(AppContext.BaseDirectory, "user_logins.json");
            _userDataDirectory = Path.Combine(AppContext.BaseDirectory, "data");
        }

        /// <summary>
        /// Adds a user activity with 3 parameters (matching expected signature)
        /// </summary>
        /// <param name="activity">The activity description</param>
        /// <param name="details">Additional details</param>
        /// <param name="category">The activity category</param>
        public void AddUserActivity(string activity, string details, string category)
        {
            try
            {
                _logger?.Log($"User activity: {activity} - {details} [{category}]", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error adding user activity: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Adds a user activity with 1 parameter (overload)
        /// </summary>
        /// <param name="activity">The activity description</param>
        public void AddUserActivity(string activity)
        {
            AddUserActivity(activity, "", "General");
        }

        /// <summary>
        /// Registers a user login
        /// </summary>
        /// <param name="username">The username</param>
        public void RegisterUserLogin(string username)
        {
            try
            {
                _logger?.Log($"User login registered: {username}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error registering user login: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tracks user login
        /// </summary>
        /// <param name="username">The username</param>
        public void TrackLogin(string username)
        {
            try
            {
                _logger?.Log($"User login: {username}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error tracking login: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tracks user logout
        /// </summary>
        /// <param name="username">The username</param>
        public void TrackLogout(string username)
        {
            try
            {
                _logger?.Log($"User logout: {username}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error tracking logout: {ex.Message}", LogLevel.ERROR);
            }
        }

        public List<UserLogin> GetUserLogins()
        {
            lock (_lockObject)
            {
                string json = File.ReadAllText(_userLoginFile);
                return JsonSerializer.Deserialize<List<UserLogin>>(json) ?? new List<UserLogin>();
            }
        }

        public void ResetUserHardwareAndIp(string username)
        {
            lock (_lockObject)
            {
                string json = File.ReadAllText(_userLoginFile);
                List<UserLogin> logins = JsonSerializer.Deserialize<List<UserLogin>>(json) ?? new List<UserLogin>();
                var userLogin = logins.FirstOrDefault(l => l.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                if (userLogin != null)
                {
                    userLogin.HardwareFingerprint = string.Empty;
                    userLogin.IpAddresses = new List<string>();
                    userLogin.LastIpAddress = string.Empty;
                    File.WriteAllText(_userLoginFile, JsonSerializer.Serialize(logins, new JsonSerializerOptions { WriteIndented = true }));
                }
            }
        }

        public List<PendingRegistration> GetPendingRegistrations()
        {
            string pendingFile = Path.Combine(_userDataDirectory, "pending_registrations.json");
            if (!File.Exists(pendingFile)) return new List<PendingRegistration>();
            string json = File.ReadAllText(pendingFile);
            return JsonSerializer.Deserialize<List<PendingRegistration>>(json) ?? new List<PendingRegistration>();
        }

        public void ApprovePendingRegistration(string username)
        {
            string pendingFile = Path.Combine(_userDataDirectory, "pending_registrations.json");
            string json = File.ReadAllText(pendingFile);
            var pending = JsonSerializer.Deserialize<List<PendingRegistration>>(json) ?? new List<PendingRegistration>();
            var reg = pending.FirstOrDefault(r => r.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
            if (reg != null)
            {
                pending.Remove(reg);
                File.WriteAllText(pendingFile, JsonSerializer.Serialize(pending, new JsonSerializerOptions { WriteIndented = true }));
                // Add to main user file (no hardware/IP yet)
                string userJson = File.ReadAllText(_userLoginFile);
                var logins = JsonSerializer.Deserialize<List<UserLogin>>(userJson) ?? new List<UserLogin>();
                logins.Add(new UserLogin { Username = reg.Username, FirstLogin = DateTime.Now, LastLogin = DateTime.Now, IpAddresses = new List<string>(), LastIpAddress = string.Empty, HardwareFingerprint = string.Empty, LoginCount = 0 });
                File.WriteAllText(_userLoginFile, JsonSerializer.Serialize(logins, new JsonSerializerOptions { WriteIndented = true }));
            }
        }

        public void DenyPendingRegistration(string username)
        {
            string pendingFile = Path.Combine(_userDataDirectory, "pending_registrations.json");
            string json = File.ReadAllText(pendingFile);
            var pending = JsonSerializer.Deserialize<List<PendingRegistration>>(json) ?? new List<PendingRegistration>();
            var reg = pending.FirstOrDefault(r => r.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
            if (reg != null)
            {
                pending.Remove(reg);
                File.WriteAllText(pendingFile, JsonSerializer.Serialize(pending, new JsonSerializerOptions { WriteIndented = true }));
            }
        }
    }
}
