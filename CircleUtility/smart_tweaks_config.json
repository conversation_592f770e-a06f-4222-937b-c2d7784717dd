[{"TweakId": "EnableHAGS_IfNeeded", "SettingName": "Enable Hardware-Accelerated GPU Scheduling (HAGS)", "Description": "May improve performance and reduce latency in CPU-limited scenarios by offloading GPU scheduling to the GPU itself. Requires Windows 10 (2004) / WDDM 2.7+ and a compatible GPU/driver. A system restart is required for changes to take effect.", "Category": "OS_GPU", "Condition": "OperatingSystem.HardwareAcceleratedGPUScheduling == HAGSStatus.Disabled && OperatingSystem.WddmVersion >= WDDMVersion.WDDM_2_7", "TweakType": "Registry", "ApplyAction": {"Path": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "ValueName": "HwSchMode", "ValueData": "2", "ValueKind": "DWORD"}, "RevertAction": {"Path": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "ValueName": "HwSchMode", "ValueData": "1", "ValueKind": "DWORD"}, "RequiresAdmin": true, "RequiresRestart": true, "AffectedComponents": ["OS", "GPU"]}, {"TweakId": "DisableHAGS_IfEnabled", "SettingName": "Disable Hardware-Accelerated GPU Scheduling (HAGS)", "Description": "Can be disabled if you suspect it's causing issues with specific games or applications. A system restart is required for changes to take effect.", "Category": "OS_GPU", "Condition": "OperatingSystem.HardwareAcceleratedGPUScheduling == HAGSStatus.Enabled", "TweakType": "Registry", "ApplyAction": {"Path": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "ValueName": "HwSchMode", "ValueData": "1", "ValueKind": "DWORD"}, "RevertAction": {"Path": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "ValueName": "HwSchMode", "ValueData": "2", "ValueKind": "DWORD"}, "RequiresAdmin": true, "RequiresRestart": true, "AffectedComponents": ["OS", "GPU"]}, {"TweakId": "DisableCoreParking", "SettingName": "Disable CPU Core Parking", "Description": "Ensures all CPU cores remain active. May reduce micro-stutters in some games by improving CPU responsiveness. Can slightly increase power consumption. A restart may be required for the change to fully apply.", "Category": "CPU_Power", "Condition": "CPU.Cores >= 2", "TweakType": "Registry", "ApplyAction": {"Path": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\5d76a2ca-e8c0-402f-a133-2158492d58ad\\dec35c318583", "ValueName": "ValueMax", "ValueData": "0", "ValueKind": "DWORD"}, "RevertAction": {"Path": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\5d76a2ca-e8c0-402f-a133-2158492d58ad\\dec35c318583", "ValueName": "ValueMax", "ValueData": "100", "ValueKind": "DWORD"}, "RequiresAdmin": true, "RequiresRestart": true, "AffectedComponents": ["CPU", "Power"]}, {"TweakId": "EnsureSystemManagedPageFile_LowRam", "SettingName": "Ensure System Managed Page File (if low RAM & not set)", "Description": "Checks if the page file is disabled or very small on systems with less than 16GB of RAM, and suggests setting it to system-managed on C: drive. This can improve system stability and performance. Requires a restart.", "Category": "OS_Memory", "Condition": "(OperatingSystem.PageFile.UsageType == PageFileUsageType.Disabled || (OperatingSystem.PageFile.UsageType == PageFileUsageType.Custom && OperatingSystem.PageFile.MaximumSizeMB < 4096)) && RAM.TotalCapacity < 16384", "TweakType": "PowerShell", "ApplyAction": {"Script": "Get-WmiObject Win32_PageFileSetting | ForEach-Object { $_.Delete() }; Clear-ItemProperty -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management' -Name PagingFiles -ErrorAction SilentlyContinue -Force; $SystemDrive = $env:SystemDrive; Set-ItemProperty -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management' -Name PagingFiles -Value \"${SystemDrive}\\pagefile.sys 0 0\" -Type MultiString -Force"}, "RevertAction": {"Script": "Write-Warning 'Reverting page file to a specific previous state is complex. Please adjust manually in System Properties if needed.'"}, "RequiresAdmin": true, "RequiresRestart": true, "AffectedComponents": ["OS", "Memory", "Storage"]}]