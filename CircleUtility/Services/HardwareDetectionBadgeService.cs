// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Media;
using CircleUtility.Models;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing hardware detection badges
    /// </summary>
    public class HardwareDetectionBadgeService : IHardwareDetectionBadgeService
    {
        private static HardwareDetectionBadgeService _instance;
        private readonly LoggingService _logger;
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IHardwareCompatibilityService _hardwareCompatibilityService;
        private readonly Dictionary<HardwareType, HardwareBadgeInfo> _badgeInfo;
        private readonly Dictionary<HardwareType, DateTime> _lastDetectionTimes;
        private readonly Dictionary<HardwareType, bool> _isNewlyDetected;
        private bool _isInitialized;

        /// <summary>
        /// Gets the singleton instance of the hardware detection badge service
        /// </summary>
        public static HardwareDetectionBadgeService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new HardwareDetectionBadgeService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Event raised when hardware badges are updated
        /// </summary>
        public event EventHandler<HardwareBadgeUpdatedEventArgs> BadgesUpdated;

        /// <summary>
        /// Initializes a new instance of the HardwareDetectionBadgeService class
        /// </summary>
        private HardwareDetectionBadgeService()
        {
            _logger = LoggingService.Instance;
            _hardwareDetectionService = HardwareDetectionService.Instance;
            _hardwareCompatibilityService = HardwareCompatibilityService.Instance;
            _badgeInfo = new Dictionary<HardwareType, HardwareBadgeInfo>();
            _lastDetectionTimes = new Dictionary<HardwareType, DateTime>();
            _isNewlyDetected = new Dictionary<HardwareType, bool>();
            _isInitialized = false;

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Initializes the service
        /// </summary>
        private void Initialize()
        {
            try
            {
                _logger.Log("Initializing HardwareDetectionBadgeService...", LogLevel.INFO);

                // Initialize badge info for each hardware type
                _badgeInfo[HardwareType.CPU] = new HardwareBadgeInfo { Type = HardwareType.CPU, IsDetected = false, CompatibilityScore = 0 };
                _badgeInfo[HardwareType.GPU] = new HardwareBadgeInfo { Type = HardwareType.GPU, IsDetected = false, CompatibilityScore = 0 };
                _badgeInfo[HardwareType.RAM] = new HardwareBadgeInfo { Type = HardwareType.RAM, IsDetected = false, CompatibilityScore = 0 };
                _badgeInfo[HardwareType.Storage] = new HardwareBadgeInfo { Type = HardwareType.Storage, IsDetected = false, CompatibilityScore = 0 };
                _badgeInfo[HardwareType.Network] = new HardwareBadgeInfo { Type = HardwareType.Network, IsDetected = false, CompatibilityScore = 0 };
                _badgeInfo[HardwareType.System] = new HardwareBadgeInfo { Type = HardwareType.System, IsDetected = false, CompatibilityScore = 0 };

                // Initialize detection times and flags
                foreach (HardwareType type in Enum.GetValues(typeof(HardwareType)))
                {
                    _lastDetectionTimes[type] = DateTime.MinValue;
                    _isNewlyDetected[type] = false;
                }

                _isInitialized = true;
                _logger.Log("HardwareDetectionBadgeService initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing HardwareDetectionBadgeService: {ex.Message}", LogLevel.ERROR);
                _isInitialized = false;
            }
        }

        /// <summary>
        /// Refreshes hardware badges
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task RefreshBadgesAsync()
        {
            try
            {
                _logger.Log("Refreshing hardware badges...", LogLevel.INFO);

                // Get hardware info
                var hardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync(true);
                if (hardwareInfo == null)
                {
                    _logger.Log("Failed to get hardware info", LogLevel.ERROR);
                    return;
                }

                // Reset newly detected flags
                foreach (HardwareType type in Enum.GetValues(typeof(HardwareType)))
                {
                    _isNewlyDetected[type] = false;
                }

                // Update CPU badge
                if (hardwareInfo.CPU != null)
                {
                    var cpuBadge = _badgeInfo[HardwareType.CPU];
                    bool wasDetectedBefore = cpuBadge.IsDetected;
                    cpuBadge.IsDetected = true;
                    cpuBadge.Name = hardwareInfo.CPU.Name;
                    cpuBadge.Manufacturer = hardwareInfo.CPU.Manufacturer;
                    cpuBadge.CompatibilityScore = await GetCompatibilityScoreAsync(HardwareType.CPU, hardwareInfo.CPU);
                    cpuBadge.Details = $"Cores: {hardwareInfo.CPU.Cores}, Threads: {hardwareInfo.CPU.LogicalProcessors}, Speed: {hardwareInfo.CPU.MaxClockSpeed / 1000.0:0.0} GHz";

                    // Check if this is newly detected hardware
                    if (!wasDetectedBefore)
                    {
                        _isNewlyDetected[HardwareType.CPU] = true;
                        _lastDetectionTimes[HardwareType.CPU] = DateTime.Now;
                        _logger.Log($"New CPU detected: {cpuBadge.Name}", LogLevel.SUCCESS);
                    }
                }

                // Update GPU badge
                if (hardwareInfo.GPU != null)
                {
                    var gpuBadge = _badgeInfo[HardwareType.GPU];
                    bool wasDetectedBefore = gpuBadge.IsDetected;
                    gpuBadge.IsDetected = true;
                    gpuBadge.Name = hardwareInfo.GPU.Name;
                    gpuBadge.Manufacturer = hardwareInfo.GPU.Vendor.ToString();
                    gpuBadge.CompatibilityScore = await GetCompatibilityScoreAsync(HardwareType.GPU, hardwareInfo.GPU);
                    gpuBadge.Details = $"Memory: {hardwareInfo.GPU.MemoryGB} GB, Driver: {hardwareInfo.GPU.DriverVersion}";

                    // Check if this is newly detected hardware
                    if (!wasDetectedBefore)
                    {
                        _isNewlyDetected[HardwareType.GPU] = true;
                        _lastDetectionTimes[HardwareType.GPU] = DateTime.Now;
                        _logger.Log($"New GPU detected: {gpuBadge.Name}", LogLevel.SUCCESS);
                    }
                }

                // Update RAM badge
                if (hardwareInfo.RAM != null)
                {
                    var ramBadge = _badgeInfo[HardwareType.RAM];
                    bool wasDetectedBefore = ramBadge.IsDetected;
                    ramBadge.IsDetected = true;
                    ramBadge.Name = $"{hardwareInfo.RAM.TotalCapacity} GB RAM";
                    ramBadge.Manufacturer = hardwareInfo.RAM.Modules.Count > 0 ? hardwareInfo.RAM.Modules[0].Manufacturer : "Unknown";
                    ramBadge.CompatibilityScore = await GetCompatibilityScoreAsync(HardwareType.RAM, hardwareInfo.RAM);
                    ramBadge.Details = $"Modules: {hardwareInfo.RAM.Modules.Count}, Speed: {(hardwareInfo.RAM.Modules.Count > 0 ? hardwareInfo.RAM.Modules[0].Speed : 0)} MHz";

                    // Check if this is newly detected hardware
                    if (!wasDetectedBefore)
                    {
                        _isNewlyDetected[HardwareType.RAM] = true;
                        _lastDetectionTimes[HardwareType.RAM] = DateTime.Now;
                        _logger.Log($"New RAM detected: {ramBadge.Name}", LogLevel.SUCCESS);
                    }
                }

                // Update Storage badge
                if (hardwareInfo.Storage != null && hardwareInfo.Storage.Count > 0)
                {
                    var storageBadge = _badgeInfo[HardwareType.Storage];
                    bool wasDetectedBefore = storageBadge.IsDetected;
                    storageBadge.IsDetected = true;
                    storageBadge.Name = hardwareInfo.Storage[0].Model;
                    storageBadge.Manufacturer = hardwareInfo.Storage[0].Manufacturer;
                    storageBadge.CompatibilityScore = await GetCompatibilityScoreAsync(HardwareType.Storage, hardwareInfo.Storage[0]);
                    storageBadge.Details = $"Type: {hardwareInfo.Storage[0].InterfaceType}, Size: {hardwareInfo.Storage[0].Size} GB";

                    // Check if this is newly detected hardware
                    if (!wasDetectedBefore)
                    {
                        _isNewlyDetected[HardwareType.Storage] = true;
                        _lastDetectionTimes[HardwareType.Storage] = DateTime.Now;
                        _logger.Log($"New storage detected: {storageBadge.Name}", LogLevel.SUCCESS);
                    }
                }

                // Update Network badge
                if (hardwareInfo.NetworkAdapters != null && hardwareInfo.NetworkAdapters.Count > 0)
                {
                    var networkBadge = _badgeInfo[HardwareType.Network];
                    bool wasDetectedBefore = networkBadge.IsDetected;
                    networkBadge.IsDetected = true;
                    networkBadge.Name = hardwareInfo.NetworkAdapters[0].Name;
                    networkBadge.Manufacturer = hardwareInfo.NetworkAdapters[0].Manufacturer;
                    networkBadge.CompatibilityScore = await GetCompatibilityScoreAsync(HardwareType.Network, hardwareInfo.NetworkAdapters[0]);
                    networkBadge.Details = $"Type: {hardwareInfo.NetworkAdapters[0].AdapterType}, Speed: {hardwareInfo.NetworkAdapters[0].Speed} Mbps";

                    // Check if this is newly detected hardware
                    if (!wasDetectedBefore)
                    {
                        _isNewlyDetected[HardwareType.Network] = true;
                        _lastDetectionTimes[HardwareType.Network] = DateTime.Now;
                        _logger.Log($"New network adapter detected: {networkBadge.Name}", LogLevel.SUCCESS);
                    }
                }

                // Update System badge
                if (hardwareInfo.OperatingSystem != null)
                {
                    var systemBadge = _badgeInfo[HardwareType.System];
                    bool wasDetectedBefore = systemBadge.IsDetected;
                    systemBadge.IsDetected = true;
                    systemBadge.Name = hardwareInfo.OperatingSystem.Name;
                    systemBadge.Manufacturer = "Microsoft";
                    systemBadge.CompatibilityScore = await GetCompatibilityScoreAsync(HardwareType.System, hardwareInfo.OperatingSystem);
                    systemBadge.Details = $"Version: {hardwareInfo.OperatingSystem.Version}, Build: {hardwareInfo.OperatingSystem.BuildNumber}";

                    // Check if this is newly detected hardware
                    if (!wasDetectedBefore)
                    {
                        _isNewlyDetected[HardwareType.System] = true;
                        _lastDetectionTimes[HardwareType.System] = DateTime.Now;
                        _logger.Log($"New operating system detected: {systemBadge.Name}", LogLevel.SUCCESS);
                    }
                }

                // Raise event to notify listeners
                BadgesUpdated?.Invoke(this, new HardwareBadgeUpdatedEventArgs(_badgeInfo));

                _logger.Log("Hardware badges refreshed", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error refreshing hardware badges: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets badge info for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>The badge info</returns>
        public HardwareBadgeInfo GetBadgeInfo(HardwareType hardwareType)
        {
            if (_badgeInfo.TryGetValue(hardwareType, out var badgeInfo))
            {
                return badgeInfo;
            }
            return null;
        }

        /// <summary>
        /// Gets all badge info
        /// </summary>
        /// <returns>Dictionary of badge info by hardware type</returns>
        public Dictionary<HardwareType, HardwareBadgeInfo> GetAllBadgeInfo()
        {
            return new Dictionary<HardwareType, HardwareBadgeInfo>(_badgeInfo);
        }

        /// <summary>
        /// Checks if a hardware component is newly detected
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>True if the hardware is newly detected, false otherwise</returns>
        public bool IsNewlyDetected(HardwareType hardwareType)
        {
            if (_isNewlyDetected.TryGetValue(hardwareType, out bool isNew))
            {
                return isNew;
            }
            return false;
        }

        /// <summary>
        /// Gets the time when a hardware component was detected
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>The detection time, or DateTime.MinValue if not detected</returns>
        public DateTime GetDetectionTime(HardwareType hardwareType)
        {
            if (_lastDetectionTimes.TryGetValue(hardwareType, out DateTime time))
            {
                return time;
            }
            return DateTime.MinValue;
        }

        /// <summary>
        /// Resets the newly detected flag for a hardware component
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        public void ResetNewlyDetected(HardwareType hardwareType)
        {
            if (_isNewlyDetected.ContainsKey(hardwareType))
            {
                _isNewlyDetected[hardwareType] = false;
            }
        }

        /// <summary>
        /// Gets the compatibility score for a hardware component
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="hardwareComponent">The hardware component</param>
        /// <returns>The compatibility score (0-100)</returns>
        private async Task<int> GetCompatibilityScoreAsync(HardwareType hardwareType, object hardwareComponent)
        {
            try
            {
                // Create a mock optimization for the hardware type
                var optimization = new HardwareSpecificOptimization
                {
                    Name = $"{hardwareType} Optimization",
                    Description = $"Optimization for {hardwareType}",
                    HardwareType = hardwareType,
                    RequiredHardware = new List<HardwareRequirement>
                    {
                        new HardwareRequirement
                        {
                            Type = hardwareType,
                            MinimumRequirement = "Any"
                        }
                    }
                };

                // Get compatibility result from compatibility service
                var result = await _hardwareCompatibilityService.ValidateOptimizationAsync(optimization, true);

                // Calculate score based on compatibility
                if (result.IsCompatible)
                {
                    // Fully compatible - high score
                    return result.Confidence == CompatibilityConfidence.High ? 95 : 85;
                }
                else
                {
                    // Not compatible - lower score based on confidence
                    return result.Confidence == CompatibilityConfidence.High ? 40 : 60;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting compatibility score: {ex.Message}", LogLevel.ERROR);
                return 50; // Default to medium score
            }
        }
    }
}
