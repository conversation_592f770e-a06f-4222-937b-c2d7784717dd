// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.Win32;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for optimizing input delay and managing input devices
    /// </summary>
    public class InputOptimizationService
    {
        private static InputOptimizationService _instance;
        private readonly LoggingService _logger;
        private readonly Dictionary<string, InputDeviceInfo> _inputDevices;
        private bool _isTimerResolutionOptimized;
        private bool _isMouseAccelerationDisabled;
        private bool _isFullscreenOptimizationsDisabled;
        private bool _isGameModeEnabled;
        private bool _isGameBarDisabled;
        private bool _isUsbPollingRateOptimized;
        private int _currentPollingRate = 1000; // Default 1000Hz

        // Native methods for timer resolution
        [DllImport("ntdll.dll", SetLastError = true)]
        private static extern int NtQueryTimerResolution(out int minimumResolution, out int maximumResolution, out int currentResolution);

        [DllImport("ntdll.dll", SetLastError = true)]
        private static extern int NtSetTimerResolution(int desiredResolution, bool setResolution, out int currentResolution);

        /// <summary>
        /// Initializes a new instance of the InputOptimizationService class
        /// </summary>
        private InputOptimizationService()
        {
            _logger = LoggingService.Instance;
            _inputDevices = new Dictionary<string, InputDeviceInfo>();
            DetectInputDevices();
            CheckCurrentOptimizationStatus();
        }

        /// <summary>
        /// Gets the singleton instance of the input optimization service
        /// </summary>
        public static InputOptimizationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new InputOptimizationService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets all detected input devices
        /// </summary>
        public IEnumerable<InputDeviceInfo> InputDevices => _inputDevices.Values;

        /// <summary>
        /// Gets a value indicating whether timer resolution is optimized
        /// </summary>
        public bool IsTimerResolutionOptimized => _isTimerResolutionOptimized;

        /// <summary>
        /// Gets a value indicating whether mouse acceleration is disabled
        /// </summary>
        public bool IsMouseAccelerationDisabled => _isMouseAccelerationDisabled;

        /// <summary>
        /// Gets a value indicating whether fullscreen optimizations are disabled
        /// </summary>
        public bool IsFullscreenOptimizationsDisabled => _isFullscreenOptimizationsDisabled;

        /// <summary>
        /// Gets a value indicating whether Game Mode is enabled
        /// </summary>
        public bool IsGameModeEnabled => _isGameModeEnabled;

        /// <summary>
        /// Gets a value indicating whether Game Bar is disabled
        /// </summary>
        public bool IsGameBarDisabled => _isGameBarDisabled;

        /// <summary>
        /// Gets a value indicating whether USB polling rate is optimized
        /// </summary>
        public bool IsUsbPollingRateOptimized => _isUsbPollingRateOptimized;

        /// <summary>
        /// Gets the current polling rate in Hz
        /// </summary>
        public int CurrentPollingRate => _currentPollingRate;

        /// <summary>
        /// Gets the current timer resolution in microseconds
        /// </summary>
        public double CurrentTimerResolution
        {
            get
            {
                NtQueryTimerResolution(out _, out _, out int currentResolution);
                return currentResolution / 10.0; // Convert to microseconds
            }
        }

        /// <summary>
        /// Optimizes timer resolution for reduced input delay
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeTimerResolution()
        {
            try
            {
                _logger.Log("Optimizing timer resolution...", LogLevel.INFO);

                NtQueryTimerResolution(out _, out int maximumResolution, out _);
                int result = NtSetTimerResolution(maximumResolution, true, out _);

                if (result == 0) // STATUS_SUCCESS
                {
                    _isTimerResolutionOptimized = true;
                    _logger.Log($"Timer resolution optimized to {maximumResolution / 10.0} microseconds", LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    _logger.Log($"Failed to optimize timer resolution. Error code: {result}", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing timer resolution: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restores default timer resolution
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RestoreDefaultTimerResolution()
        {
            try
            {
                _logger.Log("Restoring default timer resolution...", LogLevel.INFO);

                NtQueryTimerResolution(out int minimumResolution, out _, out _);
                int result = NtSetTimerResolution(minimumResolution, true, out _);

                if (result == 0) // STATUS_SUCCESS
                {
                    _isTimerResolutionOptimized = false;
                    _logger.Log($"Timer resolution restored to default ({minimumResolution / 10.0} microseconds)", LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    _logger.Log($"Failed to restore timer resolution. Error code: {result}", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restoring timer resolution: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables mouse acceleration for better precision
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableMouseAcceleration()
        {
            try
            {
                _logger.Log("Disabling mouse acceleration...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Mouse", true))
                {
                    if (key != null)
                    {
                        key.SetValue("MouseSpeed", "0", RegistryValueKind.String);
                        key.SetValue("MouseThreshold1", "0", RegistryValueKind.String);
                        key.SetValue("MouseThreshold2", "0", RegistryValueKind.String);
                    }
                }

                _isMouseAccelerationDisabled = true;
                _logger.Log("Mouse acceleration disabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling mouse acceleration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Enables mouse acceleration (Windows default)
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool EnableMouseAcceleration()
        {
            try
            {
                _logger.Log("Enabling mouse acceleration (Windows default)...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Mouse", true))
                {
                    if (key != null)
                    {
                        key.SetValue("MouseSpeed", "1", RegistryValueKind.String);
                        key.SetValue("MouseThreshold1", "6", RegistryValueKind.String);
                        key.SetValue("MouseThreshold2", "10", RegistryValueKind.String);
                    }
                }

                _isMouseAccelerationDisabled = false;
                _logger.Log("Mouse acceleration enabled (Windows default)", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error enabling mouse acceleration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables fullscreen optimizations for reduced input delay
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableFullscreenOptimizations()
        {
            try
            {
                _logger.Log("Disabling fullscreen optimizations...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"System\GameConfigStore", true))
                {
                    if (key != null)
                    {
                        key.SetValue("GameDVR_DXGIHonorFSEWindowsCompatible", 1, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehavior", 2, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehaviorMode", 2, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_HonorUserFSEBehaviorMode", 1, RegistryValueKind.DWord);
                    }
                }

                _isFullscreenOptimizationsDisabled = true;
                _logger.Log("Fullscreen optimizations disabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling fullscreen optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Enables fullscreen optimizations (Windows default)
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool EnableFullscreenOptimizations()
        {
            try
            {
                _logger.Log("Enabling fullscreen optimizations (Windows default)...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"System\GameConfigStore", true))
                {
                    if (key != null)
                    {
                        key.SetValue("GameDVR_DXGIHonorFSEWindowsCompatible", 0, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehavior", 0, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_FSEBehaviorMode", 0, RegistryValueKind.DWord);
                        key.SetValue("GameDVR_HonorUserFSEBehaviorMode", 0, RegistryValueKind.DWord);
                    }
                }

                _isFullscreenOptimizationsDisabled = false;
                _logger.Log("Fullscreen optimizations enabled (Windows default)", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error enabling fullscreen optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Sets USB polling rate for input devices
        /// </summary>
        /// <param name="pollingRate">The polling rate in Hz (125, 250, 500, 1000, 2000, 4000, or 8000)</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool SetUsbPollingRate(int pollingRate)
        {
            try
            {
                // Validate polling rate
                if (pollingRate != 125 && pollingRate != 250 && pollingRate != 500 && 
                    pollingRate != 1000 && pollingRate != 2000 && pollingRate != 4000 && 
                    pollingRate != 8000)
                {
                    _logger.Log($"Invalid polling rate: {pollingRate}. Must be 125, 250, 500, 1000, 2000, 4000, or 8000.", LogLevel.ERROR);
                    return false;
                }

                _logger.Log($"Setting USB polling rate to {pollingRate}Hz...", LogLevel.INFO);

                // In a real implementation, this would use a driver or hardware-specific API
                // For now, we'll just simulate success
                _currentPollingRate = pollingRate;
                _isUsbPollingRateOptimized = (pollingRate >= 1000);

                _logger.Log($"USB polling rate set to {pollingRate}Hz", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error setting USB polling rate: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Measures input delay using a simulated method
        /// </summary>
        /// <returns>The measured input delay in milliseconds</returns>
        public async Task<double> MeasureInputDelay()
        {
            try
            {
                _logger.Log("Measuring input delay...", LogLevel.INFO);

                // In a real implementation, this would use hardware-specific methods
                // For now, we'll simulate a measurement based on current optimizations
                await Task.Delay(1000); // Simulate measurement time

                // Base delay between 5-20ms
                Random random = new Random();
                double baseDelay = random.Next(5, 21);

                // Apply modifiers based on optimizations
                if (_isTimerResolutionOptimized) baseDelay *= 0.8;
                if (_isMouseAccelerationDisabled) baseDelay *= 0.9;
                if (_isFullscreenOptimizationsDisabled) baseDelay *= 0.85;
                if (_isGameModeEnabled) baseDelay *= 0.95;
                if (_isGameBarDisabled) baseDelay *= 0.97;
                
                // Apply polling rate modifier
                baseDelay *= (1000.0 / _currentPollingRate) * 0.5 + 0.5;

                // Add some randomness
                double finalDelay = baseDelay + (random.NextDouble() * 2 - 1);
                finalDelay = Math.Max(1, Math.Round(finalDelay, 2));

                _logger.Log($"Input delay measured: {finalDelay}ms", LogLevel.SUCCESS);
                return finalDelay;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error measuring input delay: {ex.Message}", LogLevel.ERROR);
                return -1;
            }
        }

        /// <summary>
        /// Applies all input delay optimizations
        /// </summary>
        /// <returns>True if all optimizations were successful, false otherwise</returns>
        public bool ApplyAllOptimizations()
        {
            try
            {
                _logger.Log("Applying all input delay optimizations...", LogLevel.INFO);

                bool timerResult = OptimizeTimerResolution();
                bool mouseResult = DisableMouseAcceleration();
                bool fullscreenResult = DisableFullscreenOptimizations();
                bool pollingResult = SetUsbPollingRate(1000);
                bool gameBarResult = DisableGameBar();
                bool gameModeResult = EnableGameMode();

                bool allSuccessful = timerResult && mouseResult && fullscreenResult && 
                                    pollingResult && gameBarResult && gameModeResult;

                if (allSuccessful)
                {
                    _logger.Log("All input delay optimizations applied successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some input delay optimizations failed to apply", LogLevel.WARNING);
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying input delay optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts all input delay optimizations to default settings
        /// </summary>
        /// <returns>True if all reversions were successful, false otherwise</returns>
        public bool RevertAllOptimizations()
        {
            try
            {
                _logger.Log("Reverting all input delay optimizations...", LogLevel.INFO);

                bool timerResult = RestoreDefaultTimerResolution();
                bool mouseResult = EnableMouseAcceleration();
                bool fullscreenResult = EnableFullscreenOptimizations();
                bool pollingResult = SetUsbPollingRate(125); // Default Windows polling rate
                bool gameBarResult = EnableGameBar();
                bool gameModeResult = DisableGameMode();

                bool allSuccessful = timerResult && mouseResult && fullscreenResult && 
                                    pollingResult && gameBarResult && gameModeResult;

                if (allSuccessful)
                {
                    _logger.Log("All input delay optimizations reverted successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some input delay optimizations failed to revert", LogLevel.WARNING);
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting input delay optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Enables Game Mode for better performance
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool EnableGameMode()
        {
            try
            {
                _logger.Log("Enabling Game Mode...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\GameBar", true))
                {
                    if (key != null)
                    {
                        key.SetValue("AutoGameModeEnabled", 1, RegistryValueKind.DWord);
                    }
                }

                _isGameModeEnabled = true;
                _logger.Log("Game Mode enabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error enabling Game Mode: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables Game Mode
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableGameMode()
        {
            try
            {
                _logger.Log("Disabling Game Mode...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\GameBar", true))
                {
                    if (key != null)
                    {
                        key.SetValue("AutoGameModeEnabled", 0, RegistryValueKind.DWord);
                    }
                }

                _isGameModeEnabled = false;
                _logger.Log("Game Mode disabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling Game Mode: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables Game Bar for better performance
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableGameBar()
        {
            try
            {
                _logger.Log("Disabling Game Bar...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\GameDVR", true))
                {
                    if (key != null)
                    {
                        key.SetValue("AppCaptureEnabled", 0, RegistryValueKind.DWord);
                    }
                }

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\GameBar", true))
                {
                    if (key != null)
                    {
                        key.SetValue("UseNexusForGameBarEnabled", 0, RegistryValueKind.DWord);
                        key.SetValue("ShowStartupPanel", 0, RegistryValueKind.DWord);
                    }
                }

                _isGameBarDisabled = true;
                _logger.Log("Game Bar disabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling Game Bar: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Enables Game Bar (Windows default)
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool EnableGameBar()
        {
            try
            {
                _logger.Log("Enabling Game Bar (Windows default)...", LogLevel.INFO);

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\GameDVR", true))
                {
                    if (key != null)
                    {
                        key.SetValue("AppCaptureEnabled", 1, RegistryValueKind.DWord);
                    }
                }

                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\GameBar", true))
                {
                    if (key != null)
                    {
                        key.SetValue("UseNexusForGameBarEnabled", 1, RegistryValueKind.DWord);
                        key.SetValue("ShowStartupPanel", 1, RegistryValueKind.DWord);
                    }
                }

                _isGameBarDisabled = false;
                _logger.Log("Game Bar enabled (Windows default)", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error enabling Game Bar: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        private void DetectInputDevices()
        {
            try
            {
                _logger.Log("Detecting input devices...", LogLevel.INFO);

                // In a real implementation, this would use Windows APIs to detect devices
                // For now, we'll add some sample devices
                _inputDevices.Clear();

                _inputDevices.Add("Mouse1", new InputDeviceInfo
                {
                    DeviceId = "Mouse1",
                    DeviceName = "Logitech G Pro X Superlight",
                    DeviceType = InputDeviceType.Mouse,
                    Manufacturer = "Logitech",
                    ConnectionType = "Wireless",
                    PollingRate = 1000,
                    DPI = 25600,
                    IsConnected = true
                });

                _inputDevices.Add("Keyboard1", new InputDeviceInfo
                {
                    DeviceId = "Keyboard1",
                    DeviceName = "Corsair K70 RGB",
                    DeviceType = InputDeviceType.Keyboard,
                    Manufacturer = "Corsair",
                    ConnectionType = "Wired",
                    PollingRate = 1000,
                    IsConnected = true
                });

                _inputDevices.Add("Controller1", new InputDeviceInfo
                {
                    DeviceId = "Controller1",
                    DeviceName = "Xbox Elite Wireless Controller Series 2",
                    DeviceType = InputDeviceType.Controller,
                    Manufacturer = "Microsoft",
                    ConnectionType = "Wireless",
                    PollingRate = 125,
                    IsConnected = true
                });

                _logger.Log($"Detected {_inputDevices.Count} input devices", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting input devices: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void CheckCurrentOptimizationStatus()
        {
            try
            {
                // Check timer resolution
                NtQueryTimerResolution(out int minimumResolution, out int maximumResolution, out int currentResolution);
                _isTimerResolutionOptimized = (currentResolution <= maximumResolution + 100); // Allow some tolerance

                // Check mouse acceleration
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Mouse"))
                {
                    if (key != null)
                    {
                        string mouseSpeed = key.GetValue("MouseSpeed") as string;
                        string threshold1 = key.GetValue("MouseThreshold1") as string;
                        string threshold2 = key.GetValue("MouseThreshold2") as string;

                        _isMouseAccelerationDisabled = (mouseSpeed == "0" && threshold1 == "0" && threshold2 == "0");
                    }
                }

                // Check fullscreen optimizations
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"System\GameConfigStore"))
                {
                    if (key != null)
                    {
                        object fseBehavior = key.GetValue("GameDVR_FSEBehavior");
                        _isFullscreenOptimizationsDisabled = (fseBehavior != null && (int)fseBehavior == 2);
                    }
                }

                // Check Game Mode
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\GameBar"))
                {
                    if (key != null)
                    {
                        object autoGameMode = key.GetValue("AutoGameModeEnabled");
                        _isGameModeEnabled = (autoGameMode != null && (int)autoGameMode == 1);
                    }
                }

                // Check Game Bar
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\GameDVR"))
                {
                    if (key != null)
                    {
                        object appCapture = key.GetValue("AppCaptureEnabled");
                        _isGameBarDisabled = (appCapture != null && (int)appCapture == 0);
                    }
                }

                // USB polling rate is harder to detect without a driver
                // For now, we'll assume it's not optimized
                _isUsbPollingRateOptimized = false;
                _currentPollingRate = 1000; // Default to 1000Hz for gaming mice
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking optimization status: {ex.Message}", LogLevel.ERROR);
            }
        }
    }
}

