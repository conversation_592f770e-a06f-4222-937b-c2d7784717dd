<UserControl x:Class="CircleUtility.Controls.NotificationBadge"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d"
             d:DesignHeight="20" d:DesignWidth="20">

    <UserControl.Resources>
        <!-- Pulse animation -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="BadgeEllipse"
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0.5" Duration="0:0:0.5"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="BadgeEllipse"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1" To="1.1" Duration="0:0:0.5"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="BadgeEllipse"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1" To="1.1" Duration="0:0:0.5"
                           AutoReverse="True"/>
        </Storyboard>
    </UserControl.Resources>

    <Grid>
        <Ellipse x:Name="BadgeEllipse"
                 Fill="#FFFF5252"
                 Width="{Binding ActualHeight, RelativeSource={RelativeSource Self}}"
                 Stroke="White"
                 StrokeThickness="1"
                 RenderTransformOrigin="0.5,0.5">
            <Ellipse.RenderTransform>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Ellipse.RenderTransform>
            <Ellipse.Effect>
                <DropShadowEffect Color="#FFFF5252"
                                 BlurRadius="5"
                                 ShadowDepth="0"
                                 Opacity="0.5"/>
            </Ellipse.Effect>
        </Ellipse>

        <TextBlock x:Name="CountText"
                   Text="{Binding Count, RelativeSource={RelativeSource AncestorType=UserControl}}"
                   FontFamily="Consolas"
                   FontSize="10"
                   FontWeight="Bold"
                   Foreground="White"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"/>
    </Grid>
</UserControl>
