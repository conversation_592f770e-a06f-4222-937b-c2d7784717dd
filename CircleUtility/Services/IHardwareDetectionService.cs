// Created by Arsenal on 5-17-25 12:15PM
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Interface for hardware detection service
    /// </summary>
    public interface IHardwareDetectionService
    {
        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Gets hardware information
        /// </summary>
        /// <returns>The hardware information</returns>
        HardwareInfo GetHardwareInfo();

        /// <summary>
        /// Gets hardware information
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the cache</param>
        /// <returns>The hardware information</returns>
        HardwareInfo GetHardwareInfo(bool forceRefresh);

        /// <summary>
        /// Gets hardware information asynchronously
        /// </summary>
        /// <returns>The hardware information</returns>
        Task<HardwareInfo> GetHardwareInfoAsync();

        /// <summary>
        /// Gets hardware information asynchronously
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the cache</param>
        /// <returns>The hardware information</returns>
        Task<HardwareInfo> GetHardwareInfoAsync(bool forceRefresh);
    }
}
