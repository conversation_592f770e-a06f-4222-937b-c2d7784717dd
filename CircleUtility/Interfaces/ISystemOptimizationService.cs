using System.Threading.Tasks;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for system optimization service
    /// </summary>
    public interface ISystemOptimizationService
    {
        /// <summary>
        /// Gets whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Initializes the service asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task InitializeAsync();

        /// <summary>
        /// Applies system optimization
        /// </summary>
        /// <param name="optimizationName">Name of optimization to apply</param>
        /// <returns>True if successful</returns>
        bool ApplyOptimization(string optimizationName);

        /// <summary>
        /// Reverts system optimization
        /// </summary>
        /// <param name="optimizationName">Name of optimization to revert</param>
        /// <returns>True if successful</returns>
        bool RevertOptimization(string optimizationName);

        /// <summary>
        /// Gets the current optimization score
        /// </summary>
        /// <returns>Optimization score</returns>
        int GetOptimizationScore();

        /// <summary>
        /// Optimizes network settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeNetworkSettings();

        /// <summary>
        /// Optimizes power settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizePowerSettings();

        /// <summary>
        /// Optimizes input delay settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeInputDelay();

        /// <summary>
        /// Optimizes GPU settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeGpuSettings();

        /// <summary>
        /// Optimizes thermal settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeThermalSettings();

        /// <summary>
        /// Optimizes mouse settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeMouseSettings();

        /// <summary>
        /// Optimizes keyboard settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeKeyboardSettings();

        /// <summary>
        /// Optimizes controller settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeControllerSettings();

        /// <summary>
        /// Reverts network settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertNetworkSettings();

        /// <summary>
        /// Reverts power settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertPowerSettings();

        /// <summary>
        /// Reverts input delay settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertInputDelay();

        /// <summary>
        /// Reverts GPU settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertGpuSettings();

        /// <summary>
        /// Reverts thermal settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertThermalSettings();

        /// <summary>
        /// Reverts mouse settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertMouseSettings();

        /// <summary>
        /// Reverts keyboard settings
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertKeyboardSettings();
    }
}
