<Window x:Class="CircleUtility.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:viewmodels="clr-namespace:CircleUtility.ViewModels"
        xmlns:views="clr-namespace:CircleUtility.Views"
        xmlns:converters="clr-namespace:CircleUtility.Converters"
        Title="The Circle's Utility" Height="800" Width="1100"
        WindowStartupLocation="CenterScreen"
        Background="#FF000000">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Converters/ConverterResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <!-- Menu button style -->
            <Style x:Key="MenuButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="#FF050A0F"/>
                <Setter Property="Foreground" Value="#FF00DCFF"/>
                <Setter Property="FontFamily" Value="Consolas"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <Border x:Name="ButtonBorder"
                                        Background="{TemplateBinding Background}"
                                        BorderThickness="0">
                                    <Grid>
                                        <Rectangle x:Name="LeftAccent"
                                                   Width="3"
                                                   HorizontalAlignment="Left"
                                                   Fill="#FF00C8FF"/>
                                        <ContentPresenter Margin="15,0,0,0"
                                                          VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#FF0A1420" TargetName="ButtonBorder"/>
                                    <Setter Property="Fill" Value="#FF00E8FF" TargetName="LeftAccent"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#FF0F1A28" TargetName="ButtonBorder"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Rainbow animation for title -->
            <Storyboard x:Key="RainbowAnimation" RepeatBehavior="Forever">
                <ColorAnimationUsingKeyFrames Storyboard.TargetName="TitleText"
                                              Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                              Duration="0:0:3" RepeatBehavior="Forever">
                    <LinearColorKeyFrame KeyTime="0:0:0" Value="#FF00C8FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:0.5" Value="#FF00FFD0"/>
                    <LinearColorKeyFrame KeyTime="0:0:1" Value="#FF80FFFF"/>
                    <LinearColorKeyFrame KeyTime="0:0:1.5" Value="#FF00A0FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:2" Value="#FF0080C0"/>
                    <LinearColorKeyFrame KeyTime="0:0:2.5" Value="#FF00C0FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:3" Value="#FF00C8FF"/>
                </ColorAnimationUsingKeyFrames>
            </Storyboard>

            <!-- Rainbow animation for username -->
            <Storyboard x:Key="UsernameRainbowAnimation" RepeatBehavior="Forever">
                <ColorAnimationUsingKeyFrames Storyboard.TargetName="UsernameText"
                                              Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                              Duration="0:0:2" RepeatBehavior="Forever">
                    <LinearColorKeyFrame KeyTime="0:0:0" Value="#FF00C8FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:0.3" Value="#FF00FFD0"/>
                    <LinearColorKeyFrame KeyTime="0:0:0.6" Value="#FF80FFFF"/>
                    <LinearColorKeyFrame KeyTime="0:0:0.9" Value="#FF00A0FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:1.2" Value="#FF0080C0"/>
                    <LinearColorKeyFrame KeyTime="0:0:1.6" Value="#FF00C0FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:2" Value="#FF00C8FF"/>
                </ColorAnimationUsingKeyFrames>
            </Storyboard>

            <!-- Rainbow animation for loading title -->
            <Storyboard x:Key="LoadingTitleRainbowAnimation" RepeatBehavior="Forever">
                <ColorAnimationUsingKeyFrames Storyboard.TargetName="LoadingTitle"
                                              Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                              Duration="0:0:3" RepeatBehavior="Forever">
                    <LinearColorKeyFrame KeyTime="0:0:0" Value="#FF00C8FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:0.5" Value="#FF00FFD0"/>
                    <LinearColorKeyFrame KeyTime="0:0:1" Value="#FF80FFFF"/>
                    <LinearColorKeyFrame KeyTime="0:0:1.5" Value="#FF00A0FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:2" Value="#FF0080C0"/>
                    <LinearColorKeyFrame KeyTime="0:0:2.5" Value="#FF00C0FF"/>
                    <LinearColorKeyFrame KeyTime="0:0:3" Value="#FF00C8FF"/>
                </ColorAnimationUsingKeyFrames>
            </Storyboard>


        </ResourceDictionary>
    </Window.Resources>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Window.Loaded">
            <BeginStoryboard Storyboard="{StaticResource RainbowAnimation}"/>
            <BeginStoryboard Storyboard="{StaticResource UsernameRainbowAnimation}"/>
        </EventTrigger>
    </Window.Triggers>

    <Grid>
        <!-- Main Application Content -->
        <Grid x:Name="MainContent" Visibility="Collapsed">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="30"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="#FF050A0F" BorderThickness="0,0,0,1" BorderBrush="#FF00C8FF">
                <Grid>
                    <!-- Tech patterns - left side -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="20,0,0,0">
                        <Rectangle Width="3" Height="30" Fill="#FF00B4F0" Margin="0,0,7,0"/>
                        <Rectangle Width="3" Height="20" Fill="#FF00A0DC" Margin="0,5,7,0"/>
                        <Rectangle Width="3" Height="10" Fill="#FF008CC8" Margin="0,10,0,0"/>
                    </StackPanel>

                    <!-- Tech patterns - right side -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,20,0">
                        <Rectangle Width="3" Height="10" Fill="#FF008CC8" Margin="0,10,7,0"/>
                        <Rectangle Width="3" Height="20" Fill="#FF00A0DC" Margin="0,5,7,0"/>
                        <Rectangle Width="3" Height="30" Fill="#FF00B4F0"/>
                    </StackPanel>

                    <!-- Title with shadow effect -->
                    <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock x:Name="TitleShadow"
                                   Text="THE CIRCLE UTILITY"
                                   FontFamily="Consolas"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   Foreground="#FF004060"
                                   Margin="2,2,0,0"/>

                        <TextBlock x:Name="TitleText"
                                   Text="THE CIRCLE UTILITY"
                                   FontFamily="Consolas"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"/>
                    </Grid>
                </Grid>
            </Border>

            <!-- Main content area -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="220"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Navigation menu -->
                <Border Grid.Column="0" Background="#FF050A0F" BorderThickness="0,0,1,0" BorderBrush="#FF00B4F0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="45"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Menu header -->
                        <Border Grid.Row="0" Background="#FF0A141E" BorderThickness="0,0,0,1" BorderBrush="#FF00C8FF">
                            <TextBlock Text=">> NAVIGATION"
                                       FontFamily="Consolas"
                                       FontSize="14"
                                       FontWeight="Bold"
                                       Foreground="#FF00DCFF"
                                       VerticalAlignment="Center"
                                       Margin="20,0,0,0"/>
                        </Border>

                        <!-- Menu buttons -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <ItemsControl x:Name="NavMenu"
                                          ItemsSource="{Binding MenuItems}"
                                          Margin="0,10,0,0">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Button Content="{Binding DisplayName}"
                                                Command="{Binding Command}"
                                                Style="{StaticResource MenuButtonStyle}">
                                            <Button.Triggers>
                                                <EventTrigger RoutedEvent="Button.MouseEnter">
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <ColorAnimation Storyboard.TargetProperty="(Button.Foreground).(SolidColorBrush.Color)"
                                                                            To="#FF00FFFF" Duration="0:0:0.2"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                                <EventTrigger RoutedEvent="Button.MouseLeave">
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <ColorAnimation Storyboard.TargetProperty="(Button.Foreground).(SolidColorBrush.Color)"
                                                                            To="#FF00DCFF" Duration="0:0:0.2"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                            </Button.Triggers>
                                        </Button>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- Tech pattern on left border -->
                        <Rectangle Grid.Row="1" Width="2" Height="30" Fill="#FF00C8FF" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,50,0,0"/>
                        <Rectangle Grid.Row="1" Width="2" Height="30" Fill="#FF00C8FF" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,95,0,0"/>
                        <Rectangle Grid.Row="1" Width="2" Height="30" Fill="#FF00C8FF" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,140,0,0"/>
                    </Grid>
                </Border>

                <!-- Content area -->
                <Border Grid.Column="1" Background="Black" Padding="20">
                    <ContentControl x:Name="ContentArea" Content="{Binding CurrentView}">
                        <ContentControl.Resources>
                            <!-- View templates -->
                            <DataTemplate DataType="{x:Type viewmodels:DashboardViewModel}">
                                <views:DashboardView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:SystemTweaksViewModel}">
                                <views:SystemTweaksView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:InputDelayViewModel}">
                                <views:InputDelayView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:ControllerTweaksViewModel}">
                                <views:ControllerTweaksView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:GpuOptimizationViewModel}">
                                <views:GpuOptimizationView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:BenchmarkViewModel}">
                                <views:BenchmarkView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:ToolsViewModel}">
                                <views:ToolsView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:DebloatViewModel}">
                                <views:DebloatView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:SettingsViewModel}">
                                <views:SettingsView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:DiscordViewModel}">
                                <views:DiscordView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:RevertTweaksViewModel}">
                                <views:RevertTweaksView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:AdminViewModel}">
                                <views:AdminView/>
                            </DataTemplate>
                            <DataTemplate DataType="{x:Type viewmodels:PerformanceOptimizerViewModel}">
                                <views:PerformanceOptimizerView/>
                            </DataTemplate>
                        </ContentControl.Resources>
                    </ContentControl>
                </Border>
            </Grid>

            <!-- Status bar -->
            <Border Grid.Row="2" Background="#FF050A0F" BorderThickness="0,1,0,0" BorderBrush="#FF004080">
                <Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0,0,0">
                        <TextBlock Text="Status: "
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FF808080"/>
                        <TextBlock Text="Active"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FF00FF00"/>

                        <Ellipse Width="8" Height="8" Fill="#FF00FF00" Margin="5,0,0,0">
                            <Ellipse.Effect>
                                <BlurEffect Radius="2"/>
                            </Ellipse.Effect>
                        </Ellipse>

                        <TextBlock Text=" | Discord: "
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FF808080"
                                   Margin="10,0,0,0"/>

                        <TextBlock Text="Connected"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FF00FF00"
                                   Visibility="{Binding IsDiscordConnected, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <TextBlock Text="Disconnected"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FFFF6060"
                                   Visibility="{Binding IsDiscordConnected, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>

                        <Ellipse Width="8" Height="8" Fill="#FF00FF00" Margin="5,0,0,0"
                                 Visibility="{Binding IsDiscordConnected, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Ellipse.Effect>
                                <BlurEffect Radius="2"/>
                            </Ellipse.Effect>
                        </Ellipse>

                        <Ellipse Width="8" Height="8" Fill="#FFFF6060" Margin="5,0,0,0"
                                 Visibility="{Binding IsDiscordConnected, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                            <Ellipse.Effect>
                                <BlurEffect Radius="2"/>
                            </Ellipse.Effect>
                        </Ellipse>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
                        <TextBlock Text="Logged in as: "
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FF808080"/>
                        <TextBlock x:Name="UsernameText"
                                   Text="{Binding Username}"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FF00C8FF"/>

                        <!-- Dev mode indicator -->
                        <TextBlock Text=" [DEV MODE]"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#FFFF3232"
                                   Visibility="{Binding IsDevMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                   Margin="5,0,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- Status message popup (commented out - missing converters) -->
        <!--
        <Border Background="#FF050A0F"
                BorderThickness="1"
                BorderBrush="{Binding IsStatusSuccess, Converter={StaticResource BoolToSuccessColorConverter}}"
                Margin="20"
                Padding="15,10"
                VerticalAlignment="Top"
                HorizontalAlignment="Center"
                Visibility="{Binding IsStatusVisible, Converter={StaticResource BoolToVisibilityConverter}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="{Binding IsStatusSuccess, Converter={StaticResource BoolToIconConverter}}"
                           FontFamily="Segoe UI Symbol"
                           FontSize="16"
                           Foreground="{Binding IsStatusSuccess, Converter={StaticResource BoolToSuccessColorConverter}}"
                           Margin="0,0,10,0"
                           VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1"
                           Text="{Binding StatusMessage}"
                           FontFamily="Segoe UI"
                           FontSize="12"
                           Foreground="White"
                           VerticalAlignment="Center"/>
            </Grid>
        </Border>
        -->

        <!-- Notification panel -->
        <Grid x:Name="NotificationPanel" Panel.ZIndex="100"/>

        <!-- Login Panel -->
        <Border x:Name="LoginPanel" Visibility="Visible" Background="#FF0A141E">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

                <!-- Login Title -->
                <TextBlock Text="CIRCLE UTILITY"
                           FontFamily="Consolas"
                           FontSize="36"
                           FontWeight="Bold"
                           Foreground="#FF00C8FF"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,50"/>

                <!-- Login Form -->
                <Border BorderBrush="#FF555555"
                        BorderThickness="1"
                        Background="#FF1A1A1A"
                        CornerRadius="8"
                        Padding="40"
                        HorizontalAlignment="Center">
                    <StackPanel Width="300">

                        <!-- Username Input -->
                        <TextBlock Text="USERNAME:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,10"/>

                        <TextBox x:Name="LoginUsernameInput"
                                 Height="35"
                                 FontFamily="Consolas"
                                 FontSize="14"
                                 Foreground="#FF00C8FF"
                                 Background="#FF0A141E"
                                 BorderBrush="#FF00B4F0"
                                 BorderThickness="1"
                                 Padding="10,8"
                                 Margin="0,0,0,20"/>

                        <!-- Password Input -->
                        <TextBlock Text="PASSWORD:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,10"/>

                        <Grid Margin="0,0,0,10">
                            <PasswordBox x:Name="LoginPasswordInput"
                                         Height="35"
                                         FontFamily="Consolas"
                                         FontSize="14"
                                         Foreground="#FF00C8FF"
                                         Background="#FF0A141E"
                                         BorderBrush="#FF00B4F0"
                                         BorderThickness="1"
                                         Padding="10,8,35,8"/>

                            <TextBox x:Name="LoginPasswordTextBox"
                                     Height="35"
                                     FontFamily="Consolas"
                                     FontSize="14"
                                     Foreground="#FF00C8FF"
                                     Background="#FF0A141E"
                                     BorderBrush="#FF00B4F0"
                                     BorderThickness="1"
                                     Padding="10,8,35,8"
                                     Visibility="Collapsed"/>

                            <!-- Show/Hide Password Button -->
                            <Button x:Name="TogglePasswordVisibility"
                                    Width="25"
                                    Height="25"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,0"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Cursor="Hand"
                                    Click="TogglePasswordVisibility_Click">
                                <Button.Content>
                                    <TextBlock x:Name="PasswordToggleIcon"
                                               Text="👁"
                                               FontSize="14"
                                               Foreground="#FF888888"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Button.Content>
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="3">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#FF333333"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </Grid>

                        <!-- Remember Me Checkbox -->
                        <CheckBox x:Name="RememberMeCheckBox"
                                  Content="Remember me"
                                  FontFamily="Consolas"
                                  FontSize="12"
                                  Foreground="#FF888888"
                                  Margin="0,0,0,20">
                            <CheckBox.Style>
                                <Style TargetType="CheckBox">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="CheckBox">
                                                <StackPanel Orientation="Horizontal">
                                                    <Border x:Name="CheckBoxBorder"
                                                            Width="16"
                                                            Height="16"
                                                            Background="#FF0A141E"
                                                            BorderBrush="#FF00B4F0"
                                                            BorderThickness="1"
                                                            CornerRadius="2"
                                                            Margin="0,0,8,0">
                                                        <TextBlock x:Name="CheckMark"
                                                                   Text="✓"
                                                                   FontSize="12"
                                                                   Foreground="#FF00C8FF"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"
                                                                   Visibility="Collapsed"/>
                                                    </Border>
                                                    <ContentPresenter VerticalAlignment="Center"/>
                                                </StackPanel>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsChecked" Value="True">
                                                        <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                                    </Trigger>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="#FF00E8FF"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </CheckBox.Style>
                        </CheckBox>

                        <!-- Login Button -->
                        <Button x:Name="LoginButton"
                                Content="LOGIN"
                                Height="45"
                                FontFamily="Consolas"
                                FontSize="16"
                                FontWeight="Bold"
                                Background="#FF00C8FF"
                                Foreground="White"
                                BorderThickness="0"
                                Cursor="Hand"
                                Click="LoginButton_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#FF0099CC"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#FF006699"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>

                        <!-- Register Link -->
                        <TextBlock Text="Don't have an account? Register now"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FF00C8FF"
                                   HorizontalAlignment="Center"
                                   Margin="0,15,0,0"
                                   Cursor="Hand"
                                   MouseDown="RegisterLink_MouseDown">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Foreground" Value="#FF00E8FF"/>
                                            <Setter Property="TextDecorations" Value="Underline"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <!-- Login Status -->
                        <TextBlock x:Name="LoginStatusText"
                                   Text=""
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FFFF6060"
                                   HorizontalAlignment="Center"
                                   Margin="0,15,0,0"
                                   Visibility="Collapsed"/>

                    </StackPanel>
                </Border>

            </StackPanel>
        </Border>

        <!-- Register Panel -->
        <Border x:Name="RegisterPanel" Visibility="Collapsed" Background="#FF0A141E">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

                <!-- Register Title -->
                <TextBlock Text="CREATE ACCOUNT"
                           FontFamily="Consolas"
                           FontSize="36"
                           FontWeight="Bold"
                           Foreground="#FF00C8FF"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,50"/>

                <!-- Register Form -->
                <Border BorderBrush="#FF555555"
                        BorderThickness="1"
                        Background="#FF1A1A1A"
                        CornerRadius="8"
                        Padding="40"
                        HorizontalAlignment="Center">
                    <StackPanel Width="300">

                        <!-- Username Input -->
                        <TextBlock Text="USERNAME:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,10"/>

                        <TextBox x:Name="RegisterUsernameInput"
                                 Height="35"
                                 FontFamily="Consolas"
                                 FontSize="14"
                                 Foreground="#FF00C8FF"
                                 Background="#FF0A141E"
                                 BorderBrush="#FF00B4F0"
                                 BorderThickness="1"
                                 Padding="10,8"
                                 Margin="0,0,0,20"/>

                        <!-- Password Input -->
                        <TextBlock Text="PASSWORD:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,10"/>

                        <Grid Margin="0,0,0,10">
                            <PasswordBox x:Name="RegisterPasswordInput"
                                         Height="35"
                                         FontFamily="Consolas"
                                         FontSize="14"
                                         Foreground="#FF00C8FF"
                                         Background="#FF0A141E"
                                         BorderBrush="#FF00B4F0"
                                         BorderThickness="1"
                                         Padding="10,8,35,8"/>

                            <TextBox x:Name="RegisterPasswordTextBox"
                                     Height="35"
                                     FontFamily="Consolas"
                                     FontSize="14"
                                     Foreground="#FF00C8FF"
                                     Background="#FF0A141E"
                                     BorderBrush="#FF00B4F0"
                                     BorderThickness="1"
                                     Padding="10,8,35,8"
                                     Visibility="Collapsed"/>

                            <!-- Show/Hide Password Button -->
                            <Button x:Name="RegisterTogglePasswordVisibility"
                                    Width="25"
                                    Height="25"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Margin="0,0,8,0"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Cursor="Hand"
                                    Click="RegisterTogglePasswordVisibility_Click">
                                <Button.Content>
                                    <TextBlock x:Name="RegisterPasswordToggleIcon"
                                               Text="👁"
                                               FontSize="14"
                                               Foreground="#FF888888"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Button.Content>
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="3">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#FF333333"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </Grid>

                        <!-- Confirm Password Input -->
                        <TextBlock Text="CONFIRM PASSWORD:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,10"/>

                        <PasswordBox x:Name="RegisterConfirmPasswordInput"
                                     Height="35"
                                     FontFamily="Consolas"
                                     FontSize="14"
                                     Foreground="#FF00C8FF"
                                     Background="#FF0A141E"
                                     BorderBrush="#FF00B4F0"
                                     BorderThickness="1"
                                     Padding="10,8"
                                     Margin="0,0,0,30"/>

                        <!-- Register Button -->
                        <Button x:Name="RegisterButton"
                                Content="REGISTER"
                                Height="45"
                                FontFamily="Consolas"
                                FontSize="16"
                                FontWeight="Bold"
                                Background="#FF00C8FF"
                                Foreground="White"
                                BorderThickness="0"
                                Cursor="Hand"
                                Click="RegisterButton_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#FF0099CC"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#FF006699"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>

                        <!-- Login Link -->
                        <TextBlock Text="Already have an account? Login"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FF00C8FF"
                                   HorizontalAlignment="Center"
                                   Margin="0,15,0,0"
                                   Cursor="Hand"
                                   MouseDown="LoginLink_MouseDown">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Foreground" Value="#FF00E8FF"/>
                                            <Setter Property="TextDecorations" Value="Underline"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <!-- Register Status -->
                        <TextBlock x:Name="RegisterStatusText"
                                   Text=""
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FFFF6060"
                                   HorizontalAlignment="Center"
                                   Margin="0,15,0,0"
                                   Visibility="Collapsed"/>

                    </StackPanel>
                </Border>

            </StackPanel>
        </Border>

        <!-- Loading Panel (LoadingScreenPreview Design) -->
        <Border x:Name="LoadingPanel" Visibility="Collapsed" Background="#FF0A141E">



            <!-- LoadingScreenPreview Content -->
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

                <!-- Loading Title -->
                <TextBlock x:Name="LoadingTitle"
                           Text="CIRCLE UTILITY"
                           FontFamily="Consolas"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="#FF00C8FF"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,40"/>

                <!-- Loading Spinner -->
                <Grid Width="80" Height="80" Margin="0,0,0,30">
                    <Ellipse Width="80"
                             Height="80"
                             Stroke="#FF00C8FF"
                             StrokeThickness="4"
                             StrokeDashArray="0.75,0.25"
                             StrokeDashCap="Round"
                             x:Name="LoadingSpinner">
                        <Ellipse.RenderTransform>
                            <RotateTransform Angle="0" CenterX="40" CenterY="40"/>
                        </Ellipse.RenderTransform>
                    </Ellipse>
                </Grid>

                <!-- Loading Status -->
                <TextBlock x:Name="LoadingStatus"
                           Text="INITIALIZING..."
                           FontFamily="Consolas"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>

                <!-- Clean Progress Bar -->
                <Grid Width="320" Height="25" Margin="0,0,0,20">
                    <!-- Progress Bar Container -->
                    <Border Width="300" Height="8"
                            Background="#FF333333"
                            BorderBrush="#FF555555"
                            BorderThickness="1"
                            CornerRadius="4">

                        <!-- Progress Bar -->
                        <ProgressBar x:Name="LoadingBar"
                                     Width="298"
                                     Height="6"
                                     Background="Transparent"
                                     Foreground="#FF00C8FF"
                                     BorderThickness="0"
                                     Value="0"/>
                    </Border>

                    <!-- Progress Percentage -->
                    <TextBlock x:Name="ProgressPercentage"
                               Text="0%"
                               FontFamily="Consolas"
                               FontSize="10"
                               FontWeight="Bold"
                               Foreground="#FF00C8FF"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Bottom"
                               Margin="0,0,0,-10"/>
                </Grid>

                <!-- Loading Details -->
                <TextBlock x:Name="StatusMessage"
                           Text="Initializing system..."
                           FontFamily="Consolas"
                           FontSize="12"
                           Foreground="#FF888888"
                           HorizontalAlignment="Center"
                           TextAlignment="Center"
                           TextWrapping="Wrap"
                           Width="400"
                           Margin="0,0,0,40"/>

                <!-- Access Button -->
                <Button x:Name="AccessButton"
                        Content="ACCESS SYSTEM"
                        Width="220"
                        Height="50"
                        FontFamily="Consolas"
                        FontSize="16"
                        FontWeight="Bold"
                        Background="#FF00C8FF"
                        Foreground="White"
                        BorderThickness="0"
                        Cursor="Hand"
                        Margin="0,20,0,0"
                        IsEnabled="False">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Grid>
                                            <!-- Outer glow effect -->
                                            <Border x:Name="GlowBorder"
                                                    Background="#3300C8FF"
                                                    CornerRadius="6"
                                                    Margin="-2"
                                                    Opacity="0">
                                                <Border.Effect>
                                                    <BlurEffect Radius="12"/>
                                                </Border.Effect>
                                            </Border>

                                            <!-- Inner glow effect -->
                                            <Border x:Name="InnerGlowBorder"
                                                    Background="#2200C8FF"
                                                    CornerRadius="6"
                                                    Margin="-1"
                                                    Opacity="0">
                                                <Border.Effect>
                                                    <BlurEffect Radius="6"/>
                                                </Border.Effect>
                                            </Border>

                                            <!-- Main button border -->
                                            <Border x:Name="MainBorder"
                                                    Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    BorderBrush="#FF00E8FF"
                                                    BorderThickness="2">
                                                <ContentPresenter HorizontalAlignment="Center"
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                        </Grid>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#FF0099CC"/>
                                                <Setter TargetName="GlowBorder" Property="Opacity" Value="1"/>
                                                <Setter TargetName="InnerGlowBorder" Property="Opacity" Value="0.5"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#FF006699"/>
                                                <Setter TargetName="GlowBorder" Property="Opacity" Value="0.7"/>
                                                <Setter TargetName="InnerGlowBorder" Property="Opacity" Value="0.3"/>
                                            </Trigger>
                                            <Trigger Property="IsEnabled" Value="False">
                                                <Setter Property="Opacity" Value="0.5"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

            </StackPanel>
        </Border>


    </Grid>
</Window>



