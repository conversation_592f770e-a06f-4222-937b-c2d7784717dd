using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CircleUtility.Models;
using CircleUtility.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing user sessions and security
    /// </summary>
    public class SecurityService : ISecurityService, IInitializableService, IDisposable
    {
        private static SecurityService _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;
        private readonly Dictionary<string, UserSession> _sessions;
        private readonly Timer _sessionCleanupTimer;
        private readonly TimeSpan _sessionTimeout = TimeSpan.FromMinutes(30); // Default session timeout: 30 minutes
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5); // Cleanup every 5 minutes
        private bool _disposed = false;

        /// <summary>
        /// Gets the singleton instance of the SecurityService
        /// </summary>
        public static SecurityService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new SecurityService();
                        }
                    }
                }
                else if (_instance._disposed)
                {
                    // If the instance was disposed, create a new one
                    lock (_lock)
                    {
                        if (_instance._disposed)
                        {
                            _instance = new SecurityService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Event raised when a session expires
        /// </summary>
        public event EventHandler<SessionEventArgs> SessionExpired;

        /// <summary>
        /// Gets the current session token
        /// </summary>
        public string CurrentSessionToken { get; private set; }

        /// <summary>
        /// Gets the current username
        /// </summary>
        public string CurrentUsername { get; private set; }

        /// <summary>
        /// Initializes a new instance of the SecurityService class
        /// </summary>
        private SecurityService()
        {
            _logger = LoggingService.Instance;
            _sessions = new Dictionary<string, UserSession>();

            // Start the session cleanup timer
            _sessionCleanupTimer = new Timer(CleanupSessions, null, _cleanupInterval, _cleanupInterval);
        }

        /// <summary>
        /// Public constructor for DI compatibility
        /// </summary>
        public SecurityService(bool forDI = false) : this() { }

        /// <summary>
        /// Creates a new session for the specified user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="isAdmin">Whether the user is an admin</param>
        /// <returns>The session token</returns>
        public string CreateSession(string username, bool isAdmin)
        {
            // Check if disposed
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SecurityService));
            }

            try
            {
                // Generate a session token
                string token = Guid.NewGuid().ToString();

                // Create a new session
                UserSession session = new UserSession
                {
                    Username = username,
                    Token = token,
                    IsAdmin = isAdmin,
                    CreatedAt = DateTime.Now,
                    LastActivity = DateTime.Now,
                    ExpiresAt = DateTime.Now.Add(_sessionTimeout)
                };

                // Add the session to the dictionary
                lock (_sessions)
                {
                    // Remove any existing session for this user
                    foreach (var existingSession in new List<UserSession>(_sessions.Values))
                    {
                        if (existingSession.Username == username)
                        {
                            _sessions.Remove(existingSession.Token);
                        }
                    }

                    // Add the new session
                    _sessions.Add(token, session);
                }

                // Set current session token and username
                CurrentSessionToken = token;
                CurrentUsername = username;

                // Log the action
                _logger.Log($"Created session for user: {username}", LogLevel.INFO);

                // Return the token
                return token;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating session: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Gets a session by token
        /// </summary>
        /// <param name="token">The session token</param>
        /// <returns>The session, or null if not found</returns>
        public UserSession GetSession(string token)
        {
            // Check if disposed
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SecurityService));
            }

            try
            {
                // Check if the session exists
                lock (_sessions)
                {
                    if (_sessions.TryGetValue(token, out UserSession session))
                    {
                        // Check if the session has expired
                        if (session.ExpiresAt < DateTime.Now)
                        {
                            // Session has expired
                            _sessions.Remove(token);
                            _logger.Log($"Session expired for user: {session.Username}", LogLevel.INFO);
                            SessionExpired?.Invoke(this, new SessionEventArgs { Session = session });
                            return null;
                        }

                        // Update last activity
                        session.LastActivity = DateTime.Now;
                        session.ExpiresAt = DateTime.Now.Add(_sessionTimeout);

                        return session;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting session: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Validates a session token
        /// </summary>
        /// <param name="token">The session token</param>
        /// <returns>True if the token is valid, false otherwise</returns>
        public bool ValidateSession(string token)
        {
            return GetSession(token) != null;
        }

        /// <summary>
        /// Ends a session
        /// </summary>
        /// <param name="token">The session token</param>
        public void EndSession(string token)
        {
            // Check if disposed
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SecurityService));
            }

            try
            {
                // Remove the session
                lock (_sessions)
                {
                    if (_sessions.TryGetValue(token, out UserSession session))
                    {
                        _sessions.Remove(token);

                        // Clear current session token and username if this is the current session
                        if (token == CurrentSessionToken)
                        {
                            CurrentSessionToken = null;
                            CurrentUsername = null;
                        }

                        _logger.Log($"Ended session for user: {session.Username}", LogLevel.INFO);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error ending session: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets all active sessions
        /// </summary>
        /// <returns>The active sessions</returns>
        public List<UserSession> GetActiveSessions()
        {
            // Check if disposed
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SecurityService));
            }

            try
            {
                lock (_sessions)
                {
                    return new List<UserSession>(_sessions.Values);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting active sessions: {ex.Message}", LogLevel.ERROR);
                return new List<UserSession>();
            }
        }

        /// <summary>
        /// Cleans up expired sessions
        /// </summary>
        private void CleanupSessions(object state)
        {
            try
            {
                // Get the current time
                DateTime now = DateTime.Now;

                // Find expired sessions
                List<string> expiredTokens = new List<string>();
                lock (_sessions)
                {
                    foreach (var session in _sessions.Values)
                    {
                        if (session.ExpiresAt < now)
                        {
                            expiredTokens.Add(session.Token);
                            SessionExpired?.Invoke(this, new SessionEventArgs { Session = session });
                        }
                    }

                    // Remove expired sessions
                    foreach (var token in expiredTokens)
                    {
                        _sessions.Remove(token);
                    }
                }

                // Log the action
                if (expiredTokens.Count > 0)
                {
                    _logger.Log($"Cleaned up {expiredTokens.Count} expired sessions", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error cleaning up sessions: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Disposes the SecurityService
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the SecurityService
        /// </summary>
        /// <param name="disposing">Whether to dispose managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _sessionCleanupTimer?.Dispose();

                    // Log the action
                    _logger?.Log("SecurityService disposed", LogLevel.INFO);
                }

                // Set disposed flag
                _disposed = true;
            }
        }

        /// <summary>
        /// Finalizer
        /// </summary>
        ~SecurityService()
        {
            Dispose(false);
        }

        // ISecurityService and IInitializableService implementations
        public void Initialize()
        {
            // Initialization logic, if any, for SecurityService
            _logger.Log("SecurityService initialized.", LogLevel.INFO);
        }

        public bool ValidateCredentials(string username, string password)
        {
            // This is a placeholder. In a real application, you would:
            // 1. Retrieve the stored hashed password for the username (e.g., from a database or secure store).
            // 2. Hash the provided password using the same algorithm and salt.
            // 3. Compare the hashes.
            // For now, let's assume a simple hardcoded check or a call to a (yet to be integrated) user service.
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password)) return false;

            // Example: (replace with actual user data access)
            // var user = _userService.GetUser(username); 
            // if (user == null) return false;
            // return VerifyPasswordHash(password, user.PasswordHash, user.PasswordSalt);

            // Placeholder logic:
            if ((username == "admin" && password == "admin") || (username == "user" && password == "user"))
            {
                 _logger.Log($"Credentials validated for user: {username}", LogLevel.INFO);
                return true;
            }
            _logger.Log($"Invalid credentials for user: {username}", LogLevel.WARNING);
            return false;
        }

        public string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password)) throw new ArgumentNullException(nameof(password));
            // Simple SHA256 hashing for demonstration. Use a strong, salted hashing algorithm (e.g., Argon2, scrypt, PBKDF2) in a real app.
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        public string GenerateToken(string username)
        {
            // This would typically involve creating a JWT or a similar secure token.
            // For simplicity, reusing session token generation.
            if (string.IsNullOrEmpty(username)) throw new ArgumentNullException(nameof(username));
            string token = CreateSession(username, false); // Assuming isAdmin=false for generic token
            _logger.Log($"Generated token for user: {username}", LogLevel.INFO);
            return token;
        }

        public bool ValidateToken(string token)
        {
            // Reusing session validation logic.
            bool isValid = ValidateSession(token);
            if(isValid)
                 _logger.Log($"Token validated: {token}", LogLevel.INFO);
            else
                 _logger.Log($"Invalid token: {token}", LogLevel.WARNING);
            return isValid;
        }
    }

    /// <summary>
    /// Represents a user session
    /// </summary>
    public class UserSession
    {
        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Gets or sets the session token
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is an admin
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// Gets or sets the creation time
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last activity time
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// Gets or sets the expiration time
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Gets the formatted creation time
        /// </summary>
        public string FormattedCreatedAt => CreatedAt.ToString("MMM dd, yyyy HH:mm:ss");

        /// <summary>
        /// Gets the formatted last activity time
        /// </summary>
        public string FormattedLastActivity => LastActivity.ToString("MMM dd, yyyy HH:mm:ss");

        /// <summary>
        /// Gets the formatted expiration time
        /// </summary>
        public string FormattedExpiresAt => ExpiresAt.ToString("MMM dd, yyyy HH:mm:ss");
    }

    /// <summary>
    /// Event arguments for session events
    /// </summary>
    public class SessionEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the session
        /// </summary>
        public UserSession Session { get; set; }
    }
}
