// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Threading.Tasks;
using System.Linq;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for benchmarking system performance
    /// </summary>
    public class BenchmarkingService : IBenchmarkingService
    {
        private static BenchmarkingService _instance;
        private static readonly object _instanceLock = new object();
        private readonly LoggingService _logger;
        private readonly PerformanceMonitoringService _performanceMonitoringService;
        private bool _isInitialized;
        private bool _isBenchmarkInProgress;

        /// <summary>
        /// Event raised when a benchmark is started
        /// </summary>
        public event EventHandler<BenchmarkEventArgs> BenchmarkStarted;

        /// <summary>
        /// Event raised when a benchmark is completed
        /// </summary>
        public event EventHandler<BenchmarkResultEventArgs> BenchmarkCompleted;

        /// <summary>
        /// Event raised when a benchmark is cancelled
        /// </summary>
        public event EventHandler<BenchmarkEventArgs> BenchmarkCancelled;

        /// <summary>
        /// Event raised when benchmark progress is updated
        /// </summary>
        public event EventHandler<BenchmarkProgressEventArgs> BenchmarkProgressUpdated;

        /// <summary>
        /// Initializes a new instance of the BenchmarkingService class
        /// </summary>
        private BenchmarkingService()
        {
            _logger = LoggingService.Instance;
            _performanceMonitoringService = PerformanceMonitoringService.Instance;
            _isInitialized = false;
            _isBenchmarkInProgress = false;
        }

        /// <summary>
        /// Public constructor for DI compatibility
        /// </summary>
        public BenchmarkingService(bool forDI = false) : this() { }

        /// <summary>
        /// Gets the singleton instance of the benchmarking service
        /// </summary>
        public static BenchmarkingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new BenchmarkingService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets a value indicating whether a benchmark is in progress
        /// </summary>
        public bool IsBenchmarkRunning => _isBenchmarkInProgress;

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public Task InitializeAsync()
        {
            if (_isInitialized)
            {
                return Task.CompletedTask;
            }

            try
            {
                _logger.Log("Initializing benchmarking service...", LogLevel.INFO);

                // Initialize performance monitoring service if needed
                // Note: Assuming PerformanceMonitoringService doesn't have initialization methods
                // If it does in the future, uncomment the code below
                // if (!_performanceMonitoringService.IsInitialized)
                // {
                //     return _performanceMonitoringService.InitializeAsync();
                // }

                _isInitialized = true;
                _logger.Log("Benchmarking service initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing benchmarking service: {ex.Message}", LogLevel.ERROR);
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Runs a benchmark
        /// </summary>
        /// <param name="benchmarkType">The benchmark type</param>
        /// <returns>The benchmark result</returns>
        public async Task<BenchmarkPerformanceResult> RunBenchmarkAsync(BenchmarkType benchmarkType)
        {
            return await RunBenchmarkAsync(benchmarkType, System.Threading.CancellationToken.None);
        }

        /// <summary>
        /// Runs a benchmark
        /// </summary>
        /// <param name="benchmarkType">The benchmark type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The benchmark result</returns>
        public async Task<BenchmarkPerformanceResult> RunBenchmarkAsync(BenchmarkType benchmarkType, System.Threading.CancellationToken cancellationToken)
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
            }

            if (_isBenchmarkInProgress)
            {
                throw new InvalidOperationException("A benchmark is already in progress");
            }

            _isBenchmarkInProgress = true;
            BenchmarkPerformanceResult result = new BenchmarkPerformanceResult
            {
                BenchmarkType = benchmarkType,
                StartTime = DateTime.Now,
                IsSuccessful = false
            };

            try
            {
                // Raise benchmark started event
                BenchmarkStarted?.Invoke(this, new BenchmarkEventArgs
                {
                    BenchmarkType = benchmarkType,
                    StartTime = result.StartTime
                });

                _logger.Log($"Running {benchmarkType} benchmark...", LogLevel.INFO);

                // Run the benchmark based on type
                switch (benchmarkType)
                {
                    case BenchmarkType.Quick:
                        // Report initial progress
                        BenchmarkProgressUpdated?.Invoke(this, new BenchmarkProgressEventArgs
                        {
                            BenchmarkType = benchmarkType,
                            StartTime = result.StartTime,
                            ProgressPercentage = 0,
                            StatusMessage = "Starting Quick benchmark..."
                        });

                        result = await RunQuickBenchmarkAsync();
                        break;
                    case BenchmarkType.Standard:
                        // Report initial progress
                        BenchmarkProgressUpdated?.Invoke(this, new BenchmarkProgressEventArgs
                        {
                            BenchmarkType = benchmarkType,
                            StartTime = result.StartTime,
                            ProgressPercentage = 0,
                            StatusMessage = "Starting Standard benchmark..."
                        });

                        result = await RunStandardBenchmarkAsync();
                        break;
                    case BenchmarkType.Extended:
                        // Report initial progress
                        BenchmarkProgressUpdated?.Invoke(this, new BenchmarkProgressEventArgs
                        {
                            BenchmarkType = benchmarkType,
                            StartTime = result.StartTime,
                            ProgressPercentage = 0,
                            StatusMessage = "Starting Extended benchmark..."
                        });

                        result = await RunExtendedBenchmarkAsync();
                        break;
                    default:
                        throw new ArgumentException($"Unknown benchmark type: {benchmarkType}");
                }

                result.IsSuccessful = true;
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;

                _logger.Log($"{benchmarkType} benchmark completed successfully", LogLevel.SUCCESS);

                // Raise benchmark completed event
                BenchmarkCompleted?.Invoke(this, new BenchmarkResultEventArgs
                {
                    BenchmarkType = benchmarkType,
                    StartTime = result.StartTime,
                    EndTime = result.EndTime,
                    Score = result.Score,
                    AverageFps = result.AverageFps,
                    MinimumFps = result.MinimumFps,
                    MaximumFps = result.MaximumFps,
                    FrameTimeAvg = result.FrameTimeAvg,
                    FrameTime99Percentile = result.FrameTime99Percentile
                });
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running benchmark: {ex.Message}", LogLevel.ERROR);
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                _isBenchmarkInProgress = false;
            }

            return result;
        }

        /// <summary>
        /// Cancels the current benchmark
        /// </summary>
        public void CancelBenchmark()
        {
            if (_isBenchmarkInProgress)
            {
                _logger.Log("Cancelling benchmark...", LogLevel.WARNING);

                // Raise benchmark cancelled event
                BenchmarkCancelled?.Invoke(this, new BenchmarkEventArgs
                {
                    BenchmarkType = BenchmarkType.Quick, // Default value, not important for cancellation
                    StartTime = DateTime.Now
                });

                _isBenchmarkInProgress = false;
                _logger.Log("Benchmark cancelled", LogLevel.INFO);
            }
        }

        private async Task<BenchmarkPerformanceResult> RunQuickBenchmarkAsync()
        {
            // Simulate a quick benchmark
            for (int i = 0; i <= 100; i += 25)
            {
                // Report progress
                BenchmarkProgressUpdated?.Invoke(this, new BenchmarkProgressEventArgs
                {
                    BenchmarkType = BenchmarkType.Quick,
                    StartTime = DateTime.Now,
                    ProgressPercentage = i,
                    StatusMessage = $"Running Quick benchmark... {i}%"
                });

                await Task.Delay(250);
            }

            return new BenchmarkPerformanceResult
            {
                BenchmarkType = BenchmarkType.Quick,
                Score = 1000,
                AverageFps = 100,
                MinimumFps = 90,
                MaximumFps = 110,
                FrameTimeAvg = 10,
                FrameTime99Percentile = 15,
                IsSuccessful = true
            };
        }

        private async Task<BenchmarkPerformanceResult> RunStandardBenchmarkAsync()
        {
            // Simulate a standard benchmark
            for (int i = 0; i <= 100; i += 10)
            {
                // Report progress
                BenchmarkProgressUpdated?.Invoke(this, new BenchmarkProgressEventArgs
                {
                    BenchmarkType = BenchmarkType.Standard,
                    StartTime = DateTime.Now,
                    ProgressPercentage = i,
                    StatusMessage = $"Running Standard benchmark... {i}%"
                });

                await Task.Delay(300);
            }

            return new BenchmarkPerformanceResult
            {
                BenchmarkType = BenchmarkType.Standard,
                Score = 2000,
                AverageFps = 120,
                MinimumFps = 100,
                MaximumFps = 140,
                FrameTimeAvg = 8.3,
                FrameTime99Percentile = 12,
                IsSuccessful = true
            };
        }

        private async Task<BenchmarkPerformanceResult> RunExtendedBenchmarkAsync()
        {
            // Simulate an extended benchmark
            for (int i = 0; i <= 100; i += 5)
            {
                // Report progress
                BenchmarkProgressUpdated?.Invoke(this, new BenchmarkProgressEventArgs
                {
                    BenchmarkType = BenchmarkType.Extended,
                    StartTime = DateTime.Now,
                    ProgressPercentage = i,
                    StatusMessage = $"Running Extended benchmark... {i}%"
                });

                await Task.Delay(250);
            }

            return new BenchmarkPerformanceResult
            {
                BenchmarkType = BenchmarkType.Extended,
                Score = 3000,
                AverageFps = 150,
                MinimumFps = 120,
                MaximumFps = 180,
                FrameTimeAvg = 6.7,
                FrameTime99Percentile = 10,
                IsSuccessful = true
            };
        }
    }

    /// <summary>
    /// Benchmark type
    /// </summary>
    public enum BenchmarkType
    {
        /// <summary>
        /// Quick benchmark
        /// </summary>
        Quick,

        /// <summary>
        /// Standard benchmark
        /// </summary>
        Standard,

        /// <summary>
        /// Extended benchmark
        /// </summary>
        Extended
    }

    /// <summary>
    /// Benchmark performance result
    /// </summary>
    public class BenchmarkPerformanceResult
    {
        /// <summary>
        /// Gets or sets the benchmark type
        /// </summary>
        public BenchmarkType BenchmarkType { get; set; }

        /// <summary>
        /// Gets or sets the start time of the benchmark
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the end time of the benchmark
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the duration of the benchmark
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the benchmark was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the benchmark score
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// Gets or sets the average FPS
        /// </summary>
        public double AverageFps { get; set; }

        /// <summary>
        /// Gets or sets the minimum FPS
        /// </summary>
        public double MinimumFps { get; set; }

        /// <summary>
        /// Gets or sets the maximum FPS
        /// </summary>
        public double MaximumFps { get; set; }

        /// <summary>
        /// Gets or sets the average frame time (ms)
        /// </summary>
        public double FrameTimeAvg { get; set; }

        /// <summary>
        /// Gets or sets the 99th percentile frame time (ms)
        /// </summary>
        public double FrameTime99Percentile { get; set; }
    }

    /// <summary>
    /// Benchmark result
    /// </summary>
    public class BenchmarkResult
    {
        /// <summary>
        /// Gets or sets the benchmark type
        /// </summary>
        public BenchmarkType BenchmarkType { get; set; }

        /// <summary>
        /// Gets or sets the start time of the benchmark
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the end time of the benchmark
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the duration of the benchmark
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the benchmark was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the benchmark score
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// Gets or sets the average FPS
        /// </summary>
        public double AverageFps { get; set; }

        /// <summary>
        /// Gets or sets the minimum FPS
        /// </summary>
        public double MinimumFps { get; set; }

        /// <summary>
        /// Gets or sets the maximum FPS
        /// </summary>
        public double MaximumFps { get; set; }

        /// <summary>
        /// Gets or sets the average frame time (ms)
        /// </summary>
        public double FrameTimeAvg { get; set; }

        /// <summary>
        /// Gets or sets the 99th percentile frame time (ms)
        /// </summary>
        public double FrameTime99Percentile { get; set; }
    }

    /// <summary>
    /// Event arguments for benchmark events
    /// </summary>
    public class BenchmarkEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the benchmark type
        /// </summary>
        public BenchmarkType BenchmarkType { get; set; }

        /// <summary>
        /// Gets or sets the start time of the benchmark
        /// </summary>
        public DateTime StartTime { get; set; }
    }

    /// <summary>
    /// Event arguments for benchmark progress events
    /// </summary>
    public class BenchmarkProgressEventArgs : BenchmarkEventArgs
    {
        /// <summary>
        /// Gets or sets the progress percentage (0-100)
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Gets or sets the current status message
        /// </summary>
        public string StatusMessage { get; set; }
    }

    /// <summary>
    /// Event arguments for benchmark result events
    /// </summary>
    public class BenchmarkResultEventArgs : BenchmarkEventArgs
    {
        /// <summary>
        /// Gets or sets the end time of the benchmark
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the benchmark score
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// Gets or sets the average FPS
        /// </summary>
        public double AverageFps { get; set; }

        /// <summary>
        /// Gets or sets the minimum FPS
        /// </summary>
        public double MinimumFps { get; set; }

        /// <summary>
        /// Gets or sets the maximum FPS
        /// </summary>
        public double MaximumFps { get; set; }

        /// <summary>
        /// Gets or sets the average frame time (ms)
        /// </summary>
        public double FrameTimeAvg { get; set; }

        /// <summary>
        /// Gets or sets the 99th percentile frame time (ms)
        /// </summary>
        public double FrameTime99Percentile { get; set; }
    }
}

