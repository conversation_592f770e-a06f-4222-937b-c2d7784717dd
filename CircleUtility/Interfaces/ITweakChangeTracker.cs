using System;
using System.Collections.Generic;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    public interface ITweakChangeTracker
    {
        event EventHandler<TweakChangeEventArgs> ChangeTracked;
        event EventHandler<TweakChangeEventArgs> ChangeReverted;
        Guid TrackRegistryChange(string keyPath, string valueName, object originalValue, object newValue, string tweakName, OptimizationType tweakType);
        Guid TrackFileSystemChange(string filePath, string backupFilePath, string tweakName, OptimizationType tweakType);
        Guid TrackServiceChange(string serviceName, int originalStartType, int newStartType, string tweakName, OptimizationType tweakType);
        List<TweakChange> GetActiveChanges();
        List<TweakChange> GetChangesByTweak(string tweakName);
        List<TweakChange> GetChangesByType(OptimizationType tweakType);
        List<TweakChange> GetChangesByTimePeriod(DateTime startDate, DateTime endDate);
    }
} 