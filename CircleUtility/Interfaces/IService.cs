namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Base interface for all services
    /// </summary>
    public interface IService
    {
        // Base service interface
    }

    /// <summary>
    /// Interface for services that can be stopped
    /// </summary>
    public interface IStoppableService
    {
        /// <summary>
        /// Stops the service
        /// </summary>
        void Stop();
    }

    /// <summary>
    /// Interface for services that can be stopped asynchronously
    /// </summary>
    public interface IAsyncStoppableService
    {
        /// <summary>
        /// Stops the service asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        System.Threading.Tasks.Task StopAsync();
    }

    /// <summary>
    /// Interface for hardware services
    /// </summary>
    public interface IHardwareService
    {
        /// <summary>
        /// Gets hardware information
        /// </summary>
        /// <returns>Hardware information</returns>
        object GetHardwareInfo();
    }

    /// <summary>
    /// Interface for security services
    /// </summary>
    public interface ISecurityService
    {
        /// <summary>
        /// Validates security
        /// </summary>
        /// <returns>True if valid</returns>
        bool ValidateSecurity();
    }

    /// <summary>
    /// Interface for window services
    /// </summary>
    public interface IWindowService
    {
        /// <summary>
        /// Shows a window
        /// </summary>
        void ShowWindow();
    }
}
