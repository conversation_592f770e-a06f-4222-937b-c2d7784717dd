using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedButton.xaml
    /// </summary>
    public partial class AnimatedButton : UserControl
    {
        private readonly Storyboard _glowAnimation;
        private readonly Storyboard _unglowAnimation;
        private readonly Storyboard _clickAnimation;
        private readonly Storyboard _successAnimation;
        private readonly Storyboard _resetAnimation;

        /// <summary>
        /// Gets or sets the button text
        /// </summary>
        public string ButtonText
        {
            get { return (string)GetValue(ButtonTextProperty); }
            set { SetValue(ButtonTextProperty, value); }
        }

        /// <summary>
        /// Dependency property for ButtonText
        /// </summary>
        public static readonly DependencyProperty ButtonTextProperty =
            DependencyProperty.Register("ButtonText", typeof(string), typeof(AnimatedButton), new PropertyMetadata("Button"));

        /// <summary>
        /// Gets or sets the command to execute when the button is clicked
        /// </summary>
        public ICommand Command
        {
            get { return (ICommand)GetValue(CommandProperty); }
            set { SetValue(CommandProperty, value); }
        }

        /// <summary>
        /// Dependency property for Command
        /// </summary>
        public static readonly DependencyProperty CommandProperty =
            DependencyProperty.Register("Command", typeof(ICommand), typeof(AnimatedButton), new PropertyMetadata(null));

        /// <summary>
        /// Gets or sets the command parameter
        /// </summary>
        public object CommandParameter
        {
            get { return GetValue(CommandParameterProperty); }
            set { SetValue(CommandParameterProperty, value); }
        }

        /// <summary>
        /// Dependency property for CommandParameter
        /// </summary>
        public static readonly DependencyProperty CommandParameterProperty =
            DependencyProperty.Register("CommandParameter", typeof(object), typeof(AnimatedButton), new PropertyMetadata(null));

        /// <summary>
        /// Event raised when the button is clicked
        /// </summary>
        public event RoutedEventHandler Click;

        /// <summary>
        /// Initializes a new instance of the AnimatedButton class
        /// </summary>
        public AnimatedButton()
        {
            InitializeComponent();

            // Get animations from resources
            _glowAnimation = (Storyboard)FindResource("GlowAnimation");
            _unglowAnimation = (Storyboard)FindResource("UnglowAnimation");
            _clickAnimation = (Storyboard)FindResource("ClickAnimation");
            _successAnimation = (Storyboard)FindResource("SuccessAnimation");
            _resetAnimation = (Storyboard)FindResource("ResetAnimation");
        }

        /// <summary>
        /// Handles the MouseEnter event for the main button
        /// </summary>
        private void MainButton_MouseEnter(object sender, MouseEventArgs e)
        {
            _glowAnimation.Begin(this);
        }

        /// <summary>
        /// Handles the MouseLeave event for the main button
        /// </summary>
        private void MainButton_MouseLeave(object sender, MouseEventArgs e)
        {
            _unglowAnimation.Begin(this);
        }

        /// <summary>
        /// Handles the Click event for the main button
        /// </summary>
        private void MainButton_Click(object sender, RoutedEventArgs e)
        {
            // Play click animation
            _clickAnimation.Begin(this);

            // Execute command if set
            if (Command != null && Command.CanExecute(CommandParameter))
            {
                Command.Execute(CommandParameter);
            }

            // Raise click event
            Click?.Invoke(this, e);
        }

        /// <summary>
        /// Shows the success animation
        /// </summary>
        public void ShowSuccess()
        {
            _successAnimation.Begin(this);
        }

        /// <summary>
        /// Resets the button to its default state
        /// </summary>
        public void Reset()
        {
            _resetAnimation.Begin(this);
        }

        /// <summary>
        /// Shows the success animation and then resets after a delay
        /// </summary>
        /// <param name="resetDelayMs">The delay in milliseconds before resetting</param>
        public void ShowSuccessAndReset(int resetDelayMs = 1500)
        {
            ShowSuccess();

            // Reset after delay
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Tick += (s, e) =>
            {
                Reset();
                timer.Stop();
            };
            timer.Interval = TimeSpan.FromMilliseconds(resetDelayMs);
            timer.Start();
        }
    }
}
