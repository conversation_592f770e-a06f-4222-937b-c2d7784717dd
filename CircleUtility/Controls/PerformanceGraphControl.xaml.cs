using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for PerformanceGraphControl.xaml
    /// </summary>
    public partial class PerformanceGraphControl : UserControl
    {
        private readonly List<double> _dataPoints = new List<double>();
        private readonly int _maxDataPoints = 60; // 1 minute of data at 1 point per second
        private readonly Storyboard _newDataPointAnimation;
        private double _maxValue = 100.0;
        private string _title = "Performance";
        private string _format = "{0}%";
        private Brush _graphColor = new SolidColorBrush(Color.FromRgb(0, 200, 255)); // #00C8FF
        private int _nextDataPointIndex = 0;

        /// <summary>
        /// Initializes a new instance of the PerformanceGraphControl class
        /// </summary>
        public PerformanceGraphControl()
        {
            InitializeComponent();
            DataContext = this;
            _newDataPointAnimation = (Storyboard)FindResource("NewDataPointAnimation");
        }

        /// <summary>
        /// Gets or sets the title of the graph
        /// </summary>
        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                TitleTextBlock.Text = value;
            }
        }

        /// <summary>
        /// Gets or sets the format string for the value display
        /// </summary>
        public string Format
        {
            get => _format;
            set => _format = value;
        }

        /// <summary>
        /// Gets or sets the maximum value for the graph (default is 100)
        /// </summary>
        public double MaxValue
        {
            get => _maxValue;
            set => _maxValue = value;
        }

        /// <summary>
        /// Gets or sets the graph color
        /// </summary>
        public Brush GraphColor
        {
            get => _graphColor;
            set
            {
                _graphColor = value;
                GraphLine.Stroke = value;
                HighlightEllipse.Fill = value;
            }
        }

        /// <summary>
        /// Adds a new data point to the graph
        /// </summary>
        /// <param name="value">The value to add</param>
        public void AddDataPoint(double value)
        {
            // Use a more efficient data structure for large datasets
            if (_dataPoints.Count >= _maxDataPoints)
            {
                // Instead of removing at index 0 (which shifts all elements),
                // use a circular buffer approach
                _dataPoints[_nextDataPointIndex] = value;
                _nextDataPointIndex = (_nextDataPointIndex + 1) % _maxDataPoints;
            }
            else
            {
                // Still building up initial data points
                _dataPoints.Add(value);
            }

            // Update the value display
            ValueTextBlock.Text = string.Format(_format, value);

            // Update the graph
            UpdateGraph();

            // Play the animation for the new data point
            _newDataPointAnimation.Begin(this);
        }

        /// <summary>
        /// Clears all data points from the graph
        /// </summary>
        public void ClearDataPoints()
        {
            _dataPoints.Clear();
            UpdateGraph();
        }

        /// <summary>
        /// Updates the graph display
        /// </summary>
        private void UpdateGraph()
        {
            if (_dataPoints.Count == 0)
            {
                GraphLine.Points.Clear();
                return;
            }

            // Get the canvas dimensions
            double width = GraphCanvas.ActualWidth;
            double height = GraphCanvas.ActualHeight;

            if (width <= 0 || height <= 0)
            {
                return;
            }

            // Create a new point collection with capacity to avoid resizing
            var points = new PointCollection(_dataPoints.Count);

            // Calculate the x-step based on the number of data points
            double xStep = width / Math.Max(1, _maxDataPoints - 1);

            // Optimize for the case where we have a full buffer
            if (_dataPoints.Count >= _maxDataPoints)
            {
                // We need to reorder the points since we're using a circular buffer
                // First add points from _nextDataPointIndex to the end
                for (int i = _nextDataPointIndex; i < _dataPoints.Count; i++)
                {
                    double x = (i - _nextDataPointIndex) * xStep;
                    double normalizedValue = Math.Min(1.0, _dataPoints[i] / _maxValue);
                    double y = height - (normalizedValue * height);
                    points.Add(new Point(x, y));
                }

                // Then add points from the beginning to _nextDataPointIndex
                for (int i = 0; i < _nextDataPointIndex; i++)
                {
                    double x = (i + _dataPoints.Count - _nextDataPointIndex) * xStep;
                    double normalizedValue = Math.Min(1.0, _dataPoints[i] / _maxValue);
                    double y = height - (normalizedValue * height);
                    points.Add(new Point(x, y));
                }
            }
            else
            {
                // We don't have a full buffer yet, so just add points in order
                for (int i = 0; i < _dataPoints.Count; i++)
                {
                    double x = i * xStep;
                    double normalizedValue = Math.Min(1.0, _dataPoints[i] / _maxValue);
                    double y = height - (normalizedValue * height);
                    points.Add(new Point(x, y));
                }
            }

            // Update the graph line
            GraphLine.Points = points;

            // Update the highlight ellipse position
            if (_dataPoints.Count > 0)
            {
                // Get the last data point (most recent)
                int lastIndex = (_nextDataPointIndex - 1 + _dataPoints.Count) % _dataPoints.Count;
                double lastValue = _dataPoints[lastIndex];

                // Calculate position
                double lastX = (points.Count - 1) * xStep;
                double lastNormalizedValue = Math.Min(1.0, lastValue / _maxValue);
                double lastY = height - (lastNormalizedValue * height);

                Canvas.SetLeft(HighlightEllipse, lastX - 4); // Center the 8x8 ellipse
                Canvas.SetTop(HighlightEllipse, lastY - 4);
            }
        }

        /// <summary>
        /// Handles the control being loaded
        /// </summary>
        protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
        {
            base.OnRenderSizeChanged(sizeInfo);
            UpdateGraph();
        }
    }
}
