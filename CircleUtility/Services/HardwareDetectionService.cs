// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Win32;
using CircleUtility.Interfaces;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for detecting and caching hardware information
    /// </summary>
    public class HardwareDetectionService : IHardwareDetectionService
    {
        private static HardwareDetectionService _instance;
        private readonly LoggingService _logger;
        private readonly string _cacheDirectory;
        private readonly string _hardwareCacheFilePath;
        private HardwareInfo _cachedHardwareInfo;
        private DateTime _lastCacheUpdate;
        private readonly TimeSpan _cacheExpirationTime = TimeSpan.FromHours(1);
        private bool _isInitialized;

        // Memory cache for frequently accessed hardware components
        private readonly Dictionary<string, object> _memoryCache = new Dictionary<string, object>();
        private readonly Dictionary<string, DateTime> _memoryCacheTimestamps = new Dictionary<string, DateTime>();
        private readonly TimeSpan _memoryCacheExpiration = TimeSpan.FromMinutes(5); // Memory cache valid for 5 minutes
        private readonly object _memoryCacheLock = new object();

        /// <summary>
        /// Initializes a new instance of the HardwareDetectionService class
        /// </summary>
        private HardwareDetectionService()
        {
            _logger = LoggingService.Instance;
            _cacheDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Cache");
            _hardwareCacheFilePath = Path.Combine(_cacheDirectory, "hardware_cache.json");
            _lastCacheUpdate = DateTime.MinValue;

            // Ensure cache directory exists
            if (!Directory.Exists(_cacheDirectory))
            {
                Directory.CreateDirectory(_cacheDirectory);
            }

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Gets the singleton instance of the hardware detection service
        /// </summary>
        public static HardwareDetectionService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new HardwareDetectionService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the hardware detection service
        /// </summary>
        public void Initialize()
        {
            try
            {
                _logger.LogInfo("Initializing hardware detection service...");

                // Load cached hardware info
                LoadHardwareCache();

                // Gather hardware information
                GatherCPUInfo();
                GatherGPUInfo();
                GatherRAMInfo();
                GatherStorageInfo();
                GatherNetworkInfo();

                _isInitialized = true;
                _logger.LogSuccess("Hardware detection service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize hardware detection service", ex);
                throw;
            }
        }

        /// <summary>
        /// Loads hardware cache from disk
        /// </summary>
        private void LoadHardwareCache()
        {
            try
            {
                if (File.Exists(_hardwareCacheFilePath))
                {
                    string json = File.ReadAllText(_hardwareCacheFilePath);
                    HardwareCacheData cacheData = JsonSerializer.Deserialize<HardwareCacheData>(json);

                    if (cacheData != null && cacheData.HardwareInfo != null)
                    {
                        _cachedHardwareInfo = cacheData.HardwareInfo;
                        _lastCacheUpdate = cacheData.LastUpdated;
                        _logger.Log("Hardware cache loaded", LogLevel.INFO);
                    }
                    else
                    {
                        _cachedHardwareInfo = new HardwareInfo();
                        _lastCacheUpdate = DateTime.MinValue;
                    }
                }
                else
                {
                    _cachedHardwareInfo = new HardwareInfo();
                    _lastCacheUpdate = DateTime.MinValue;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading hardware cache: {ex.Message}", LogLevel.ERROR);
                _cachedHardwareInfo = new HardwareInfo();
                _lastCacheUpdate = DateTime.MinValue;
            }
        }

        /// <summary>
        /// Saves hardware cache to disk
        /// </summary>
        private void SaveHardwareCache()
        {
            try
            {
                if (_cachedHardwareInfo != null)
                {
                    HardwareCacheData cacheData = new HardwareCacheData
                    {
                        HardwareInfo = _cachedHardwareInfo,
                        LastUpdated = DateTime.Now
                    };

                    string json = JsonSerializer.Serialize(cacheData, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(_hardwareCacheFilePath, json);

                    _logger.Log("Hardware cache saved", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving hardware cache: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets hardware information
        /// </summary>
        /// <returns>The hardware information</returns>
        public HardwareInfo GetHardwareInfo()
        {
            return GetHardwareInfo(false);
        }

        /// <summary>
        /// Gets hardware information
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the cache</param>
        /// <returns>The hardware information</returns>
        public HardwareInfo GetHardwareInfo(bool forceRefresh)
        {
            try
            {
                // Check if cache is valid
                if (!forceRefresh && _cachedHardwareInfo != null && (DateTime.Now - _lastCacheUpdate) < _cacheExpirationTime)
                {
                    _logger.Log("Using cached hardware info", LogLevel.INFO);
                    return _cachedHardwareInfo;
                }

                // If forcing refresh, clear memory cache
                if (forceRefresh)
                {
                    _logger.Log("Forcing hardware refresh, clearing memory cache", LogLevel.INFO);
                    ClearMemoryCache();
                }

                // Detect hardware
                _logger.Log("Detecting hardware...", LogLevel.INFO);
                _cachedHardwareInfo = DetectHardware();
                _lastCacheUpdate = DateTime.Now;

                // Save cache
                SaveHardwareCache();

                return _cachedHardwareInfo;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting hardware info: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Gets hardware information asynchronously
        /// </summary>
        /// <returns>The hardware information</returns>
        public async Task<HardwareInfo> GetHardwareInfoAsync()
        {
            return await GetHardwareInfoAsync(false);
        }

        /// <summary>
        /// Gets hardware information asynchronously
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the cache</param>
        /// <returns>The hardware information</returns>
        public async Task<HardwareInfo> GetHardwareInfoAsync(bool forceRefresh)
        {
            return await Task.Run(() => GetHardwareInfo(forceRefresh));
        }

        /// <summary>
        /// Detects hardware
        /// </summary>
        /// <returns>The hardware information</returns>
        private HardwareInfo DetectHardware()
        {
            try
            {
                HardwareInfo hardwareInfo = new HardwareInfo();

                // Detect CPU
                hardwareInfo.CPU = DetectCPU();

                // Detect GPU
                hardwareInfo.GPU = DetectGPU();

                // Detect RAM
                hardwareInfo.RAM = DetectRAM();

                // Detect storage
                hardwareInfo.Storage = DetectStorage();

                // Detect network adapters
                hardwareInfo.NetworkAdapters = DetectNetworkAdapters();

                // Detect motherboard
                hardwareInfo.Motherboard = DetectMotherboard();

                // Detect operating system
                hardwareInfo.OperatingSystem = DetectOperatingSystem();

                return hardwareInfo;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting hardware: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Gets or adds an item to the memory cache
        /// </summary>
        /// <typeparam name="T">The type of the item</typeparam>
        /// <param name="key">The cache key</param>
        /// <param name="factory">The factory function to create the item if not in cache</param>
        /// <returns>The cached or newly created item</returns>
        private T GetOrAddToMemoryCache<T>(string key, Func<T> factory)
        {
            lock (_memoryCacheLock)
            {
                // Check if the item is in the cache and not expired
                if (_memoryCache.TryGetValue(key, out object cachedItem) &&
                    _memoryCacheTimestamps.TryGetValue(key, out DateTime timestamp) &&
                    (DateTime.Now - timestamp) < _memoryCacheExpiration)
                {
                    return (T)cachedItem;
                }

                // Item not in cache or expired, create new item
                T newItem = factory();

                // Add or update cache
                _memoryCache[key] = newItem;
                _memoryCacheTimestamps[key] = DateTime.Now;

                return newItem;
            }
        }

        /// <summary>
        /// Clears the memory cache
        /// </summary>
        public void ClearMemoryCache()
        {
            lock (_memoryCacheLock)
            {
                _memoryCache.Clear();
                _memoryCacheTimestamps.Clear();
                _logger.Log("Memory cache cleared", LogLevel.INFO);
            }
        }

        /// <summary>
        /// Detects CPU information
        /// </summary>
        /// <returns>The CPU information</returns>
        private CPUInfo DetectCPU()
        {
            try
            {
                if (_cachedHardwareInfo == null)
                    _cachedHardwareInfo = new HardwareInfo();
                var cpuInfo = new Dictionary<string, string>();
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        cpuInfo["Name"] = obj["Name"]?.ToString();
                        cpuInfo["Manufacturer"] = obj["Manufacturer"]?.ToString();
                        cpuInfo["Cores"] = obj["NumberOfCores"]?.ToString();
                        cpuInfo["LogicalProcessors"] = obj["NumberOfLogicalProcessors"]?.ToString();
                        cpuInfo["MaxClockSpeed"] = obj["MaxClockSpeed"]?.ToString();
                        cpuInfo["Architecture"] = obj["Architecture"]?.ToString();
                        cpuInfo["L2CacheSize"] = obj["L2CacheSize"]?.ToString();
                        cpuInfo["L3CacheSize"] = obj["L3CacheSize"]?.ToString();
                        break; // Only get the first CPU
                    }
                }

                _cachedHardwareInfo.CPU = new CPUInfo
                {
                    Name = cpuInfo["Name"],
                    Manufacturer = cpuInfo["Manufacturer"],
                    Cores = int.Parse(cpuInfo["Cores"]),
                    LogicalProcessors = int.Parse(cpuInfo["LogicalProcessors"]),
                    MaxClockSpeed = int.Parse(cpuInfo["MaxClockSpeed"]),
                    Architecture = cpuInfo["Architecture"],
                    L2CacheSize = int.Parse(cpuInfo["L2CacheSize"]),
                    L3CacheSize = int.Parse(cpuInfo["L3CacheSize"]),
                };

                return _cachedHardwareInfo.CPU;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting CPU: {ex.Message}", LogLevel.ERROR);
                return new CPUInfo { Name = "Unknown" };
            }
        }

        /// <summary>
        /// Detects GPU information
        /// </summary>
        /// <returns>The GPU information</returns>
        private GPUInfo DetectGPU()
        {
            return GetOrAddToMemoryCache("GPU", () =>
            {
                try
                {
                    // Changed gpuInfo to be a dictionary
                    var gpuDetails = new Dictionary<string, string>(); 

                    using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            gpuDetails["Name"] = obj["Name"]?.ToString() ?? "Unknown";
                            // Manufacturer / Vendor determination
                            string manufacturer = obj["AdapterCompatibility"]?.ToString();
                            string vendor = "Other";
                            if (manufacturer != null)
                            {
                                if (manufacturer.Contains("NVIDIA", StringComparison.OrdinalIgnoreCase)) vendor = "NVIDIA";
                                else if (manufacturer.Contains("AMD", StringComparison.OrdinalIgnoreCase) || manufacturer.Contains("ATI", StringComparison.OrdinalIgnoreCase)) vendor = "AMD";
                                else if (manufacturer.Contains("Intel", StringComparison.OrdinalIgnoreCase)) vendor = "Intel";
                            }
                            gpuDetails["Vendor"] = vendor;
                            gpuDetails["DriverVersion"] = obj["DriverVersion"]?.ToString() ?? "Unknown";
                            gpuDetails["AdapterRAM"] = (Convert.ToInt64(obj["AdapterRAM"] ?? 0) / (1024 * 1024)).ToString(); // MB
                            gpuDetails["CurrentHorizontalResolution"] = obj["CurrentHorizontalResolution"]?.ToString() ?? "0";
                            gpuDetails["CurrentVerticalResolution"] = obj["CurrentVerticalResolution"]?.ToString() ?? "0";
                            gpuDetails["CurrentRefreshRate"] = obj["CurrentRefreshRate"]?.ToString() ?? "0";
                            break; // Only get the first GPU
                        }
                    }

                    // Map dictionary to GPUInfo object
                    return new GPUInfo
                    {
                        Name = gpuDetails["Name"],
                        Vendor = Enum.Parse<GPUVendor>(gpuDetails["Vendor"]), 
                        DriverVersion = gpuDetails["DriverVersion"],
                        VideoMemory = long.Parse(gpuDetails["AdapterRAM"]),
                        CurrentResolution = $"{gpuDetails["CurrentHorizontalResolution"]} x {gpuDetails["CurrentVerticalResolution"]}",
                        RefreshRate = int.Parse(gpuDetails["CurrentRefreshRate"])
                    };
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error detecting GPU: {ex.Message}", LogLevel.ERROR);
                    return new GPUInfo { Name = "Unknown" }; // Return a default object on error
                }
            });
        }

        /// <summary>
        /// Detects RAM information
        /// </summary>
        /// <returns>The RAM information</returns>
        private RAMInfo DetectRAM()
        {
            return GetOrAddToMemoryCache("RAM", () =>
            {
                try
                {
                    RAMInfo ramInfo = new RAMInfo();
                    long totalMemory = 0;

                    using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMemory"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            totalMemory += Convert.ToInt64(obj["Capacity"]);
                            ramInfo.Modules.Add(new RAMModuleInfo
                            {
                                Manufacturer = obj["Manufacturer"]?.ToString(),
                                PartNumber = obj["PartNumber"]?.ToString(),
                                Capacity = Convert.ToInt64(obj["Capacity"]) / (1024 * 1024 * 1024), // Convert to GB
                                Speed = Convert.ToInt32(obj["Speed"] ?? 0)
                            });
                        }
                    }

                    ramInfo.TotalCapacity = totalMemory / (1024 * 1024 * 1024); // Convert to GB

                    return ramInfo;
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error detecting RAM: {ex.Message}", LogLevel.ERROR);
                    return new RAMInfo { TotalCapacity = 0 };
                }
            });
        }

        /// <summary>
        /// Detects storage information
        /// </summary>
        /// <returns>The storage information</returns>
        private List<StorageInfo> DetectStorage()
        {
            try
            {
                List<StorageInfo> storageInfoList = new List<StorageInfo>();

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        StorageInfo storageInfo = new StorageInfo
                        {
                            Model = obj["Model"]?.ToString(),
                            Manufacturer = obj["Manufacturer"]?.ToString(),
                            InterfaceType = obj["InterfaceType"]?.ToString(),
                            Size = Convert.ToInt64(obj["Size"] ?? 0) / (1024 * 1024 * 1024) // Convert to GB
                        };

                        storageInfoList.Add(storageInfo);
                    }
                }

                return storageInfoList;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting storage: {ex.Message}", LogLevel.ERROR);
                return new List<StorageInfo>();
            }
        }

        /// <summary>
        /// Detects network adapter information
        /// </summary>
        /// <returns>The network adapter information</returns>
        private List<NetworkAdapterInfo> DetectNetworkAdapters()
        {
            try
            {
                List<NetworkAdapterInfo> networkAdapterInfoList = new List<NetworkAdapterInfo>();

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_NetworkAdapter WHERE PhysicalAdapter = True"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        NetworkAdapterInfo networkAdapterInfo = new NetworkAdapterInfo
                        {
                            Name = obj["Name"]?.ToString(),
                            Manufacturer = obj["Manufacturer"]?.ToString(),
                            MACAddress = obj["MACAddress"]?.ToString(),
                            AdapterType = obj["AdapterType"]?.ToString(),
                            Speed = Convert.ToInt64(obj["Speed"] ?? 0) / (1000 * 1000) // Convert to Mbps
                        };

                        networkAdapterInfoList.Add(networkAdapterInfo);
                    }
                }

                return networkAdapterInfoList;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting network adapters: {ex.Message}", LogLevel.ERROR);
                return new List<NetworkAdapterInfo>();
            }
        }

        /// <summary>
        /// Detects motherboard information
        /// </summary>
        /// <returns>The motherboard information</returns>
        private MotherboardInfo DetectMotherboard()
        {
            try
            {
                MotherboardInfo motherboardInfo = new MotherboardInfo();

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        motherboardInfo.Manufacturer = obj["Manufacturer"]?.ToString();
                        motherboardInfo.Model = obj["Product"]?.ToString();
                        motherboardInfo.SerialNumber = obj["SerialNumber"]?.ToString();
                        break; // Only get the first motherboard
                    }
                }

                return motherboardInfo;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting motherboard: {ex.Message}", LogLevel.ERROR);
                return new MotherboardInfo { Manufacturer = "Unknown", Model = "Unknown" };
            }
        }

        /// <summary>
        /// Detects operating system information
        /// </summary>
        /// <returns>The operating system information</returns>
        private OSInfo DetectOperatingSystem()
        {
            return GetOrAddToMemoryCache("OperatingSystem", () =>
            {
                _logger.LogDebug("Detecting Operating System details...");
                var osInfo = new OSInfo();
                try
                {
                    ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT Caption, Version, OSArchitecture, BuildNumber, InstallDate FROM Win32_OperatingSystem");
                    foreach (ManagementObject os in searcher.Get())
                    {
                        osInfo.Name = os["Caption"]?.ToString()?.Trim() ?? "Unknown";
                        osInfo.Version = os["Version"]?.ToString() ?? "Unknown";
                        osInfo.Architecture = os["OSArchitecture"]?.ToString() ?? "Unknown";
                        osInfo.BuildNumber = os["BuildNumber"]?.ToString() ?? "Unknown"; // Ensure this is populated
                        if (DateTime.TryParse(os["InstallDate"]?.ToString(), out DateTime installDate))
                        {
                            osInfo.InstallDate = installDate;
                        }
                        break; // Typically only one OS
                    }

                    // Populate new OS details
                    osInfo.WddmVersion = DetectWDDMVersion(osInfo.BuildNumber);
                    osInfo.HardwareAcceleratedGPUScheduling = DetectHAGSStatus(osInfo.WddmVersion);
                    osInfo.PageFile = DetectPageFileInfo();

                    _logger.LogDebug($"Operating System detected: {osInfo.Name}, Build: {osInfo.BuildNumber}, WDDM: {osInfo.WddmVersion}, HAGS: {osInfo.HardwareAcceleratedGPUScheduling}");
                }
                catch (Exception ex)
                {
                    _logger.LogError("Failed to detect Operating System information", ex);
                    // Set defaults if detection fails to avoid null references
                    osInfo.Name = osInfo.Name ?? "Unknown";
                    osInfo.Version = osInfo.Version ?? "Unknown";
                    osInfo.Architecture = osInfo.Architecture ?? "Unknown";
                    osInfo.BuildNumber = osInfo.BuildNumber ?? "Unknown";
                    osInfo.WddmVersion = WDDMVersion.Unknown;
                    osInfo.HardwareAcceleratedGPUScheduling = HAGSStatus.Unknown;
                    osInfo.PageFile = osInfo.PageFile ?? new PageFileInfo { UsageType = PageFileUsageType.Unknown };
                }
                return osInfo;
            });
        }

        // Placeholder for DetectWDDMVersion
        private WDDMVersion DetectWDDMVersion(string buildNumberString)
        {
            _logger.LogDebug($"Attempting to detect WDDM version from OS Build: {buildNumberString}");
            if (string.IsNullOrWhiteSpace(buildNumberString) || !int.TryParse(buildNumberString, out int buildNumber))
            {
                _logger.LogWarning("Could not parse OS BuildNumber for WDDM detection.");
                return WDDMVersion.Unknown;
            }

            // Windows 11 versions and their WDDM
            if (buildNumber >= 26100) // Windows 11 24H2+
            {
                return WDDMVersion.WDDM_3_2; // Or newer if known
            }
            if (buildNumber >= 22621) // Windows 11 22H2/23H2 (Nickel)
            {
                return WDDMVersion.WDDM_3_1;
            }
            if (buildNumber >= 22000) // Windows 11 21H2 (Sun Valley)
            {
                return WDDMVersion.WDDM_3_0;
            }

            // Windows 10 versions and their WDDM
            // For HAGS, WDDM 2.7 is key, which came with 20H1 (build 19041)
            if (buildNumber >= 19041) // Windows 10 20H1, 20H2, 21H1, 21H2, 22H2
            {
                return WDDMVersion.WDDM_2_7;
            }
            if (buildNumber >= 18362) // Windows 10 19H1/19H2 (Vanadium)
            {
                return WDDMVersion.WDDM_2_6;
            }
            if (buildNumber >= 17763) // Windows 10 RS5 (Redstone 5)
            {
                return WDDMVersion.WDDM_2_5;
            }
            if (buildNumber >= 17134) // Windows 10 RS4 (Redstone 4)
            {
                return WDDMVersion.WDDM_2_4;
            }
            if (buildNumber >= 16299) // Windows 10 RS3 (Redstone 3)
            {
                return WDDMVersion.WDDM_2_3;
            }
            if (buildNumber >= 15063) // Windows 10 RS2 (Redstone 2 - Creators Update)
            {
                return WDDMVersion.WDDM_2_2;
            }
            if (buildNumber >= 14393) // Windows 10 RS1 (Redstone 1 - Anniversary Update)
            {
                return WDDMVersion.WDDM_2_1;
            }
            if (buildNumber >= 10586) // Windows 10 TH2 (Threshold 2 - November Update)
            {
                return WDDMVersion.WDDM_2_0;
            }
            if (buildNumber >= 10240) // Windows 10 TH1 (Threshold 1 - Initial Release)
            {
                return WDDMVersion.WDDM_2_0;
            }
            
            // Older OS or unknown build
            if (buildNumber < 10240) // Pre-Windows 10
            {
                 return WDDMVersion.Unsupported;
            }

            _logger.LogWarning($"Unknown OS BuildNumber for WDDM detection: {buildNumberString}");
            return WDDMVersion.Unknown; 
        }

        // Placeholder for DetectHAGSStatus
        private HAGSStatus DetectHAGSStatus(WDDMVersion wddmVersion)
        {
            _logger.LogDebug($"Attempting to detect HAGS status. Current WDDM version: {wddmVersion}");

            // HAGS requires WDDM 2.7 or newer.
            // The WDDMVersion enum is ordered, so we can compare.
            if (wddmVersion < WDDMVersion.WDDM_2_7)
            {
                _logger.LogInfo("HAGS is Unsupported due to WDDM version being older than 2.7.");
                return HAGSStatus.Unsupported;
            }

            try
            {
                // The registry key for HAGS is HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers
                // The value is HwSchMode (DWORD)
                // 1 = Off (but supported)
                // 2 = On
                // If the value doesn't exist, it might mean drivers haven't enabled it, or OS doesn't fully support the UI toggle yet (older builds of Win10 2004).
                // However, if WDDM is 2.7+, we assume the underlying OS capability is there.
                // A more robust check would be to see if the GPU driver itself reports HAGS capability, but that's more complex via WMI/DXGI.
                // For now, we'll rely on the registry key which reflects user/OEM choice if the feature is available.

                using (RegistryKey graphicsDriversKey = Registry.LocalMachine.OpenSubKey("SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers"))
                {
                    if (graphicsDriversKey == null)
                    {
                        _logger.LogWarning("GraphicsDrivers registry key not found. HAGS status Unknown (likely Unsupported).");
                        return HAGSStatus.Unknown; // Or Unsupported, depending on strictness.
                    }

                    object hwSchModeValue = graphicsDriversKey.GetValue("HwSchMode");

                    if (hwSchModeValue == null)
                    {
                        // If WDDM >= 2.7 but HwSchMode value is not present, it implies HAGS is available but not explicitly configured (default off).
                        // Or it could mean an older GPU/driver on a HAGS-capable OS that doesn't support it.
                        // For simplicity, if the value isn't there, we'll consider it as effectively Disabled or an Unknown/Unsupported state from the OS config perspective.
                        // Let's assume if WDDM is high enough, it's 'Supported' but 'Disabled' by default if not set.
                        // However, a safer bet for the tweak app is to call it Unsupported if not explicitly configurable.
                        _logger.LogInfo("HwSchMode registry value not found. Assuming HAGS is not actively configured (Unsupported or Disabled by default).");
                        return HAGSStatus.Unsupported; // Or Unknown if we want to differentiate "not set" from "driver doesn't support"
                    }

                    if (hwSchModeValue is int mode)
                    {
                        switch (mode)
                        {
                            case 1:
                                _logger.LogInfo("HAGS is Supported and Disabled.");
                                return HAGSStatus.Disabled;
                            case 2:
                                _logger.LogInfo("HAGS is Supported and Enabled.");
                                return HAGSStatus.Enabled;
                            default:
                                _logger.LogWarning($"Unknown HwSchMode value: {mode}. HAGS status Unknown.");
                                return HAGSStatus.Unknown;
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"HwSchMode registry value is not a DWORD. Type: {hwSchModeValue.GetType()}. HAGS status Unknown.");
                        return HAGSStatus.Unknown;
                    }
                }
            }
            catch (System.Security.SecurityException ex)
            {
                _logger.LogError("SecurityException while accessing HAGS registry key. HAGS status Unknown.", ex);
                return HAGSStatus.Unknown; // Or .Unsupported if we assume no access means not applicable
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception while detecting HAGS status from registry.", ex);
                return HAGSStatus.Unknown;
            }
        }

        // Placeholder for DetectPageFileInfo
        private PageFileInfo DetectPageFileInfo()
        {
            _logger.LogDebug("Attempting to detect Page File information...");
            var pageFileInfo = new PageFileInfo(); // Default to Unknown/Disabled

            try
            {
                // Query Win32_PageFileSetting for configured page files.
                // If a page file is system-managed, InitialSize and MaximumSize are often 0.
                // If specific sizes are set, those are used.
                // If no entries are found, it could mean paging is completely disabled.
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT Name, InitialSize, MaximumSize FROM Win32_PageFileSetting");
                ManagementObjectCollection results = searcher.Get();

                if (results.Count == 0)
                {
                    _logger.LogInfo("No Win32_PageFileSetting entries found. Assuming Page File is Disabled.");
                    pageFileInfo.UsageType = PageFileUsageType.Disabled;
                    return pageFileInfo;
                }

                // For simplicity, we'll focus on the first active page file setting found.
                // A more complex system might handle multiple page files across different drives.
                foreach (ManagementObject obj in results)
                {
                    pageFileInfo.InitialSizeMB = Convert.ToInt64(obj["InitialSize"]); // These are in MB
                    pageFileInfo.MaximumSizeMB = Convert.ToInt64(obj["MaximumSize"]);

                    if (pageFileInfo.InitialSizeMB == 0 && pageFileInfo.MaximumSizeMB == 0)
                    {
                        // This typically indicates a system-managed page file.
                        // We can also check Win32_PageFileUsage for actual current size if needed,
                        // but for configuration status, Win32_PageFileSetting is better.
                        pageFileInfo.UsageType = PageFileUsageType.SystemManaged;
                        _logger.LogInfo($"Page File found on {pageFileInfo.DriveLetter}: System Managed.");
                    }
                    else
                    {
                        pageFileInfo.UsageType = PageFileUsageType.Custom;
                        _logger.LogInfo($"Page File found on {pageFileInfo.DriveLetter}: Custom Size. Initial: {pageFileInfo.InitialSizeMB}MB, Max: {pageFileInfo.MaximumSizeMB}MB.");
                    }
                    // We'll take the first one found. A more robust solution might aggregate or pick the one on C:
                    break; 
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to detect Page File information using Win32_PageFileSetting.", ex);
                pageFileInfo.UsageType = PageFileUsageType.Unknown; // Fallback on error
            }
            
            // If still unknown after trying Win32_PageFileSetting (e.g. it failed, or returned 0 count but we want to be sure)
            // we could try to query Win32_PageFileUsage, which shows current usage rather than settings.
            // If Win32_PageFileUsage also returns nothing, then it's likely truly disabled.
            if (pageFileInfo.UsageType == PageFileUsageType.Unknown || pageFileInfo.UsageType == PageFileUsageType.Disabled)
            {
                try
                {
                    ManagementObjectSearcher usageSearcher = new ManagementObjectSearcher("SELECT Name FROM Win32_PageFileUsage");
                    if (usageSearcher.Get().Count > 0)
                    {
                         // If settings said disabled/unknown, but usage shows one, it's likely system managed but not showing typical 0/0 in settings.
                         // This can happen. If a file exists and is in use, it's not truly disabled.
                         if (pageFileInfo.UsageType == PageFileUsageType.Disabled) {
                            _logger.LogInfo("Win32_PageFileSetting indicated disabled, but Win32_PageFileUsage found an active page file. Marking as System Managed.");
                            pageFileInfo.UsageType = PageFileUsageType.SystemManaged;
                         }
                    }
                    else if (pageFileInfo.UsageType == PageFileUsageType.Unknown)
                    {
                        // If still unknown and no usage, then it's fair to say disabled.
                        _logger.LogInfo("No Win32_PageFileUsage entries found and status was Unknown. Marking Page File as Disabled.");
                        pageFileInfo.UsageType = PageFileUsageType.Disabled;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Failed to query Win32_PageFileUsage for confirmation.", ex);
                     // Keep previous state if this secondary check fails
                }
            }

            return pageFileInfo;
        }

        public void Stop()
        {
            try
            {
                _logger.LogInfo("Stopping hardware detection service...");
                _cachedHardwareInfo = null;
                _lastCacheUpdate = DateTime.MinValue;
                _isInitialized = false;
                _logger.LogSuccess("Hardware detection service stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to stop hardware detection service", ex);
                throw;
            }
        }

        public string GetSystemInfo()
        {
            try
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("Hardware detection service is not initialized");
                }

                var info = new System.Text.StringBuilder();
                info.AppendLine("=== System Hardware Information ===");
                info.AppendLine();

                // CPU Information
                info.AppendLine("=== CPU ===");
                if (_cachedHardwareInfo.CPU != null)
                {
                    info.AppendLine($"Name: {_cachedHardwareInfo.CPU.Name}");
                    info.AppendLine($"Manufacturer: {_cachedHardwareInfo.CPU.Manufacturer}");
                    info.AppendLine($"Cores: {_cachedHardwareInfo.CPU.Cores}");
                    info.AppendLine($"Logical Processors: {_cachedHardwareInfo.CPU.LogicalProcessors}");
                    info.AppendLine($"Max Clock Speed: {_cachedHardwareInfo.CPU.MaxClockSpeed} MHz");
                    info.AppendLine($"Architecture: {_cachedHardwareInfo.CPU.Architecture}");
                    info.AppendLine($"L2 Cache Size: {_cachedHardwareInfo.CPU.L2CacheSize} KB");
                    info.AppendLine($"L3 Cache Size: {_cachedHardwareInfo.CPU.L3CacheSize} KB");
                }
                info.AppendLine();

                // GPU Information
                info.AppendLine("=== GPU ===");
                if (_cachedHardwareInfo.GPU != null)
                {
                    info.AppendLine($"Name: {_cachedHardwareInfo.GPU.Name}");
                    info.AppendLine($"Manufacturer: {_cachedHardwareInfo.GPU.FormattedVendor}");
                    info.AppendLine($"Driver Version: {_cachedHardwareInfo.GPU.DriverVersion}");
                    info.AppendLine($"Video Memory: {_cachedHardwareInfo.GPU.VideoMemory} MB");
                    info.AppendLine($"Current Resolution: {_cachedHardwareInfo.GPU.CurrentResolution}");
                    info.AppendLine($"Refresh Rate: {_cachedHardwareInfo.GPU.RefreshRate} Hz");
                }
                info.AppendLine();

                // RAM Information
                info.AppendLine("=== RAM ===");
                if (_cachedHardwareInfo.RAM != null)
                {
                    info.AppendLine($"Total Capacity: {_cachedHardwareInfo.RAM.TotalCapacity} GB");
                    foreach (var module in _cachedHardwareInfo.RAM.Modules)
                    {
                        info.AppendLine($"Module: {module.Manufacturer} - {module.PartNumber}");
                        info.AppendLine($"Capacity: {module.Capacity} GB");
                        info.AppendLine($"Speed: {module.Speed} MHz");
                    }
                }
                info.AppendLine();

                // Storage Information
                info.AppendLine("=== Storage ===");
                if (_cachedHardwareInfo.Storage != null)
                {
                    foreach (var drive in _cachedHardwareInfo.Storage)
                    {
                        info.AppendLine($"Model: {drive.Model}");
                        info.AppendLine($"Manufacturer: {drive.Manufacturer}");
                        info.AppendLine($"Interface Type: {drive.InterfaceType}");
                        info.AppendLine($"Size: {drive.Size} GB");
                    }
                }
                info.AppendLine();

                // Network Information
                info.AppendLine("=== Network ===");
                if (_cachedHardwareInfo.NetworkAdapters != null)
                {
                    foreach (var adapter in _cachedHardwareInfo.NetworkAdapters)
                    {
                        info.AppendLine($"Name: {adapter.Name}");
                        info.AppendLine($"Manufacturer: {adapter.Manufacturer}");
                        info.AppendLine($"MAC Address: {adapter.MACAddress}");
                        info.AppendLine($"Adapter Type: {adapter.AdapterType}");
                        info.AppendLine($"Speed: {adapter.Speed} Mbps");
                    }
                }

                return info.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to get system information", ex);
                throw;
            }
        }

        public async Task<bool> OptimizeHardware()
        {
            try
            {
                _logger.LogInfo("Starting hardware optimization...");

                // This is just a placeholder - implement actual optimization logic
                await Task.Delay(1000); // Simulate work

                _logger.LogSuccess("Hardware optimization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to optimize hardware", ex);
                return false;
            }
        }

        public async Task<bool> RunCompatibilityTests()
        {
            try
            {
                _logger.LogInfo("Starting hardware compatibility tests...");

                // This is just a placeholder - implement actual compatibility testing logic
                await Task.Delay(1000); // Simulate work

                _logger.LogSuccess("Hardware compatibility tests completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to run hardware compatibility tests", ex);
                return false;
            }
        }

        private void GatherCPUInfo()
        {
            try
            {
                if (_cachedHardwareInfo == null)
                    _cachedHardwareInfo = new HardwareInfo();
                var cpuInfo = new Dictionary<string, string>();
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        cpuInfo["Name"] = obj["Name"]?.ToString() ?? "Unknown";
                        cpuInfo["Manufacturer"] = obj["Manufacturer"]?.ToString() ?? "Unknown";
                        cpuInfo["Description"] = obj["Description"]?.ToString() ?? "Unknown";
                        cpuInfo["NumberOfCores"] = obj["NumberOfCores"]?.ToString() ?? "Unknown";
                        cpuInfo["NumberOfLogicalProcessors"] = obj["NumberOfLogicalProcessors"]?.ToString() ?? "Unknown";
                        cpuInfo["MaxClockSpeed"] = $"{obj["MaxClockSpeed"]} MHz";
                        cpuInfo["L2CacheSize"] = $"{obj["L2CacheSize"]} KB";
                        cpuInfo["L3CacheSize"] = $"{obj["L3CacheSize"]} KB";
                        break; // We only need the first CPU
                    }
                }
                _cachedHardwareInfo.CPU = new CPUInfo
                {
                    Name = cpuInfo["Name"],
                    Manufacturer = cpuInfo["Manufacturer"],
                    Cores = int.Parse(cpuInfo["NumberOfCores"]),
                    LogicalProcessors = int.Parse(cpuInfo["NumberOfLogicalProcessors"]),
                    MaxClockSpeed = int.Parse(cpuInfo["MaxClockSpeed"]),
                    Architecture = cpuInfo["Architecture"],
                    L2CacheSize = int.Parse(cpuInfo["L2CacheSize"]),
                    L3CacheSize = int.Parse(cpuInfo["L3CacheSize"]),
                    Description = cpuInfo["Description"]
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to gather CPU information", ex);
                _cachedHardwareInfo.CPU = new CPUInfo { Name = "Unknown" };
            }
        }

        private void GatherGPUInfo()
        {
            try
            {
                var gpuInfo = new Dictionary<string, string>();
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        gpuInfo["Name"] = obj["Name"]?.ToString() ?? "Unknown";
                        gpuInfo["AdapterRAM"] = $"{Convert.ToInt64(obj["AdapterRAM"] ?? 0) / (1024 * 1024)} MB";
                        gpuInfo["DriverVersion"] = obj["DriverVersion"]?.ToString() ?? "Unknown";
                        gpuInfo["CurrentHorizontalResolution"] = obj["CurrentHorizontalResolution"]?.ToString() ?? "0";
                        gpuInfo["CurrentVerticalResolution"] = obj["CurrentVerticalResolution"]?.ToString() ?? "0";
                        gpuInfo["CurrentRefreshRate"] = obj["CurrentRefreshRate"]?.ToString() ?? "0";
                        break; // We only need the first GPU
                    }
                }
                _cachedHardwareInfo.GPU = new GPUInfo
                {
                    Name = gpuInfo["Name"],
                    VideoMemory = long.Parse(gpuInfo["AdapterRAM"].Split(' ')[0]),
                    DriverVersion = gpuInfo["DriverVersion"],
                    CurrentResolution = $"{gpuInfo["CurrentHorizontalResolution"]} x {gpuInfo["CurrentVerticalResolution"]}",
                    RefreshRate = int.Parse(gpuInfo["CurrentRefreshRate"] ?? "0"),
                    Vendor = GPUVendor.Other // Defaulting vendor, can be improved
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to gather GPU information", ex);
                _cachedHardwareInfo.GPU = new GPUInfo { Name = "Unknown" };
            }
        }

        private void GatherRAMInfo()
        {
            try
            {
                var ramInfo = new Dictionary<string, string>();
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMemory"))
                {
                    ulong totalCapacity = 0;
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        totalCapacity += Convert.ToUInt64(obj["Capacity"] ?? 0);
                        ramInfo["Speed"] = $"{obj["Speed"]} MHz";
                        ramInfo["Manufacturer"] = obj["Manufacturer"]?.ToString() ?? "Unknown";
                    }
                    ramInfo["TotalCapacity"] = $"{totalCapacity / (1024 * 1024 * 1024)} GB";
                }
                _cachedHardwareInfo.RAM = new RAMInfo
                {
                    TotalCapacity = long.Parse(ramInfo["TotalCapacity"].Split(' ')[0]),
                    Modules = new List<RAMModuleInfo>
                    {
                        new RAMModuleInfo
                        {
                            Manufacturer = ramInfo["Manufacturer"],
                            Speed = int.Parse(ramInfo["Speed"].Split(' ')[0])
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to gather RAM information", ex);
                _cachedHardwareInfo.RAM = new RAMInfo { TotalCapacity = 0 };
            }
        }

        private void GatherStorageInfo()
        {
            try
            {
                var storageInfo = new List<Dictionary<string, string>>();
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var driveInfo = new Dictionary<string, string>
                        {
                            ["Model"] = obj["Model"]?.ToString() ?? "Unknown",
                            ["Size"] = $"{Convert.ToUInt64(obj["Size"] ?? 0) / (1024 * 1024 * 1024)} GB",
                            ["InterfaceType"] = obj["InterfaceType"]?.ToString() ?? "Unknown",
                        };
                        storageInfo.Add(driveInfo);
                    }
                }
                _cachedHardwareInfo.Storage = storageInfo.Select(info => new StorageInfo
                {
                    Model = info["Model"],
                    Manufacturer = info["Manufacturer"],
                    InterfaceType = info["InterfaceType"],
                    Size = long.Parse(info["Size"].Split(' ')[0]),
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to gather storage information", ex);
                _cachedHardwareInfo.Storage = new List<StorageInfo>();
            }
        }

        private void GatherNetworkInfo()
        {
            try
            {
                var networkInfo = new List<Dictionary<string, string>>();
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_NetworkAdapter WHERE PhysicalAdapter=True"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var adapterInfo = new Dictionary<string, string>
                        {
                            ["Name"] = obj["Name"]?.ToString() ?? "Unknown",
                            ["Manufacturer"] = obj["Manufacturer"]?.ToString() ?? "Unknown",
                            ["AdapterType"] = obj["AdapterType"]?.ToString() ?? "Unknown",
                            ["Speed"] = obj["Speed"] != null ? $"{Convert.ToUInt64(obj["Speed"]) / 1000000} Mbps" : "Unknown",
                            ["MACAddress"] = obj["MACAddress"]?.ToString() ?? "Unknown"
                        };
                        networkInfo.Add(adapterInfo);
                    }
                }
                _cachedHardwareInfo.NetworkAdapters = networkInfo.Select(info => new NetworkAdapterInfo
                {
                    Name = info["Name"],
                    Manufacturer = info["Manufacturer"],
                    MACAddress = info["MACAddress"],
                    AdapterType = info["AdapterType"],
                    Speed = long.Parse(info["Speed"].Split(' ')[0])
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to gather network information", ex);
                _cachedHardwareInfo.NetworkAdapters = new List<NetworkAdapterInfo>();
            }
        }

        // Add a public constructor for DI
        public HardwareDetectionService(bool forDI = false)
            : this() // Call the private constructor
        {
            // This constructor is for DI compatibility
        }
    }

    /// <summary>
    /// Model for hardware cache data
    /// </summary>
    public class HardwareCacheData
    {
        /// <summary>
        /// Gets or sets the hardware information
        /// </summary>
        public HardwareInfo HardwareInfo { get; set; }

        /// <summary>
        /// Gets or sets the last updated date
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }
}







