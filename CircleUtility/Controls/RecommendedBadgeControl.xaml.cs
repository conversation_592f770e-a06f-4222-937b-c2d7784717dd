// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for RecommendedBadgeControl.xaml
    /// </summary>
    public partial class RecommendedBadgeControl : UserControl
    {
        private static readonly Color RecommendedColor = Color.FromRgb(0, 200, 83);  // Green
        private Storyboard _glowAnimation;

        /// <summary>
        /// Gets or sets the recommendation reason
        /// </summary>
        public string RecommendationReason
        {
            get { return (string)GetValue(RecommendationReasonProperty); }
            set { SetValue(RecommendationReasonProperty, value); }
        }

        /// <summary>
        /// Dependency property for RecommendationReason
        /// </summary>
        public static readonly DependencyProperty RecommendationReasonProperty =
            DependencyProperty.Register("RecommendationReason", typeof(string), typeof(RecommendedBadgeControl),
                new PropertyMetadata("Recommended for your hardware", OnRecommendationReasonChanged));

        private static void OnRecommendationReasonChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is RecommendedBadgeControl control && e.NewValue is string reason)
            {
                control.MainGrid.ToolTip = reason;
            }
        }

        /// <summary>
        /// Gets or sets the recommendation score (0-100)
        /// </summary>
        public int RecommendationScore
        {
            get { return (int)GetValue(RecommendationScoreProperty); }
            set { SetValue(RecommendationScoreProperty, value); }
        }

        /// <summary>
        /// Dependency property for RecommendationScore
        /// </summary>
        public static readonly DependencyProperty RecommendationScoreProperty =
            DependencyProperty.Register("RecommendationScore", typeof(int), typeof(RecommendedBadgeControl),
                new PropertyMetadata(80, OnRecommendationScoreChanged));

        private static void OnRecommendationScoreChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is RecommendedBadgeControl control && e.NewValue is int score)
            {
                control.UpdateGlowIntensity(score);
            }
        }

        /// <summary>
        /// Initializes a new instance of the RecommendedBadgeControl class
        /// </summary>
        public RecommendedBadgeControl()
        {
            InitializeComponent();

            // Get the glow animation from resources
            _glowAnimation = (Storyboard)FindResource("GlowAnimation");

            // Start the animation
            _glowAnimation.Begin(this);

            // Set initial tooltip
            MainGrid.ToolTip = RecommendationReason;
        }

        /// <summary>
        /// Updates the glow intensity based on the recommendation score
        /// </summary>
        /// <param name="score">The recommendation score (0-100)</param>
        private void UpdateGlowIntensity(int score)
        {
            // Adjust glow intensity based on score
            if (score >= 90)
            {
                // Highly recommended - brightest glow
                ((DropShadowEffect)BadgeBackground.Effect).BlurRadius = 15;
                ((DropShadowEffect)BadgeBackground.Effect).Opacity = 0.8;
            }
            else if (score >= 80)
            {
                // Recommended - medium glow
                ((DropShadowEffect)BadgeBackground.Effect).BlurRadius = 10;
                ((DropShadowEffect)BadgeBackground.Effect).Opacity = 0.6;
            }
            else
            {
                // Somewhat recommended - subtle glow
                ((DropShadowEffect)BadgeBackground.Effect).BlurRadius = 8;
                ((DropShadowEffect)BadgeBackground.Effect).Opacity = 0.4;
            }
        }
    }
}
