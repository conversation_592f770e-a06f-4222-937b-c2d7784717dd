using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Models;
using CircleUtility.Services;
using System.Windows;
using System.Threading.Tasks;
using System.Linq;
using System.Windows.Threading;
using Microsoft.Extensions.Configuration;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the Admin Control Panel
    /// </summary>
    public class AdminViewModel : ViewModelBase, IConfigAware
    {
        private readonly LoggingService _logger;
        private readonly IUserTrackingService _userTracking;
        private readonly HardwareFingerprintService _hardwareFingerprintService;
        private readonly SecurityService _securityService;
        private readonly CentralConfigService _configService;
        private readonly UpdateService _updateService;
        private ObservableCollection<UserActivity> _userActivities;
        private ObservableCollection<LogEntryModel> _logEntries;
        private ObservableCollection<UserProfile> _userProfiles;
        private ObservableCollection<HardwareProfile> _hardwareProfiles;
        private ObservableCollection<SystemMetric> _systemMetrics;
        private ObservableCollection<UserPunishment> _punishmentHistory;
        private string _selectedTab;
        private UserProfile _selectedUser;
        private DiscordUser _selectedSecurityUser;
        private string _searchText;
        private bool _searchByHardwareId;
        private bool _searchByIp;
        private bool _includeDisabled;
        private bool _includeAdmins;
        private string _punishmentReason;
        private PunishmentType _selectedPunishmentType;
        private DateTime? _punishmentExpirationDate;
        private bool _isLoading;
        private string _statusMessage;
        private bool _isStatusVisible;
        private bool _isStatusSuccess;
        private DispatcherTimer _refreshTimer;
        private int _refreshInterval;
        private int _refreshIntervalIndex;
        private bool _autoStartWithWindows;
        private bool _autoUpdate;
        private string _updateServerUrl;
        private bool _requireLogin;
        private int _sessionTimeout;
        private CentralConfig _currentConfig;
        private ObservableCollection<UserAccess> _users;
        private UserAccess _selectedUserAccess;
        private bool _isUpdateAvailable;
        private string _updateVersion;
        private string _updateNotes;
        private bool _isCheckingUpdate;
        private ObservableCollection<UpdateHistory> _updateHistory;
        private TimeSpan _updateCheckInterval;
        private DateTime _lastUpdateCheck;
        private readonly EnhancedDiscordUserService _discordUserService;
        private readonly DiscordService _discordService;
        private ObservableCollection<DiscordUser> _searchResults = new ObservableCollection<DiscordUser>();
        public ObservableCollection<PendingRegistration> PendingRegistrations { get; set; } = new ObservableCollection<PendingRegistration>();
        public ICommand ApprovePendingRegistrationCommand { get; }
        public ICommand DenyPendingRegistrationCommand { get; }

        /// <summary>
        /// Initializes a new instance of the AdminViewModel class
        /// </summary>
        public AdminViewModel(IUserTrackingService userTracking)
        {
            _logger = LoggingService.Instance;
            _userTracking = userTracking;
            _hardwareFingerprintService = HardwareFingerprintService.Instance;
            _securityService = SecurityService.Instance;
            _configService = new CentralConfigService(
                "****************************************",
                "AquaknowsJava",
                "TheCircleUtility"
            );
            _updateService = UpdateService.Instance;

            // Initialize collections
            UserActivities = new ObservableCollection<UserActivity>();
            LogEntries = new ObservableCollection<LogEntryModel>();
            UserProfiles = new ObservableCollection<UserProfile>();
            HardwareProfiles = new ObservableCollection<HardwareProfile>();
            SystemMetrics = new ObservableCollection<SystemMetric>();
            Users = new ObservableCollection<UserAccess>();

            // Initialize commands
            RefreshDataCommand = new RelayCommand(RefreshData);
            AddUserCommand = new RelayCommand(AddUser);
            EditUserCommand = new RelayCommand(EditUser, () => SelectedUser != null);
            DeleteUserCommand = new RelayCommand(DeleteUser, () => SelectedUser != null);
            LockUserCommand = new RelayCommand(LockUser, () => SelectedUser != null && !SelectedUser.IsLocked);
            UnlockUserCommand = new RelayCommand(UnlockUser, () => SelectedUser != null && SelectedUser.IsLocked);
            LockHardwareCommand = new RelayCommand(LockHardware, () => SelectedUser != null);
            ViewUsersByIpCommand = new RelayCommand(ViewUsersByIp, () => SelectedUser != null && !string.IsNullOrEmpty(SelectedUser.LastIpAddress));

            // New commands for enhanced admin functionality
            SearchUsersCommand = new RelayCommand(SearchUsers);
            ClearSearchCommand = new RelayCommand(ClearSearch);
            PromoteToAdminCommand = new RelayCommand(PromoteToAdmin, 
                () => SelectedSecurityUser != null && !SelectedSecurityUser.IsAdmin);
            AddHardwareIdCommand = new RelayCommand(AddHardwareId, 
                () => SelectedSecurityUser != null && !string.IsNullOrEmpty(SelectedSecurityUser.HardwareId));
            AddIpAddressCommand = new RelayCommand(AddIpAddress, () => SelectedSecurityUser != null);
            ViewPunishmentHistoryCommand = new RelayCommand(ViewPunishmentHistory, () => SelectedSecurityUser != null);
            ApplyPunishmentCommand = new RelayCommand(ApplyPunishment, () => SelectedSecurityUser != null && !string.IsNullOrEmpty(PunishmentReason));
            RevokePunishmentCommand = new RelayCommand<string>(RevokePunishment);
            ViewUsersByHardwareIdCommand = new RelayCommand(ViewUsersByHardwareId, 
                () => SelectedSecurityUser != null && !string.IsNullOrEmpty(SelectedSecurityUser.HardwareId));
            ViewUsersByHardwareCommand = new RelayCommand(ViewUsersByHardware, () => SelectedUser != null && !string.IsNullOrEmpty(SelectedUser.HardwareFingerprint));
            ClearLogsCommand = new RelayCommand(ClearLogs);
            ExportLogsCommand = new RelayCommand(ExportLogs);
            SearchCommand = new RelayCommand(PerformSearch);
            SwitchTabCommand = new RelayCommand<string>(SwitchTab);

            // Configuration tab commands
            SaveConfigCommand = new RelayCommand(SaveConfig);
            ResetConfigCommand = new RelayCommand(ResetConfig);

            // Updates tab commands
            CheckForUpdatesCommand = new RelayCommand(CheckForUpdates);
            UploadUpdateCommand = new RelayCommand(UploadUpdate);
            ManageVersionsCommand = new RelayCommand(ManageVersions);
            CreateReleaseCommand = new RelayCommand(CreateRelease);
            ViewReleaseNotesCommand = new RelayCommand(ViewReleaseNotes);

            // Initialize properties
            SelectedTab = "Users";
            IsLoading = false;
            StatusMessage = "";
            IsStatusVisible = false;
            RefreshIntervalIndex = 3; // 30 seconds
            RefreshInterval = 30; // 30 seconds

            // Initialize configuration properties
            AutoStartWithWindows = false;
            AutoUpdate = true;
            UpdateCheckInterval = TimeSpan.FromDays(1); // Daily
            UpdateServerUrl = "https://updates.circleutility.com";
            RequireLogin = true;
            SessionTimeout = 30; // 30 minutes

            // Initialize EnhancedDiscordUserService
            var configBuilder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            IConfiguration configuration = configBuilder.Build();
            _discordUserService = new EnhancedDiscordUserService(configuration);

            // Initialize DiscordService
            _discordService = DiscordService.Instance;
            // Load webhook URL from config if available
            var webhookUrl = configuration["Discord:WebhookUrl"];
            if (!string.IsNullOrWhiteSpace(webhookUrl))
            {
                _discordService.Initialize(webhookUrl);
            }

            // Load initial data
            LoadInitialData();

            // Set up refresh timer
            SetupRefreshTimer();

            _logger.Log("AdminViewModel initialized", LogLevel.INFO);

            // Subscribe to update events
            _updateService.UpdateAvailable += (s, updateInfo) =>
            {
                IsUpdateAvailable = true;
                UpdateVersion = updateInfo.Version;
                UpdateNotes = updateInfo.ReleaseNotes;
                StatusMessage = $"Update {updateInfo.Version} is available!";
            };

            _updateService.UpdateProgress += (s, message) =>
            {
                StatusMessage = message;
            };

            _updateService.UpdateError += (s, ex) =>
            {
                StatusMessage = $"Update error: {ex.Message}";
            };

            EmergencyCommand = new RelayCommand(SendEmergencyAlert);

            ResetHardwareAndIpCommand = new RelayCommand(ResetHardwareAndIp, () => SelectedSecurityUser != null);

            ApprovePendingRegistrationCommand = new RelayCommand<string>(ApprovePendingRegistration);
            DenyPendingRegistrationCommand = new RelayCommand<string>(DenyPendingRegistration);
            LoadPendingRegistrations();
        }

        private void RefreshData()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Refreshing data...";
                IsStatusVisible = true;

                // User activity retrieval not implemented
                _logger.Log("User activity retrieval is not yet implemented.", LogLevel.WARNING);
                UserActivities = new ObservableCollection<UserActivity>();

                // User profile retrieval not implemented
                _logger.Log("User profile retrieval is not yet implemented.", LogLevel.WARNING);
                UserProfiles = new ObservableCollection<UserProfile>();

                // Hardware profile retrieval not implemented
                _logger.Log("Hardware profile retrieval is not yet implemented.", LogLevel.WARNING);
                HardwareProfiles = new ObservableCollection<HardwareProfile>();

                // System metrics retrieval not implemented
                _logger.Log("System metrics retrieval is not yet implemented.", LogLevel.WARNING);
                SystemMetrics = new ObservableCollection<SystemMetric>();

                // User access retrieval from config not implemented
                _logger.Log("User access retrieval from config is not yet implemented.", LogLevel.WARNING);
                Users.Clear();

                IsLoading = false;
                StatusMessage = "Data refreshed (stubbed).";
                IsStatusSuccess = true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error refreshing data: {ex.Message}", LogLevel.ERROR);
                IsLoading = false;
                StatusMessage = $"Error refreshing data: {ex.Message}";
                IsStatusSuccess = false;
            }
        }

        private async void AddUser()
        {
            // Prompt for user details (simple input dialog for now)
            var username = Microsoft.VisualBasic.Interaction.InputBox("Enter username:", "Add User");
            if (string.IsNullOrWhiteSpace(username)) return;
            var password = Microsoft.VisualBasic.Interaction.InputBox("Enter password:", "Add User");
            if (string.IsNullOrWhiteSpace(password)) return;
            var displayName = Microsoft.VisualBasic.Interaction.InputBox("Enter display name:", "Add User");
            if (string.IsNullOrWhiteSpace(displayName)) displayName = username;
            var role = Microsoft.VisualBasic.Interaction.InputBox("Enter role (Admin/User):", "Add User", "User");
            if (string.IsNullOrWhiteSpace(role)) role = "User";

            var newUser = new DiscordUser
            {
                Username = username,
                Password = password,
                DisplayName = displayName,
                Role = role,
                IsActive = true,
                DiscordId = $"{username}_{DateTime.Now.Ticks}",
                JoinDate = DateTime.Now,
                Email = $"{username}@circleutility.local"
            };

            var success = await _discordUserService.AddUserAsync(newUser);
            if (success)
            {
                StatusMessage = $"User '{username}' added successfully.";
                IsStatusSuccess = true;
                IsStatusVisible = true;
                // Refresh user list
                var users = await _discordUserService.LoadUsersFromFileAsync(true);
                Users.Clear();
                foreach (var user in users)
                {
                    Users.Add(new UserAccess { Username = user.Username, IsAdmin = user.IsAdmin, IsActive = user.IsActive });
                }

                // Push to GitHub
                await PushUserFileToGitHubAsync($"Add user '{username}' via admin panel");
                await _discordService.SendMessageAsync($"User '{username}' was added via the admin panel.");
            }
            else
            {
                StatusMessage = $"Failed to add user '{username}'. User may already exist.";
                IsStatusSuccess = false;
                IsStatusVisible = true;
            }
        }

        private async Task PushUserFileToGitHubAsync(string commitMessage)
        {
            try
            {
                var githubService = new GitHubService(_configService.GitHubToken, _configService.GitHubOwner, _configService.GitHubRepo);
                var userFilePath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CircleUtility", "discord_users.json");
                var json = System.IO.File.ReadAllText(userFilePath);
                var pushSuccess = await githubService.UpdateFileAsync("discord_users.json", json, commitMessage);
                if (pushSuccess)
                {
                    StatusMessage += "\nUser file pushed to GitHub.";
                }
                else
                {
                    StatusMessage += "\nFailed to push user file to GitHub.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage += $"\nGitHub push error: {ex.Message}";
            }
        }

        private async void EditUser()
        {
            if (SelectedUser == null) return;
            var username = SelectedUser.Username;
            var users = await _discordUserService.LoadUsersFromFileAsync(true);
            var user = users.FirstOrDefault(u => u.Username == username);
            if (user == null)
            {
                StatusMessage = $"User '{username}' not found.";
                IsStatusSuccess = false;
                IsStatusVisible = true;
                return;
            }
            var newDisplayName = Microsoft.VisualBasic.Interaction.InputBox($"Edit display name for {username}:", "Edit User", user.DisplayName);
            if (!string.IsNullOrWhiteSpace(newDisplayName)) user.DisplayName = newDisplayName;
            var newRole = Microsoft.VisualBasic.Interaction.InputBox($"Edit role for {username} (Admin/User):", "Edit User", user.Role);
            if (!string.IsNullOrWhiteSpace(newRole)) user.Role = newRole;
            var newPassword = Microsoft.VisualBasic.Interaction.InputBox($"Edit password for {username} (leave blank to keep current):", "Edit User", "");
            if (!string.IsNullOrWhiteSpace(newPassword)) user.Password = newPassword;
            await _discordUserService.SaveUsersToFileAsync(users);
            StatusMessage = $"User '{username}' updated.";
            IsStatusSuccess = true;
            IsStatusVisible = true;
            // Refresh user list
            var updatedUsers = await _discordUserService.LoadUsersFromFileAsync(true);
            Users.Clear();
            foreach (var u in updatedUsers)
            {
                Users.Add(new UserAccess { Username = u.Username, IsAdmin = u.IsAdmin, IsActive = u.IsActive });
            }
            await PushUserFileToGitHubAsync($"Edit user '{username}' via admin panel");
            await _discordService.SendMessageAsync($"User '{username}' was edited via the admin panel.");
        }

        private async void DeleteUser()
        {
            if (SelectedUser == null) return;
            var username = SelectedUser.Username;
            var users = await _discordUserService.LoadUsersFromFileAsync(true);
            var user = users.FirstOrDefault(u => u.Username == username);
            if (user == null)
            {
                StatusMessage = $"User '{username}' not found.";
                IsStatusSuccess = false;
                IsStatusVisible = true;
                return;
            }
            var confirm = MessageBox.Show($"Are you sure you want to delete user '{username}'?", "Delete User", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (confirm != MessageBoxResult.Yes) return;
            users.Remove(user);
            await _discordUserService.SaveUsersToFileAsync(users);
            StatusMessage = $"User '{username}' deleted.";
            IsStatusSuccess = true;
            IsStatusVisible = true;
            // Refresh user list
            var updatedUsers = await _discordUserService.LoadUsersFromFileAsync(true);
            Users.Clear();
            foreach (var u in updatedUsers)
            {
                Users.Add(new UserAccess { Username = u.Username, IsAdmin = u.IsAdmin, IsActive = u.IsActive });
            }
            await PushUserFileToGitHubAsync($"Delete user '{username}' via admin panel");
            await _discordService.SendMessageAsync($"User '{username}' was deleted via the admin panel.");
        }

        private void LockUser()
        {
            MessageBox.Show("Lock user functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "Lock user functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }

        private void UnlockUser()
        {
            MessageBox.Show("Unlock user functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "Unlock user functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }

        private void LockHardware()
        {
            MessageBox.Show("Lock hardware functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "Lock hardware functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }

        private void ViewUsersByIp()
        {
            MessageBox.Show("View users by IP functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "View users by IP functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }

        private async void SearchUsers()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Searching users...";
                IsStatusVisible = true;
                var users = await _discordUserService.LoadUsersFromFileAsync(true);
                var filtered = users.AsEnumerable();
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    var search = SearchText.ToLower();
                    filtered = filtered.Where(u =>
                        u.Username.ToLower().Contains(search) ||
                        (!string.IsNullOrWhiteSpace(u.DisplayName) && u.DisplayName.ToLower().Contains(search)) ||
                        (!string.IsNullOrWhiteSpace(u.Email) && u.Email.ToLower().Contains(search))
                    );
                }
                if (SearchByHardwareId)
                {
                    filtered = filtered.Where(u => !string.IsNullOrWhiteSpace(u.HardwareId) && u.HardwareId.ToLower().Contains(SearchText.ToLower()));
                }
                if (SearchByIp)
                {
                    filtered = filtered.Where(u => !string.IsNullOrWhiteSpace(u.LastIpAddress) && u.LastIpAddress.ToLower().Contains(SearchText.ToLower()));
                }
                if (!IncludeDisabled)
                {
                    filtered = filtered.Where(u => u.IsActive);
                }
                if (!IncludeAdmins)
                {
                    filtered = filtered.Where(u => !u.IsAdmin);
                }
                SearchResults = new ObservableCollection<DiscordUser>(filtered);
                StatusMessage = $"Found {SearchResults.Count} user(s).";
                IsStatusSuccess = true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"Search error: {ex.Message}";
                IsStatusSuccess = false;
            }
            finally
            {
                IsLoading = false;
                IsStatusVisible = true;
            }
        }

        private void ClearSearch()
        {
            SearchText = string.Empty;
            SearchByHardwareId = false;
            SearchByIp = false;
            IncludeDisabled = false;
            IncludeAdmins = false;
            SearchResults.Clear();
            StatusMessage = "Search cleared.";
            IsStatusSuccess = true;
            IsStatusVisible = true;
        }

        private void PromoteToAdmin()
        {
            MessageBox.Show("Promote to admin functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "Promote to admin functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }

        private void AddHardwareId() { MessageBox.Show("Add hardware ID functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void AddIpAddress() { MessageBox.Show("Add IP address functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ViewPunishmentHistory() { MessageBox.Show("View punishment history functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ApplyPunishment() { MessageBox.Show("Apply punishment functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void RevokePunishment(string punishmentId) { MessageBox.Show("Revoke punishment functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ViewUsersByHardwareId() { MessageBox.Show("View users by hardware ID functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ViewUsersByHardware() { MessageBox.Show("View users by hardware functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ClearLogs()
        {
            MessageBox.Show("Clear logs functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "Clear logs functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }
        private void ExportLogs()
        {
            MessageBox.Show("Export logs functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "Export logs functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }
        private void PerformSearch() { MessageBox.Show("Search functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void SwitchTab(string tab)
        {
            MessageBox.Show($"Switch tab to {tab} functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = $"Switch tab to {tab} functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }
        private void SaveConfig() { MessageBox.Show("Save config functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ResetConfig() { MessageBox.Show("Reset config functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void CheckForUpdates()
        {
            MessageBox.Show("Check for updates functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = "Check for updates functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }
        private void UploadUpdate() { MessageBox.Show("Upload update functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ManageVersions() { MessageBox.Show("Manage versions functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void CreateRelease() { MessageBox.Show("Create release functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void ViewReleaseNotes() { MessageBox.Show("View release notes functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void LoadInitialData() { MessageBox.Show("Load initial data functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information); }
        private void LoadTabData(string tab)
        {
            MessageBox.Show($"Load tab data for {tab} functionality is not yet implemented.", "Not Implemented", MessageBoxButton.OK, MessageBoxImage.Information);
            StatusMessage = $"Load tab data for {tab} functionality not implemented.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
        }
        private void SetupRefreshTimer()
        {
            // Create and configure the refresh timer
            _refreshTimer = new DispatcherTimer();
            _refreshTimer.Tick += (sender, e) => RefreshData();

            // Set initial interval based on RefreshInterval property
            UpdateRefreshTimer();
        }
        private void UpdateRefreshTimer()
        {
            if (_refreshTimer != null)
            {
                // Stop the timer if it's running
                if (_refreshTimer.IsEnabled)
                {
                    _refreshTimer.Stop();
                }

                // If refresh interval is greater than 0, start the timer
                if (_refreshInterval > 0)
                {
                    _refreshTimer.Interval = TimeSpan.FromSeconds(_refreshInterval);
                    _refreshTimer.Start();
                }
            }
        }
        private void ShowStatus(string message, bool isSuccess) { }

        /// <summary>
        /// Gets or sets the collection of user activities
        /// </summary>
        public ObservableCollection<UserActivity> UserActivities
        {
            get => _userActivities;
            set => SetProperty(ref _userActivities, value);
        }

        /// <summary>
        /// Gets or sets the collection of log entries
        /// </summary>
        public ObservableCollection<LogEntryModel> LogEntries
        {
            get => _logEntries;
            set => SetProperty(ref _logEntries, value);
        }

        /// <summary>
        /// Gets or sets the collection of user profiles
        /// </summary>
        public ObservableCollection<UserProfile> UserProfiles
        {
            get => _userProfiles;
            set => SetProperty(ref _userProfiles, value);
        }

        /// <summary>
        /// Gets or sets the collection of system metrics
        /// </summary>
        public ObservableCollection<SystemMetric> SystemMetrics
        {
            get => _systemMetrics;
            set => SetProperty(ref _systemMetrics, value);
        }

        /// <summary>
        /// Gets or sets the collection of hardware profiles
        /// </summary>
        public ObservableCollection<HardwareProfile> HardwareProfiles
        {
            get => _hardwareProfiles;
            set => SetProperty(ref _hardwareProfiles, value);
        }

        /// <summary>
        /// Gets or sets the selected tab
        /// </summary>
        public string SelectedTab
        {
            get => _selectedTab;
            set
            {
                if (SetProperty(ref _selectedTab, value))
                {
                    // Load data for the selected tab
                    LoadTabData(value);
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected user
        /// </summary>
        public UserProfile SelectedUser
        {
            get => _selectedUser;
            set
            {
                if (SetProperty(ref _selectedUser, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected security user
        /// </summary>
        public DiscordUser SelectedSecurityUser
        {
            get => _selectedSecurityUser;
            set
            {
                if (SetProperty(ref _selectedSecurityUser, value))
                {
                    CommandManager.InvalidateRequerySuggested();

                    // Load punishment history if available
                    if (value != null && value.PunishmentHistory != null)
                    {
                        PunishmentHistory = new ObservableCollection<UserPunishment>(value.PunishmentHistory);
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets the search text
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to search by hardware ID
        /// </summary>
        public bool SearchByHardwareId
        {
            get => _searchByHardwareId;
            set => SetProperty(ref _searchByHardwareId, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to search by IP address
        /// </summary>
        public bool SearchByIp
        {
            get => _searchByIp;
            set => SetProperty(ref _searchByIp, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to include disabled users in search results
        /// </summary>
        public bool IncludeDisabled
        {
            get => _includeDisabled;
            set => SetProperty(ref _includeDisabled, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to include admin users in search results
        /// </summary>
        public bool IncludeAdmins
        {
            get => _includeAdmins;
            set => SetProperty(ref _includeAdmins, value);
        }

        /// <summary>
        /// Gets or sets the punishment reason
        /// </summary>
        public string PunishmentReason
        {
            get => _punishmentReason;
            set
            {
                if (SetProperty(ref _punishmentReason, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected punishment type
        /// </summary>
        public PunishmentType SelectedPunishmentType
        {
            get => _selectedPunishmentType;
            set => SetProperty(ref _selectedPunishmentType, value);
        }

        /// <summary>
        /// Gets or sets the punishment expiration date
        /// </summary>
        public DateTime? PunishmentExpirationDate
        {
            get => _punishmentExpirationDate;
            set => SetProperty(ref _punishmentExpirationDate, value);
        }

        /// <summary>
        /// Gets or sets the punishment history
        /// </summary>
        public ObservableCollection<UserPunishment> PunishmentHistory
        {
            get => _punishmentHistory ?? (_punishmentHistory = new ObservableCollection<UserPunishment>());
            set => SetProperty(ref _punishmentHistory, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether data is loading
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is a success
        /// </summary>
        public bool IsStatusSuccess
        {
            get => _isStatusSuccess;
            set => SetProperty(ref _isStatusSuccess, value);
        }

        /// <summary>
        /// Gets or sets the refresh interval in seconds
        /// </summary>
        public int RefreshInterval
        {
            get => _refreshInterval;
            set
            {
                if (SetProperty(ref _refreshInterval, value))
                {
                    UpdateRefreshTimer();
                }
            }
        }

        /// <summary>
        /// Gets or sets the refresh interval index for the ComboBox
        /// </summary>
        public int RefreshIntervalIndex
        {
            get => _refreshIntervalIndex;
            set
            {
                if (SetProperty(ref _refreshIntervalIndex, value))
                {
                    // Convert index to actual refresh interval in seconds
                    switch (value)
                    {
                        case 0: // Off
                            RefreshInterval = 0;
                            break;
                        case 1: // 5 seconds
                            RefreshInterval = 5;
                            break;
                        case 2: // 10 seconds
                            RefreshInterval = 10;
                            break;
                        case 3: // 30 seconds
                            RefreshInterval = 30;
                            break;
                        case 4: // 1 minute
                            RefreshInterval = 60;
                            break;
                        default:
                            RefreshInterval = 30; // Default to 30 seconds
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether to auto-start with Windows
        /// </summary>
        public bool AutoStartWithWindows
        {
            get => _autoStartWithWindows;
            set => SetProperty(ref _autoStartWithWindows, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to automatically update the application
        /// </summary>
        public bool AutoUpdate
        {
            get => _autoUpdate;
            set => SetProperty(ref _autoUpdate, value);
        }

        /// <summary>
        /// Gets or sets the update check interval
        /// </summary>
        public TimeSpan UpdateCheckInterval
        {
            get => _updateCheckInterval;
            set
            {
                if (SetProperty(ref _updateCheckInterval, value))
                {
                    _updateService.UpdateCheckInterval = value;
                }
            }
        }

        /// <summary>
        /// Gets or sets the update server URL
        /// </summary>
        public string UpdateServerUrl
        {
            get => _updateServerUrl;
            set => SetProperty(ref _updateServerUrl, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether to require login
        /// </summary>
        public bool RequireLogin
        {
            get => _requireLogin;
            set => SetProperty(ref _requireLogin, value);
        }

        /// <summary>
        /// Gets or sets the session timeout in minutes
        /// </summary>
        public int SessionTimeout
        {
            get => _sessionTimeout;
            set => SetProperty(ref _sessionTimeout, value);
        }

        /// <summary>
        /// Gets the refresh data command
        /// </summary>
        public ICommand RefreshDataCommand { get; }

        /// <summary>
        /// Gets the add user command
        /// </summary>
        public ICommand AddUserCommand { get; }

        /// <summary>
        /// Gets the edit user command
        /// </summary>
        public ICommand EditUserCommand { get; }

        /// <summary>
        /// Gets the delete user command
        /// </summary>
        public ICommand DeleteUserCommand { get; }

        /// <summary>
        /// Gets the lock user command
        /// </summary>
        public ICommand LockUserCommand { get; }

        /// <summary>
        /// Gets the unlock user command
        /// </summary>
        public ICommand UnlockUserCommand { get; }

        /// <summary>
        /// Gets the lock hardware command
        /// </summary>
        public ICommand LockHardwareCommand { get; }

        /// <summary>
        /// Gets the view users by IP command
        /// </summary>
        public ICommand ViewUsersByIpCommand { get; }

        /// <summary>
        /// Gets the search users command
        /// </summary>
        public ICommand SearchUsersCommand { get; }

        /// <summary>
        /// Gets the clear search command
        /// </summary>
        public ICommand ClearSearchCommand { get; }

        /// <summary>
        /// Gets the promote to admin command
        /// </summary>
        public ICommand PromoteToAdminCommand { get; }

        /// <summary>
        /// Gets the add hardware ID command
        /// </summary>
        public ICommand AddHardwareIdCommand { get; }

        /// <summary>
        /// Gets the add IP address command
        /// </summary>
        public ICommand AddIpAddressCommand { get; }

        /// <summary>
        /// Gets the view punishment history command
        /// </summary>
        public ICommand ViewPunishmentHistoryCommand { get; }

        /// <summary>
        /// Gets the apply punishment command
        /// </summary>
        public ICommand ApplyPunishmentCommand { get; }

        /// <summary>
        /// Gets the revoke punishment command
        /// </summary>
        public ICommand RevokePunishmentCommand { get; }

        /// <summary>
        /// Gets the view users by hardware ID command
        /// </summary>
        public ICommand ViewUsersByHardwareIdCommand { get; }

        /// <summary>
        /// Gets the view users by hardware command
        /// </summary>
        public ICommand ViewUsersByHardwareCommand { get; }

        /// <summary>
        /// Gets the clear logs command
        /// </summary>
        public ICommand ClearLogsCommand { get; }

        /// <summary>
        /// Gets the export logs command
        /// </summary>
        public ICommand ExportLogsCommand { get; }

        /// <summary>
        /// Gets the search command
        /// </summary>
        public ICommand SearchCommand { get; }

        /// <summary>
        /// Gets the switch tab command
        /// </summary>
        public ICommand SwitchTabCommand { get; }

        /// <summary>
        /// Gets the save config command
        /// </summary>
        public ICommand SaveConfigCommand { get; }

        /// <summary>
        /// Gets the reset config command
        /// </summary>
        public ICommand ResetConfigCommand { get; }

        /// <summary>
        /// Gets the check for updates command
        /// </summary>
        public ICommand CheckForUpdatesCommand { get; }

        /// <summary>
        /// Gets the upload update command
        /// </summary>
        public ICommand UploadUpdateCommand { get; }

        /// <summary>
        /// Gets the manage versions command
        /// </summary>
        public ICommand ManageVersionsCommand { get; }

        /// <summary>
        /// Gets the create release command
        /// </summary>
        public ICommand CreateReleaseCommand { get; }

        /// <summary>
        /// Gets the view release notes command
        /// </summary>
        public ICommand ViewReleaseNotesCommand { get; }

        public ObservableCollection<UserAccess> Users
        {
            get => _users;
            set => SetProperty(ref _users, value);
        }

        public UserAccess SelectedUserAccess
        {
            get => _selectedUserAccess;
            set => SetProperty(ref _selectedUserAccess, value);
        }

        public bool IsUpdateAvailable
        {
            get => _isUpdateAvailable;
            set => SetProperty(ref _isUpdateAvailable, value);
        }

        public string UpdateVersion
        {
            get => _updateVersion;
            set => SetProperty(ref _updateVersion, value);
        }

        public string UpdateNotes
        {
            get => _updateNotes;
            set => SetProperty(ref _updateNotes, value);
        }

        public bool IsCheckingUpdate
        {
            get => _isCheckingUpdate;
            set => SetProperty(ref _isCheckingUpdate, value);
        }

        public ObservableCollection<UpdateHistory> UpdateHistory
        {
            get => _updateHistory;
            set => SetProperty(ref _updateHistory, value);
        }

        public DateTime LastUpdateCheck
        {
            get => _lastUpdateCheck;
            set => SetProperty(ref _lastUpdateCheck, value);
        }

        public ObservableCollection<DiscordUser> SearchResults
        {
            get => _searchResults;
            set => SetProperty(ref _searchResults, value);
        }

        public void OnConfigUpdated(CentralConfig config)
        {
            _currentConfig = config;
            // DispatcherExtensions.Invoke is not implemented; use Dispatcher.Invoke directly
            Application.Current.Dispatcher.Invoke(() =>
            {
                Users.Clear();
                foreach (var user in config.Users)
                {
                    Users.Add(user);
                }
            });
        }

        private void InstallUpdate()
        {
            try
            {
                if (!IsUpdateAvailable) return;

                var result = MessageBox.Show(
                    $"Do you want to install version {UpdateVersion} now?\n\n{UpdateNotes}",
                    "Install Update",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _updateService.DownloadAndInstallUpdateAsync(UpdateVersion).Wait();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error installing update: {ex.Message}";
            }
        }

        private void RollbackToVersion(string version)
        {
            try
            {
                var result = MessageBox.Show(
                    $"Are you sure you want to roll back to version {version}? This will restore the previous version of the application.",
                    "Confirm Rollback",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    StatusMessage = $"Rolling back to version {version}...";
                    // TODO: Implement RollbackToVersionAsync in UpdateService
                    // _updateService.RollbackToVersionAsync(version).Wait();
                    StatusMessage = $"Successfully rolled back to version {version}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error rolling back update: {ex.Message}";
            }
        }

        private void LoadUpdateHistory()
        {
            UpdateHistory = new ObservableCollection<UpdateHistory>(_updateService.UpdateHistory);
        }

        public ICommand EmergencyCommand { get; }

        private async void SendEmergencyAlert()
        {
            await _discordService.SendMessageAsync("🚨 EMERGENCY ALERT triggered from the Admin Panel! 🚨");
            StatusMessage = "Emergency alert sent to Discord.";
            IsStatusSuccess = true;
            IsStatusVisible = true;
        }

        public ICommand ResetHardwareAndIpCommand { get; }

        private void ResetHardwareAndIp()
        {
            if (SelectedSecurityUser == null) return;
            var username = SelectedSecurityUser.Username;
            _userTracking.ResetUserHardwareAndIp(username);
            StatusMessage = $"Hardware and IP reset for user '{username}'.";
            IsStatusSuccess = true;
            IsStatusVisible = true;
        }

        private void LoadPendingRegistrations()
        {
            PendingRegistrations.Clear();
            var pending = _userTracking.GetPendingRegistrations();
            foreach (var reg in pending)
                PendingRegistrations.Add(reg);
        }

        private void ApprovePendingRegistration(string username)
        {
            _userTracking.ApprovePendingRegistration(username);
            LoadPendingRegistrations();
            StatusMessage = $"User '{username}' approved. They may now log in for the first time.";
            IsStatusSuccess = true;
            IsStatusVisible = true;
            // TODO: Push to GitHub here if needed
        }

        private async void DenyPendingRegistration(string username)
        {
            _userTracking.DenyPendingRegistration(username);
            LoadPendingRegistrations();
            StatusMessage = $"User '{username}' denied.";
            IsStatusSuccess = false;
            IsStatusVisible = true;
            var reg = PendingRegistrations.FirstOrDefault(r => r.Username == username);
            string discordUsername = reg?.DiscordUsername ?? "unknown";
            string discordId = reg?.DiscordId ?? "unknown";
            await _discordService.SendMessageAsync($"User '{username}' (Discord: {discordUsername}, ID: {discordId}) was denied access to the utility.");
        }
    }
}






