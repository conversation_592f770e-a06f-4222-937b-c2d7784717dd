// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using CircleUtility.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace CircleUtility.Tests
{
    /// <summary>
    /// Tests for the SecureConfigStorage class
    /// </summary>
    [TestClass]
    public class SecureConfigStorageTests
    {
        private SecureConfigStorage _secureConfigStorage = null!;
        private string _testDirectory = null!;

        /// <summary>
        /// Initializes the test class
        /// </summary>
        [TestInitialize]
        public void Initialize()
        {
            // Create test directory
            _testDirectory = Path.Combine(Path.GetTempPath(), "CircleUtilityTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            // Initialize secure config storage with test directory
            string secureConfigDirectory = Path.Combine(_testDirectory, "SecureConfig");
            string secureConfigFilePath = Path.Combine(secureConfigDirectory, "secure_config.dat");
            string encryptionKeyPath = Path.Combine(secureConfigDirectory, "secure_key.dat");
            _secureConfigStorage = new SecureConfigStorage(secureConfigDirectory, secureConfigFilePath, encryptionKeyPath);
        }

        /// <summary>
        /// Cleans up after the test class
        /// </summary>
        [TestCleanup]
        public void Cleanup()
        {
            // Clean up test directory
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }

            // Reset the singleton instance
            System.Reflection.FieldInfo? instanceField = typeof(SecureConfigStorage).GetField("_instance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            if (instanceField != null)
            {
                instanceField.SetValue(null, null);
            }

            // We're done with the secure config storage
            // No need to set to null as the test class will be recreated for each test
        }

        /// <summary>
        /// Tests that the secure config storage can be initialized
        /// </summary>
        [TestMethod]
        public void SecureConfigStorage_Initialize_Success()
        {
            // Assert
            Assert.IsNotNull(_secureConfigStorage);
            Assert.IsTrue(_secureConfigStorage.IsInitialized);
        }

        /// <summary>
        /// Tests that a secure configuration can be saved and loaded
        /// </summary>
        [TestMethod]
        public void SecureConfigStorage_SaveAndLoadSecureConfig_Success()
        {
            // Arrange
            TestConfig testConfig = new TestConfig
            {
                Name = "Test Config",
                Value = 42,
                IsEnabled = true,
                CreatedDate = DateTime.Now,
                Items = new List<string> { "Item1", "Item2", "Item3" }
            };

            // Act
            bool saveResult = _secureConfigStorage.SaveSecureConfig(testConfig);
            TestConfig loadedConfig = _secureConfigStorage.LoadSecureConfig<TestConfig>();

            // Assert
            Assert.IsTrue(saveResult);
            Assert.IsNotNull(loadedConfig);
            Assert.AreEqual(testConfig.Name, loadedConfig.Name);
            Assert.AreEqual(testConfig.Value, loadedConfig.Value);
            Assert.AreEqual(testConfig.IsEnabled, loadedConfig.IsEnabled);
            Assert.AreEqual(testConfig.CreatedDate.Date, loadedConfig.CreatedDate.Date);
            Assert.AreEqual(testConfig.Items.Count, loadedConfig.Items.Count);
            for (int i = 0; i < testConfig.Items.Count; i++)
            {
                Assert.AreEqual(testConfig.Items[i], loadedConfig.Items[i]);
            }
        }

        /// <summary>
        /// Tests that a secure configuration can be deleted
        /// </summary>
        [TestMethod]
        public void SecureConfigStorage_DeleteSecureConfig_Success()
        {
            // Arrange
            TestConfig testConfig = new TestConfig
            {
                Name = "Test Config",
                Value = 42,
                IsEnabled = true,
                CreatedDate = DateTime.Now,
                Items = new List<string> { "Item1", "Item2", "Item3" }
            };

            // Save config
            bool saveResult = _secureConfigStorage.SaveSecureConfig(testConfig);
            Assert.IsTrue(saveResult);

            // Act
            bool deleteResult = _secureConfigStorage.DeleteSecureConfig();
            TestConfig loadedConfig = _secureConfigStorage.LoadSecureConfig<TestConfig>();

            // Assert
            Assert.IsTrue(deleteResult);
            Assert.IsNotNull(loadedConfig);
            Assert.AreNotEqual(testConfig.Name, loadedConfig.Name);
            Assert.AreNotEqual(testConfig.Value, loadedConfig.Value);
        }

        /// <summary>
        /// Tests that a credential can be stored and retrieved
        /// </summary>
        [TestMethod]
        public void SecureConfigStorage_StoreAndRetrieveCredential_Success()
        {
            // Arrange
            string credentialName = "TestCredential";
            string username = "testuser";
            string password = "testpassword";

            // Act
            bool storeResult = _secureConfigStorage.StoreCredential(credentialName, username, password);
            bool retrieveResult = _secureConfigStorage.RetrieveCredential(credentialName, out string retrievedUsername, out string retrievedPassword);

            // Assert
            Assert.IsTrue(storeResult);
            Assert.IsTrue(retrieveResult);
            Assert.AreEqual(username, retrievedUsername);
            Assert.AreEqual(password, retrievedPassword);
        }

        /// <summary>
        /// Tests that a credential can be deleted
        /// </summary>
        [TestMethod]
        public void SecureConfigStorage_DeleteCredential_Success()
        {
            // Arrange
            string credentialName = "TestCredential";
            string username = "testuser";
            string password = "testpassword";

            // Store credential
            bool storeResult = _secureConfigStorage.StoreCredential(credentialName, username, password);
            Assert.IsTrue(storeResult);

            // Act
            bool deleteResult = _secureConfigStorage.DeleteCredential(credentialName);
            bool retrieveResult = _secureConfigStorage.RetrieveCredential(credentialName, out string retrievedUsername, out string retrievedPassword);

            // Assert
            Assert.IsTrue(deleteResult);
            Assert.IsFalse(retrieveResult);
            Assert.IsNull(retrievedUsername);
            Assert.IsNull(retrievedPassword);
        }

        /// <summary>
        /// Tests that multiple credentials can be stored and retrieved
        /// </summary>
        [TestMethod]
        public void SecureConfigStorage_StoreAndRetrieveMultipleCredentials_Success()
        {
            // Arrange
            string credentialName1 = "TestCredential1";
            string username1 = "testuser1";
            string password1 = "testpassword1";

            string credentialName2 = "TestCredential2";
            string username2 = "testuser2";
            string password2 = "testpassword2";

            // Act
            bool storeResult1 = _secureConfigStorage.StoreCredential(credentialName1, username1, password1);
            bool storeResult2 = _secureConfigStorage.StoreCredential(credentialName2, username2, password2);

            bool retrieveResult1 = _secureConfigStorage.RetrieveCredential(credentialName1, out string retrievedUsername1, out string retrievedPassword1);
            bool retrieveResult2 = _secureConfigStorage.RetrieveCredential(credentialName2, out string retrievedUsername2, out string retrievedPassword2);

            // Assert
            Assert.IsTrue(storeResult1);
            Assert.IsTrue(storeResult2);
            Assert.IsTrue(retrieveResult1);
            Assert.IsTrue(retrieveResult2);
            Assert.AreEqual(username1, retrievedUsername1);
            Assert.AreEqual(password1, retrievedPassword1);
            Assert.AreEqual(username2, retrievedUsername2);
            Assert.AreEqual(password2, retrievedPassword2);
        }

        /// <summary>
        /// Tests that a credential can be updated
        /// </summary>
        [TestMethod]
        public void SecureConfigStorage_UpdateCredential_Success()
        {
            // Arrange
            string credentialName = "TestCredential";
            string username = "testuser";
            string password = "testpassword";
            string newUsername = "newuser";
            string newPassword = "newpassword";

            // Store credential
            bool storeResult = _secureConfigStorage.StoreCredential(credentialName, username, password);
            Assert.IsTrue(storeResult);

            // Act
            bool updateResult = _secureConfigStorage.StoreCredential(credentialName, newUsername, newPassword);
            bool retrieveResult = _secureConfigStorage.RetrieveCredential(credentialName, out string retrievedUsername, out string retrievedPassword);

            // Assert
            Assert.IsTrue(updateResult);
            Assert.IsTrue(retrieveResult);
            Assert.AreEqual(newUsername, retrievedUsername);
            Assert.AreEqual(newPassword, retrievedPassword);
        }
    }

    /// <summary>
    /// Test configuration class
    /// </summary>
    public class TestConfig
    {
        /// <summary>
        /// Gets or sets the name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the value
        /// </summary>
        public int Value { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the config is enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the created date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the items
        /// </summary>
        public List<string> Items { get; set; }

        /// <summary>
        /// Initializes a new instance of the TestConfig class
        /// </summary>
        public TestConfig()
        {
            Name = string.Empty;
            Value = 0;
            IsEnabled = false;
            CreatedDate = DateTime.MinValue;
            Items = new List<string>();
        }
    }
}
