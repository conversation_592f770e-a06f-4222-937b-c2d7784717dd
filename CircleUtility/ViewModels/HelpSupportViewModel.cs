using System;
using System.Windows.Input;
using CircleUtility.Services;
using Microsoft.Extensions.Configuration;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    public class HelpSupportViewModel : ViewModelBase
    {
        private string _feedbackText;
        private bool _isAnonymous;
        private string _statusMessage;
        private bool _isStatusVisible;
        private readonly IDiscordService _discordService;
        private readonly string _adminRoleId;
        private readonly string _username;

        public HelpSupportViewModel(ILoggerService logger, IDiscordService discordService, string username = null) : base(logger)
        {
            _discordService = discordService;
            _username = username ?? "User";
            var configBuilder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            var configuration = configBuilder.Build();
            _adminRoleId = configuration["Discord:AdminRoleId"];
            SendFeedbackCommand = new RelayCommand(SendFeedback, CanSendFeedback);
        }

        public string FeedbackText
        {
            get => _feedbackText;
            set { SetProperty(ref _feedbackText, value); ((RelayCommand)SendFeedbackCommand).RaiseCanExecuteChanged(); }
        }

        public bool IsAnonymous
        {
            get => _isAnonymous;
            set => SetProperty(ref _isAnonymous, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        public ICommand SendFeedbackCommand { get; }

        private bool CanSendFeedback()
        {
            return !string.IsNullOrWhiteSpace(FeedbackText);
        }

        private async void SendFeedback()
        {
            IsStatusVisible = false;
            try
            {
                var title = IsAnonymous ? "Anonymous Feedback" : $"Feedback from {_username}";
                var description = FeedbackText;
                int color = 15844367; // Gold
                var fields = new[]
                {
                    new { name = "Type", value = IsAnonymous ? "Anonymous" : "User", inline = true },
                    new { name = "Time", value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), inline = true }
                };
                string roleIdToMention = IsAnonymous ? null : _adminRoleId;
                var result = await _discordService.SendEmbedNotificationAsync(title, description, color, fields, roleIdToMention);
                if (result)
                {
                    StatusMessage = "Feedback sent successfully!";
                }
                else
                {
                    StatusMessage = "Failed to send feedback. Please try again later.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
            }
            IsStatusVisible = true;
            FeedbackText = string.Empty;
        }
    }
} 