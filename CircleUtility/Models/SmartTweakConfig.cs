using System.Collections.Generic;
// Assuming Enums like H<PERSON><PERSON>tatus, WDDMVersion are in CircleUtility.Models
// If they are in a sub-namespace like CircleUtility.Models.Enums, adjust accordingly.

namespace CircleUtility.Models
{
    public class SmartTweakAction
    {
        // For Registry TweakType
        public string Path { get; set; } // Registry Key Path
        public string ValueName { get; set; } // Registry Value Name
        public string ValueData { get; set; } // Registry Value Data (as string, convert as needed based on ValueKind)
        public string ValueKind { get; set; } // e.g., "DWORD", "String", "Binary", "MultiString", "QWORD"

        // For PowerShell TweakType
        public string Script { get; set; }

        // Could add other properties if needed for other TweakTypes
    }

    public class SmartTweak
    {
        public string TweakId { get; set; }
        public string SettingName { get; set; }
        public string Description { get; set; }
        public string Category { get; set; } // e.g., "OS", "CPU", "GPU", "Network", "Power"
        public string Condition { get; set; } // Expression to evaluate against HardwareInfo
        public string TweakType { get; set; } // "Registry", "PowerShell", "Informational"
        public SmartTweakAction ApplyAction { get; set; }
        public SmartTweakAction RevertAction { get; set; }
        public bool RequiresAdmin { get; set; }
        public bool RequiresRestart { get; set; }
        public List<string> AffectedComponents { get; set; } // e.g., ["OS", "GPU"]

        [System.Text.Json.Serialization.JsonIgnore] // This property should not be serialized to/from JSON
        public bool IsCurrentlyApplied { get; set; }

        public SmartTweak()
        {
            AffectedComponents = new List<string>();
            // Initialize actions to prevent null reference if JSON doesn't have them (though they should)
            ApplyAction = new SmartTweakAction();
            RevertAction = new SmartTweakAction();
        }
    }
} 