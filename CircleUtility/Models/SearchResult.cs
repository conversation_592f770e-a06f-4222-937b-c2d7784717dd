using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a search result
    /// </summary>
    public class SearchResult
    {
        /// <summary>
        /// Gets or sets the name of the search result
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Gets or sets the category of the search result
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// Gets or sets the description of the search result
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Gets or sets the path to the search result
        /// </summary>
        public string Path { get; set; }
    }
}
