using System.Threading.Tasks;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for hardware optimization service
    /// </summary>
    public interface IHardwareOptimizationService
    {
        /// <summary>
        /// Gets recommended smart tweaks asynchronously
        /// </summary>
        /// <returns>Task with recommended tweaks</returns>
        Task<object> GetRecommendedSmartTweaksAsync();

        /// <summary>
        /// Applies smart tweak asynchronously
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        /// <returns>Task representing the async operation</returns>
        Task<bool> ApplySmartTweakAsync(object tweak);

        /// <summary>
        /// Reverts smart tweak asynchronously
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        /// <returns>Task representing the async operation</returns>
        Task<bool> RevertSmartTweakAsync(object tweak);

        /// <summary>
        /// Reverts optimization
        /// </summary>
        /// <param name="optimizationName">Name of optimization to revert</param>
        /// <returns>True if successful</returns>
        bool RevertOptimization(string optimizationName);

        /// <summary>
        /// Reverts power profile asynchronously
        /// </summary>
        /// <param name="profileName">Name of power profile to revert</param>
        /// <returns>Task representing the async operation</returns>
        Task<bool> RevertPowerProfileAsync(string profileName);
    }
}
