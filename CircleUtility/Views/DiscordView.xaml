<UserControl x:Class="CircleUtility.Views.DiscordView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:CircleUtility.Converters"
             Background="Black">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Text="Discord Integration" 
                   FontFamily="Consolas" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20"/>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Status message -->
                <Border Grid.Row="0" 
                        Background="{Binding IsStatusSuccess, Converter={StaticResource BoolToColorConverter}}" 
                        BorderThickness="1" 
                        BorderBrush="{Binding IsStatusSuccess, Converter={StaticResource BoolToColorConverter}}" 
                        Padding="15" 
                        Margin="0,0,0,20"
                        Visibility="{Binding IsStatusVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="{Binding StatusMessage}" 
                               FontFamily="Consolas" 
                               FontSize="14" 
                               Foreground="White"/>
                </Border>
                
                <!-- Discord settings -->
                <Border Grid.Row="1" 
                        Background="#FF050A0F" 
                        BorderThickness="1" 
                        BorderBrush="#FF00C8FF" 
                        Padding="15" 
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Header -->
                        <TextBlock Text="Discord Configuration" 
                                   FontFamily="Consolas" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>
                        
                        <!-- Enable Discord -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="Enable Discord:" 
                                       FontFamily="Consolas" 
                                       FontSize="12" 
                                       Foreground="White"
                                       VerticalAlignment="Center"/>
                            
                            <CheckBox Grid.Column="1" 
                                      IsChecked="{Binding EnableDiscordIntegration}"
                                      VerticalAlignment="Center"/>
                        </Grid>
                        
                        <!-- Webhook URL -->
                        <Grid Grid.Row="2" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="Webhook URL:" 
                                       FontFamily="Consolas" 
                                       FontSize="12" 
                                       Foreground="White"
                                       VerticalAlignment="Center"/>
                            
                            <TextBox Grid.Column="1" 
                                     Text="{Binding DiscordWebhookUrl}" 
                                     Background="#FF0A141E"
                                     Foreground="White"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Height="30"
                                     Padding="5,0"
                                     VerticalContentAlignment="Center"
                                     IsEnabled="{Binding EnableDiscordIntegration}"/>
                        </Grid>
                        
                        <!-- Discord User ID -->
                        <Grid Grid.Row="3" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="User ID:" 
                                       FontFamily="Consolas" 
                                       FontSize="12" 
                                       Foreground="White"
                                       VerticalAlignment="Center"/>
                            
                            <TextBox Grid.Column="1" 
                                     Text="{Binding DiscordUserId}" 
                                     Background="#FF0A141E"
                                     Foreground="White"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Height="30"
                                     Padding="5,0"
                                     VerticalContentAlignment="Center"
                                     IsEnabled="{Binding EnableDiscordIntegration}"/>
                        </Grid>
                        
                        <!-- Enable Discord Commands -->
                        <Grid Grid.Row="4">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Text="Enable Commands:" 
                                       FontFamily="Consolas" 
                                       FontSize="12" 
                                       Foreground="White"
                                       VerticalAlignment="Center"/>
                            
                            <CheckBox Grid.Column="1" 
                                      IsChecked="{Binding EnableDiscordCommands}"
                                      VerticalAlignment="Center"
                                      IsEnabled="{Binding EnableDiscordIntegration}"/>
                        </Grid>
                    </Grid>
                </Border>
                
                <!-- Discord commands -->
                <Border Grid.Row="2" 
                        Background="#FF050A0F" 
                        BorderThickness="1" 
                        BorderBrush="#FF00C8FF" 
                        Padding="15" 
                        Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Header -->
                        <TextBlock Text="Discord Commands" 
                                   FontFamily="Consolas" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>
                        
                        <!-- Commands info -->
                        <Border Grid.Row="1" 
                                Background="#FF0A141E" 
                                BorderThickness="1" 
                                BorderBrush="#FF004080" 
                                Padding="10"
                                Margin="0,0,0,15">
                            <TextBlock Text="Available Commands:&#x0a;?alive - Check if utility is running&#x0a;?emergency - Send emergency notification&#x0a;?about - Show information about the utility"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="#FF808080"
                                       TextWrapping="Wrap"/>
                        </Border>
                        
                        <!-- Test buttons -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal">
                            <Button Content="Test ?alive" 
                                    Command="{Binding TestAliveCommand}"
                                    Width="120" 
                                    Height="30" 
                                    Margin="0,0,10,0"
                                    Background="#FF001428" 
                                    Foreground="White" 
                                    BorderBrush="#FF00C8FF" 
                                    BorderThickness="1"/>
                            
                            <Button Content="Test ?emergency" 
                                    Command="{Binding TestEmergencyCommand}"
                                    Width="120" 
                                    Height="30" 
                                    Margin="0,0,10,0"
                                    Background="#FF001428" 
                                    Foreground="White" 
                                    BorderBrush="#FF00C8FF" 
                                    BorderThickness="1"/>
                            
                            <Button Content="Test ?about" 
                                    Command="{Binding TestAboutCommand}"
                                    Width="120" 
                                    Height="30" 
                                    Background="#FF001428" 
                                    Foreground="White" 
                                    BorderBrush="#FF00C8FF" 
                                    BorderThickness="1"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- Send message -->
                <Border Grid.Row="3" 
                        Background="#FF050A0F" 
                        BorderThickness="1" 
                        BorderBrush="#FF00C8FF" 
                        Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Header -->
                        <TextBlock Text="Send Message" 
                                   FontFamily="Consolas" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,15"/>
                        
                        <!-- Message input -->
                        <TextBox Grid.Row="1" 
                                 Text="{Binding TestMessage}" 
                                 Background="#FF0A141E"
                                 Foreground="White"
                                 BorderBrush="#FF00C8FF"
                                 BorderThickness="1"
                                 Height="80"
                                 Padding="5"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 IsEnabled="{Binding EnableDiscordIntegration}"
                                 Margin="0,0,0,10"/>
                        
                        <!-- Send button -->
                        <Button Grid.Row="2" 
                                Content="Send Message" 
                                Command="{Binding SendMessageCommand}"
                                Width="150" 
                                Height="30" 
                                HorizontalAlignment="Right" 
                                Background="#FF001428" 
                                Foreground="White" 
                                BorderBrush="#FF00C8FF" 
                                BorderThickness="1"/>
                    </Grid>
                </Border>
            </Grid>
        </ScrollViewer>
        
        <!-- Action buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Test Connection" 
                    Command="{Binding TestDiscordCommand}"
                    Width="150" 
                    Height="40" 
                    Margin="0,0,10,0" 
                    Background="#FF001428" 
                    Foreground="White" 
                    BorderBrush="#FF00A0DC" 
                    BorderThickness="1"/>
            
            <Button Content="Save Settings" 
                    Command="{Binding SaveSettingsCommand}"
                    Width="150" 
                    Height="40" 
                    Background="#FF001428" 
                    Foreground="White" 
                    BorderBrush="#FF00C8FF" 
                    BorderThickness="1"/>
        </StackPanel>
    </Grid>
</UserControl>
