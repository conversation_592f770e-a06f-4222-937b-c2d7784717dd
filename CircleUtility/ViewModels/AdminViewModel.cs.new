// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows.Threading;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the admin view
    /// </summary>
    public class AdminViewModel : INotifyPropertyChanged
    {
        private readonly ILogger _logger;
        private readonly AutoUpdateService _updateService;
        private string _selectedTab;
        private string _searchText;
        private bool _isLoading;
        private string _statusMessage;
        private bool _isStatusVisible;
        private bool _isStatusSuccess;
        private ObservableCollection<LogEntry> _logEntries;
        
        /// <summary>
        /// Initializes a new instance of the AdminViewModel class
        /// </summary>
        /// <param name="logger">The logger</param>
        public AdminViewModel(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _updateService = AutoUpdateService.Instance;
            _selectedTab = "Dashboard";
            _searchText = string.Empty;
            _isLoading = false;
            _statusMessage = string.Empty;
            _isStatusVisible = false;
            _isStatusSuccess = true;
            _logEntries = new ObservableCollection<LogEntry>();
            
            // Initialize commands
            ClearLogsCommand = new RelayCommand(ClearLogs);
            ExportLogsCommand = new RelayCommand(ExportLogs);
            SearchCommand = new RelayCommand(PerformSearch);
            SwitchTabCommand = new RelayCommand<string>(SwitchTab);
            SaveConfigCommand = new RelayCommand(SaveConfig);
            ResetConfigCommand = new RelayCommand(ResetConfig);
            CheckForUpdatesCommand = new RelayCommand(CheckForUpdates);
            UploadUpdateCommand = new RelayCommand(UploadUpdate);
            ManageVersionsCommand = new RelayCommand(ManageVersions);
            CreateReleaseCommand = new RelayCommand(CreateRelease);
            ViewReleaseNotesCommand = new RelayCommand(ViewReleaseNotes);
            
            // Load logs
            LoadLogs();
        }
        
        /// <summary>
        /// Gets or sets the selected tab
        /// </summary>
        public string SelectedTab
        {
            get => _selectedTab;
            set
            {
                if (_selectedTab != value)
                {
                    _selectedTab = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// Gets or sets the search text
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether the view is loading
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether the status is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set
            {
                if (_isStatusVisible != value)
                {
                    _isStatusVisible = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether the status is a success
        /// </summary>
        public bool IsStatusSuccess
        {
            get => _isStatusSuccess;
            set
            {
                if (_isStatusSuccess != value)
                {
                    _isStatusSuccess = value;
                    OnPropertyChanged();
                }
            }
        }
        
        /// <summary>
        /// Gets the log entries
        /// </summary>
        public ObservableCollection<LogEntry> LogEntries => _logEntries;
        
        /// <summary>
        /// Gets the clear logs command
        /// </summary>
        public ICommand ClearLogsCommand { get; }
        
        /// <summary>
        /// Gets the export logs command
        /// </summary>
        public ICommand ExportLogsCommand { get; }
        
        /// <summary>
        /// Gets the search command
        /// </summary>
        public ICommand SearchCommand { get; }
        
        /// <summary>
        /// Gets the switch tab command
        /// </summary>
        public ICommand SwitchTabCommand { get; }
        
        /// <summary>
        /// Gets the save config command
        /// </summary>
        public ICommand SaveConfigCommand { get; }
        
        /// <summary>
        /// Gets the reset config command
        /// </summary>
        public ICommand ResetConfigCommand { get; }
        
        /// <summary>
        /// Gets the check for updates command
        /// </summary>
        public ICommand CheckForUpdatesCommand { get; }
        
        /// <summary>
        /// Gets the upload update command
        /// </summary>
        public ICommand UploadUpdateCommand { get; }
        
        /// <summary>
        /// Gets the manage versions command
        /// </summary>
        public ICommand ManageVersionsCommand { get; }
        
        /// <summary>
        /// Gets the create release command
        /// </summary>
        public ICommand CreateReleaseCommand { get; }
        
        /// <summary>
        /// Gets the view release notes command
        /// </summary>
        public ICommand ViewReleaseNotesCommand { get; }
        
        /// <summary>
        /// Loads the logs
        /// </summary>
        private void LoadLogs()
        {
            try
            {
                // In a real implementation, this would load logs from a file or database
                // For now, we'll just add some sample logs
                _logEntries.Add(new LogEntry { Timestamp = DateTime.Now.AddMinutes(-5), Message = "Application started", Level = LogLevel.INFO });
                _logEntries.Add(new LogEntry { Timestamp = DateTime.Now.AddMinutes(-4), Message = "User logged in", Level = LogLevel.INFO });
                _logEntries.Add(new LogEntry { Timestamp = DateTime.Now.AddMinutes(-3), Message = "System tweaks applied", Level = LogLevel.INFO });
                _logEntries.Add(new LogEntry { Timestamp = DateTime.Now.AddMinutes(-2), Message = "Failed to apply GPU optimization", Level = LogLevel.ERROR });
                _logEntries.Add(new LogEntry { Timestamp = DateTime.Now.AddMinutes(-1), Message = "User logged out", Level = LogLevel.INFO });
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading logs: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error loading logs", false);
            }
        }
        
        /// <summary>
        /// Clears the logs
        /// </summary>
        public void ClearLogs()
        {
            try
            {
                LogEntries.Clear();
                ShowStatus("Logs cleared successfully", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error clearing logs: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error clearing logs", false);
            }
        }
        
        /// <summary>
        /// Exports the logs
        /// </summary>
        public void ExportLogs()
        {
            try
            {
                // In a real implementation, this would export the logs to a file
                // For now, we'll just show a status message
                ShowStatus("Logs exported successfully", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error exporting logs: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error exporting logs", false);
            }
        }
        
        /// <summary>
        /// Performs a search
        /// </summary>
        public void PerformSearch()
        {
            try
            {
                // In a real implementation, this would search the data
                // For now, we'll just show a status message
                ShowStatus($"Search for '{SearchText}' completed", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error performing search: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error performing search", false);
            }
        }
        
        /// <summary>
        /// Switches to the specified tab
        /// </summary>
        /// <param name="tabName">The name of the tab to switch to</param>
        public void SwitchTab(string tabName)
        {
            try
            {
                if (!string.IsNullOrEmpty(tabName))
                {
                    SelectedTab = tabName;
                    _logger.Log($"Switched to tab: {tabName}", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error switching tab: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error switching tab", false);
            }
        }
        
        /// <summary>
        /// Saves the configuration
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                // In a real implementation, this would save the configuration
                // For now, we'll just show a status message
                ShowStatus("Configuration saved successfully", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving configuration: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error saving configuration", false);
            }
        }
        
        /// <summary>
        /// Resets the configuration to defaults
        /// </summary>
        public void ResetConfig()
        {
            try
            {
                // In a real implementation, this would reset the configuration
                // For now, we'll just show a status message
                ShowStatus("Configuration reset to defaults", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error resetting configuration: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error resetting configuration", false);
            }
        }
        
        /// <summary>
        /// Checks for updates
        /// </summary>
        public async void CheckForUpdates()
        {
            try
            {
                IsLoading = true;
                ShowStatus("Checking for updates...", true);
                
                // Get the auto update service
                var updateService = AutoUpdateService.Instance;
                
                // Check for updates
                bool updateAvailable = await updateService.CheckForUpdatesAsync();
                
                if (updateAvailable)
                {
                    // Ask if the user wants to update now
                    var result = System.Windows.MessageBox.Show(
                        "An update is available. Do you want to download and install it now?",
                        "Update Available",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Question);
                    
                    if (result == System.Windows.MessageBoxResult.Yes)
                    {
                        // Update the application
                        bool success = await updateService.UpdateAsync();
                        
                        if (success)
                        {
                            ShowStatus("Update installed successfully. The application will restart.", true);
                        }
                        else
                        {
                            ShowStatus("Failed to install update.", false);
                        }
                    }
                    else
                    {
                        ShowStatus("Update available but not installed.", true);
                    }
                }
                else
                {
                    ShowStatus("No new updates available", true);
                }
                
                IsLoading = false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking for updates: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error checking for updates", false);
                IsLoading = false;
            }
        }
        
        /// <summary>
        /// Uploads an update
        /// </summary>
        public void UploadUpdate()
        {
            try
            {
                // Create a file dialog to select the update file
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "Select Update File",
                    Filter = "Update Files (*.zip, *.exe)|*.zip;*.exe|All Files (*.*)|*.*",
                    CheckFileExists = true
                };
                
                // Show the dialog and get the result
                bool? result = openFileDialog.ShowDialog();
                
                // If the user selected a file
                if (result == true)
                {
                    string filePath = openFileDialog.FileName;
                    string fileName = System.IO.Path.GetFileName(filePath);
                    
                    // Show a confirmation dialog
                    var confirmResult = System.Windows.MessageBox.Show(
                        $"Are you sure you want to upload the update file '{fileName}'?",
                        "Confirm Upload",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Question);
                    
                    if (confirmResult == System.Windows.MessageBoxResult.Yes)
                    {
                        // In a real implementation, this would upload the file to a server
                        // For now, we'll just show a status message with the file name
                        _logger.Log($"Update file selected: {filePath}", LogLevel.INFO);
                        ShowStatus($"Update '{fileName}' uploaded successfully", true);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error uploading update: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error uploading update", false);
            }
        }
        
        /// <summary>
        /// Manages versions
        /// </summary>
        public void ManageVersions()
        {
            try
            {
                // In a real implementation, this would manage versions
                // For now, we'll just show a status message
                ShowStatus("Version management opened", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error managing versions: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error managing versions", false);
            }
        }
        
        /// <summary>
        /// Creates a new release
        /// </summary>
        public void CreateRelease()
        {
            try
            {
                // In a real implementation, this would create a new release
                // For now, we'll just show a status message
                ShowStatus("Release created successfully", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating release: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error creating release", false);
            }
        }
        
        /// <summary>
        /// Views release notes
        /// </summary>
        public void ViewReleaseNotes()
        {
            try
            {
                // In a real implementation, this would show release notes
                // For now, we'll just show a status message
                ShowStatus("Release notes viewed", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error viewing release notes: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error viewing release notes", false);
            }
        }
        
        /// <summary>
        /// Shows a status message
        /// </summary>
        /// <param name="message">The message to show</param>
        /// <param name="isSuccess">Whether the status is a success</param>
        public void ShowStatus(string message, bool isSuccess)
        {
            StatusMessage = message;
            IsStatusSuccess = isSuccess;
            IsStatusVisible = true;
            
            // Hide the status after 5 seconds
            var timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(5);
            timer.Tick += (sender, e) =>
            {
                IsStatusVisible = false;
                timer.Stop();
            };
            timer.Start();
        }
        
        /// <summary>
        /// Event that is raised when a property changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;
        
        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The property name</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
