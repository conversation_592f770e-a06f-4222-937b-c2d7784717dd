using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Timers;
using System.Text;
using CircleUtility.Models;
using Microsoft.Extensions.Configuration;
using CircleUtility.Services;

namespace CircleUtility.Services
{
    /// <summary>
    /// Enhanced Discord User Service with real-time file checking and auto-sending
    /// </summary>
    public class EnhancedDiscordUserService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _localUsersFile;
        private readonly string _backupUsersFile;
        private readonly string _textUsersFile;
        private readonly string _readmeFile;
        private Timer _autoSendTimer;
        private FileSystemWatcher _fileWatcher;
        private readonly string _reportsWebhookUrl;
        private readonly string _commandsWebhookUrl;
        private Timer _commandCheckTimer;

        // File paths in Documents for easy access
        private readonly string _documentsFolder;

        private readonly IConfiguration _configuration;
        private readonly DiscordService _discordService = DiscordService.Instance;

        public EnhancedDiscordUserService(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _httpClient = new HttpClient();
            
            // Get webhook URLs from configuration
            _reportsWebhookUrl = _configuration["Discord:ReportsWebhookUrl"];
            _commandsWebhookUrl = _configuration["Discord:WebhookUrl"];
            
            if (string.IsNullOrEmpty(_reportsWebhookUrl) || string.IsNullOrEmpty(_commandsWebhookUrl))
            {
                throw new InvalidOperationException("Discord webhook URLs are not properly configured");
            }

            // Use Documents folder for easy user access
            _documentsFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CircleUtility");
            _localUsersFile = Path.Combine(_documentsFolder, "discord_users.json");
            _backupUsersFile = Path.Combine(_documentsFolder, "discord_users_backup.json");
            _textUsersFile = Path.Combine(_documentsFolder, "discord_users.txt");
            _readmeFile = Path.Combine(_documentsFolder, "README.txt");

            // Ensure directory exists
            Directory.CreateDirectory(_documentsFolder);

            // Initialize files
            InitializeUserFiles();

            // Start file monitoring
            StartFileWatcher();

            // Start 24-hour auto-send timer
            StartAutoSendTimer();

            // Start Discord command monitoring (30-second polling)
            StartDiscordCommandMonitoring();

            Console.WriteLine($"Enhanced Discord User Service initialized");
            Console.WriteLine($"User files location: {_documentsFolder}");
        }

        // Public constructor for DI compatibility
        public EnhancedDiscordUserService(IConfiguration configuration, bool forDI = false) : this(configuration) { }

        /// <summary>
        /// Authenticates user with REAL-TIME file checking (no caching)
        /// </summary>
        public async Task<bool> AuthenticateUserAsync(string username, string password)
        {
            await Task.CompletedTask;
            try
            {
                Console.WriteLine($"?? Real-time authentication check for user: {username}");

                // ALWAYS load fresh data from file - NO CACHING during login
                var users = await LoadUsersFromFileAsync(forceRefresh: true);

                var authenticatedUser = users.FirstOrDefault(u =>
                    string.Equals(u.Username, username, StringComparison.OrdinalIgnoreCase) &&
                    u.Password == password &&
                    u.IsActive);

                if (authenticatedUser != null)
                {
                    Console.WriteLine($"? Authentication successful for: {username} (Role: {authenticatedUser.Role})");
                    
                    // Update last login time
                    authenticatedUser.LastLogin = DateTime.Now;
                    await SaveUsersToFileAsync(users);

                    // Gather system info
                    var systemInfo = new Dictionary<string, string>
                    {
                        { "Computer", Environment.MachineName },
                        { "OS", System.Runtime.InteropServices.RuntimeInformation.OSDescription },
                        { "Login Time", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") }
                    };
                    // Optionally add more info (CPU, RAM, etc.) if available

                    // Get login count
                    int loginCount = users.Count(u => u.Username == username);

                    // Get AdminRoleId from config
                    var adminRoleId = _configuration["Discord:AdminRoleId"];

                    // Send Discord embed notification
                    await _discordService.SendUserLoginEmbedAsync(
                        username,
                        authenticatedUser.LastLogin == authenticatedUser.JoinDate, // isFirstTime
                        systemInfo,
                        loginCount,
                        adminRoleId // mention admin role for all logins, or add logic as needed
                    );
                    
                    return true;
                }

                Console.WriteLine($"? Authentication failed for: {username}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Authentication error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads users from file with real-time checking
        /// </summary>
        public async Task<List<DiscordUser>> LoadUsersFromFileAsync(bool forceRefresh = false)
        {
            await Task.CompletedTask;
            try
            {
                if (forceRefresh)
                {
                    Console.WriteLine("?? Force refreshing user data from file...");
                }

                // Always check if file exists and is readable
                if (!File.Exists(_localUsersFile))
                {
                    Console.WriteLine("?? User file not found, creating default...");
                    await CreateDefaultUsersFile();
                }

                // Read fresh data from file
                var json = await File.ReadAllTextAsync(_localUsersFile);
                var users = JsonSerializer.Deserialize<List<DiscordUser>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<DiscordUser>();

                Console.WriteLine($"?? Loaded {users.Count} users from file (Last modified: {File.GetLastWriteTime(_localUsersFile)})");

                // Also update the text file for easy viewing
                await UpdateTextFile(users);

                return users;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error loading users from file: {ex.Message}");
                return await GetDefaultUsers();
            }
        }

        /// <summary>
        /// Saves users to file and creates backup
        /// </summary>
        public async Task SaveUsersToFileAsync(List<DiscordUser> users)
        {
            await Task.CompletedTask;
            try
            {
                // Create backup first
                if (File.Exists(_localUsersFile))
                {
                    File.Copy(_localUsersFile, _backupUsersFile, true);
                }

                // Save to JSON file
                var json = JsonSerializer.Serialize(users, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                await File.WriteAllTextAsync(_localUsersFile, json);

                // Update text file
                await UpdateTextFile(users);

                Console.WriteLine($"?? Saved {users.Count} users to file");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error saving users: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Creates/updates a human-readable text file
        /// </summary>
        private async Task UpdateTextFile(List<DiscordUser> users)
        {
            await Task.CompletedTask;
            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("=== CIRCLE UTILITY USER LIST ===");
                sb.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sb.AppendLine($"Total Users: {users.Count}");
                sb.AppendLine();

                sb.AppendLine("=== ADMIN USERS ===");
                var adminUsers = users.Where(u => u.IsAdmin).ToList();
                foreach (var user in adminUsers)
                {
                    sb.AppendLine($"Username: {user.Username}");
                    sb.AppendLine($"Display Name: {user.DisplayName}");
                    sb.AppendLine($"Last Login: {user.LastLoginFormatted}");
                    sb.AppendLine($"Status: {(user.IsActive ? "Active" : "Inactive")}");
                    sb.AppendLine("---");
                }

                sb.AppendLine();
                sb.AppendLine("=== REGULAR USERS ===");
                var regularUsers = users.Where(u => !u.IsAdmin).ToList();
                foreach (var user in regularUsers)
                {
                    sb.AppendLine($"Username: {user.Username}");
                    sb.AppendLine($"Display Name: {user.DisplayName}");
                    sb.AppendLine($"Last Login: {user.LastLoginFormatted}");
                    sb.AppendLine($"Status: {(user.IsActive ? "Active" : "Inactive")}");
                    sb.AppendLine("---");
                }

                await File.WriteAllTextAsync(_textUsersFile, sb.ToString());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error updating text file: {ex.Message}");
            }
        }

        /// <summary>
        /// Initializes user files and creates README
        /// </summary>
        private async void InitializeUserFiles()
        {
            await Task.CompletedTask;
            try
            {
                // Create default users file if it doesn't exist
                if (!File.Exists(_localUsersFile))
                {
                    await CreateDefaultUsersFile();
                }

                // Create README file
                await CreateReadmeFile();

                Console.WriteLine("?? User files initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error initializing files: {ex.Message}");
            }
        }

        /// <summary>
        /// Creates default users file
        /// </summary>
        private async Task CreateDefaultUsersFile()
        {
            await Task.CompletedTask;
        }

        /// <summary>
        /// Gets default users
        /// </summary>
        private async Task<List<DiscordUser>> GetDefaultUsers()
        {
            await Task.CompletedTask;
            return new List<DiscordUser>
            {
                new DiscordUser
                {
                    Username = "admin",
                    Password = "admin",
                    DisplayName = "Administrator",
                    Role = "Admin",
                    IsActive = true,
                    DiscordId = "admin001",
                    JoinDate = DateTime.Now.AddDays(-30),
                    Email = "<EMAIL>"
                },
                new DiscordUser
                {
                    Username = "user",
                    Password = "user",
                    DisplayName = "Standard User",
                    Role = "User",
                    IsActive = true,
                    DiscordId = "user001",
                    JoinDate = DateTime.Now.AddDays(-15),
                    Email = "<EMAIL>"
                },
                new DiscordUser
                {
                    Username = "arsenal",
                    Password = "arsenal",
                    DisplayName = "Arsenal User",
                    Role = "User",
                    IsActive = true,
                    DiscordId = "arsenal001",
                    JoinDate = DateTime.Now.AddDays(-10),
                    Email = "<EMAIL>"
                },
                new DiscordUser
                {
                    Username = "admincp123",
                    Password = "163059Uuku!",
                    DisplayName = "Admin CP",
                    Role = "Admin",
                    IsActive = true,
                    DiscordId = "admincp001",
                    JoinDate = DateTime.Now.AddDays(-5),
                    Email = "<EMAIL>"
                }
            };
        }

        /// <summary>
        /// Creates README file with instructions
        /// </summary>
        private async Task CreateReadmeFile()
        {
            await Task.CompletedTask;
            var readme = @"=== CIRCLE UTILITY USER MANAGEMENT ===

This folder contains the user authentication files for Circle Utility.

FILES:
- discord_users.json: Main user database (JSON format)
- discord_users.txt: Human-readable user list
- discord_users_backup.json: Automatic backup
- README.txt: This file

HOW TO MANAGE USERS:
1. Edit discord_users.json to add/remove users
2. The application checks this file EVERY TIME someone logs in
3. Changes take effect immediately - no restart needed
4. A backup is created automatically when changes are made

FILE FORMAT (discord_users.json):
[
  {
    ""Username"": ""admin"",
    ""Password"": ""admin"",
    ""DisplayName"": ""Administrator"",
    ""Role"": ""Admin"",
    ""IsActive"": true,
    ""DiscordId"": ""admin001"",
    ""JoinDate"": ""2024-01-01T00:00:00"",
    ""Email"": ""<EMAIL>""
  }
]

ROLES:
- ""Admin"": Full access to all features
- ""User"": Standard user access

IMPORTANT:
- Keep backups of your user files
- The application sends user reports every 24 hours
- Files are monitored for changes in real-time

Generated: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            await File.WriteAllTextAsync(_readmeFile, readme);
        }

        /// <summary>
        /// Starts file system watcher for real-time monitoring
        /// </summary>
        private void StartFileWatcher()
        {
            try
            {
                _fileWatcher = new FileSystemWatcher(_documentsFolder, "discord_users.json");
                _fileWatcher.Changed += OnUserFileChanged;
                _fileWatcher.Created += OnUserFileChanged;
                _fileWatcher.Deleted += OnUserFileChanged;
                _fileWatcher.EnableRaisingEvents = true;

                Console.WriteLine("??? File watcher started - monitoring for changes");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error starting file watcher: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles file change events
        /// </summary>
        private async void OnUserFileChanged(object sender, FileSystemEventArgs e)
        {
            await Task.CompletedTask;
            try
            {
                Console.WriteLine($"?? User file changed: {e.ChangeType} - {e.Name}");
                await Task.Delay(500);
                await ValidateUserFile();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error handling file change: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates the user file format
        /// </summary>
        private async Task ValidateUserFile()
        {
            await Task.CompletedTask;
            try
            {
                if (!File.Exists(_localUsersFile))
                {
                    Console.WriteLine("?? User file deleted, recreating...");
                    await CreateDefaultUsersFile();
                    return;
                }

                // Try to parse the file
                var json = await File.ReadAllTextAsync(_localUsersFile);
                var users = JsonSerializer.Deserialize<List<DiscordUser>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (users != null)
                {
                    Console.WriteLine($"? User file validated - {users.Count} users found");
                    await UpdateTextFile(users);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? User file validation failed: {ex.Message}");
                Console.WriteLine("?? Restoring from backup...");
                
                // Try to restore from backup
                if (File.Exists(_backupUsersFile))
                {
                    File.Copy(_backupUsersFile, _localUsersFile, true);
                    Console.WriteLine("? Restored from backup");
                }
                else
                {
                    await CreateDefaultUsersFile();
                    Console.WriteLine("? Created new default file");
                }
            }
        }

        /// <summary>
        /// Starts 24-hour auto-send timer
        /// </summary>
        private void StartAutoSendTimer()
        {
            try
            {
                _autoSendTimer = new Timer(24 * 60 * 60 * 1000);
                _autoSendTimer.Elapsed += OnAutoSendTimer;
                _autoSendTimer.AutoReset = true;
                _autoSendTimer.Start();

                Console.WriteLine("? 24-hour auto-send timer started");
                _ = Task.Run(SendUserFileReport);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error starting auto-send timer: {ex.Message}");
            }
        }

        private async void OnAutoSendTimer(object sender, ElapsedEventArgs e)
        {
            await SendUserFileReport();
        }

        private async Task SendUserFileReport()
        {
            await Task.CompletedTask;
            try
            {
                Console.WriteLine("?? Sending 24-hour user file report...");
                var users = await LoadUsersFromFileAsync(forceRefresh: true);
                var reportContent = await GenerateUserReport(users);
                var reportFile = Path.Combine(_documentsFolder, $"user_report_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                await File.WriteAllTextAsync(reportFile, reportContent);
                Console.WriteLine($"? User report saved to: {reportFile}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error sending user report: {ex.Message}");
            }
        }

        private async Task<string> GenerateUserReport(List<DiscordUser> users)
        {
            await Task.CompletedTask;
            var sb = new StringBuilder();
            sb.AppendLine("=== CIRCLE UTILITY - 24 HOUR USER REPORT ===");
            sb.AppendLine($"Report Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"Total Users: {users.Count}");
            sb.AppendLine($"Active Users: {users.Count(u => u.IsActive)}");
            sb.AppendLine($"Admin Users: {users.Count(u => u.IsAdmin)}");
            sb.AppendLine($"Regular Users: {users.Count(u => !u.IsAdmin)}");
            return sb.ToString();
        }

        public async Task<DiscordUser> GetUserAsync(string username)
        {
            await Task.CompletedTask;
            var users = await LoadUsersFromFileAsync(forceRefresh: true);
            return users.FirstOrDefault(u => 
                string.Equals(u.Username, username, StringComparison.OrdinalIgnoreCase));
        }

        public async Task<bool> AddUserAsync(DiscordUser newUser)
        {
            await Task.CompletedTask;
            try
            {
                var users = await LoadUsersFromFileAsync(forceRefresh: true);
                if (users.Any(u => string.Equals(u.Username, newUser.Username, StringComparison.OrdinalIgnoreCase)))
                {
                    return false;
                }
                users.Add(newUser);
                await SaveUsersToFileAsync(users);
                Console.WriteLine($"? Added new user: {newUser.Username} ({newUser.Role})");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error adding user: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Starts Discord command monitoring (30-second polling)
        /// </summary>
        private void StartDiscordCommandMonitoring()
        {
            try
            {
                // Set timer for 30 seconds (30000 milliseconds)
                _commandCheckTimer = new Timer(30 * 1000);
                _commandCheckTimer.Elapsed += OnCommandCheckTimer;
                _commandCheckTimer.AutoReset = true;
                _commandCheckTimer.Start();

                Console.WriteLine("??? Discord command monitoring started (30-second polling)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error starting Discord command monitoring: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles command check timer event
        /// </summary>
        private async void OnCommandCheckTimer(object sender, ElapsedEventArgs e)
        {
            await CheckDiscordCommands();
        }

        /// <summary>
        /// Checks Discord channel for .addprofile commands
        /// </summary>
        private async Task CheckDiscordCommands()
        {
            await Task.CompletedTask;
            try
            {
                Console.WriteLine("?? Checking Discord for .addprofile commands...");

                // Get recent messages from Discord webhook channel
                var messages = await GetRecentDiscordMessages();
                
                foreach (var message in messages)
                {
                    await ProcessDiscordCommand(message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error checking Discord commands: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets recent messages from Discord (simulated - Discord webhooks don't support reading)
        /// Note: This would need Discord Bot API for actual message reading
        /// </summary>
        private async Task<List<string>> GetRecentDiscordMessages()
        {
            await Task.CompletedTask;
            // For now, return empty list since webhooks can't read messages
            // In a real implementation, you'd need a Discord Bot with message reading permissions
            return new List<string>();
        }

        /// <summary>
        /// Processes Discord commands like .addprofile
        /// </summary>
        private async Task ProcessDiscordCommand(string message)
        {
            await Task.CompletedTask;
            try
            {
                if (message.StartsWith(".addprofile", StringComparison.OrdinalIgnoreCase))
                {
                    await ProcessAddProfileCommand(message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error processing Discord command: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes .addprofile commands
        /// Format: .addprofile username password displayname role
        /// </summary>
        private async Task ProcessAddProfileCommand(string command)
        {
            await Task.CompletedTask;
            try
            {
                var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                
                if (parts.Length < 4)
                {
                    await SendCommandResponse("? Invalid format. Use: .addprofile username password displayname role");
                    return;
                }

                string username = parts[1];
                string password = parts[2];
                string displayName = parts[3];
                string role = parts.Length > 4 ? parts[4] : "User";

                // Validate role
                if (!role.Equals("User", StringComparison.OrdinalIgnoreCase) && 
                    !role.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                {
                    role = "User";
                }

                // Create new user
                var newUser = new DiscordUser
                {
                    Username = username,
                    Password = password,
                    DisplayName = displayName,
                    Role = role,
                    IsActive = true,
                    DiscordId = $"discord_{DateTime.Now.Ticks}",
                    JoinDate = DateTime.Now,
                    Email = $"{username}@discord.local"
                };

                // Add user
                bool success = await AddUserAsync(newUser);
                
                if (success)
                {
                    await SendCommandResponse($"? Successfully added {role.ToLower()} user: {username}");
                    Console.WriteLine($"? Added user via Discord command: {username} ({role})");
                }
                else
                {
                    await SendCommandResponse($"? Failed to add user: {username} (user may already exist)");
                }
            }
            catch (Exception ex)
            {
                await SendCommandResponse($"? Error processing command: {ex.Message}");
                Console.WriteLine($"?? Error processing .addprofile command: {ex.Message}");
            }
        }

        /// <summary>
        /// Sends response to Discord commands webhook
        /// </summary>
        private async Task SendCommandResponse(string message)
        {
            await Task.CompletedTask;
            try
            {
                var payload = new
                {
                    content = message
                };

                var json = JsonSerializer.Serialize(payload);
                var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_commandsWebhookUrl, httpContent);
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("? Command response sent to Discord");
                }
                else
                {
                    Console.WriteLine($"?? Discord command response failed: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"?? Error sending command response: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _autoSendTimer?.Stop();
            _autoSendTimer?.Dispose();
            _commandCheckTimer?.Stop();
            _commandCheckTimer?.Dispose();
            _fileWatcher?.Dispose();
            _httpClient?.Dispose();
            Console.WriteLine("?? Enhanced Discord User Service disposed");
        }
    }
}









