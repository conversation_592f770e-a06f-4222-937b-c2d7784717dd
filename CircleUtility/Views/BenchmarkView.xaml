<UserControl x:Class="CircleUtility.Views.BenchmarkView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:CircleUtility.Views"
             Background="Black">

    <UserControl.Resources>
        <!-- Glowing button style -->
        <Style x:Key="GlowingButton" TargetType="Button">
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- Outer glow effect -->
                            <Border x:Name="GlowBorder"
                                    Background="#3300C8FF"
                                    CornerRadius="2"
                                    Margin="-2"
                                    Opacity="0">
                                <Border.Effect>
                                    <BlurEffect Radius="10"/>
                                </Border.Effect>
                            </Border>

                            <!-- Button background -->
                            <Border x:Name="ButtonBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="GlowBorder" Property="Opacity" Value="1"/>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#FF00FFFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF002850"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Danger button style -->
        <Style x:Key="DangerGlowingButton" TargetType="Button" BasedOn="{StaticResource GlowingButton}">
            <Setter Property="Background" Value="#FF280000"/>
            <Setter Property="BorderBrush" Value="#FFFF3232"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- Outer glow effect -->
                            <Border x:Name="GlowBorder"
                                    Background="#33FF3232"
                                    CornerRadius="2"
                                    Margin="-2"
                                    Opacity="0">
                                <Border.Effect>
                                    <BlurEffect Radius="10"/>
                                </Border.Effect>
                            </Border>

                            <!-- Button background -->
                            <Border x:Name="ButtonBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="GlowBorder" Property="Opacity" Value="1"/>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#FFFF6464"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF500000"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with glow effect -->
        <TextBlock Text="Benchmark"
                   FontFamily="Consolas"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20">
            <TextBlock.Effect>
                <DropShadowEffect Color="#FF00C8FF" BlurRadius="15" ShadowDepth="0" Opacity="0.7"/>
            </TextBlock.Effect>
        </TextBlock>

        <!-- Description -->
        <TextBlock Grid.Row="1"
                   Text="Run benchmarks to measure your system's performance and track improvements after applying optimizations."
                   FontFamily="Consolas"
                   FontSize="12"
                   Foreground="White"
                   TextWrapping="Wrap"
                   Margin="0,0,0,20"/>

        <!-- Benchmark content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Benchmark options -->
            <Border Grid.Column="0"
                    Background="#FF050A0F"
                    BorderThickness="1"
                    BorderBrush="#FF00C8FF"
                    Padding="15"
                    Margin="0,0,20,0">
                <Border.Effect>
                    <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Text="Benchmark Options"
                               FontFamily="Consolas"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="#FF00C8FF"
                               Margin="0,0,0,15">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="5" ShadowDepth="0" Opacity="0.5"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- Benchmark selection -->
                    <StackPanel Grid.Row="1" Margin="0,0,0,15">
                        <TextBlock Text="Select Benchmark:"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="White"
                                   Margin="0,0,0,5"/>

                        <ComboBox ItemsSource="{Binding BenchmarkOptions}"
                                  SelectedItem="{Binding SelectedBenchmark}"
                                  Background="#FF0A141E"
                                  Foreground="White"
                                  BorderBrush="#FF00C8FF"
                                  BorderThickness="1"
                                  FontFamily="Consolas"
                                  Height="30">
                            <ComboBox.Resources>
                                <SolidColorBrush x:Key="{x:Static SystemColors.WindowBrushKey}" Color="#FF0A141E"/>
                                <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                                <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                                <SolidColorBrush x:Key="{x:Static SystemColors.WindowTextBrushKey}" Color="White"/>
                            </ComboBox.Resources>
                            <ComboBox.Effect>
                                <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                            </ComboBox.Effect>
                        </ComboBox>
                    </StackPanel>

                    <!-- Benchmark status -->
                    <StackPanel Grid.Row="2" Margin="0,0,0,15">
                        <TextBlock Text="Status:"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="White"
                                   Margin="0,0,0,5"/>

                        <TextBlock Text="{Binding BenchmarkStatus}"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FF00C8FF"
                                   TextWrapping="Wrap"/>
                    </StackPanel>

                    <!-- Benchmark progress -->
                    <StackPanel Grid.Row="3" Margin="0,0,0,15">
                        <TextBlock Text="Progress:"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="White"
                                   Margin="0,0,0,5"/>

                        <ProgressBar Value="{Binding BenchmarkProgress}"
                                     Height="20"
                                     Background="#FF0A141E"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Foreground="#FF00C8FF"/>
                    </StackPanel>

                    <!-- Benchmark actions -->
                    <StackPanel Grid.Row="4" VerticalAlignment="Bottom">
                        <Button Content="Run Benchmark"
                                Command="{Binding RunBenchmarkCommand}"
                                Style="{StaticResource GlowingButton}"/>

                        <Button Content="Cancel Benchmark"
                                Command="{Binding CancelBenchmarkCommand}"
                                Style="{StaticResource DangerGlowingButton}"
                                IsEnabled="{Binding IsBenchmarkRunning}"/>

                        <Button Content="Clear Results"
                                Command="{Binding ClearResultsCommand}"
                                Style="{StaticResource GlowingButton}"/>

                        <Button Content="Export Results"
                                Command="{Binding ExportResultsCommand}"
                                Style="{StaticResource GlowingButton}"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Benchmark results -->
            <Border Grid.Column="1"
                    Background="#FF050A0F"
                    BorderThickness="1"
                    BorderBrush="#FF00C8FF"
                    Padding="15">
                <Border.Effect>
                    <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Text="Benchmark Results"
                               FontFamily="Consolas"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="#FF00C8FF"
                               Margin="0,0,0,15">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="5" ShadowDepth="0" Opacity="0.5"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- Results content -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="200"/>
                        </Grid.RowDefinitions>

                        <!-- Results list -->
                        <DataGrid Grid.Row="0"
                                  ItemsSource="{Binding BenchmarkResults}"
                                  AutoGenerateColumns="False"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeRows="False"
                                  CanUserSortColumns="True"
                                  IsReadOnly="True">
                            <DataGrid.Resources>
                                <!-- Style for DataGrid header -->
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#FF001428"/>
                                    <Setter Property="Foreground" Value="#FF00C8FF"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Padding" Value="8,5"/>
                                    <Setter Property="BorderBrush" Value="#FF004080"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                                <!-- Style for DataGrid cells -->
                                <Style TargetType="DataGridCell">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="BorderBrush" Value="#FF004080"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="DataGridCell">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}">
                                                    <ContentPresenter Margin="{TemplateBinding Padding}"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#FF004080"/>
                                            <Setter Property="Foreground" Value="#FF00FFFF"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Resources>
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Benchmark" Binding="{Binding Name}" Width="150"/>
                                <DataGridTextColumn Header="Score" Binding="{Binding Score}" Width="100"/>
                                <DataGridTextColumn Header="Unit" Binding="{Binding Unit}" Width="80"/>
                                <DataGridTextColumn Header="Date/Time" Binding="{Binding DateTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}" Width="150"/>
                                <DataGridTextColumn Header="System Config" Binding="{Binding SystemConfig}" Width="*"/>
                                <DataGridTextColumn Header="Notes" Binding="{Binding Notes}" Width="200"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- Performance comparison header -->
                        <TextBlock Grid.Row="1"
                                   Text="Performance Comparison"
                                   FontFamily="Consolas"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF00C8FF"
                                   Margin="0,15,0,10">
                            <TextBlock.Effect>
                                <DropShadowEffect Color="#FF00C8FF" BlurRadius="5" ShadowDepth="0" Opacity="0.5"/>
                            </TextBlock.Effect>
                        </TextBlock>

                        <!-- Comparison Chart -->
                        <Border Grid.Row="2"
                                Background="#FF0A141E"
                                BorderThickness="1"
                                BorderBrush="#FF00C8FF">
                            <Border.Effect>
                                <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                            </Border.Effect>
                            <Grid>
                                <!-- This is a placeholder for a chart control -->
                                <!-- In a real implementation, you would use a charting library -->
                                <Canvas>
                                    <!-- X-axis -->
                                    <Line X1="50" Y1="170" X2="550" Y2="170" Stroke="#FF00C8FF" StrokeThickness="1" Opacity="0.5"/>

                                    <!-- Y-axis -->
                                    <Line X1="50" Y1="20" X2="50" Y2="170" Stroke="#FF00C8FF" StrokeThickness="1" Opacity="0.5"/>

                                    <!-- X-axis labels -->
                                    <TextBlock Canvas.Left="100" Canvas.Top="175" Text="Before" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>
                                    <TextBlock Canvas.Left="200" Canvas.Top="175" Text="After Tweaks" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>
                                    <TextBlock Canvas.Left="300" Canvas.Top="175" Text="After OC" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>
                                    <TextBlock Canvas.Left="400" Canvas.Top="175" Text="Final" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>

                                    <!-- Y-axis labels -->
                                    <TextBlock Canvas.Left="20" Canvas.Top="160" Text="0" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>
                                    <TextBlock Canvas.Left="10" Canvas.Top="120" Text="100" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>
                                    <TextBlock Canvas.Left="10" Canvas.Top="80" Text="200" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>
                                    <TextBlock Canvas.Left="10" Canvas.Top="40" Text="300" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>

                                    <!-- Grid lines -->
                                    <Line X1="50" Y1="120" X2="550" Y2="120" Stroke="#FF00C8FF" StrokeThickness="1" StrokeDashArray="4,2" Opacity="0.2"/>
                                    <Line X1="50" Y1="80" X2="550" Y2="80" Stroke="#FF00C8FF" StrokeThickness="1" StrokeDashArray="4,2" Opacity="0.2"/>
                                    <Line X1="50" Y1="40" X2="550" Y2="40" Stroke="#FF00C8FF" StrokeThickness="1" StrokeDashArray="4,2" Opacity="0.2"/>

                                    <!-- Sample data bars with glow effect -->
                                    <Rectangle Canvas.Left="80" Canvas.Top="120" Width="40" Height="50" Fill="#FF00C8FF" Opacity="0.7">
                                        <Rectangle.Effect>
                                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                                        </Rectangle.Effect>
                                    </Rectangle>
                                    <Rectangle Canvas.Left="180" Canvas.Top="100" Width="40" Height="70" Fill="#FF00C8FF" Opacity="0.7">
                                        <Rectangle.Effect>
                                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                                        </Rectangle.Effect>
                                    </Rectangle>
                                    <Rectangle Canvas.Left="280" Canvas.Top="70" Width="40" Height="100" Fill="#FF00C8FF" Opacity="0.7">
                                        <Rectangle.Effect>
                                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                                        </Rectangle.Effect>
                                    </Rectangle>
                                    <Rectangle Canvas.Left="380" Canvas.Top="40" Width="40" Height="130" Fill="#FF00C8FF" Opacity="0.7">
                                        <Rectangle.Effect>
                                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                                        </Rectangle.Effect>
                                    </Rectangle>

                                    <!-- Legend -->
                                    <Rectangle Canvas.Left="450" Canvas.Top="20" Width="15" Height="15" Fill="#FF00C8FF" Opacity="0.7">
                                        <Rectangle.Effect>
                                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="5" ShadowDepth="0" Opacity="0.5"/>
                                        </Rectangle.Effect>
                                    </Rectangle>
                                    <TextBlock Canvas.Left="470" Canvas.Top="20" Text="FPS" FontFamily="Consolas" FontSize="12" FontWeight="Bold" Foreground="#FFFFFFFF"/>
                                </Canvas>

                                <!-- Overlay text when no data -->
                                <TextBlock Text="Run benchmarks to see comparison data"
                                           FontFamily="Consolas"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#FFFFFFFF"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           Visibility="{Binding HasBenchmarkResults, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                    <TextBlock.Effect>
                                        <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.7"/>
                                    </TextBlock.Effect>
                                </TextBlock>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
