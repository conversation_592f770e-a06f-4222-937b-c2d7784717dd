using System;
using CircleUtility.Models;
using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Services;

namespace CircleUtility
{
    public class DirectMainWindow
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("Starting direct main window application");

                // Initialize services
                var serviceProvider = InitializeServices();

                // Create application
                Application app = new Application();

                // Get required services
                var hardwareDetectionService = serviceProvider.GetRequiredService<IHardwareDetectionService>();
                var hardwareOptimizationService = serviceProvider.GetRequiredService<IHardwareOptimizationService>();
                var hardwareCompatibilityService = serviceProvider.GetRequiredService<IHardwareCompatibilityService>();
                var performanceMonitoringService = serviceProvider.GetRequiredService<IPerformanceMonitoringService>();
                var benchmarkingService = serviceProvider.GetRequiredService<IBenchmarkingService>();

                // Create main window directly (skip welcome screen)
                Console.WriteLine("Creating main window");
                MainWindow mainWindow = new MainWindow(
                    hardwareDetectionService,
                    hardwareOptimizationService,
                    hardwareCompatibilityService,
                    performanceMonitoringService,
                    benchmarkingService, UserTrackingService.Instance);

                // Set as main window
                Console.WriteLine("Setting main window");
                app.MainWindow = mainWindow;

                // Show the window
                Console.WriteLine("Showing main window");
                mainWindow.Show();

                // Run the application
                Console.WriteLine("Running application");
                app.Run();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                MessageBox.Show($"Error: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private static ServiceProvider InitializeServices()
        {
            try
            {
                Console.WriteLine("Initializing services");

                // Create necessary directories
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string logsDir = Path.Combine(baseDir, "logs");
                string dataDir = Path.Combine(baseDir, "data");

                if (!Directory.Exists(logsDir))
                    Directory.CreateDirectory(logsDir);

                if (!Directory.Exists(dataDir))
                    Directory.CreateDirectory(dataDir);

                // Initialize logging service (this will create the singleton instance)
                var logger = LoggingService.Instance;
                logger.Log("Services initialization started", LogLevel.INFO);

                // Create a service collection
                var services = new ServiceCollection();

                // Register all services using standardized extension method
                logger.Log("Registering all CircleUtility services", LogLevel.INFO);
                services.AddCircleUtilityServices(
                    includeUIServices: false,  // No UI services for direct main window
                    includeSecurityServices: false);

                // Build the service provider
                logger.Log("Building service provider", LogLevel.INFO);
                var serviceProvider = services.BuildServiceProvider();

                // Initialize all services
                logger.Log("Initializing all services", LogLevel.INFO);
                serviceProvider.InitializeAllServices();

                logger.Log("All services initialized successfully", LogLevel.SUCCESS);

                return serviceProvider;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing services: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}





