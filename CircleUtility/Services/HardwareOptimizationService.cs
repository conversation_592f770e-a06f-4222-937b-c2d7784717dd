// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Win32;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for hardware-specific optimizations
    /// </summary>
    public class HardwareOptimizationService : IHardwareOptimizationService
    {
        private static HardwareOptimizationService _instance;
        private readonly LoggingService _logger;
        private readonly HardwareDetectionService _hardwareDetectionService;
        private readonly AdminOperationsService _adminOperationsService;
        private readonly string _optimizationsDirectory;
        private readonly List<HardwareSpecificOptimization> _optimizations;
        private readonly List<PowerManagementProfile> _powerProfiles;
        private HardwareCompatibilityService _compatibilityService;
        private HardwareCompatibilityMonitor _compatibilityMonitor;
        private bool _isInitialized;
        private bool _checkCompatibilityBeforeApplying = true;

        /// <summary>
        /// Event raised when an optimization is applied
        /// </summary>
        public event EventHandler<OptimizationEventArgs> OptimizationApplied;

        /// <summary>
        /// Event raised when an optimization is reverted
        /// </summary>
        public event EventHandler<OptimizationEventArgs> OptimizationReverted;

        /// <summary>
        /// Event raised when a power profile is applied
        /// </summary>
        public event EventHandler<PowerProfileEventArgs> PowerProfileApplied;

        /// <summary>
        /// Event raised when a power profile is reverted
        /// </summary>
        public event EventHandler<PowerProfileEventArgs> PowerProfileReverted;

        /// <summary>
        /// Initializes a new instance of the HardwareOptimizationService class
        /// </summary>
        private HardwareOptimizationService()
        {
            _logger = LoggingService.Instance;
            _hardwareDetectionService = _hardwareDetectionService ?? throw new ArgumentNullException(nameof(_hardwareDetectionService));
            _adminOperationsService = AdminOperationsService.Instance;
            _compatibilityService = HardwareCompatibilityService.Instance;
            // Don't initialize _compatibilityMonitor here to avoid circular dependency
            _optimizationsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Optimizations");
            _optimizations = new List<HardwareSpecificOptimization>();
            _powerProfiles = new List<PowerManagementProfile>();
            _isInitialized = false;

            // Ensure optimizations directory exists
            if (!Directory.Exists(_optimizationsDirectory))
            {
                Directory.CreateDirectory(_optimizationsDirectory);
            }
        }

        /// <summary>
        /// Gets the singleton instance of the hardware optimization service
        /// </summary>
        public static HardwareOptimizationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new HardwareOptimizationService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets all hardware-specific optimizations
        /// </summary>
        public List<HardwareSpecificOptimization> Optimizations => _optimizations;

        /// <summary>
        /// Gets all power management profiles
        /// </summary>
        public List<PowerManagementProfile> PowerProfiles => _powerProfiles;

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task InitializeAsync()
        {
            if (_isInitialized)
            {
                return;
            }

            try
            {
                _logger.Log("Initializing hardware optimization service...", LogLevel.INFO);

                // Load optimizations
                LoadOptimizations();

                // Load power profiles
                LoadPowerProfiles();

                // Initialize hardware detection service
                await _hardwareDetectionService.GetHardwareInfoAsync(false);

                // Initialize compatibility service
                await _compatibilityService.InitializeAsync();

                try
                {
                    // Initialize compatibility monitor
                    _logger.Log("Initializing compatibility monitor...", LogLevel.INFO);
                    _compatibilityMonitor = HardwareCompatibilityMonitor.Instance;

                    // Subscribe to compatibility events
                    _compatibilityMonitor.IncompatibilityDetected += OnIncompatibilityDetected;

                    // Initialize and start compatibility monitoring
                    _compatibilityMonitor.Initialize(_compatibilityService, this);

                    // Start monitoring with a timeout
                    var monitoringTask = _compatibilityMonitor.StartMonitoringAsync();
                    bool monitoringStarted = monitoringTask.Wait(TimeSpan.FromSeconds(5));

                    if (monitoringStarted)
                    {
                        _logger.Log("Compatibility monitoring started", LogLevel.SUCCESS);

                        // Perform initial compatibility check with a timeout
                        var checkTask = _compatibilityMonitor.PerformFullCompatibilityCheckAsync(true);
                        bool checkCompleted = checkTask.Wait(TimeSpan.FromSeconds(5));

                        if (checkCompleted)
                        {
                            _logger.Log("Initial compatibility check completed", LogLevel.SUCCESS);
                        }
                        else
                        {
                            _logger.Log("Initial compatibility check timed out", LogLevel.WARNING);
                        }
                    }
                    else
                    {
                        _logger.Log("Compatibility monitoring start timed out", LogLevel.WARNING);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error initializing compatibility monitoring: {ex.Message}", LogLevel.ERROR);
                    // Continue anyway to prevent application crash
                }

                _isInitialized = true;
                _logger.Log("Hardware optimization service initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing hardware optimization service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets optimizations for specific hardware
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="model">The model</param>
        /// <returns>The optimizations for the hardware</returns>
        public IEnumerable<HardwareSpecificOptimization> GetOptimizationsForHardwareModel(HardwareType hardwareType, string manufacturer, string model)
        {
            return _optimizations.Where(o =>
                o.HardwareType == hardwareType &&
                (string.IsNullOrEmpty(o.Manufacturer) || o.Manufacturer.Equals(manufacturer, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(o.ModelPattern) || Regex.IsMatch(model, o.ModelPattern, RegexOptions.IgnoreCase)) &&
                (o.SpecificModels.Count == 0 || o.SpecificModels.Any(m => model.Contains(m, StringComparison.OrdinalIgnoreCase))));
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer (optional)</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType, string manufacturer = null)
        {
            try
            {
                // Filter by hardware type
                var result = _optimizations.Where(o => o.HardwareType == hardwareType).ToList();

                // Filter by manufacturer if specified
                if (!string.IsNullOrEmpty(manufacturer))
                {
                    result = result.Where(o =>
                        string.IsNullOrEmpty(o.Manufacturer) ||
                        o.Manufacturer.Equals(manufacturer, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting optimizations for hardware: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardwareType(HardwareType hardwareType)
        {
            try
            {
                return _optimizations.Where(o => o.HardwareType == hardwareType).ToList();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting optimizations for hardware type: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType)
        {
            return GetOptimizationsForHardwareType(hardwareType);
        }

        /// <summary>
        /// Gets optimizations for specific hardware
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="model">The model</param>
        /// <returns>The optimizations for the hardware</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType, string manufacturer, string model)
        {
            return GetOptimizationsForHardwareModel(hardwareType, manufacturer, model).ToList();
        }

        /// <summary>
        /// Gets all applied optimizations
        /// </summary>
        /// <returns>The list of applied optimizations</returns>
        public List<HardwareSpecificOptimization> GetAppliedOptimizations()
        {
            try
            {
                return _optimizations.Where(o => o.IsEnabled).ToList();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting applied optimizations: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for a specific hardware type and category
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="category">The category (e.g., "Recommended" or "All")</param>
        /// <returns>The list of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForCategory(HardwareType hardwareType, string manufacturer, string category)
        {
            try
            {
                // Get optimizations for the hardware type and manufacturer
                var result = GetOptimizationsForHardwareType(hardwareType);

                // Filter by manufacturer if specified
                if (!string.IsNullOrEmpty(manufacturer))
                {
                    result = result.Where(o =>
                        !string.IsNullOrEmpty(o.Manufacturer) &&
                        o.Manufacturer.Equals(manufacturer, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Filter by category if specified
                if (!string.IsNullOrEmpty(category))
                {
                    if (category.Equals("Recommended", StringComparison.OrdinalIgnoreCase))
                    {
                        // Only return recommended optimizations
                        result = result.Where(o => o.IsRecommended).ToList();
                    }
                    else if (!category.Equals("All", StringComparison.OrdinalIgnoreCase))
                    {
                        // Filter by specific category
                        result = result.Where(o =>
                            !string.IsNullOrEmpty(o.Category) &&
                            o.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting optimizations for hardware and category: {ex.Message}", LogLevel.ERROR);
                return new List<HardwareSpecificOptimization>();
            }
        }

        /// <summary>
        /// Gets optimizations for the current system
        /// </summary>
        /// <returns>The optimizations for the current system</returns>
        public IEnumerable<HardwareSpecificOptimization> GetOptimizationsForCurrentSystem()
        {
            List<HardwareSpecificOptimization> result = new List<HardwareSpecificOptimization>();

            // Get hardware info
            HardwareInfo hardwareInfo = _hardwareDetectionService.GetHardwareInfo();

            if (hardwareInfo == null)
            {
                _logger.Log("Failed to get hardware info", LogLevel.ERROR);
                return result;
            }

            // Get CPU optimizations
            if (hardwareInfo.CPU != null)
            {
                result.AddRange(GetOptimizationsForHardwareModel(
                    HardwareType.CPU,
                    hardwareInfo.CPU.Manufacturer,
                    hardwareInfo.CPU.Name));
            }

            // Get GPU optimizations
            if (hardwareInfo.GPU != null)
            {
                result.AddRange(GetOptimizationsForHardwareModel(
                    HardwareType.GPU,
                    hardwareInfo.GPU.Vendor.ToString(),
                    hardwareInfo.GPU.Name));
            }

            // Get RAM optimizations
            if (hardwareInfo.RAM != null)
            {
                result.AddRange(GetOptimizationsForHardwareModel(
                    HardwareType.RAM,
                    hardwareInfo.RAM.Modules.FirstOrDefault()?.Manufacturer ?? "Unknown",
                    hardwareInfo.RAM.Modules.FirstOrDefault()?.PartNumber ?? "Unknown"));
            }

            // Get system-wide optimizations
            result.AddRange(_optimizations.Where(o => o.HardwareType == HardwareType.System));

            return result;
        }

        /// <summary>
        /// Gets power profiles for the current system
        /// </summary>
        /// <returns>The power profiles for the current system</returns>
        public IEnumerable<PowerManagementProfile> GetPowerProfilesForCurrentSystem()
        {
            // Get hardware info
            HardwareInfo hardwareInfo = _hardwareDetectionService.GetHardwareInfo();

            if (hardwareInfo == null)
            {
                _logger.Log("Failed to get hardware info", LogLevel.ERROR);
                return _powerProfiles;
            }

            string cpuModel = hardwareInfo.CPU?.Name ?? string.Empty;
            string gpuModel = hardwareInfo.GPU?.Name ?? string.Empty;

            // Filter profiles based on hardware compatibility
            return _powerProfiles.Where(p =>
                p.HardwareCompatibility.Count == 0 ||
                p.HardwareCompatibility.Any(h =>
                    cpuModel.Contains(h, StringComparison.OrdinalIgnoreCase) ||
                    gpuModel.Contains(h, StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// Applies an optimization by name
        /// </summary>
        /// <param name="optimizationName">The name of the optimization to apply</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ApplyOptimization(string optimizationName)
        {
            try
            {
                _logger.Log($"Applying optimization by name: {optimizationName}", LogLevel.INFO);

                // Find the optimization
                var optimization = _optimizations.FirstOrDefault(o => o.Name == optimizationName);
                if (optimization == null)
                {
                    _logger.Log($"Optimization not found: {optimizationName}", LogLevel.ERROR);
                    return false;
                }

                // Apply the optimization asynchronously but wait for the result
                return ApplyOptimizationAsync(optimization).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying optimization by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies an optimization
        /// </summary>
        /// <param name="optimization">The optimization to apply</param>
        /// <param name="bypassCompatibilityCheck">Whether to bypass the compatibility check</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ApplyOptimizationAsync(HardwareSpecificOptimization optimization, bool bypassCompatibilityCheck = false)
        {
            try
            {
                _logger.Log($"Applying optimization: {optimization.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (optimization.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required for this optimization", LogLevel.WARNING);
                    return false;
                }

                // Check compatibility if enabled
                if (_checkCompatibilityBeforeApplying && !bypassCompatibilityCheck)
                {
                    var compatibilityResult = await _compatibilityService.ValidateOptimizationAsync(optimization, true);

                    if (!compatibilityResult.IsCompatible)
                    {
                        _logger.Log($"Optimization '{optimization.Name}' is not compatible with your hardware: {compatibilityResult.IncompatibilityReason}", LogLevel.WARNING);

                        // Return false if not compatible
                        return false;
                    }
                }

                bool success = false;

                // Apply optimization based on method
                switch (optimization.Method)
                {
                    case OptimizationMethod.Registry:
                        success = await ApplyRegistryOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Command:
                        success = await ApplyCommandOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.File:
                        success = await ApplyFileOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Api:
                        success = await ApplyApiOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Multiple:
                        // Apply multiple methods
                        bool registrySuccess = string.IsNullOrEmpty(optimization.RegistryKey) ? true : await ApplyRegistryOptimizationAsync(optimization);
                        bool commandSuccess = string.IsNullOrEmpty(optimization.Command) ? true : await ApplyCommandOptimizationAsync(optimization);
                        bool fileSuccess = string.IsNullOrEmpty(optimization.FilePath) ? true : await ApplyFileOptimizationAsync(optimization);
                        bool apiSuccess = string.IsNullOrEmpty(optimization.ApiCall) ? true : await ApplyApiOptimizationAsync(optimization);
                        success = registrySuccess && commandSuccess && fileSuccess && apiSuccess;
                        break;
                    default:
                        _logger.Log($"Unsupported optimization method: {optimization.Method}", LogLevel.ERROR);
                        return false;
                }

                if (success)
                {
                    // Update optimization status
                    optimization.IsEnabled = true;
                    optimization.LastAppliedDate = DateTime.Now;
                    SaveOptimization(optimization);

                    _logger.Log($"Optimization applied: {optimization.Name}", LogLevel.SUCCESS);
                    OptimizationApplied?.Invoke(this, new OptimizationEventArgs(optimization));
                }
                else
                {
                    _logger.Log($"Failed to apply optimization: {optimization.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying optimization: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts an optimization by name
        /// </summary>
        /// <param name="optimizationName">The name of the optimization to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertOptimization(string optimizationName)
        {
            try
            {
                _logger.Log($"Reverting optimization by name: {optimizationName}", LogLevel.INFO);

                // Find the optimization
                var optimization = _optimizations.FirstOrDefault(o => o.Name == optimizationName);
                if (optimization == null)
                {
                    _logger.Log($"Optimization not found: {optimizationName}", LogLevel.ERROR);
                    return false;
                }

                // Revert the optimization asynchronously but wait for the result
                return RevertOptimizationAsync(optimization).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting optimization by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts an optimization
        /// </summary>
        /// <param name="optimization">The optimization to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            try
            {
                _logger.Log($"Reverting optimization: {optimization.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (optimization.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required to revert this optimization", LogLevel.WARNING);
                    return false;
                }

                bool success = false;

                // Revert optimization based on method
                switch (optimization.Method)
                {
                    case OptimizationMethod.Registry:
                        success = await RevertRegistryOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Command:
                        success = await RevertCommandOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.File:
                        success = await RevertFileOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Api:
                        success = await RevertApiOptimizationAsync(optimization);
                        break;
                    case OptimizationMethod.Multiple:
                        // Revert multiple methods
                        bool registrySuccess = string.IsNullOrEmpty(optimization.RegistryKey) ? true : await RevertRegistryOptimizationAsync(optimization);
                        bool commandSuccess = string.IsNullOrEmpty(optimization.RevertCommand) ? true : await RevertCommandOptimizationAsync(optimization);
                        bool fileSuccess = string.IsNullOrEmpty(optimization.FilePath) ? true : await RevertFileOptimizationAsync(optimization);
                        bool apiSuccess = string.IsNullOrEmpty(optimization.ApiCall) ? true : await RevertApiOptimizationAsync(optimization);
                        success = registrySuccess && commandSuccess && fileSuccess && apiSuccess;
                        break;
                    default:
                        _logger.Log($"Unsupported optimization method: {optimization.Method}", LogLevel.ERROR);
                        return false;
                }

                if (success)
                {
                    // Update optimization status
                    optimization.IsEnabled = false;
                    SaveOptimization(optimization);

                    _logger.Log($"Optimization reverted: {optimization.Name}", LogLevel.SUCCESS);
                    OptimizationReverted?.Invoke(this, new OptimizationEventArgs(optimization));
                }
                else
                {
                    _logger.Log($"Failed to revert optimization: {optimization.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting optimization: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies a power profile by name
        /// </summary>
        /// <param name="profileName">The name of the profile to apply</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ApplyPowerProfileAsync(string profileName)
        {
            try
            {
                _logger.Log($"Applying power profile by name: {profileName}", LogLevel.INFO);

                // Find the profile
                var profile = _powerProfiles.FirstOrDefault(p => p.Name == profileName);
                if (profile == null)
                {
                    _logger.Log($"Power profile not found: {profileName}", LogLevel.ERROR);
                    return false;
                }

                // Apply the profile
                return await ApplyPowerProfileAsync(profile);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying power profile by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Applies a power management profile
        /// </summary>
        /// <param name="profile">The profile to apply</param>
        /// <param name="skipCompatibilityCheck">Whether to skip the compatibility check</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ApplyPowerProfileAsync(PowerManagementProfile profile, bool skipCompatibilityCheck = false)
        {
            return await ApplyPowerProfileInternalAsync(profile, skipCompatibilityCheck, false);
        }

        /// <summary>
        /// Applies a power management profile
        /// </summary>
        /// <param name="profile">The profile to apply</param>
        /// <param name="bypassCompatibilityCheck">Whether to bypass the compatibility check</param>
        /// <param name="bypassThermalWarning">Whether to bypass the thermal warning for high-performance profiles</param>
        /// <returns>True if successful, false otherwise</returns>
        private async Task<bool> ApplyPowerProfileInternalAsync(PowerManagementProfile profile, bool bypassCompatibilityCheck = false, bool bypassThermalWarning = false)
        {
            try
            {
                _logger.Log($"Applying power profile: {profile.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (profile.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required for this power profile", LogLevel.WARNING);
                    return false;
                }

                // Check compatibility if enabled
                if (_checkCompatibilityBeforeApplying && !bypassCompatibilityCheck)
                {
                    var compatibilityResult = await _compatibilityService.ValidatePowerProfileAsync(profile, true);

                    if (!compatibilityResult.IsCompatible)
                    {
                        _logger.Log($"Power profile '{profile.Name}' is not compatible with your hardware: {compatibilityResult.IncompatibilityReason}", LogLevel.WARNING);

                        // Return false if not compatible
                        return false;
                    }
                }

                // Check thermal impact for high-performance profiles
                if (!bypassThermalWarning && profile.ThermalImpact >= 80)
                {
                    _logger.Log($"Power profile '{profile.Name}' has a high thermal impact. Ensure your cooling system is adequate.", LogLevel.WARNING);

                    // Note: In a real implementation, we would show a warning dialog here
                    // For now, we'll just log a warning and continue
                }

                // Apply CPU settings
                bool cpuSuccess = await ApplyCpuPowerSettingsAsync(profile.CpuSettings);

                // Apply GPU settings
                bool gpuSuccess = await ApplyGpuPowerSettingsAsync(profile.GpuSettings);

                // Apply system settings
                bool systemSuccess = await ApplySystemPowerSettingsAsync(profile.SystemSettings);

                bool success = cpuSuccess && gpuSuccess && systemSuccess;

                if (success)
                {
                    // Update profile status
                    profile.IsEnabled = true;
                    profile.LastAppliedDate = DateTime.Now;
                    SavePowerProfile(profile);

                    _logger.Log($"Power profile applied: {profile.Name}", LogLevel.SUCCESS);
                    PowerProfileApplied?.Invoke(this, new PowerProfileEventArgs(profile));
                }
                else
                {
                    _logger.Log($"Failed to apply power profile: {profile.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying power profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts a power profile by name
        /// </summary>
        /// <param name="profileName">The name of the profile to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertPowerProfileAsync(string profileName)
        {
            try
            {
                _logger.Log($"Reverting power profile by name: {profileName}", LogLevel.INFO);

                // Find the profile
                var profile = _powerProfiles.FirstOrDefault(p => p.Name == profileName);
                if (profile == null)
                {
                    _logger.Log($"Power profile not found: {profileName}", LogLevel.ERROR);
                    return false;
                }

                // Revert the profile
                return await RevertPowerProfileAsync(profile);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting power profile by name: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts a power management profile
        /// </summary>
        /// <param name="profile">The profile to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertPowerProfileAsync(PowerManagementProfile profile)
        {
            try
            {
                _logger.Log($"Reverting power profile: {profile.Name}", LogLevel.INFO);

                // Check if admin privileges are required
                if (profile.RequiresAdmin && !_adminOperationsService.IsRunningAsAdmin())
                {
                    _logger.Log("Admin privileges required to revert this power profile", LogLevel.WARNING);
                    return false;
                }

                // Revert to default power plan
                bool powerPlanSuccess = await SetPowerPlanAsync("Balanced");

                // Revert CPU settings
                bool cpuSuccess = await RevertCpuPowerSettingsAsync();

                // Revert GPU settings
                bool gpuSuccess = await RevertGpuPowerSettingsAsync();

                // Revert system settings
                bool systemSuccess = await RevertSystemPowerSettingsAsync();

                bool success = powerPlanSuccess && cpuSuccess && gpuSuccess && systemSuccess;

                if (success)
                {
                    // Update profile status
                    profile.IsEnabled = false;
                    SavePowerProfile(profile);

                    _logger.Log($"Power profile reverted: {profile.Name}", LogLevel.SUCCESS);
                    PowerProfileReverted?.Invoke(this, new PowerProfileEventArgs(profile));
                }
                else
                {
                    _logger.Log($"Failed to revert power profile: {profile.Name}", LogLevel.ERROR);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting power profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        // Implementation of optimization methods would go here
        // For brevity, we'll just add placeholder methods that return true

        private async Task<bool> ApplyRegistryOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyCommandOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyFileOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyApiOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertRegistryOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertCommandOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertFileOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertApiOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyCpuPowerSettingsAsync(CpuPowerSettings settings)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplyGpuPowerSettingsAsync(GpuPowerSettings settings)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> ApplySystemPowerSettingsAsync(SystemPowerSettings settings)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertCpuPowerSettingsAsync()
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertGpuPowerSettingsAsync()
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> RevertSystemPowerSettingsAsync()
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private async Task<bool> SetPowerPlanAsync(string planName)
        {
            // Placeholder implementation
            await Task.Delay(100);
            return true;
        }

        private void LoadOptimizations()
        {
            try
            {
                _optimizations.Clear();

                string optimizationsPath = Path.Combine(_optimizationsDirectory, "hardware");
                if (!Directory.Exists(optimizationsPath))
                {
                    Directory.CreateDirectory(optimizationsPath);
                }

                foreach (string file in Directory.GetFiles(optimizationsPath, "*.json"))
                {
                    try
                    {
                        string json = File.ReadAllText(file);
                        var optimizations = JsonSerializer.Deserialize<List<HardwareSpecificOptimization>>(json);

                        if (optimizations != null)
                        {
                            _optimizations.AddRange(optimizations);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Log($"Error loading optimization from {file}: {ex.Message}", LogLevel.ERROR);
                    }
                }

                _logger.Log($"Loaded {_optimizations.Count} hardware-specific optimizations", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading optimizations: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void LoadPowerProfiles()
        {
            try
            {
                _powerProfiles.Clear();

                string profilesPath = Path.Combine(_optimizationsDirectory, "power");
                if (!Directory.Exists(profilesPath))
                {
                    Directory.CreateDirectory(profilesPath);
                }

                foreach (string file in Directory.GetFiles(profilesPath, "*.json"))
                {
                    try
                    {
                        string json = File.ReadAllText(file);
                        var profiles = JsonSerializer.Deserialize<List<PowerManagementProfile>>(json);

                        if (profiles != null)
                        {
                            _powerProfiles.AddRange(profiles);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Log($"Error loading power profile from {file}: {ex.Message}", LogLevel.ERROR);
                    }
                }

                _logger.Log($"Loaded {_powerProfiles.Count} power management profiles", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading power profiles: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void SaveOptimization(HardwareSpecificOptimization optimization)
        {
            try
            {
                string optimizationsPath = Path.Combine(_optimizationsDirectory, "hardware");
                if (!Directory.Exists(optimizationsPath))
                {
                    Directory.CreateDirectory(optimizationsPath);
                }

                string filePath = Path.Combine(optimizationsPath, $"{SanitizeFileName(optimization.Name)}.json");
                string json = JsonSerializer.Serialize(optimization, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving optimization: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void SavePowerProfile(PowerManagementProfile profile)
        {
            try
            {
                string profilesPath = Path.Combine(_optimizationsDirectory, "power");
                if (!Directory.Exists(profilesPath))
                {
                    Directory.CreateDirectory(profilesPath);
                }

                string filePath = Path.Combine(profilesPath, $"{SanitizeFileName(profile.Name)}.json");
                string json = JsonSerializer.Serialize(profile, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving power profile: {ex.Message}", LogLevel.ERROR);
            }
        }

        private string SanitizeFileName(string fileName)
        {
            return string.Join("_", fileName.Split(Path.GetInvalidFileNameChars()));
        }

        /// <summary>
        /// Handles incompatibility detection events
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event arguments</param>
        private void OnIncompatibilityDetected(object sender, IncompatibilityDetectedEventArgs e)
        {
            try
            {
                // Log the incompatibility
                string message = string.Empty;

                if (!string.IsNullOrEmpty(e.OptimizationName))
                {
                    message = $"Incompatibility detected for optimization '{e.OptimizationName}': {e.Reason}";
                    _logger.Log(message, LogLevel.WARNING);

                    // Find the optimization
                    var optimization = _optimizations.FirstOrDefault(o => o.Name == e.OptimizationName && o.IsEnabled);

                    if (optimization != null)
                    {
                        // Automatically revert the optimization if it's high confidence
                        if (e.Confidence == CompatibilityConfidence.High)
                        {
                            _logger.Log($"Automatically reverting incompatible optimization: {optimization.Name}", LogLevel.WARNING);

                            // Revert the optimization asynchronously
                            Task.Run(async () => await RevertOptimizationAsync(optimization));
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(e.PowerProfileName))
                {
                    message = $"Incompatibility detected for power profile '{e.PowerProfileName}': {e.Reason}";
                    _logger.Log(message, LogLevel.WARNING);

                    // Find the power profile
                    var profile = _powerProfiles.FirstOrDefault(p => p.Name == e.PowerProfileName && p.IsEnabled);

                    if (profile != null)
                    {
                        // Automatically revert the power profile if it's high confidence
                        if (e.Confidence == CompatibilityConfidence.High)
                        {
                            _logger.Log($"Automatically reverting incompatible power profile: {profile.Name}", LogLevel.WARNING);

                            // Revert the power profile asynchronously
                            Task.Run(async () => await RevertPowerProfileAsync(profile));
                        }
                    }
                }

                // Note: In a real implementation, we would show a notification to the user
                // For now, we'll just log the incompatibility
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling incompatibility detection: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets a value indicating whether compatibility checking is enabled
        /// </summary>
        public bool CheckCompatibilityBeforeApplying
        {
            get => _checkCompatibilityBeforeApplying;
            set => _checkCompatibilityBeforeApplying = value;
        }
    }

    /// <summary>
    /// Event arguments for optimization events
    /// </summary>
    public class OptimizationEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the optimization
        /// </summary>
        public HardwareSpecificOptimization Optimization { get; }

        /// <summary>
        /// Initializes a new instance of the OptimizationEventArgs class
        /// </summary>
        /// <param name="optimization">The optimization</param>
        public OptimizationEventArgs(HardwareSpecificOptimization optimization)
        {
            Optimization = optimization;
        }
    }

    /// <summary>
    /// Event arguments for power profile events
    /// </summary>
    public class PowerProfileEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the power profile
        /// </summary>
        public PowerManagementProfile Profile { get; }

        /// <summary>
        /// Initializes a new instance of the PowerProfileEventArgs class
        /// </summary>
        /// <param name="profile">The power profile</param>
        public PowerProfileEventArgs(PowerManagementProfile profile)
        {
            Profile = profile;
        }
    }
}




