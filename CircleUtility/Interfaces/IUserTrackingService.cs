namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for user tracking service
    /// </summary>
    public interface IUserTrackingService
    {
        /// <summary>
        /// Tracks user action
        /// </summary>
        /// <param name="action">The action to track</param>
        void TrackAction(string action);

        /// <summary>
        /// Gets user statistics
        /// </summary>
        /// <returns>User statistics</returns>
        object GetUserStatistics();
    }
}
