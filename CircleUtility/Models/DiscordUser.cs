#nullable enable
using System;
using System.ComponentModel;
using System.Collections.Generic;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a Discord user account
    /// </summary>
    public class DiscordUser : INotifyPropertyChanged
    {
        public string LastIpAddress { get; set; } = string.Empty;
        public List<string> IpAddresses { get; set; } = new List<string>();
        public string HardwareFingerprint { get; set; } = string.Empty;
        private string _username = string.Empty;
        private string _password = string.Empty;
        private string _displayName = string.Empty;
        private string _role = "User";
        private bool _isActive = true;
        private string _discordId = string.Empty;
        private DateTime _joinDate = DateTime.Now;
        private DateTime? _lastLogin;
        private string _email = string.Empty;
        private string _avatarUrl = string.Empty;
        private string _hardwareId = string.Empty;
        private List<UserPunishment> _punishmentHistory = new List<UserPunishment>();
        private DateTime _lastModified = DateTime.UtcNow;
        private string _phoneNumber = string.Empty;

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username
        {
            get => _username;
            set
            {
                _username = value ?? string.Empty;
                OnPropertyChanged(nameof(Username));
            }
        }

        /// <summary>
        /// Gets or sets the password
        /// </summary>
        public string Password
        {
            get => _password;
            set
            {
                _password = value ?? string.Empty;
                OnPropertyChanged(nameof(Password));
            }
        }

        /// <summary>
        /// Gets or sets the display name
        /// </summary>
        public string DisplayName
        {
            get => _displayName;
            set
            {
                _displayName = value ?? string.Empty;
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        /// <summary>
        /// Gets or sets the user role (User, Admin, Moderator)
        /// </summary>
        public string Role
        {
            get => _role;
            set
            {
                _role = value ?? "User";
                OnPropertyChanged(nameof(Role));
                OnPropertyChanged(nameof(IsAdmin));
            }
        }

        /// <summary>
        /// Gets whether the user is an admin
        /// </summary>
        public bool IsAdmin => Role.Equals("Admin", StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// Gets or sets whether the user account is active
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        /// <summary>
        /// Gets or sets the Discord ID
        /// </summary>
        public string DiscordId
        {
            get => _discordId;
            set
            {
                _discordId = value ?? string.Empty;
                OnPropertyChanged(nameof(DiscordId));
            }
        }

        /// <summary>
        /// Gets or sets the join date
        /// </summary>
        public DateTime JoinDate
        {
            get => _joinDate;
            set
            {
                _joinDate = value;
                OnPropertyChanged(nameof(JoinDate));
            }
        }

        /// <summary>
        /// Gets or sets the last login date
        /// </summary>
        public DateTime? LastLogin
        {
            get => _lastLogin;
            set
            {
                _lastLogin = value;
                OnPropertyChanged(nameof(LastLogin));
                OnPropertyChanged(nameof(LastLoginFormatted));
            }
        }

        /// <summary>
        /// Gets the formatted last login date
        /// </summary>
        public string LastLoginFormatted => LastLogin?.ToString("yyyy-MM-dd HH:mm") ?? "Never";

        /// <summary>
        /// Gets or sets the email address
        /// </summary>
        public string Email
        {
            get => _email;
            set
            {
                _email = value ?? string.Empty;
                OnPropertyChanged(nameof(Email));
            }
        }

        /// <summary>
        /// Gets or sets the avatar URL
        /// </summary>
        public string AvatarUrl
        {
            get => _avatarUrl;
            set
            {
                _avatarUrl = value ?? string.Empty;
                OnPropertyChanged(nameof(AvatarUrl));
            }
        }

        /// <summary>
        /// Gets or sets the hardware ID associated with the user
        /// </summary>
        public string HardwareId
        {
            get => _hardwareId;
            set
            {
                _hardwareId = value ?? string.Empty;
                OnPropertyChanged(nameof(HardwareId));
            }
        }

        /// <summary>
        /// Gets or sets the punishment history for the user
        /// </summary>
        public List<UserPunishment> PunishmentHistory
        {
            get => _punishmentHistory;
            set
            {
                _punishmentHistory = value ?? new List<UserPunishment>();
                OnPropertyChanged(nameof(PunishmentHistory));
            }
        }

        /// <summary>
        /// Gets or sets the last modified date for the user profile.
        /// </summary>
        public DateTime LastModified
        {
            get => _lastModified;
            set
            {
                _lastModified = value;
                OnPropertyChanged(nameof(LastModified));
            }
        }

        /// <summary>
        /// Gets or sets the phone number for the user.
        /// </summary>
        public string PhoneNumber
        {
            get => _phoneNumber;
            set
            {
                _phoneNumber = value ?? string.Empty;
                OnPropertyChanged(nameof(PhoneNumber));
            }
        }

        /// <summary>
        /// Gets the role color for UI display
        /// </summary>
        public string RoleColor => Role switch
        {
            "Admin" => "#FF00C8FF",
            "Moderator" => "#FFFF8C00",
            "User" => "#FF00FF00",
            _ => "#FFFFFF"
        };

        /// <summary>
        /// Gets the status color for UI display
        /// </summary>
        public string StatusColor => IsActive ? "#FF00FF00" : "#FFFF0000";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Creates a copy of the user
        /// </summary>
        public DiscordUser Clone()
        {
            return new DiscordUser
            {
                Username = this.Username,
                Password = this.Password,
                DisplayName = this.DisplayName,
                Role = this.Role,
                IsActive = this.IsActive,
                DiscordId = this.DiscordId,
                JoinDate = this.JoinDate,
                LastLogin = this.LastLogin,
                Email = this.Email,
                AvatarUrl = this.AvatarUrl,
                HardwareId = this.HardwareId,
                PunishmentHistory = new List<UserPunishment>(this.PunishmentHistory),
                LastModified = this.LastModified,
                PhoneNumber = this.PhoneNumber
            };
        }

        public override string ToString()
        {
            return $"{DisplayName} ({Username})";
        }
    }
}
