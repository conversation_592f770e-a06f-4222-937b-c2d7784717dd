using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    public interface IHardwareDetectionBadgeService
    {
        bool IsInitialized { get; }
        event EventHandler<HardwareBadgeUpdatedEventArgs> BadgesUpdated;
        Task RefreshBadgesAsync();
        HardwareBadgeInfo GetBadgeInfo(HardwareType hardwareType);
        Dictionary<HardwareType, HardwareBadgeInfo> GetAllBadgeInfo();
        bool IsNewlyDetected(HardwareType hardwareType);
        DateTime GetDetectionTime(HardwareType hardwareType);
        void ResetNewlyDetected(HardwareType hardwareType);
    }
} 