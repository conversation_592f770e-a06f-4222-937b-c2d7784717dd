using System;
using System.Windows;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for dialog services
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// Shows a thermal warning dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="profile">The power profile</param>
        /// <returns>True if the user chose to proceed, false otherwise</returns>
        bool ShowThermalWarningDialog(Window owner, PowerManagementProfile profile);

        /// <summary>
        /// Shows a message dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons to show</param>
        /// <returns>The dialog result</returns>
        MessageDialogResult ShowMessageDialog(Window owner, string title, string message, MessageDialogButtons buttons = MessageDialogButtons.OK);

        /// <summary>
        /// Shows an error dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons to show</param>
        /// <returns>The dialog result</returns>
        MessageDialogResult ShowErrorDialog(Window owner, string title, string message, MessageDialogButtons buttons = MessageDialogButtons.OK);

        /// <summary>
        /// Shows a warning dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons to show</param>
        /// <returns>The dialog result</returns>
        MessageDialogResult ShowWarningDialog(Window owner, string title, string message, MessageDialogButtons buttons = MessageDialogButtons.OK);

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <returns>True if confirmed, false otherwise</returns>
        bool ShowConfirmationDialog(Window owner, string title, string message);

        /// <summary>
        /// Shows a file open dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="filter">The file filter</param>
        /// <param name="initialDirectory">The initial directory</param>
        /// <returns>The selected file path, or null if cancelled</returns>
        string ShowOpenFileDialog(Window owner, string filter, string initialDirectory = null);

        /// <summary>
        /// Shows a file save dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="filter">The file filter</param>
        /// <param name="initialDirectory">The initial directory</param>
        /// <param name="defaultFileName">The default file name</param>
        /// <returns>The selected file path, or null if cancelled</returns>
        string ShowSaveFileDialog(Window owner, string filter, string initialDirectory = null, string defaultFileName = null);
    }

    /// <summary>
    /// Enum for message dialog buttons
    /// </summary>
    public enum MessageDialogButtons
    {
        /// <summary>
        /// OK button
        /// </summary>
        OK,

        /// <summary>
        /// OK and Cancel buttons
        /// </summary>
        OKCancel,

        /// <summary>
        /// Yes and No buttons
        /// </summary>
        YesNo,

        /// <summary>
        /// Yes, No, and Cancel buttons
        /// </summary>
        YesNoCancel
    }

    /// <summary>
    /// Enum for message dialog result
    /// </summary>
    public enum MessageDialogResult
    {
        /// <summary>
        /// OK result
        /// </summary>
        OK,

        /// <summary>
        /// Cancel result
        /// </summary>
        Cancel,

        /// <summary>
        /// Yes result
        /// </summary>
        Yes,

        /// <summary>
        /// No result
        /// </summary>
        No
    }
}
