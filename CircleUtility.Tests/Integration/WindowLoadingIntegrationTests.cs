using System;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using CircleUtility.Services;
using CircleUtility.Models;
using CircleUtility.Tests.Helpers;

namespace CircleUtility.Tests.Integration
{
    [TestFixture]
    public class WindowLoadingIntegrationTests
    {
        private WindowLoadingManager _windowLoadingManager = null!;
        private WindowHealthMonitor _windowHealthMonitor = null!;

        [SetUp]
        public void Setup()
        {
            // Enable test mode
            TestHelper.EnableTestMode();
            
            // Get instances
            _windowLoadingManager = WindowLoadingManager.Instance;
            _windowHealthMonitor = WindowHealthMonitor.Instance;
        }

        [TearDown]
        public void TearDown()
        {
            // Stop monitoring
            _windowHealthMonitor.StopMonitoring();
            
            // Disable test mode
            TestHelper.DisableTestMode();
        }

        [Test]
        public async Task CompleteWindowLoadingFlow_ShouldValidateAllSteps()
        {
            // Arrange
            var stepResults = new List<string>();
            var eventResults = new List<string>();
            
            _windowLoadingManager.StepCompleted += (sender, args) =>
            {
                stepResults.Add($"{args.Step.StepName}:{args.Result.IsSuccessful}");
                eventResults.Add($"StepCompleted:{args.Step.StepName}");
            };
            
            _windowLoadingManager.LoadingCompleted += (sender, args) =>
            {
                eventResults.Add($"LoadingCompleted:{args.SuccessfulSteps}:{args.FailedSteps}");
            };
            
            _windowLoadingManager.LoadingFailed += (sender, args) =>
            {
                eventResults.Add($"LoadingFailed:{args.Step.StepName}:{args.Result.ErrorMessage}");
            };

            // Act
            Console.WriteLine("Starting complete window loading flow test...");
            bool result = await _windowLoadingManager.StartLoadingSequence();

            // Assert
            Console.WriteLine($"Window loading sequence result: {result}");
            Console.WriteLine($"Step results count: {stepResults.Count}");
            Console.WriteLine($"Event results count: {eventResults.Count}");
            
            // Print all step results
            Console.WriteLine("Step Results:");
            foreach (var stepResult in stepResults)
            {
                Console.WriteLine($"  {stepResult}");
            }
            
            // Print all event results
            Console.WriteLine("Event Results:");
            foreach (var eventResult in eventResults)
            {
                Console.WriteLine($"  {eventResult}");
            }

            // Verify we got some results
            ClassicAssert.IsTrue(stepResults.Count > 0, "Should have step results");
            ClassicAssert.IsTrue(eventResults.Count > 0, "Should have event results");
            
            // Verify validation results
            var validationResults = _windowLoadingManager.GetValidationResults();
            ClassicAssert.IsTrue(validationResults.Count > 0, "Should have validation results");
            
            Console.WriteLine("Validation Results:");
            foreach (var kvp in validationResults)
            {
                Console.WriteLine($"  {kvp.Key}: {kvp.Value.IsSuccessful} ({kvp.Value.LoadingTime.TotalMilliseconds:F0}ms)");
                if (!kvp.Value.IsSuccessful)
                {
                    Console.WriteLine($"    Error: {kvp.Value.ErrorMessage}");
                }
            }
            
            // Verify total loading time
            var totalTime = _windowLoadingManager.GetTotalLoadingTime();
            Console.WriteLine($"Total loading time: {totalTime.TotalMilliseconds:F0}ms");
            ClassicAssert.IsTrue(totalTime.TotalMilliseconds >= 0, "Total time should be non-negative");
        }

        [Test]
        public async Task WindowHealthMonitoring_ShouldProvideHealthReport()
        {
            // Arrange
            var healthReports = new List<ApplicationLoadingHealthReport>();
            var criticalIssues = new List<List<string>>();
            
            _windowHealthMonitor.HealthUpdated += (sender, args) =>
            {
                healthReports.Add(args.HealthReport);
            };
            
            _windowHealthMonitor.CriticalIssueDetected += (sender, args) =>
            {
                criticalIssues.Add(args.CriticalIssues);
            };

            // Act
            Console.WriteLine("Starting window health monitoring test...");
            
            // Perform a health check
            var healthReport = await _windowHealthMonitor.PerformHealthCheck();
            
            // Start monitoring for a short time
            _windowHealthMonitor.StartMonitoring();
            await Task.Delay(2000); // Monitor for 2 seconds
            _windowHealthMonitor.StopMonitoring();

            // Assert
            ClassicAssert.IsNotNull(healthReport, "Health report should not be null");
            Console.WriteLine($"Health Score: {healthReport.HealthScore:F1}/100");
            Console.WriteLine($"Total Startup Time: {healthReport.TotalStartupTime.TotalMilliseconds:F0}ms");
            Console.WriteLine($"Window Health Infos: {healthReport.WindowHealthInfos.Count}");
            Console.WriteLine($"Critical Issues: {healthReport.CriticalIssues.Count}");
            Console.WriteLine($"Recommendations: {healthReport.Recommendations.Count}");
            Console.WriteLine($"Is Production Ready: {healthReport.IsProductionReady}");
            
            // Print window health details
            Console.WriteLine("Window Health Details:");
            foreach (var windowHealth in healthReport.WindowHealthInfos)
            {
                Console.WriteLine($"  {windowHealth.WindowName}:");
                Console.WriteLine($"    Responsive: {windowHealth.IsResponsive}");
                Console.WriteLine($"    Load Time: {windowHealth.LoadTime.TotalMilliseconds:F0}ms");
                Console.WriteLine($"    Memory Usage: {windowHealth.MemoryUsage / (1024 * 1024):F1}MB");
                Console.WriteLine($"    UI Elements: {windowHealth.UIElementCount}");
                Console.WriteLine($"    Animations Working: {windowHealth.AnimationsWorking}");
                Console.WriteLine($"    Warnings: {windowHealth.Warnings.Count}");
                
                foreach (var warning in windowHealth.Warnings)
                {
                    Console.WriteLine($"      - {warning}");
                }
            }
            
            // Print critical issues
            if (healthReport.CriticalIssues.Count > 0)
            {
                Console.WriteLine("Critical Issues:");
                foreach (var issue in healthReport.CriticalIssues)
                {
                    Console.WriteLine($"  - {issue}");
                }
            }
            
            // Print recommendations
            if (healthReport.Recommendations.Count > 0)
            {
                Console.WriteLine("Recommendations:");
                foreach (var recommendation in healthReport.Recommendations)
                {
                    Console.WriteLine($"  - {recommendation}");
                }
            }
            
            // Verify basic health report properties
            ClassicAssert.IsTrue(healthReport.HealthScore >= 0 && healthReport.HealthScore <= 100, 
                "Health score should be between 0 and 100");
            ClassicAssert.IsTrue(healthReport.TotalStartupTime.TotalMilliseconds >= 0, 
                "Startup time should be non-negative");
            ClassicAssert.IsNotNull(healthReport.WindowHealthInfos, "Window health infos should not be null");
            ClassicAssert.IsNotNull(healthReport.CriticalIssues, "Critical issues should not be null");
            ClassicAssert.IsNotNull(healthReport.Recommendations, "Recommendations should not be null");
        }

        [Test]
        public async Task WindowLoadingSequence_ShouldValidateExpectedSteps()
        {
            // Act
            bool result = await _windowLoadingManager.StartLoadingSequence();
            var validationResults = _windowLoadingManager.GetValidationResults();

            // Assert
            Console.WriteLine("Validating expected window loading steps...");
            
            // Expected steps based on our WindowLoadingManager implementation
            string[] expectedSteps = { "LoginWindow", "WelcomeScreen", "MainWindow", "Services", "UI_Components" };
            
            foreach (string expectedStep in expectedSteps)
            {
                ClassicAssert.IsTrue(validationResults.ContainsKey(expectedStep), 
                    $"Should validate {expectedStep}");
                
                var stepResult = validationResults[expectedStep];
                Console.WriteLine($"{expectedStep}: {(stepResult.IsSuccessful ? "PASS" : "FAIL")} ({stepResult.LoadingTime.TotalMilliseconds:F0}ms)");
                
                if (!stepResult.IsSuccessful)
                {
                    Console.WriteLine($"  Error: {stepResult.ErrorMessage}");
                    if (stepResult.Exception != null)
                    {
                        Console.WriteLine($"  Exception: {stepResult.Exception.Message}");
                    }
                }
                
                // Verify step has reasonable timing
                ClassicAssert.IsTrue(stepResult.LoadingTime.TotalMilliseconds >= 0, 
                    $"{expectedStep} should have non-negative loading time");
                ClassicAssert.IsTrue(stepResult.LoadingTime.TotalSeconds < 30, 
                    $"{expectedStep} should complete within 30 seconds");
            }
        }

        [Test]
        public void WindowLoadingConfiguration_ShouldHaveReasonableDefaults()
        {
            // Arrange & Act
            var config = new WindowLoadingConfiguration();

            // Assert
            Console.WriteLine("Validating window loading configuration defaults...");
            Console.WriteLine($"Enable Detailed Logging: {config.EnableDetailedLogging}");
            Console.WriteLine($"Show Progress To User: {config.ShowProgressToUser}");
            Console.WriteLine($"Max Total Loading Time: {config.MaxTotalLoadingTime.TotalMinutes:F1} minutes");
            Console.WriteLine($"Continue On Non-Required Failures: {config.ContinueOnNonRequiredFailures}");
            Console.WriteLine($"Retry Failed Steps: {config.RetryFailedSteps}");
            Console.WriteLine($"Max Retry Attempts: {config.MaxRetryAttempts}");
            Console.WriteLine($"Retry Delay: {config.RetryDelay.TotalSeconds:F1} seconds");
            
            ClassicAssert.IsTrue(config.EnableDetailedLogging, "Should enable detailed logging by default");
            ClassicAssert.IsTrue(config.ShowProgressToUser, "Should show progress to user by default");
            ClassicAssert.IsTrue(config.MaxTotalLoadingTime.TotalMinutes >= 1, "Should allow at least 1 minute for loading");
            ClassicAssert.IsTrue(config.ContinueOnNonRequiredFailures, "Should continue on non-required failures by default");
            ClassicAssert.IsTrue(config.RetryFailedSteps, "Should retry failed steps by default");
            ClassicAssert.IsTrue(config.MaxRetryAttempts >= 1, "Should allow at least 1 retry attempt");
            ClassicAssert.IsTrue(config.RetryDelay.TotalSeconds >= 0, "Retry delay should be non-negative");
        }

        [Test]
        public async Task WindowLoadingManager_ShouldHandleMultipleSequentialCalls()
        {
            // Act
            Console.WriteLine("Testing multiple sequential window loading calls...");
            
            bool result1 = await _windowLoadingManager.StartLoadingSequence();
            var results1 = _windowLoadingManager.GetValidationResults();
            var time1 = _windowLoadingManager.GetTotalLoadingTime();
            
            Console.WriteLine($"First call: {result1} ({time1.TotalMilliseconds:F0}ms, {results1.Count} results)");
            
            bool result2 = await _windowLoadingManager.StartLoadingSequence();
            var results2 = _windowLoadingManager.GetValidationResults();
            var time2 = _windowLoadingManager.GetTotalLoadingTime();
            
            Console.WriteLine($"Second call: {result2} ({time2.TotalMilliseconds:F0}ms, {results2.Count} results)");

            // Assert
            // Both calls should work
            Console.WriteLine("Both sequential calls should work independently");
            ClassicAssert.IsTrue(results1.Count > 0, "First call should have results");
            ClassicAssert.IsTrue(results2.Count > 0, "Second call should have results");
            
            // Results should be independent (second call should replace first)
            ClassicAssert.AreEqual(results1.Count, results2.Count, "Both calls should validate same number of steps");
        }
    }
}
