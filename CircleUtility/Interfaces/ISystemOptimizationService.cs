using System;
using System.Threading.Tasks;

namespace CircleUtility.Interfaces
{
    public interface ISystemOptimizationService
    {
        bool OptimizeNetworkSettings();
        bool OptimizePowerSettings();
        bool OptimizeInputDelay();
        bool RevertNetworkSettings();
        bool RevertPowerSettings();
        bool RevertInputDelay();
        bool OptimizeMouseSettings();
        bool OptimizeKeyboardSettings();
        bool OptimizeControllerSettings();
        bool OptimizeGpuSettings();
        bool OptimizeThermalSettings();
        bool RevertGpuSettings();
        bool RevertThermalSettings();
        bool RevertMouseSettings();
        bool RevertKeyboardSettings();
        double GetOptimizationScore();
        string GetSystemInfo();
        bool RunOptimization();
        Task<bool> RunOptimizationAsync();
        bool RevertOptimization();
        Task<bool> RevertOptimizationAsync();
    }
} 