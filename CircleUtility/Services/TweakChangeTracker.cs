using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;
using Microsoft.Win32;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for tracking changes made by tweaks
    /// </summary>
    public class TweakChangeTracker
    {
        private static TweakChangeTracker _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;
        private readonly string _changeHistoryDirectory;
        private readonly string _changeHistoryFile;
        private readonly string _backupDirectory;
        private List<TweakChange> _changeHistory;

        /// <summary>
        /// Gets the singleton instance of the TweakChangeTracker
        /// </summary>
        public static TweakChangeTracker Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TweakChangeTracker();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Event raised when a change is tracked
        /// </summary>
        public event EventHandler<TweakChangeEventArgs> ChangeTracked;

#nullable enable
        /// <summary>
        /// Event raised when a change is reverted
        /// </summary>
        public event EventHandler<TweakChangeEventArgs>? ChangeReverted;
#nullable restore

        // Ensure the event is used somewhere to avoid the warning
        private void RaiseChangeRevertedEvent(TweakChange change)
        {
            ChangeReverted?.Invoke(this, new TweakChangeEventArgs { Change = change });
        }

        /// <summary>
        /// Initializes a new instance of the TweakChangeTracker class
        /// </summary>
        private TweakChangeTracker()
        {
            _logger = LoggingService.Instance;
            _changeHistoryDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TweakHistory");
            _changeHistoryFile = Path.Combine(_changeHistoryDirectory, "change_history.json");
            _backupDirectory = Path.Combine(_changeHistoryDirectory, "Backups");
            _changeHistory = new List<TweakChange>();

            // Ensure directories exist
            if (!Directory.Exists(_changeHistoryDirectory))
            {
                Directory.CreateDirectory(_changeHistoryDirectory);
            }

            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Initializes the service
        /// </summary>
        private void Initialize()
        {
            try
            {
                // Load change history
                LoadChangeHistory();

                _logger.Log("TweakChangeTracker initialized", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing TweakChangeTracker: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Loads the change history from the history file
        /// </summary>
        private void LoadChangeHistory()
        {
            try
            {
                if (File.Exists(_changeHistoryFile))
                {
                    string json = File.ReadAllText(_changeHistoryFile);
                    _changeHistory = JsonSerializer.Deserialize<List<TweakChange>>(json) ?? new List<TweakChange>();
                    _logger.Log($"Loaded {_changeHistory.Count} tweak change entries", LogLevel.INFO);
                }
                else
                {
                    _changeHistory = new List<TweakChange>();
                    _logger.Log("No tweak change history file found, starting with empty history", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading tweak change history: {ex.Message}", LogLevel.ERROR);
                _changeHistory = new List<TweakChange>();
            }
        }

        /// <summary>
        /// Saves the change history to the history file
        /// </summary>
        private void SaveChangeHistory()
        {
            try
            {
                string json = JsonSerializer.Serialize(_changeHistory, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_changeHistoryFile, json);
                _logger.Log($"Saved {_changeHistory.Count} tweak change entries", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving tweak change history: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tracks a registry change
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The registry value name</param>
        /// <param name="originalValue">The original value</param>
        /// <param name="newValue">The new value</param>
        /// <param name="tweakName">The name of the tweak</param>
        /// <param name="tweakType">The type of tweak</param>
        /// <returns>The change ID</returns>
        public Guid TrackRegistryChange(string keyPath, string valueName, object originalValue, object newValue, string tweakName, OptimizationType tweakType)
        {
            try
            {
                // Create a new change entry
                var change = new TweakChange
                {
                    Id = Guid.NewGuid(),
                    Type = TweakChangeType.Registry,
                    TweakName = tweakName,
                    TweakType = tweakType,
                    AppliedDate = DateTime.Now,
                    IsActive = true,
                    RegistryKeyPath = keyPath,
                    RegistryValueName = valueName,
                    OriginalValue = originalValue?.ToString(),
                    NewValue = newValue?.ToString()
                };

                // Add to history
                _changeHistory.Add(change);
                SaveChangeHistory();

                // Raise event
                ChangeTracked?.Invoke(this, new TweakChangeEventArgs { Change = change });

                _logger.Log($"Tracked registry change: {keyPath}\\{valueName} for tweak {tweakName}", LogLevel.INFO);
                return change.Id;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error tracking registry change: {ex.Message}", LogLevel.ERROR);
                return Guid.Empty;
            }
        }

        /// <summary>
        /// Tracks a file system change
        /// </summary>
        /// <param name="filePath">The file path</param>
        /// <param name="backupFilePath">The backup file path</param>
        /// <param name="tweakName">The name of the tweak</param>
        /// <param name="tweakType">The type of tweak</param>
        /// <returns>The change ID</returns>
        public Guid TrackFileSystemChange(string filePath, string backupFilePath, string tweakName, OptimizationType tweakType)
        {
            try
            {
                // Create a new change entry
                var change = new TweakChange
                {
                    Id = Guid.NewGuid(),
                    Type = TweakChangeType.FileSystem,
                    TweakName = tweakName,
                    TweakType = tweakType,
                    AppliedDate = DateTime.Now,
                    IsActive = true,
                    FilePath = filePath,
                    BackupFilePath = backupFilePath
                };

                // Add to history
                _changeHistory.Add(change);
                SaveChangeHistory();

                // Raise event
                ChangeTracked?.Invoke(this, new TweakChangeEventArgs { Change = change });

                _logger.Log($"Tracked file system change: {filePath} for tweak {tweakName}", LogLevel.INFO);
                return change.Id;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error tracking file system change: {ex.Message}", LogLevel.ERROR);
                return Guid.Empty;
            }
        }

        /// <summary>
        /// Tracks a service configuration change
        /// </summary>
        /// <param name="serviceName">The service name</param>
        /// <param name="originalStartType">The original start type</param>
        /// <param name="newStartType">The new start type</param>
        /// <param name="tweakName">The name of the tweak</param>
        /// <param name="tweakType">The type of tweak</param>
        /// <returns>The change ID</returns>
        public Guid TrackServiceChange(string serviceName, int originalStartType, int newStartType, string tweakName, OptimizationType tweakType)
        {
            try
            {
                // Create a new change entry
                var change = new TweakChange
                {
                    Id = Guid.NewGuid(),
                    Type = TweakChangeType.Service,
                    TweakName = tweakName,
                    TweakType = tweakType,
                    AppliedDate = DateTime.Now,
                    IsActive = true,
                    ServiceName = serviceName,
                    OriginalValue = originalStartType.ToString(),
                    NewValue = newStartType.ToString()
                };

                // Add to history
                _changeHistory.Add(change);
                SaveChangeHistory();

                // Raise event
                ChangeTracked?.Invoke(this, new TweakChangeEventArgs { Change = change });

                _logger.Log($"Tracked service change: {serviceName} for tweak {tweakName}", LogLevel.INFO);
                return change.Id;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error tracking service change: {ex.Message}", LogLevel.ERROR);
                return Guid.Empty;
            }
        }

        /// <summary>
        /// Gets all active changes
        /// </summary>
        /// <returns>The active changes</returns>
        public List<TweakChange> GetActiveChanges()
        {
            return _changeHistory.Where(c => c.IsActive).ToList();
        }

        /// <summary>
        /// Gets all changes for a specific tweak
        /// </summary>
        /// <param name="tweakName">The tweak name</param>
        /// <returns>The changes for the tweak</returns>
        public List<TweakChange> GetChangesByTweak(string tweakName)
        {
            return _changeHistory.Where(c => c.TweakName == tweakName).ToList();
        }

        /// <summary>
        /// Gets all changes of a specific type
        /// </summary>
        /// <param name="tweakType">The tweak type</param>
        /// <returns>The changes of the specified type</returns>
        public List<TweakChange> GetChangesByType(OptimizationType tweakType)
        {
            return _changeHistory.Where(c => c.TweakType == tweakType).ToList();
        }

        /// <summary>
        /// Gets all changes in a specific time period
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <returns>The changes in the specified time period</returns>
        public List<TweakChange> GetChangesByTimePeriod(DateTime startDate, DateTime endDate)
        {
            return _changeHistory.Where(c => c.AppliedDate >= startDate && c.AppliedDate <= endDate).ToList();
        }
    }
}
