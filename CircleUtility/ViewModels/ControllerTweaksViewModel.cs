using CircleUtility.Commands;
using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Helpers;
using CircleUtility.Models;
using CircleUtility.Services;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the controller tweaks view
    /// </summary>
    public class ControllerTweaksViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly IUserTrackingService _userTracking;
        private SystemTweak _selectedTweak;
        private int _controllerPollingRate = 125; // Default polling rate

        /// <summary>
        /// Initializes a new instance of the ControllerTweaksViewModel class
        /// </summary>
        public ControllerTweaksViewModel(IUserTrackingService userTracking = null)
        {
            _logger = LoggingService.Instance;
            _userTracking = userTracking;

            // Initialize commands
            ApplyTweakCommand = new RelayCommand<SystemTweak>(ApplyTweak);
            RevertTweakCommand = new RelayCommand<SystemTweak>(RevertTweak);
            ApplyAllTweaksCommand = new RelayCommand(ApplyAllTweaks);
            RevertAllTweaksCommand = new RelayCommand(RevertAllTweaks);
            ApplyControllerOverclockCommand = new RelayCommand(ApplyControllerOverclock);
            ResetControllerOverclockCommand = new RelayCommand(ResetControllerOverclock);

            // Initialize controller tweaks
            InitializeControllerTweaks();

            _logger.Log("ControllerTweaksViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of controller tweaks
        /// </summary>
        public ObservableCollection<SystemTweak> ControllerTweaks { get; } = new ObservableCollection<SystemTweak>();

        /// <summary>
        /// Gets or sets the selected controller tweak
        /// </summary>
        public SystemTweak SelectedTweak
        {
            get => _selectedTweak;
            set => SetProperty(ref _selectedTweak, value);
        }

        /// <summary>
        /// Gets the apply tweak command
        /// </summary>
        public ICommand ApplyTweakCommand { get; }

        /// <summary>
        /// Gets the revert tweak command
        /// </summary>
        public ICommand RevertTweakCommand { get; }

        /// <summary>
        /// Gets the apply all tweaks command
        /// </summary>
        public ICommand ApplyAllTweaksCommand { get; }

        /// <summary>
        /// Gets the revert all tweaks command
        /// </summary>
        public ICommand RevertAllTweaksCommand { get; }

        /// <summary>
        /// Gets the apply controller overclock command
        /// </summary>
        public ICommand ApplyControllerOverclockCommand { get; }

        /// <summary>
        /// Gets the reset controller overclock command
        /// </summary>
        public ICommand ResetControllerOverclockCommand { get; }

        /// <summary>
        /// Gets or sets the controller polling rate
        /// </summary>
        public int ControllerPollingRate
        {
            get => _controllerPollingRate;
            set => SetProperty(ref _controllerPollingRate, value);
        }

        /// <summary>
        /// Initializes the controller tweaks
        /// </summary>
        private void InitializeControllerTweaks()
        {
            // Xbox controller tweaks
            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Optimize Xbox Controller Polling Rate",
                Description = "Increases the polling rate of Xbox controllers for faster response time.",
                Category = "Xbox",
                ApplyAction = () => OptimizeXboxControllerPollingRate(),
                RevertAction = () => RevertXboxControllerPollingRate(),
                RequiresAdmin = true
            });

            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Disable Xbox Controller Vibration",
                Description = "Disables controller vibration to reduce input latency.",
                Category = "Xbox",
                ApplyAction = () => DisableXboxControllerVibration(),
                RevertAction = () => EnableXboxControllerVibration(),
                RequiresAdmin = false
            });

            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Optimize Xbox Controller Deadzone",
                Description = "Sets optimal deadzone values for Xbox controllers.",
                Category = "Xbox",
                ApplyAction = () => OptimizeXboxControllerDeadzone(),
                RevertAction = () => RevertXboxControllerDeadzone(),
                RequiresAdmin = false
            });

            // PlayStation controller tweaks
            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Optimize PlayStation Controller Polling Rate",
                Description = "Increases the polling rate of PlayStation controllers for faster response time.",
                Category = "PlayStation",
                ApplyAction = () => OptimizePlayStationControllerPollingRate(),
                RevertAction = () => RevertPlayStationControllerPollingRate(),
                RequiresAdmin = true
            });

            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Disable PlayStation Controller Vibration",
                Description = "Disables controller vibration to reduce input latency.",
                Category = "PlayStation",
                ApplyAction = () => DisablePlayStationControllerVibration(),
                RevertAction = () => EnablePlayStationControllerVibration(),
                RequiresAdmin = false
            });

            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Optimize PlayStation Controller Deadzone",
                Description = "Sets optimal deadzone values for PlayStation controllers.",
                Category = "PlayStation",
                ApplyAction = () => OptimizePlayStationControllerDeadzone(),
                RevertAction = () => RevertPlayStationControllerDeadzone(),
                RequiresAdmin = false
            });

            // General controller tweaks
            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Disable Controller Power Saving",
                Description = "Disables power saving features for controllers to prevent input latency.",
                Category = "General",
                ApplyAction = () => DisableControllerPowerSaving(),
                RevertAction = () => EnableControllerPowerSaving(),
                RequiresAdmin = true
            });

            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Optimize USB Polling Rate",
                Description = "Increases USB polling rate for faster controller response time.",
                Category = "General",
                ApplyAction = () => OptimizeUsbPollingRate(),
                RevertAction = () => RevertUsbPollingRate(),
                RequiresAdmin = true
            });

            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Controller Overclocking",
                Description = "Overclocks controller polling rate to 1000Hz for minimal input latency (advanced users only).",
                Category = "General",
                ApplyAction = () => OverclockController(),
                RevertAction = () => RevertControllerOverclock(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            // Bluetooth controller tweaks
            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Optimize Bluetooth Controller Latency",
                Description = "Optimizes Bluetooth settings for lower controller latency.",
                Category = "Bluetooth",
                ApplyAction = () => OptimizeBluetoothControllerLatency(),
                RevertAction = () => RevertBluetoothControllerLatency(),
                RequiresAdmin = true
            });

            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Prioritize Bluetooth Traffic",
                Description = "Prioritizes Bluetooth traffic for controller input to reduce latency.",
                Category = "Bluetooth",
                ApplyAction = () => PrioritizeBluetoothTraffic(),
                RevertAction = () => RevertBluetoothTrafficPriority(),
                RequiresAdmin = true
            });

            // Game-specific controller tweaks
            ControllerTweaks.Add(new SystemTweak
            {
                Name = "Optimize Controller for FPS Games",
                Description = "Applies optimal controller settings for first-person shooter games.",
                Category = "Game-Specific",
                ApplyAction = () => OptimizeControllerForFpsGames(),
                RevertAction = () => RevertControllerGameSpecificSettings(),
                RequiresAdmin = false
            });
        }

        /// <summary>
        /// Applies a controller tweak
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        private void ApplyTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Applying controller tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Controller Tweak", $"Applied {tweak.Name}");

            // Execute the tweak's action
            tweak.ApplyAction?.Invoke();
            tweak.IsApplied = true;
        }

        /// <summary>
        /// Reverts a controller tweak
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        private void RevertTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Reverting controller tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Controller Tweak", $"Reverted {tweak.Name}");

            // Execute the tweak's revert action
            tweak.RevertAction?.Invoke();
            tweak.IsApplied = false;
        }

        /// <summary>
        /// Applies all controller tweaks
        /// </summary>
        private void ApplyAllTweaks()
        {
            _logger.Log("Applying all controller tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Controller Tweak", "Applied all tweaks");

            foreach (var tweak in ControllerTweaks)
            {
                tweak.ApplyAction?.Invoke();
                tweak.IsApplied = true;
            }
        }

        /// <summary>
        /// Reverts all controller tweaks
        /// </summary>
        private void RevertAllTweaks()
        {
            _logger.Log("Reverting all controller tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Controller Tweak", "Reverted all tweaks");

            foreach (var tweak in ControllerTweaks)
            {
                tweak.RevertAction?.Invoke();
                tweak.IsApplied = false;
            }
        }

        // Tweak implementation methods (these would be implemented in a full version)
        private void OptimizeXboxControllerPollingRate() { }
        private void RevertXboxControllerPollingRate() { }
        private void DisableXboxControllerVibration() { }
        private void EnableXboxControllerVibration() { }
        private void OptimizeXboxControllerDeadzone() { }
        private void RevertXboxControllerDeadzone() { }
        private void OptimizePlayStationControllerPollingRate() { }
        private void RevertPlayStationControllerPollingRate() { }
        private void DisablePlayStationControllerVibration() { }
        private void EnablePlayStationControllerVibration() { }
        private void OptimizePlayStationControllerDeadzone() { }
        private void RevertPlayStationControllerDeadzone() { }
        private void DisableControllerPowerSaving() { }
        private void EnableControllerPowerSaving() { }
        private void OptimizeUsbPollingRate() { }
        private void RevertUsbPollingRate() { }
        private void OverclockController() { }
        private void RevertControllerOverclock() { }
        private void OptimizeBluetoothControllerLatency() { }
        private void RevertBluetoothControllerLatency() { }
        private void PrioritizeBluetoothTraffic() { }
        private void RevertBluetoothTrafficPriority() { }
        private void OptimizeControllerForFpsGames() { }
        private void RevertControllerGameSpecificSettings() { }

        /// <summary>
        /// Applies the controller overclock with the selected polling rate
        /// </summary>
        private void ApplyControllerOverclock()
        {
            try
            {
                _logger.Log($"Applying controller overclock with polling rate: {ControllerPollingRate}Hz", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "Controller Tweak", $"Applied controller overclock: {ControllerPollingRate}Hz");

                // In a real implementation, this would modify registry settings or use a driver
                // to change the controller polling rate

                // Show a success message
                System.Windows.MessageBox.Show(
                    $"Controller polling rate has been set to {ControllerPollingRate}Hz.\n\n" +
                    $"Higher polling rates result in lower input latency but may increase CPU usage slightly.",
                    "Controller Overclock Applied",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying controller overclock: {ex.Message}", LogLevel.ERROR);
                System.Windows.MessageBox.Show(
                    $"An error occurred while applying controller overclock:\n{ex.Message}",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Resets the controller polling rate to the default value
        /// </summary>
        private void ResetControllerOverclock()
        {
            try
            {
                _logger.Log("Resetting controller polling rate to default", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "Controller Tweak", "Reset controller polling rate");

                // Reset the polling rate to the default value
                ControllerPollingRate = 125;

                // In a real implementation, this would modify registry settings or use a driver
                // to reset the controller polling rate

                // Show a success message
                System.Windows.MessageBox.Show(
                    "Controller polling rate has been reset to the default value (125Hz).",
                    "Controller Settings Reset",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error resetting controller polling rate: {ex.Message}", LogLevel.ERROR);
                System.Windows.MessageBox.Show(
                    $"An error occurred while resetting controller polling rate:\n{ex.Message}",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
    }
}



