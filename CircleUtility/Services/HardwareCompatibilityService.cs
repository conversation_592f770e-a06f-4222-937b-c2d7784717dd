// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for validating hardware compatibility with optimizations
    /// </summary>
    public class HardwareCompatibilityService : IHardwareCompatibilityService
    {
        private readonly LoggingService _logger;
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;
        private readonly Dictionary<string, CompatibilityResult> _compatibilityCache;
        private readonly TimeSpan _cacheExpirationTime = TimeSpan.FromMinutes(30);
        private HardwareInfo _currentHardwareInfo;
        private bool _isInitialized;

        /// <summary>
        /// Event raised when compatibility status changes for an optimization
        /// </summary>
        public event EventHandler<CompatibilityChangedEventArgs> CompatibilityChanged;

        /// <summary>
        /// Initializes a new instance of the HardwareCompatibilityService class
        /// </summary>
        /// <summary>
        /// Parameterless constructor for singleton pattern
        /// </summary>
        public HardwareCompatibilityService()
        {
            // Initialize with default values
        }

        private HardwareCompatibilityService(IHardwareDetectionService hardwareDetectionService, IPerformanceMonitoringService performanceMonitoringService)
        {
            _logger = LoggingService.Instance;
            _hardwareDetectionService = hardwareDetectionService;
            _performanceMonitoringService = performanceMonitoringService;
            _compatibilityCache = new Dictionary<string, CompatibilityResult>();
            _isInitialized = false;

            // Subscribe to performance monitoring events to detect hardware changes
            _performanceMonitoringService.MetricsUpdated += OnPerformanceMetricsUpdated;
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task InitializeAsync()
        {
            if (_isInitialized)
            {
                return;
            }

            try
            {
                _logger.Log("Initializing hardware compatibility service...", LogLevel.INFO);

                // Get initial hardware info
                _currentHardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync(true);

                if (_currentHardwareInfo == null)
                {
                    _logger.Log("Failed to get hardware info during initialization", LogLevel.ERROR);
                    return;
                }

                _isInitialized = true;
                _logger.Log("Hardware compatibility service initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing hardware compatibility service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Validates an optimization against the current hardware
        /// </summary>
        /// <param name="optimization">The optimization to validate</param>
        /// <returns>The compatibility result</returns>
        public async Task<CompatibilityResult> ValidateOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            return await ValidateOptimizationAsync(optimization, false);
        }

        /// <summary>
        /// Validates an optimization against the current hardware
        /// </summary>
        /// <param name="optimization">The optimization to validate</param>
        /// <param name="forceRefresh">Whether to force a refresh of the hardware info</param>
        /// <returns>The compatibility result</returns>
        public async Task<CompatibilityResult> ValidateOptimizationAsync(HardwareSpecificOptimization optimization, bool forceRefresh)
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
            }

            // Check cache first if not forcing refresh
            if (!forceRefresh && _compatibilityCache.TryGetValue(optimization.Name, out CompatibilityResult cachedResult))
            {
                if ((DateTime.Now - cachedResult.LastValidated) < _cacheExpirationTime)
                {
                    return cachedResult;
                }
            }

            // Get latest hardware info if needed
            if (forceRefresh || _currentHardwareInfo == null)
            {
                _currentHardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync(forceRefresh);

                if (_currentHardwareInfo == null)
                {
                    _logger.Log("Failed to get hardware info during validation", LogLevel.ERROR);
                    return new CompatibilityResult
                    {
                        IsCompatible = false,
                        Confidence = CompatibilityConfidence.Unknown,
                        IncompatibilityReason = "Unable to detect hardware information",
                        LastValidated = DateTime.Now
                    };
                }
            }

            // Validate the optimization
            CompatibilityResult result = ValidateOptimization(optimization, _currentHardwareInfo);

            // Cache the result
            _compatibilityCache[optimization.Name] = result;

            return result;
        }

        /// <summary>
        /// Validates a power profile against the current hardware
        /// </summary>
        /// <param name="profile">The power profile to validate</param>
        /// <returns>The compatibility result</returns>
        public async Task<CompatibilityResult> ValidatePowerProfileAsync(PowerManagementProfile profile)
        {
            return await ValidatePowerProfileAsync(profile, false);
        }

        /// <summary>
        /// Validates a power profile against the current hardware
        /// </summary>
        /// <param name="profile">The power profile to validate</param>
        /// <param name="forceRefresh">Whether to force a refresh of the hardware info</param>
        /// <returns>The compatibility result</returns>
        public async Task<CompatibilityResult> ValidatePowerProfileAsync(PowerManagementProfile profile, bool forceRefresh)
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
            }

            // Check cache first if not forcing refresh
            if (!forceRefresh && _compatibilityCache.TryGetValue(profile.Name, out CompatibilityResult cachedResult))
            {
                if ((DateTime.Now - cachedResult.LastValidated) < _cacheExpirationTime)
                {
                    return cachedResult;
                }
            }

            // Get latest hardware info if needed
            if (forceRefresh || _currentHardwareInfo == null)
            {
                _currentHardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync(forceRefresh);

                if (_currentHardwareInfo == null)
                {
                    _logger.Log("Failed to get hardware info during validation", LogLevel.ERROR);
                    return new CompatibilityResult
                    {
                        IsCompatible = false,
                        Confidence = CompatibilityConfidence.Unknown,
                        IncompatibilityReason = "Unable to detect hardware information",
                        LastValidated = DateTime.Now
                    };
                }
            }

            // Validate the power profile
            CompatibilityResult result = ValidatePowerProfile(profile, _currentHardwareInfo);

            // Cache the result
            _compatibilityCache[profile.Name] = result;

            return result;
        }

        /// <summary>
        /// Validates all optimizations against the current hardware
        /// </summary>
        /// <param name="optimizations">The optimizations to validate</param>
        /// <param name="forceRefresh">Whether to force a refresh of the hardware info</param>
        /// <returns>A dictionary of optimization names to compatibility results</returns>
        public async Task<Dictionary<string, CompatibilityResult>> ValidateOptimizationsAsync(
            IEnumerable<HardwareSpecificOptimization> optimizations, bool forceRefresh = false)
        {
            Dictionary<string, CompatibilityResult> results = new Dictionary<string, CompatibilityResult>();

            foreach (var optimization in optimizations)
            {
                results[optimization.Name] = await ValidateOptimizationAsync(optimization, forceRefresh);
                // Only force refresh for the first one
                forceRefresh = false;
            }

            return results;
        }

        /// <summary>
        /// Validates all power profiles against the current hardware
        /// </summary>
        /// <param name="profiles">The power profiles to validate</param>
        /// <param name="forceRefresh">Whether to force a refresh of the hardware info</param>
        /// <returns>A dictionary of profile names to compatibility results</returns>
        public async Task<Dictionary<string, CompatibilityResult>> ValidatePowerProfilesAsync(
            IEnumerable<PowerManagementProfile> profiles, bool forceRefresh = false)
        {
            Dictionary<string, CompatibilityResult> results = new Dictionary<string, CompatibilityResult>();

            foreach (var profile in profiles)
            {
                results[profile.Name] = await ValidatePowerProfileAsync(profile, forceRefresh);
                // Only force refresh for the first one
                forceRefresh = false;
            }

            return results;
        }

        /// <summary>
        /// Clears the compatibility cache
        /// </summary>
        public void ClearCache()
        {
            _compatibilityCache.Clear();
            _logger.Log("Compatibility cache cleared", LogLevel.INFO);
        }

        /// <summary>
        /// Validates an optimization against hardware info
        /// </summary>
        /// <param name="optimization">The optimization to validate</param>
        /// <param name="hardwareInfo">The hardware info</param>
        /// <returns>The compatibility result</returns>
        private CompatibilityResult ValidateOptimization(HardwareSpecificOptimization optimization, HardwareInfo hardwareInfo)
        {
            CompatibilityResult result = new CompatibilityResult
            {
                IsCompatible = true,
                Confidence = CompatibilityConfidence.High,
                LastValidated = DateTime.Now
            };

            try
            {
                // Validate based on hardware type
                switch (optimization.HardwareType)
                {
                    case HardwareType.CPU:
                        ValidateCpuOptimization(optimization, hardwareInfo.CPU, result);
                        break;
                    case HardwareType.GPU:
                        ValidateGpuOptimization(optimization, hardwareInfo.GPU, result);
                        break;
                    case HardwareType.RAM:
                        ValidateRamOptimization(optimization, hardwareInfo.RAM, result);
                        break;
                    case HardwareType.Storage:
                        ValidateStorageOptimization(optimization, hardwareInfo.Storage, result);
                        break;
                    case HardwareType.Network:
                        ValidateNetworkOptimization(optimization, hardwareInfo.NetworkAdapters, result);
                        break;
                    case HardwareType.Motherboard:
                        ValidateMotherboardOptimization(optimization, hardwareInfo.Motherboard, result);
                        break;
                    case HardwareType.System:
                        ValidateSystemOptimization(optimization, hardwareInfo, result);
                        break;
                    default:
                        result.IsCompatible = false;
                        result.Confidence = CompatibilityConfidence.Unknown;
                        result.IncompatibilityReason = "Unknown hardware type";
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error validating optimization {optimization.Name}: {ex.Message}", LogLevel.ERROR);
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.Unknown;
                result.IncompatibilityReason = $"Error during validation: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// Validates a power profile against hardware info
        /// </summary>
        /// <param name="profile">The power profile to validate</param>
        /// <param name="hardwareInfo">The hardware info</param>
        /// <returns>The compatibility result</returns>
        private CompatibilityResult ValidatePowerProfile(PowerManagementProfile profile, HardwareInfo hardwareInfo)
        {
            CompatibilityResult result = new CompatibilityResult
            {
                IsCompatible = true,
                Confidence = CompatibilityConfidence.High,
                LastValidated = DateTime.Now
            };

            try
            {
                // Check hardware compatibility list
                if (profile.HardwareCompatibility.Count > 0)
                {
                    bool isCompatible = false;
                    string cpuModel = hardwareInfo.CPU?.Name ?? string.Empty;
                    string gpuModel = hardwareInfo.GPU?.Name ?? string.Empty;

                    foreach (string hardware in profile.HardwareCompatibility)
                    {
                        if (cpuModel.Contains(hardware, StringComparison.OrdinalIgnoreCase) ||
                            gpuModel.Contains(hardware, StringComparison.OrdinalIgnoreCase))
                        {
                            isCompatible = true;
                            break;
                        }
                    }

                    if (!isCompatible)
                    {
                        result.IsCompatible = false;
                        result.Confidence = CompatibilityConfidence.Medium;
                        result.IncompatibilityReason = "This profile is not specifically designed for your hardware";
                    }
                }

                // Validate CPU settings
                ValidateCpuPowerSettings(profile.CpuSettings, hardwareInfo.CPU, result);

                // Validate GPU settings
                ValidateGpuPowerSettings(profile.GpuSettings, hardwareInfo.GPU, result);

                // Validate system settings
                ValidateSystemPowerSettings(profile.SystemSettings, hardwareInfo, result);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error validating power profile {profile.Name}: {ex.Message}", LogLevel.ERROR);
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.Unknown;
                result.IncompatibilityReason = $"Error during validation: {ex.Message}";
            }

            return result;
        }

        // Implementation of validation methods would go here
        // For brevity, we'll just add placeholder methods

        private void ValidateCpuOptimization(HardwareSpecificOptimization optimization, CPUInfo cpuInfo, CompatibilityResult result)
        {
            // Check manufacturer
            if (!string.IsNullOrEmpty(optimization.Manufacturer) &&
                !cpuInfo.Manufacturer.Contains(optimization.Manufacturer, StringComparison.OrdinalIgnoreCase))
            {
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.High;
                result.IncompatibilityReason = $"This optimization is for {optimization.Manufacturer} CPUs only";
                return;
            }

            // Check model pattern
            if (!string.IsNullOrEmpty(optimization.ModelPattern) &&
                !Regex.IsMatch(cpuInfo.Name, optimization.ModelPattern, RegexOptions.IgnoreCase))
            {
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.High;
                result.IncompatibilityReason = $"This optimization is not compatible with your CPU model";
                return;
            }

            // Check specific models
            if (optimization.SpecificModels.Count > 0 &&
                !optimization.SpecificModels.Any(m => cpuInfo.Name.Contains(m, StringComparison.OrdinalIgnoreCase)))
            {
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.High;
                result.IncompatibilityReason = $"This optimization is only for specific CPU models";
                return;
            }
        }

        private void ValidateGpuOptimization(HardwareSpecificOptimization optimization, GPUInfo gpuInfo, CompatibilityResult result)
        {
            // Check manufacturer/vendor
            if (!string.IsNullOrEmpty(optimization.Manufacturer) &&
                !gpuInfo.Vendor.ToString().Contains(optimization.Manufacturer, StringComparison.OrdinalIgnoreCase))
            {
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.High;
                result.IncompatibilityReason = $"This optimization is for {optimization.Manufacturer} GPUs only";
                return;
            }

            // Check model pattern
            if (!string.IsNullOrEmpty(optimization.ModelPattern) &&
                !Regex.IsMatch(gpuInfo.Name, optimization.ModelPattern, RegexOptions.IgnoreCase))
            {
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.High;
                result.IncompatibilityReason = $"This optimization is not compatible with your GPU model";
                return;
            }

            // Check specific models
            if (optimization.SpecificModels.Count > 0 &&
                !optimization.SpecificModels.Any(m => gpuInfo.Name.Contains(m, StringComparison.OrdinalIgnoreCase)))
            {
                result.IsCompatible = false;
                result.Confidence = CompatibilityConfidence.High;
                result.IncompatibilityReason = $"This optimization is only for specific GPU models";
                return;
            }
        }

        private void ValidateRamOptimization(HardwareSpecificOptimization optimization, RAMInfo ramInfo, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void ValidateStorageOptimization(HardwareSpecificOptimization optimization, List<StorageInfo> storageInfo, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void ValidateNetworkOptimization(HardwareSpecificOptimization optimization, List<NetworkAdapterInfo> networkAdapters, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void ValidateMotherboardOptimization(HardwareSpecificOptimization optimization, MotherboardInfo motherboardInfo, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void ValidateSystemOptimization(HardwareSpecificOptimization optimization, HardwareInfo hardwareInfo, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void ValidateCpuPowerSettings(CpuPowerSettings settings, CPUInfo cpuInfo, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void ValidateGpuPowerSettings(GpuPowerSettings settings, GPUInfo gpuInfo, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void ValidateSystemPowerSettings(SystemPowerSettings settings, HardwareInfo hardwareInfo, CompatibilityResult result)
        {
            // Placeholder implementation
        }

        private void OnPerformanceMetricsUpdated(object sender, PerformanceMetricsEventArgs e)
        {
            // Check for significant hardware changes that might affect compatibility
            // This is a simplified implementation
            Task.Run(async () =>
            {
                try
                {
                    // Only check occasionally to avoid excessive hardware detection
                    if (DateTime.Now.Second % 30 == 0) // Check every 30 seconds
                    {
                        HardwareInfo newHardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync(true);

                        if (newHardwareInfo != null && HasSignificantHardwareChanges(_currentHardwareInfo, newHardwareInfo))
                        {
                            _logger.Log("Significant hardware changes detected, clearing compatibility cache", LogLevel.INFO);
                            _currentHardwareInfo = newHardwareInfo;
                            ClearCache();

                            // Raise event to notify listeners
                            CompatibilityChanged?.Invoke(this, new CompatibilityChangedEventArgs
                            {
                                Reason = "Hardware configuration has changed"
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error checking for hardware changes: {ex.Message}", LogLevel.ERROR);
                }
            });
        }

        private bool HasSignificantHardwareChanges(HardwareInfo oldInfo, HardwareInfo newInfo)
        {
            if (oldInfo == null || newInfo == null)
            {
                return true;
            }

            // Check for significant changes that would affect compatibility
            // This is a simplified implementation

            // Check CPU changes
            if (oldInfo.CPU?.Name != newInfo.CPU?.Name ||
                oldInfo.CPU?.Manufacturer != newInfo.CPU?.Manufacturer)
            {
                return true;
            }

            // Check GPU changes
            if (oldInfo.GPU?.Name != newInfo.GPU?.Name ||
                oldInfo.GPU?.Vendor != newInfo.GPU?.Vendor)
            {
                return true;
            }

            // Check RAM changes
            if (oldInfo.RAM?.TotalCapacity != newInfo.RAM?.TotalCapacity)
            {
                return true;
            }

            return false;
        }
    }

    /// <summary>
    /// Compatibility confidence levels
    /// </summary>
    public enum CompatibilityConfidence
    {
        /// <summary>
        /// Unknown confidence
        /// </summary>
        Unknown,

        /// <summary>
        /// Low confidence
        /// </summary>
        Low,

        /// <summary>
        /// Medium confidence
        /// </summary>
        Medium,

        /// <summary>
        /// High confidence
        /// </summary>
        High
    }

    /// <summary>
    /// Result of a compatibility check
    /// </summary>
    public class CompatibilityResult
    {
        /// <summary>
        /// Gets or sets a value indicating whether the optimization is compatible
        /// </summary>
        public bool IsCompatible { get; set; }

        /// <summary>
        /// Gets or sets the confidence level of the compatibility check
        /// </summary>
        public CompatibilityConfidence Confidence { get; set; }

        /// <summary>
        /// Gets or sets the reason for incompatibility
        /// </summary>
        public string IncompatibilityReason { get; set; }

        /// <summary>
        /// Gets or sets the date the compatibility was last validated
        /// </summary>
        public DateTime LastValidated { get; set; }
    }

    /// <summary>
    /// Event arguments for compatibility changed events
    /// </summary>
    public class CompatibilityChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the reason for the compatibility change
        /// </summary>
        public string Reason { get; set; }
    }
}







