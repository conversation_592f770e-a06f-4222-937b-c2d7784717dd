using System;
using System.Globalization;
using System.Windows.Data;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts a tool name to a description
    /// </summary>
    public class ToolToDescriptionConverter : IValueConverter
    {
        /// <summary>
        /// Converts a tool name to its description
        /// </summary>
        /// <param name="value">The tool name to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>The tool description</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string toolName)
            {
                switch (toolName)
                {
                    case "Disk Cleanup":
                        return "Cleans up temporary files, system caches, and other unnecessary files to free up disk space.";
                    case "Registry Cleaner":
                        return "Scans and repairs registry issues to improve system stability and performance.";
                    case "Driver Updater":
                        return "Checks for outdated drivers and updates them to improve hardware compatibility and performance.";
                    case "Network Optimizer":
                        return "Optimizes network settings for lower latency and better throughput in games.";
                    case "Startup Manager":
                        return "Manages startup programs to improve boot time and reduce background processes.";
                    case "Process Priority Manager":
                        return "Adjusts process priorities to ensure games and important applications get the resources they need.";
                    case "Memory Optimizer":
                        return "Optimizes memory usage to reduce stuttering and improve performance in games.";
                    case "Temp File Cleaner":
                        return "Cleans temporary files that can accumulate and slow down your system.";
                    case "System File Checker":
                        return "Scans and repairs corrupted system files to improve stability.";
                    case "DirectX Diagnostic":
                        return "Runs DirectX diagnostic tool to identify and fix graphics-related issues.";
                    case "GPU Driver Cleaner":
                        return "Completely removes GPU drivers for a clean reinstallation.";
                    case "Windows Update Reset":
                        return "Resets Windows Update components to fix update-related issues.";
                    default:
                        return "Select a tool to see its description.";
                }
            }
            
            return "Select a tool to see its description.";
        }

        /// <summary>
        /// Converts a description back to a tool name (not implemented)
        /// </summary>
        /// <param name="value">The description to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>Not implemented</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
