using CircleUtility.Interfaces;
using System;
using CircleUtility.Models;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using CircleUtility.Controls;
using CircleUtility.Commands;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the main window
    /// </summary>
    public class MainViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTracking;
        private readonly DiscordService _discord;

        // Hardware services
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IHardwareOptimizationService _hardwareOptimizationService;
        private readonly IHardwareCompatibilityService _hardwareCompatibilityService;
        private readonly IHardwareRecommendationService _hardwareRecommendationService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;
        private readonly IBenchmarkingService _benchmarkingService;
        

        private object _currentView;
        private string _username;
        private bool _isDevMode;
        private bool _isDiscordConnected;
        private ObservableCollection<MenuItemViewModel> _menuItems;
        private string _statusMessage;
        private bool _isStatusVisible;

        /// <summary>
        /// Initializes a new instance of the MainViewModel class
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="hardwareDetectionService">The hardware detection service</param>
        /// <param name="hardwareOptimizationService">The hardware optimization service</param>
        /// <param name="hardwareCompatibilityService">The hardware compatibility service</param>
        /// <param name="hardwareRecommendationService">The hardware recommendation service</param>
        /// <param name="performanceMonitoringService">The performance monitoring service</param>
        /// <param name="benchmarkingService">The benchmarking service</param>
        public MainViewModel(
            string username,
            IHardwareDetectionService hardwareDetectionService,
            IHardwareOptimizationService hardwareOptimizationService,
            IHardwareCompatibilityService hardwareCompatibilityService,
            IHardwareRecommendationService hardwareRecommendationService,
            IPerformanceMonitoringService performanceMonitoringService,
            IBenchmarkingService benchmarkingService)
        {
            _logger = LoggingService.Instance;
            _userTracking = UserTrackingService.Instance;
            _discord = DiscordService.Instance;

            // Store the injected services
            _hardwareDetectionService = hardwareDetectionService ?? throw new ArgumentNullException(nameof(hardwareDetectionService));
            _hardwareOptimizationService = hardwareOptimizationService ?? throw new ArgumentNullException(nameof(hardwareOptimizationService));
            _hardwareCompatibilityService = hardwareCompatibilityService ?? throw new ArgumentNullException(nameof(hardwareCompatibilityService));
            _hardwareRecommendationService = hardwareRecommendationService ?? throw new ArgumentNullException(nameof(hardwareRecommendationService));
            _performanceMonitoringService = performanceMonitoringService ?? throw new ArgumentNullException(nameof(performanceMonitoringService));
            _benchmarkingService = benchmarkingService ?? throw new ArgumentNullException(nameof(benchmarkingService));

            Username = username;

            // Get dev mode setting from configuration
            var configManager = ConfigurationManager.Instance;
            IsDevMode = (bool)configManager.GetSetting("advanced", "enabledebugmode");

            // Log the mode we're running in
            _logger.Log($"Application running in {(IsDevMode ? "DEV" : "LIVE")} mode", LogLevel.INFO);

            // Initialize Discord with default settings
            string discordWebhook = "https://discord.com/api/webhooks/1373372270596329584/iu4SrIROsxYYZNB66gpmuRiwovhX1uNf7lS-s21szYkmAamf2ppJl9wdghVkzc50CIGa";
            string discordUserId = "200121271449681920";
            _discord.Initialize(discordWebhook, discordUserId);
            _discord.StartListening();

            // Set Discord as connected
            IsDiscordConnected = true;

            IsStatusVisible = false;
            StatusMessage = string.Empty;

            // Initialize commands
            NavigateCommand = new RelayCommand<string>(Navigate);

            // Initialize menu items
            InitializeMenuItems();

            // Set initial view
            CurrentView = new DashboardViewModel(
                _hardwareDetectionService,
                _performanceMonitoringService,
                _hardwareOptimizationService,
                _hardwareRecommendationService,
                _hardwareCompatibilityService);

            // Set up Discord status check timer
            SetupDiscordStatusCheck();

            _logger.Log($"MainViewModel initialized for user: {Username}", LogLevel.INFO);
        }

        /// <summary>
        /// Gets or sets the current view
        /// </summary>
        public object CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the application is in dev mode
        /// </summary>
        public bool IsDevMode
        {
            get => _isDevMode;
            set => SetProperty(ref _isDevMode, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether Discord is connected
        /// </summary>
        public bool IsDiscordConnected
        {
            get => _isDiscordConnected;
            set => SetProperty(ref _isDiscordConnected, value);
        }

        /// <summary>
        /// Gets the menu items
        /// </summary>
        public ObservableCollection<MenuItemViewModel> MenuItems
        {
            get => _menuItems;
            private set => SetProperty(ref _menuItems, value);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status message is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets a value indicating whether the status is a success
        /// </summary>
        public bool IsStatusSuccess { get; private set; }

        /// <summary>
        /// Gets the navigation command
        /// </summary>
        public ICommand NavigateCommand { get; }

        /// <summary>
        /// Navigates to a view
        /// </summary>
        /// <param name="viewName">The name of the view to navigate to</param>
        private void Navigate(string viewName)
        {
            _logger.Log($"Navigating to: {viewName}", LogLevel.INFO);

            // Record user activity
            _userTracking.AddUserActivity(Username, "Navigation", $"Navigated to {viewName}");

            // Navigate to the selected view
            switch (viewName)
            {
                case "Dashboard":
                    CurrentView = new DashboardViewModel(
                        _hardwareDetectionService,
                        _performanceMonitoringService,
                        _hardwareOptimizationService,
                        _hardwareRecommendationService,
                        _hardwareCompatibilityService);
                    break;
                case "System Tweaks":
                    CurrentView = new SystemTweaksViewModel();
                    break;
                case "Game Profiles":
                    CurrentView = new GameProfilesViewModel();
                    break;
                case "Input Delay":
                    CurrentView = new InputDelayViewModel();
                    break;
                case "Controller Tweaks":
                    CurrentView = new ControllerTweaksViewModel();
                    break;
                case "GPU Optimization":
                    CurrentView = new GpuOptimizationViewModel();
                    break;
                case "Benchmark":
                    CurrentView = new BenchmarkViewModel();
                    break;
                case "Tools":
                    CurrentView = new ToolsViewModel();
                    break;
                case "Debloat":
                    CurrentView = new DebloatViewModel();
                    break;
                case "Settings":
                    CurrentView = new SettingsViewModel();
                    break;
                case "Discord":
                    CurrentView = new DiscordViewModel();
                    break;
                case "Revert Tweaks":
                    CurrentView = new RevertTweaksViewModel();
                    break;
                case "Admin":
                    // Show the enhanced admin login screen
                    ShowAdminLoginScreen();
                    break;
                case "Performance Optimizer":
                    CurrentView = new PerformanceOptimizerViewModel();
                    break;
                default:
                    _logger.Log($"Unknown view: {viewName}", LogLevel.WARNING);
                    // Default to Dashboard if view not found
                    CurrentView = new DashboardViewModel(
                        _hardwareDetectionService,
                        _performanceMonitoringService,
                        _hardwareOptimizationService,
                        _hardwareRecommendationService,
                        _hardwareCompatibilityService);
                    break;
            }
        }

        /// <summary>
        /// Initializes the menu items
        /// </summary>
        private void InitializeMenuItems()
        {
            try
            {
                MenuItems = new ObservableCollection<MenuItemViewModel>
                {
                    new MenuItemViewModel(">> Dashboard", "Dashboard", NavigateCommand),
                    new MenuItemViewModel("* System Tweaks", "System Tweaks", NavigateCommand),
                    new MenuItemViewModel("+ Game Profiles", "Game Profiles", NavigateCommand),
                    new MenuItemViewModel("! Input Delay", "Input Delay", NavigateCommand),
                    new MenuItemViewModel("^ Controller Tweaks", "Controller Tweaks", NavigateCommand),
                    new MenuItemViewModel("~ GPU Optimization", "GPU Optimization", NavigateCommand),
                    new MenuItemViewModel("# Benchmark", "Benchmark", NavigateCommand),
                    new MenuItemViewModel("= Tools", "Tools", NavigateCommand),
                    new MenuItemViewModel("- Debloat", "Debloat", NavigateCommand),
                    new MenuItemViewModel("$ Discord", "Discord", NavigateCommand),
                    new MenuItemViewModel("@ Settings", "Settings", NavigateCommand),
                    new MenuItemViewModel("& Revert Tweaks", "Revert Tweaks", NavigateCommand),
                    new MenuItemViewModel("% Admin", "Admin", NavigateCommand),
                    new MenuItemViewModel("? Performance Optimizer", "Performance Optimizer", NavigateCommand)
                };

                _logger.Log("Menu items initialized", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing menu items: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Shows a status message
        /// </summary>
        /// <param name="message">The message to show</param>
        /// <param name="isSuccess">Whether the message is a success message</param>
        public void ShowStatusMessage(string message, bool isSuccess)
        {
            try
            {
                StatusMessage = message;
                IsStatusSuccess = isSuccess;
                IsStatusVisible = true;

                // Create a timer to hide the message after 5 seconds
                var timer = new DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(5);
                timer.Tick += (sender, e) =>
                {
                    IsStatusVisible = false;
                    timer.Stop();
                };
                timer.Start();

                // Also show as a notification
                var notificationService = NotificationService.Instance;
                if (isSuccess)
                {
                    notificationService.AddSuccessNotification("Success", message);
                }
                else
                {
                    notificationService.AddErrorNotification("Error", message);
                }

                _logger.Log($"Status message shown: {message}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing status message: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Shows a test notification of each type
        /// </summary>
        public void ShowTestNotifications()
        {
            try
            {
                var notificationService = NotificationService.Instance;

                // Show info notification
                notificationService.AddInfoNotification("Information", "This is an information notification");

                // Show success notification after a delay
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    notificationService.AddSuccessNotification("Success", "Operation completed successfully");
                }), DispatcherPriority.Background, null);

                // Show warning notification after a delay
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    notificationService.AddWarningNotification("Warning", "This action may have consequences");
                }), DispatcherPriority.Background, null);

                // Show error notification after a delay
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    notificationService.AddErrorNotification("Error", "An error occurred during the operation");
                }), DispatcherPriority.Background, null);

                _logger.Log("Test notifications shown", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing test notifications: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Checks if the user has admin privileges
        /// </summary>
        /// <param name="username">The username to check</param>
        /// <returns>True if the user has admin privileges, false otherwise</returns>
        private bool IsAdminUser(string username)
        {
            try
            {
                // Use the SecurityService to check if the user is an admin
                return SecurityService.Instance.IsAdmin(username);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking admin privileges: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Sets up the Discord status check
        /// </summary>
        private void SetupDiscordStatusCheck()
        {
            try
            {
                // Create a timer to check Discord status
                var timer = new DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(5);
                timer.Tick += (sender, e) =>
                {
                    // Update Discord connection status
                    IsDiscordConnected = _discord.IsEnabled && _discord.IsListening;
                };
                timer.Start();

                _logger.Log("Discord status check timer started", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error setting up Discord status check: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Shows the enhanced admin login screen
        /// </summary>
        private void ShowAdminLoginScreen()
        {
            try
            {
                _logger.Log("Showing admin login screen", LogLevel.INFO);

                // Create the enhanced login screen
                var loginScreen = new EnhancedLoginScreen();

                // Create a window to host the login screen
                var loginWindow = new Window
                {
                    Title = "Admin Authentication",
                    Width = 400,
                    Height = 550,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Application.Current.MainWindow,
                    ResizeMode = ResizeMode.NoResize,
                    Background = Brushes.Transparent,
                    WindowStyle = WindowStyle.None,
                    AllowsTransparency = true,
                    Content = loginScreen
                };

                // Show the dialog
                bool? result = loginWindow.ShowDialog();

                // Check if authentication was successful
                if (loginScreen.LoginSuccessful)
                {
                    // Get the session token
                    string sessionToken = loginScreen.SessionToken;

                    // Validate the session
                    if (SessionManager.Instance.ValidateSession(sessionToken))
                    {
                        // Set the admin view
                        CurrentView = new AdminViewModel();
                        _logger.Log("Admin authentication successful", LogLevel.SUCCESS);
                    }
                    else
                    {
                        // Session validation failed
                        CurrentView = new DashboardViewModel(
                            _hardwareDetectionService,
                            _performanceMonitoringService,
                            _hardwareOptimizationService,
                            _hardwareRecommendationService,
                            _hardwareCompatibilityService);
                        _logger.Log("Admin session validation failed", LogLevel.WARNING);
                        ShowStatusMessage("Session validation failed. Please try again.", false);
                    }
                }
                else
                {
                    // Authentication cancelled or failed
                    CurrentView = new DashboardViewModel(
                        _hardwareDetectionService,
                        _performanceMonitoringService,
                        _hardwareOptimizationService,
                        _hardwareRecommendationService,
                        _hardwareCompatibilityService);
                    _logger.Log("Admin authentication cancelled", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing admin login screen: {ex.Message}", LogLevel.ERROR);
                ShowStatusMessage($"Error: {ex.Message}", false);

                // Return to Dashboard
                CurrentView = new DashboardViewModel(
                    _hardwareDetectionService,
                    _performanceMonitoringService,
                    _hardwareOptimizationService,
                    _hardwareRecommendationService,
                    _hardwareCompatibilityService);
            }
        }
    }
}








