<UserControl x:Class="CircleUtility.Controls.AnimatedCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="300"
             Loaded="UserControl_Loaded">
    
    <UserControl.Resources>
        <!-- Reveal animation -->
        <Storyboard x:Key="RevealAnimation">
            <DoubleAnimation Storyboard.TargetName="MainBorder"
                            Storyboard.TargetProperty="Opacity"
                            From="0" To="1" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainBorder"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                            From="50" To="0" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
        
        <!-- Hover animation -->
        <Storyboard x:Key="HoverAnimation">
            <DoubleAnimation Storyboard.TargetName="GlowEffect"
                            Storyboard.TargetProperty="BlurRadius"
                            From="5" To="15" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="GlowEffect"
                            Storyboard.TargetProperty="Opacity"
                            From="0.5" To="0.8" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="MainBorder"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                            From="0" To="-5" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
        
        <!-- Unhover animation -->
        <Storyboard x:Key="UnhoverAnimation">
            <DoubleAnimation Storyboard.TargetName="GlowEffect"
                            Storyboard.TargetProperty="BlurRadius"
                            From="15" To="5" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="GlowEffect"
                            Storyboard.TargetProperty="Opacity"
                            From="0.8" To="0.5" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="MainBorder"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                            From="-5" To="0" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>
    
    <Border x:Name="MainBorder"
           Background="#FF050A0F"
           BorderBrush="#FF00C8FF"
           BorderThickness="1"
           CornerRadius="5"
           Opacity="0"
           MouseEnter="MainBorder_MouseEnter"
           MouseLeave="MainBorder_MouseLeave">
        <Border.RenderTransform>
            <TranslateTransform Y="0"/>
        </Border.RenderTransform>
        <Border.Effect>
            <DropShadowEffect x:Name="GlowEffect"
                             Color="#FF00C8FF"
                             BlurRadius="5"
                             ShadowDepth="0"
                             Opacity="0.5"/>
        </Border.Effect>
        
        <Grid Margin="15">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <TextBlock x:Name="HeaderText"
                      Text="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      FontFamily="Consolas"
                      FontSize="16"
                      FontWeight="Bold"
                      Foreground="#FF00C8FF"
                      Margin="0,0,0,10">
                <TextBlock.Effect>
                    <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                </TextBlock.Effect>
            </TextBlock>
            
            <!-- Content -->
            <ContentPresenter Grid.Row="1" Content="{Binding Content, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
        </Grid>
    </Border>
</UserControl>
