using NUnit;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using CircleUtility.ViewModels;
using CircleUtility.Services;
using CircleUtility.Models;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace CircleUtility.Tests
{
    [TestFixture]
    public class DashboardViewModelTests
    {
        private Mock<IHardwareDetectionService> _mockHardwareDetectionService = null!;
        private Mock<IPerformanceMonitoringService> _mockPerformanceMonitoringService = null!;
        private Mock<IHardwareOptimizationService> _mockHardwareOptimizationService = null!;
        private Mock<IHardwareRecommendationService> _mockHardwareRecommendationService = null!;
        private Mock<IHardwareCompatibilityService> _mockHardwareCompatibilityService = null!;
        private Mock<HardwareDetectionBadgeService> _mockHardwareBadgeService = null!;
        // We can't mock NotificationService because it's a singleton with a private constructor
        private DashboardViewModel _viewModel = null!;

        [SetUp]
        public void Setup()
        {
            // Setup mocks
            _mockHardwareDetectionService = new Mock<IHardwareDetectionService>();
            _mockPerformanceMonitoringService = new Mock<IPerformanceMonitoringService>();
            _mockHardwareOptimizationService = new Mock<IHardwareOptimizationService>();
            _mockHardwareRecommendationService = new Mock<IHardwareRecommendationService>();
            _mockHardwareCompatibilityService = new Mock<IHardwareCompatibilityService>();
            _mockHardwareBadgeService = new Mock<HardwareDetectionBadgeService>();

            // Initialize LoggingService first since NotificationService depends on it
            var loggerField = typeof(LoggingService).GetField("_instance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            if (loggerField != null && loggerField.GetValue(null) == null)
            {
                // Create a new instance of LoggingService using reflection
                var constructor = typeof(LoggingService).GetConstructor(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance, null, Type.EmptyTypes, null);
                if (constructor != null)
                {
                    var logger = constructor.Invoke(null);
                    loggerField.SetValue(null, logger);
                }
            }

            // Setup hardware info
            var hardwareInfo = new HardwareInfo
            {
                CPU = new CPUInfo { Name = "Test CPU", Cores = 8, Threads = 16, MaxClockSpeed = 4500 },
                GPU = new GPUInfo { Name = "Test GPU", MemoryGB = 8, Vendor = GPUVendor.NVIDIA },
                RAM = new RAMInfo { TotalCapacity = 16 },
                Storage = new List<StorageInfo>
                {
                    new StorageInfo { Model = "Test SSD", Size = 512 }
                },
                NetworkAdapters = new List<NetworkAdapterInfo>
                {
                    new NetworkAdapterInfo { Name = "Test Network Adapter" }
                },
                OperatingSystem = new OSInfo { Name = "Windows 11", Version = "22H2" }
            };

            // Setup performance metrics
            var metrics = new PerformanceMetrics
            {
                CpuUsage = 20,
                RamUsage = 30,
                GpuUsage = 15,
                CpuTemperature = 50,
                DiskReadRate = 10,
                DiskWriteRate = 5,
                NetworkDownload = 2,
                NetworkUpload = 1
            };

            // Setup mock methods
            _mockHardwareDetectionService.Setup(m => m.GetHardwareInfo(It.IsAny<bool>())).Returns(hardwareInfo);
            _mockPerformanceMonitoringService.Setup(m => m.IsMonitoringActive).Returns(true);
            _mockPerformanceMonitoringService.Setup(m => m.GetCurrentMetrics()).Returns(metrics);
            // We'll use a field to store the optimization score instead of mocking the service

            // Create view model with mocked dependencies
            _viewModel = new DashboardViewModel(
                _mockHardwareDetectionService.Object,
                _mockPerformanceMonitoringService.Object,
                _mockHardwareOptimizationService.Object,
                _mockHardwareRecommendationService.Object,
                _mockHardwareCompatibilityService.Object);
        }

        [Test]
        public void SystemHealthScore_ShouldBeCalculated()
        {
            // Arrange - Set up test values
            _viewModel.CpuUsageValue = 20;
            _viewModel.RamUsageValue = 30;
            _viewModel.GpuUsageValue = 15;
            _viewModel.CpuTemperature = 50;
            _viewModel.StorageUsage = 40;

            // Act - Call the method through reflection since it's private
            var calculateMethod = typeof(DashboardViewModel).GetMethod("CalculateSystemHealthScore",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            calculateMethod?.Invoke(_viewModel, null);

            // Assert - Check that the score is within a reasonable range
            ClassicAssert.IsTrue(_viewModel.SystemHealthScore > 0);
            ClassicAssert.IsTrue(_viewModel.SystemHealthScore <= 100);
        }

        [Test]
        public void SystemHealthScore_WithHighUsage_ShouldBeLower()
        {
            // First calculate a baseline score
            _viewModel.CpuUsageValue = 20;
            _viewModel.RamUsageValue = 30;
            _viewModel.GpuUsageValue = 15;
            _viewModel.CpuTemperature = 50;
            _viewModel.StorageUsage = 40;

            var calculateMethod = typeof(DashboardViewModel).GetMethod("CalculateSystemHealthScore",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            calculateMethod?.Invoke(_viewModel, null);

            int baselineScore = _viewModel.SystemHealthScore;

            // Now set up test values with high resource usage
            _viewModel.CpuUsageValue = 80;
            _viewModel.RamUsageValue = 90;
            _viewModel.GpuUsageValue = 85;
            _viewModel.CpuTemperature = 80;
            _viewModel.StorageUsage = 90;

            // Act - Call the method through reflection
            calculateMethod?.Invoke(_viewModel, null);

            // Assert - Check that the score is lower with high usage
            ClassicAssert.IsTrue(_viewModel.SystemHealthScore < baselineScore);
        }

        [Test]
        public void SystemHealthScore_WithLowUsage_ShouldBeHigher()
        {
            // First calculate a baseline score
            _viewModel.CpuUsageValue = 50;
            _viewModel.RamUsageValue = 50;
            _viewModel.GpuUsageValue = 50;
            _viewModel.CpuTemperature = 50;
            _viewModel.StorageUsage = 50;

            var calculateMethod = typeof(DashboardViewModel).GetMethod("CalculateSystemHealthScore",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            calculateMethod?.Invoke(_viewModel, null);

            int baselineScore = _viewModel.SystemHealthScore;

            // Now set up test values with low resource usage
            _viewModel.CpuUsageValue = 5;
            _viewModel.RamUsageValue = 10;
            _viewModel.GpuUsageValue = 5;
            _viewModel.CpuTemperature = 30;
            _viewModel.StorageUsage = 20;

            // Act - Call the method through reflection
            calculateMethod?.Invoke(_viewModel, null);

            // Assert - Check that the score is higher with low usage
            ClassicAssert.IsTrue(_viewModel.SystemHealthScore > baselineScore);
        }


    }
}
