// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Threading;
using NUnit.Framework;
using NUnit.Framework.Interfaces;
using NUnit.Framework.Internal;
using NUnit.Framework.Internal.Commands;

namespace CircleUtility.Tests
{
    /// <summary>
    /// Attribute that marks a test class as requiring STA thread mode
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class STATestClassAttribute : TestFixtureAttribute
    {
        public STATestClassAttribute()
        {
            // No special initialization needed
        }
    }

    /// <summary>
    /// Attribute that marks a test method as requiring STA thread mode
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class STATestMethodAttribute : TestAttribute, IWrapSetUpTearDown
    {
        public STATestMethodAttribute()
        {
            // No special initialization needed
        }

        public TestCommand Wrap(TestCommand command)
        {
            return new STATestCommand(command);
        }

        private class STATestCommand : DelegatingTestCommand
        {
            public STATestCommand(TestCommand innerCommand)
                : base(innerCommand)
            {
            }

            public override TestResult Execute(TestExecutionContext context)
            {
                if (Thread.CurrentThread.GetApartmentState() == ApartmentState.STA)
                {
                    return innerCommand.Execute(context);
                }

                TestResult? result = null;
                var thread = new Thread(() =>
                {
                    result = innerCommand.Execute(context);
                });

                thread.SetApartmentState(ApartmentState.STA);
                thread.Start();
                thread.Join();

                return result ?? throw new InvalidOperationException("Test execution failed to produce a result");
            }
        }
    }
}
