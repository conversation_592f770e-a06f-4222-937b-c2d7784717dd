using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management;
using System.Net;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for hardware fingerprinting and device identification
    /// </summary>
    public class HardwareFingerprintService
    {
        private static HardwareFingerprintService _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;
        private readonly string _fingerprintCachePath;
        private readonly Dictionary<string, HardwareProfile> _hardwareProfiles;

        /// <summary>
        /// Gets the singleton instance of the HardwareFingerprintService
        /// </summary>
        public static HardwareFingerprintService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new HardwareFingerprintService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes a new instance of the HardwareFingerprintService class
        /// </summary>
        private HardwareFingerprintService()
        {
            _logger = LoggingService.Instance;
            _hardwareProfiles = new Dictionary<string, HardwareProfile>();

            // Set up fingerprint cache path
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CircleUtility");

            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _fingerprintCachePath = Path.Combine(appDataPath, "hardware_profiles.dat");

            // Load cached hardware profiles
            LoadHardwareProfiles();
        }

        /// <summary>
        /// Gets the current machine's hardware fingerprint
        /// </summary>
        /// <returns>A unique hardware fingerprint</returns>
        public string GetHardwareFingerprint()
        {
            try
            {
                // Collect hardware information
                List<string> components = new List<string>();

                // CPU ID
                components.Add(GetCpuId());

                // Motherboard serial
                components.Add(GetMotherboardSerial());

                // MAC addresses (sorted to ensure consistency)
                components.AddRange(GetMacAddresses());

                // Volume serial numbers
                components.AddRange(GetVolumeSerials());

                // BIOS info
                components.Add(GetBiosInfo());

                // GPU info
                components.Add(GetGpuInfo());

                // RAM size
                components.Add(GetTotalPhysicalMemory().ToString());

                // Join all components and create a hash
                string combinedInfo = string.Join("|", components.Where(c => !string.IsNullOrEmpty(c)));
                string fingerprint = CreateSHA256Hash(combinedInfo);

                _logger.Log("Hardware fingerprint generated successfully", LogLevel.INFO);
                return fingerprint;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error generating hardware fingerprint: {ex.Message}", LogLevel.ERROR);
                // Fallback to a less reliable but still useful identifier
                return CreateSHA256Hash(Environment.MachineName + Environment.UserName);
            }
        }

        /// <summary>
        /// Gets the current machine's IP addresses
        /// </summary>
        /// <returns>A list of IP addresses</returns>
        public List<string> GetIpAddresses()
        {
            try
            {
                List<string> ipAddresses = new List<string>();

                // Get local IP addresses
                foreach (var netInterface in NetworkInterface.GetAllNetworkInterfaces())
                {
                    // Only consider up and running interfaces that are not loopback
                    if (netInterface.OperationalStatus == OperationalStatus.Up &&
                        netInterface.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    {
                        foreach (var addr in netInterface.GetIPProperties().UnicastAddresses)
                        {
                            if (addr.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                            {
                                ipAddresses.Add(addr.Address.ToString());
                            }
                        }
                    }
                }

                // Get external IP address
                try
                {
                    using (var client = new HttpClient())
                    {
                        string externalIp = client.GetStringAsync("https://api.ipify.org").Result.Trim();
                        if (!string.IsNullOrEmpty(externalIp))
                        {
                            ipAddresses.Add(externalIp);
                        }
                    }
                }
                catch
                {
                    // Ignore errors getting external IP
                }

                return ipAddresses.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting IP addresses: {ex.Message}", LogLevel.ERROR);
                return new List<string> { "127.0.0.1" };
            }
        }

        /// <summary>
        /// Gets or creates a hardware profile for the current machine
        /// </summary>
        /// <returns>The hardware profile</returns>
        public HardwareProfile GetCurrentHardwareProfile()
        {
            try
            {
                // Get hardware fingerprint
                string fingerprint = GetHardwareFingerprint();

                // Check if profile exists
                if (_hardwareProfiles.TryGetValue(fingerprint, out HardwareProfile profile))
                {
                    // Update IP addresses
                    profile.IpAddresses = GetIpAddresses();
                    profile.LastSeen = DateTime.Now;
                    SaveHardwareProfiles();
                    return profile;
                }

                // Create new profile
                profile = new HardwareProfile
                {
                    Fingerprint = fingerprint,
                    CpuInfo = GetCpuInfo(),
                    GpuInfo = GetGpuInfo(),
                    MotherboardInfo = GetMotherboardInfo(),
                    RamInfo = GetRamInfo(),
                    OsInfo = GetOsInfo(),
                    MacAddresses = GetMacAddresses(),
                    IpAddresses = GetIpAddresses(),
                    FirstSeen = DateTime.Now,
                    LastSeen = DateTime.Now,
                    AssociatedUsernames = new List<string>()
                };

                // Add to dictionary
                _hardwareProfiles.Add(fingerprint, profile);
                SaveHardwareProfiles();

                _logger.Log("New hardware profile created", LogLevel.INFO);
                return profile;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting hardware profile: {ex.Message}", LogLevel.ERROR);

                // Create a minimal profile
                var minimalProfile = new HardwareProfile
                {
                    Fingerprint = CreateSHA256Hash(Environment.MachineName + Environment.UserName),
                    OsInfo = Environment.OSVersion.ToString(),
                    FirstSeen = DateTime.Now,
                    LastSeen = DateTime.Now,
                    AssociatedUsernames = new List<string>()
                };

                return minimalProfile;
            }
        }

        /// <summary>
        /// Associates a username with the current hardware profile
        /// </summary>
        /// <param name="username">The username to associate</param>
        public void AssociateUsernameWithHardware(string username)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username))
                    return;

                // Get current hardware profile
                HardwareProfile profile = GetCurrentHardwareProfile();

                // Add username if not already associated
                if (!profile.AssociatedUsernames.Contains(username))
                {
                    profile.AssociatedUsernames.Add(username);
                    SaveHardwareProfiles();
                    _logger.Log($"Username '{username}' associated with hardware profile", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error associating username with hardware: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets all hardware profiles
        /// </summary>
        /// <returns>A list of hardware profiles</returns>
        public List<HardwareProfile> GetAllHardwareProfiles()
        {
            return _hardwareProfiles.Values.ToList();
        }

        /// <summary>
        /// Locks a hardware profile
        /// </summary>
        /// <param name="fingerprint">The hardware fingerprint to lock</param>
        /// <param name="reason">The reason for locking</param>
        public void LockHardwareProfile(string fingerprint, string reason)
        {
            try
            {
                if (_hardwareProfiles.TryGetValue(fingerprint, out HardwareProfile profile))
                {
                    profile.IsLocked = true;
                    profile.LockReason = reason;
                    profile.LockDate = DateTime.Now;
                    SaveHardwareProfiles();
                    _logger.Log($"Hardware profile locked: {fingerprint}, Reason: {reason}", LogLevel.WARNING);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error locking hardware profile: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Unlocks a hardware profile
        /// </summary>
        /// <param name="fingerprint">The hardware fingerprint to unlock</param>
        public void UnlockHardwareProfile(string fingerprint)
        {
            try
            {
                if (_hardwareProfiles.TryGetValue(fingerprint, out HardwareProfile profile))
                {
                    profile.IsLocked = false;
                    profile.LockReason = null;
                    profile.LockDate = null;
                    SaveHardwareProfiles();
                    _logger.Log($"Hardware profile unlocked: {fingerprint}", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error unlocking hardware profile: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Checks if the current hardware is locked
        /// </summary>
        /// <returns>True if locked, false otherwise</returns>
        public bool IsCurrentHardwareLocked(out string reason)
        {
            reason = null;
            try
            {
                string fingerprint = GetHardwareFingerprint();
                if (_hardwareProfiles.TryGetValue(fingerprint, out HardwareProfile profile))
                {
                    reason = profile.LockReason;
                    return profile.IsLocked;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking if hardware is locked: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        #region Private Helper Methods

        private string GetCpuId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        return obj["ProcessorId"]?.ToString() ?? "";
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetCpuInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT Name, NumberOfCores FROM Win32_Processor"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        string name = obj["Name"]?.ToString() ?? "Unknown CPU";
                        string cores = obj["NumberOfCores"]?.ToString() ?? "?";
                        return $"{name} ({cores} cores)";
                    }
                }
            }
            catch { }
            return "Unknown CPU";
        }

        private string GetMotherboardSerial()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString() ?? "";
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetMotherboardInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT Manufacturer, Product FROM Win32_BaseBoard"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        string manufacturer = obj["Manufacturer"]?.ToString() ?? "Unknown";
                        string product = obj["Product"]?.ToString() ?? "Unknown";
                        return $"{manufacturer} {product}";
                    }
                }
            }
            catch { }
            return "Unknown Motherboard";
        }

        private List<string> GetMacAddresses()
        {
            List<string> macs = new List<string>();
            try
            {
                foreach (NetworkInterface nic in NetworkInterface.GetAllNetworkInterfaces())
                {
                    // Only consider physical adapters that are up
                    if (nic.OperationalStatus == OperationalStatus.Up &&
                        (nic.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
                         nic.NetworkInterfaceType == NetworkInterfaceType.Wireless80211))
                    {
                        string mac = nic.GetPhysicalAddress().ToString();
                        if (!string.IsNullOrEmpty(mac))
                        {
                            macs.Add(mac);
                        }
                    }
                }
            }
            catch { }
            return macs.OrderBy(m => m).ToList();
        }

        private List<string> GetVolumeSerials()
        {
            List<string> serials = new List<string>();
            try
            {
                foreach (DriveInfo drive in DriveInfo.GetDrives())
                {
                    if (drive.IsReady && drive.DriveType == DriveType.Fixed)
                    {
                        using (var searcher = new ManagementObjectSearcher(
                            $"SELECT VolumeSerialNumber FROM Win32_LogicalDisk WHERE DeviceID='{drive.Name.TrimEnd('\\')}'"
                        ))
                        {
                            foreach (var obj in searcher.Get())
                            {
                                string serial = obj["VolumeSerialNumber"]?.ToString();
                                if (!string.IsNullOrEmpty(serial))
                                {
                                    serials.Add(serial);
                                }
                            }
                        }
                    }
                }
            }
            catch { }
            return serials;
        }

        private string GetBiosInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT Manufacturer, SerialNumber FROM Win32_BIOS"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        string manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                        string serial = obj["SerialNumber"]?.ToString() ?? "";
                        return $"{manufacturer}|{serial}";
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetGpuInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT Name, AdapterRAM FROM Win32_VideoController"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        string name = obj["Name"]?.ToString() ?? "Unknown GPU";
                        return name;
                    }
                }
            }
            catch { }
            return "Unknown GPU";
        }

        private string GetRamInfo()
        {
            try
            {
                ulong totalMemory = GetTotalPhysicalMemory();
                return $"{totalMemory / (1024 * 1024 * 1024.0):F1} GB";
            }
            catch { }
            return "Unknown RAM";
        }

        private ulong GetTotalPhysicalMemory()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        if (obj["TotalPhysicalMemory"] != null)
                        {
                            return Convert.ToUInt64(obj["TotalPhysicalMemory"]);
                        }
                    }
                }
            }
            catch { }
            return 0;
        }

        private string GetOsInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT Caption, Version FROM Win32_OperatingSystem"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        string caption = obj["Caption"]?.ToString() ?? "Unknown OS";
                        string version = obj["Version"]?.ToString() ?? "";
                        return $"{caption} {version}";
                    }
                }
            }
            catch { }
            return Environment.OSVersion.ToString();
        }

        private string CreateSHA256Hash(string input)
        {
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] bytes = Encoding.UTF8.GetBytes(input);
                byte[] hash = sha256.ComputeHash(bytes);
                return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
            }
        }

        private void LoadHardwareProfiles()
        {
            try
            {
                _hardwareProfiles.Clear();

                if (File.Exists(_fingerprintCachePath))
                {
                    string json = File.ReadAllText(_fingerprintCachePath);
                    var profiles = System.Text.Json.JsonSerializer.Deserialize<List<HardwareProfile>>(json);

                    if (profiles != null)
                    {
                        foreach (var profile in profiles)
                        {
                            _hardwareProfiles[profile.Fingerprint] = profile;
                        }
                    }

                    _logger.Log($"Loaded {_hardwareProfiles.Count} hardware profiles", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading hardware profiles: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void SaveHardwareProfiles()
        {
            try
            {
                var profiles = _hardwareProfiles.Values.ToList();
                string json = System.Text.Json.JsonSerializer.Serialize(profiles, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                File.WriteAllText(_fingerprintCachePath, json);
                _logger.Log($"Saved {profiles.Count} hardware profiles", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving hardware profiles: {ex.Message}", LogLevel.ERROR);
            }
        }

        #endregion
    }
}
