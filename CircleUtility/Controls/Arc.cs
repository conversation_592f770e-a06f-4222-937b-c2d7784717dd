using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Shapes;

namespace CircleUtility.Controls
{
    /// <summary>
    /// A custom control that draws an arc
    /// </summary>
    public class Arc : Shape
    {
        /// <summary>
        /// Dependency property for StartAngle
        /// </summary>
        public static readonly DependencyProperty StartAngleProperty =
            DependencyProperty.Register("StartAngle", typeof(double), typeof(Arc),
                new FrameworkPropertyMetadata(0.0, FrameworkPropertyMetadataOptions.AffectsRender));

        /// <summary>
        /// Dependency property for EndAngle
        /// </summary>
        public static readonly DependencyProperty EndAngleProperty =
            DependencyProperty.Register("EndAngle", typeof(double), typeof(Arc),
                new FrameworkPropertyMetadata(90.0, FrameworkPropertyMetadataOptions.AffectsRender));

        /// <summary>
        /// Gets or sets the start angle in degrees
        /// </summary>
        public double StartAngle
        {
            get => (double)GetValue(StartAngleProperty);
            set => SetValue(StartAngleProperty, value);
        }

        /// <summary>
        /// Gets or sets the end angle in degrees
        /// </summary>
        public double EndAngle
        {
            get => (double)GetValue(EndAngleProperty);
            set => SetValue(EndAngleProperty, value);
        }

        /// <summary>
        /// Defines the geometry of the arc
        /// </summary>
        protected override Geometry DefiningGeometry
        {
            get
            {
                // Convert angles from degrees to radians
                double startAngleRadians = StartAngle * Math.PI / 180;
                double endAngleRadians = EndAngle * Math.PI / 180;

                // Calculate the size of the arc
                double width = Math.Max(0, ActualWidth - StrokeThickness);
                double height = Math.Max(0, ActualHeight - StrokeThickness);

                // Calculate the center point
                double centerX = width / 2;
                double centerY = height / 2;

                // Calculate the radius
                double radiusX = width / 2;
                double radiusY = height / 2;

                // Calculate start and end points
                double startX = centerX + radiusX * Math.Cos(startAngleRadians);
                double startY = centerY + radiusY * Math.Sin(startAngleRadians);
                double endX = centerX + radiusX * Math.Cos(endAngleRadians);
                double endY = centerY + radiusY * Math.Sin(endAngleRadians);

                // Create the geometry
                StreamGeometry geometry = new StreamGeometry();
                using (StreamGeometryContext context = geometry.Open())
                {
                    // Move to the start point
                    context.BeginFigure(new Point(startX, startY), false, false);

                    // Draw the arc
                    context.ArcTo(
                        new Point(endX, endY),
                        new Size(radiusX, radiusY),
                        0,
                        (endAngleRadians - startAngleRadians) > Math.PI,
                        SweepDirection.Counterclockwise,
                        true,
                        false);
                }

                return geometry;
            }
        }
    }
}
