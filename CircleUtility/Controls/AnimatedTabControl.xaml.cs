using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    public partial class AnimatedTabControl : UserControl
    {
        private ContentPresenter _currentContentPresenter;
        private ContentPresenter _newContentPresenter;
        private int _previousIndex = -1;
        private bool _isAnimating = false;
        private readonly Dictionary<string, Storyboard> _storyboards = new();

        public TabControl TabControl => InnerTabControl;

        public AnimationType AnimationType { get; set; } = AnimationType.Slide;

        public AnimatedTabControl()
        {
            InitializeComponent();
            CreateAnimations();
        }

        private void CreateAnimations()
        {
            // --- Slide In From Right ---
            _storyboards["SlideInFromRight"] = CreateStoryboard(
                new Thickness(800, 0, -800, 0),
                new Thickness(0),
                0, 1,
                easeOut: true
            );

            // --- Slide Out To Left ---
            _storyboards["SlideOutToLeft"] = CreateStoryboard(
                new Thickness(0),
                new Thickness(-800, 0, 800, 0),
                1, 0,
                easeOut: false
            );

            // --- Slide In From Left ---
            _storyboards["SlideInFromLeft"] = CreateStoryboard(
                new Thickness(-800, 0, 800, 0),
                new Thickness(0),
                0, 1,
                easeOut: true
            );

            // --- Slide Out To Right ---
            _storyboards["SlideOutToRight"] = CreateStoryboard(
                new Thickness(0),
                new Thickness(800, 0, -800, 0),
                1, 0,
                easeOut: false
            );

            // --- Fade In ---
            _storyboards["FadeIn"] = CreateOpacityStoryboard(0, 1, 0.2, easeOut: true);

            // --- Fade Out ---
            _storyboards["FadeOut"] = CreateOpacityStoryboard(1, 0, 0.2, easeOut: false);
        }

        private Storyboard CreateStoryboard(Thickness fromMargin, Thickness toMargin, double fromOpacity, double toOpacity, bool easeOut)
        {
            var easing = new CubicEase { EasingMode = easeOut ? EasingMode.EaseOut : EasingMode.EaseIn };

            var marginAnimation = new ThicknessAnimation
            {
                From = fromMargin,
                To = toMargin,
                Duration = TimeSpan.FromSeconds(0.3),
                EasingFunction = easing
            };
            Storyboard.SetTargetProperty(marginAnimation, new PropertyPath("Margin"));

            var opacityAnimation = new DoubleAnimation
            {
                From = fromOpacity,
                To = toOpacity,
                Duration = TimeSpan.FromSeconds(0.3),
                EasingFunction = easing
            };
            Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

            return new Storyboard
            {
                Children = { marginAnimation, opacityAnimation }
            };
        }

        private Storyboard CreateOpacityStoryboard(double from, double to, double duration, bool easeOut)
        {
            var easing = new CubicEase { EasingMode = easeOut ? EasingMode.EaseOut : EasingMode.EaseIn };

            var opacityAnimation = new DoubleAnimation
            {
                From = from,
                To = to,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = easing
            };
            Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

            return new Storyboard
            {
                Children = { opacityAnimation }
            };
        }

        private void InnerTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isAnimating || InnerTabControl.SelectedIndex == -1)
                return;

            var selectedItem = InnerTabControl.SelectedItem;
            if (selectedItem == null) return;

            var tabItem = InnerTabControl.ItemContainerGenerator.ContainerFromItem(selectedItem) as TabItem;
            _newContentPresenter = FindVisualChild<ContentPresenter>(tabItem);

            if (_newContentPresenter != null && _currentContentPresenter != null && _previousIndex != -1)
            {
                bool isMovingRight = InnerTabControl.SelectedIndex > _previousIndex;
                AnimateTabChange(isMovingRight);
            }
            else
            {
                _currentContentPresenter = _newContentPresenter;
            }

            _previousIndex = InnerTabControl.SelectedIndex;
        }

        private void AnimateTabChange(bool isMovingRight)
        {
            _isAnimating = true;
            _newContentPresenter.Opacity = 0;

            string inKey, outKey;

            if (AnimationType == AnimationType.Slide)
            {
                inKey = isMovingRight ? "SlideInFromRight" : "SlideInFromLeft";
                outKey = isMovingRight ? "SlideOutToLeft" : "SlideOutToRight";
            }
            else
            {
                inKey = "FadeIn";
                outKey = "FadeOut";
            }

            var inStoryboard = _storyboards[inKey].Clone();
            var outStoryboard = _storyboards[outKey].Clone();

            Storyboard.SetTarget(inStoryboard, _newContentPresenter);
            Storyboard.SetTarget(outStoryboard, _currentContentPresenter);

            outStoryboard.Completed += (_, _) =>
            {
                _currentContentPresenter = _newContentPresenter;
                _isAnimating = false;
            };

            inStoryboard.Begin();
            outStoryboard.Begin();
        }

        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T found)
                    return found;

                var descendant = FindVisualChild<T>(child);
                if (descendant != null)
                    return descendant;
            }
            return null;
        }
    }

    public enum AnimationType
    {
        Slide,
        Fade
    }
}
