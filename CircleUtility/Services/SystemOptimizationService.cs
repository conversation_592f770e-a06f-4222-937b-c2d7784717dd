using System;
using CircleUtility.Models;
using CircleUtility.Interfaces;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Threading;
using Microsoft.Win32;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for system optimization functions
    /// </summary>
    public class SystemOptimizationService : CircleUtility.Interfaces.ISystemOptimizationService
    {
        private static SystemOptimizationService _instance;
        private readonly LoggingService _logger;
        private readonly ISystemOperations _systemOps;

        private SystemOptimizationService()
        {
            _logger = LoggingService.Instance;
            _systemOps = SystemOperationsFactory.Create();
        }

        /// <summary>
        /// Gets the singleton instance of the system optimization service
        /// </summary>
        public static SystemOptimizationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SystemOptimizationService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Optimizes network settings for gaming
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeNetworkSettings()
        {
            try
            {
                _logger.Log("Optimizing network settings...", LogLevel.INFO);

                // Run network optimization commands with admin privileges
                RunProcessAsAdmin("netsh", "winsock reset");
                RunProcessAsAdmin("netsh", "int tcp set global autotuninglevel=normal");
                RunProcessAsAdmin("netsh", "int tcp set global chimney=enabled");
                RunProcessAsAdmin("netsh", "int tcp set global ecncapability=enabled");
                RunProcessAsAdmin("netsh", "int tcp set global rss=enabled");

                // Set registry values for network optimization
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true))
                {
                    if (key != null)
                    {
                        key.SetValue("DefaultTTL", 64, RegistryValueKind.DWord);
                        key.SetValue("Tcp1323Opts", 1, RegistryValueKind.DWord);
                        key.SetValue("TcpMaxDupAcks", 2, RegistryValueKind.DWord);
                        key.SetValue("SackOpts", 1, RegistryValueKind.DWord);
                    }
                }

                _logger.Log("Network settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing network settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes power settings for gaming
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizePowerSettings()
        {
            try
            {
                _logger.Log("Optimizing power settings...", LogLevel.INFO);

                // Set high performance power plan
                RunProcessAsAdmin("powercfg", "-setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c");

                // Disable hibernation
                RunProcessAsAdmin("powercfg", "-h off");

                // Disable USB selective suspend
                RunProcessAsAdmin("powercfg", "-setacvalueindex scheme_current sub_2a737441_1930_4402_8d77_b2bebba308a3 d4e98f31_5ffe_4ce1_be31_1b38b384c009 0");
                RunProcessAsAdmin("powercfg", "-setdcvalueindex scheme_current sub_2a737441_1930_4402_8d77_b2bebba308a3 d4e98f31_5ffe_4ce1_be31_1b38b384c009 0");

                // Set processor performance to maximum
                RunProcessAsAdmin("powercfg", "-setacvalueindex scheme_current sub_54533251_82be_4824_96c1_47b60b740d00 bc5038f7_23e0_4960_96da_33abaf5935ec 100");
                RunProcessAsAdmin("powercfg", "-setdcvalueindex scheme_current sub_54533251_82be_4824_96c1_47b60b740d00 bc5038f7_23e0_4960_96da_33abaf5935ec 100");

                // Apply changes
                RunProcessAsAdmin("powercfg", "-setactive scheme_current");

                _logger.Log("Power settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing power settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes input delay settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeInputDelay()
        {
            // In test mode, we still want to make the registry changes for testing
            // but we'll use the mock system operations that won't actually modify the system

            try
            {
                _logger.Log("Optimizing input delay settings...", LogLevel.INFO);

                // Disable full-screen optimizations
                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\GameDVR",
                    "AppCaptureEnabled",
                    0,
                    RegistryValueKind.DWord);

                // Disable Game Bar
                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\GameBar",
                    "AutoGameModeEnabled",
                    0,
                    RegistryValueKind.DWord);

                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\SOFTWARE\Microsoft\GameBar",
                    "UseNexusForGameBarEnabled",
                    0,
                    RegistryValueKind.DWord);

                // Disable mouse acceleration
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Mouse", true))
                {
                    if (key != null)
                    {
                        key.SetValue("MouseSpeed", "0", RegistryValueKind.String);
                        key.SetValue("MouseThreshold1", "0", RegistryValueKind.String);
                        key.SetValue("MouseThreshold2", "0", RegistryValueKind.String);
                    }
                }

                // Set timer resolution
                RunProcessAsAdmin("bcdedit", "/set useplatformtick yes");
                RunProcessAsAdmin("bcdedit", "/set disabledynamictick yes");

                _logger.Log("Input delay settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing input delay settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Runs a process with admin privileges
        /// </summary>
        /// <param name="fileName">The process file name</param>
        /// <param name="arguments">The process arguments</param>
        /// <returns>The process exit code</returns>
        private int RunProcessAsAdmin(string fileName, string arguments)
        {
            try
            {
                // Use the system operations interface to run the process
                return _systemOps.RunProcessAsAdmin(fileName, arguments, true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running process as admin: {ex.Message}", LogLevel.ERROR);
                return -1;
            }
        }

        /// <summary>
        /// Reverts network settings to default
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertNetworkSettings()
        {
            try
            {
                _logger.Log("Reverting network settings to default...", LogLevel.INFO);

                // Reset Winsock and TCP/IP stack
                RunProcessAsAdmin("netsh", "winsock reset");
                RunProcessAsAdmin("netsh", "int ip reset");
                RunProcessAsAdmin("netsh", "int tcp reset");

                // Reset TCP/IP parameters in registry
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters", true))
                {
                    if (key != null)
                    {
                        // Remove custom values to restore defaults
                        key.DeleteValue("DefaultTTL", false);
                        key.DeleteValue("Tcp1323Opts", false);
                        key.DeleteValue("TcpMaxDupAcks", false);
                        key.DeleteValue("SackOpts", false);
                    }
                }

                _logger.Log("Network settings reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting network settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts power settings to default
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertPowerSettings()
        {
            try
            {
                _logger.Log("Reverting power settings to default...", LogLevel.INFO);

                // Set balanced power plan
                RunProcessAsAdmin("powercfg", "-setactive 381b4222-f694-41f0-9685-ff5bb260df2e");

                // Enable hibernation
                RunProcessAsAdmin("powercfg", "-h on");

                // Reset USB selective suspend to default
                RunProcessAsAdmin("powercfg", "-restoredefaultschemes");

                _logger.Log("Power settings reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting power settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts input delay settings to default
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertInputDelay()
        {
            try
            {
                _logger.Log("Reverting input delay settings to default...", LogLevel.INFO);

                // Re-enable full-screen optimizations
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\GameDVR", true))
                {
                    if (key != null)
                    {
                        key.SetValue("AppCaptureEnabled", 1, RegistryValueKind.DWord);
                    }
                }

                // Re-enable Game Bar
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\GameBar", true))
                {
                    if (key != null)
                    {
                        key.SetValue("AutoGameModeEnabled", 1, RegistryValueKind.DWord);
                        key.SetValue("UseNexusForGameBarEnabled", 1, RegistryValueKind.DWord);
                    }
                }

                // Re-enable mouse acceleration (Windows default)
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Mouse", true))
                {
                    if (key != null)
                    {
                        key.SetValue("MouseSpeed", "1", RegistryValueKind.String);
                        key.SetValue("MouseThreshold1", "6", RegistryValueKind.String);
                        key.SetValue("MouseThreshold2", "10", RegistryValueKind.String);
                    }
                }

                // Reset timer resolution
                RunProcessAsAdmin("bcdedit", "/deletevalue useplatformtick");
                RunProcessAsAdmin("bcdedit", "/deletevalue disabledynamictick");

                _logger.Log("Input delay settings reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting input delay settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets system information
        /// </summary>
        /// <returns>A string containing system information</returns>
        public string GetSystemInfo()
        {
            try
            {
                StringBuilder sb = new StringBuilder();

                // Get OS info
                sb.AppendLine("=== Operating System ===");
                sb.AppendLine($"OS: {Environment.OSVersion}");
                sb.AppendLine($"64-bit OS: {Environment.Is64BitOperatingSystem}");
                sb.AppendLine($"System Directory: {Environment.SystemDirectory}");
                sb.AppendLine();

                // Get processor info
                sb.AppendLine("=== Processor ===");
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        sb.AppendLine($"Name: {obj["Name"]}");
                        sb.AppendLine($"Cores: {obj["NumberOfCores"]}");
                        sb.AppendLine($"Logical Processors: {obj["NumberOfLogicalProcessors"]}");
                        sb.AppendLine($"Max Clock Speed: {obj["MaxClockSpeed"]} MHz");
                    }
                }
                sb.AppendLine();

                // Get memory info
                sb.AppendLine("=== Memory ===");
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        long totalMemory = Convert.ToInt64(obj["TotalPhysicalMemory"]);
                        sb.AppendLine($"Total Physical Memory: {totalMemory / (1024 * 1024 * 1024.0):F2} GB");
                    }
                }
                sb.AppendLine();

                // Get graphics info
                sb.AppendLine("=== Graphics ===");
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        sb.AppendLine($"Name: {obj["Name"]}");
                        sb.AppendLine($"Driver Version: {obj["DriverVersion"]}");
                        sb.AppendLine($"Video Memory: {Convert.ToInt64(obj["AdapterRAM"]) / (1024 * 1024 * 1024.0):F2} GB");
                        sb.AppendLine($"Current Resolution: {obj["CurrentHorizontalResolution"]} x {obj["CurrentVerticalResolution"]}");
                        sb.AppendLine($"Refresh Rate: {obj["CurrentRefreshRate"]} Hz");
                    }
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting system info: {ex.Message}", LogLevel.ERROR);
                return "Error retrieving system information.";
            }
        }

        /// <summary>
        /// Optimizes mouse settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeMouseSettings()
        {
            // In test mode, we still want to make the registry changes for testing
            // but we'll use the mock system operations that won't actually modify the system

            try
            {
                _logger.Log("Optimizing mouse settings...", LogLevel.INFO);

                // Disable mouse acceleration
                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\Control Panel\Mouse",
                    "MouseSpeed",
                    "0",
                    RegistryValueKind.String);

                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\Control Panel\Mouse",
                    "MouseThreshold1",
                    "0",
                    RegistryValueKind.String);

                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\Control Panel\Mouse",
                    "MouseThreshold2",
                    "0",
                    RegistryValueKind.String);

                // Set mouse polling rate (requires admin)
                RunProcessAsAdmin("devcon.exe", "setprop \"HID\\VID*&PID*&MI*\" DeviceParameters\\PollingRate 1 1");

                _logger.Log("Mouse settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing mouse settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes keyboard settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeKeyboardSettings()
        {
            // In test mode, we still want to make the registry changes for testing
            // but we'll use the mock system operations that won't actually modify the system

            try
            {
                _logger.Log("Optimizing keyboard settings...", LogLevel.INFO);

                // Set keyboard repeat rate
                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\Control Panel\Keyboard",
                    "KeyboardDelay",
                    "0",
                    RegistryValueKind.String);

                _systemOps.SetRegistryValue(
                    @"HKEY_CURRENT_USER\Control Panel\Keyboard",
                    "KeyboardSpeed",
                    "31",
                    RegistryValueKind.String);

                // Set keyboard polling rate (requires admin)
                RunProcessAsAdmin("devcon.exe", "setprop \"HID\\VID*&PID*&MI*\" DeviceParameters\\PollingRate 1 1");

                _logger.Log("Keyboard settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing keyboard settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes controller settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeControllerSettings()
        {
            // In test mode, we still want to make the registry changes for testing
            // but we'll use the mock system operations that won't actually modify the system

            try
            {
                _logger.Log("Optimizing controller settings...", LogLevel.INFO);

                // Set controller polling rate (requires admin)
                RunProcessAsAdmin("devcon.exe", "setprop \"HID\\VID*&PID*&MI*\" DeviceParameters\\PollingRate 1 1");

                // Disable controller power saving
                _systemOps.SetRegistryValue(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\HidUsb\Parameters",
                    "DisableSelectiveSuspend",
                    1,
                    RegistryValueKind.DWord);

                _logger.Log("Controller settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing controller settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes GPU settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeGpuSettings()
        {
            try
            {
                _logger.Log("Optimizing GPU settings...", LogLevel.INFO);

                // Set GPU power mode to maximum performance
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000", true))
                {
                    if (key != null)
                    {
                        key.SetValue("PerfLevelSrc", 2, RegistryValueKind.DWord);
                        key.SetValue("PowerMizerEnable", 1, RegistryValueKind.DWord);
                        key.SetValue("PowerMizerLevel", 1, RegistryValueKind.DWord);
                        key.SetValue("PowerMizerLevelAC", 1, RegistryValueKind.DWord);
                    }
                }

                _logger.Log("GPU settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing GPU settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Optimizes thermal settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool OptimizeThermalSettings()
        {
            try
            {
                _logger.Log("Optimizing thermal settings...", LogLevel.INFO);

                // Set active cooling policy
                RunProcessAsAdmin("powercfg", "-setacvalueindex scheme_current sub_processor 94d3a615-a899-4ac5-ae2b-e4d8f634367f 1");
                RunProcessAsAdmin("powercfg", "-setdcvalueindex scheme_current sub_processor 94d3a615-a899-4ac5-ae2b-e4d8f634367f 1");

                // Apply changes
                RunProcessAsAdmin("powercfg", "-setactive scheme_current");

                _logger.Log("Thermal settings optimized successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing thermal settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts GPU settings to default
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertGpuSettings()
        {
            try
            {
                _logger.Log("Reverting GPU settings to default...", LogLevel.INFO);

                // Reset GPU power mode to default
                using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000", true))
                {
                    if (key != null)
                    {
                        key.DeleteValue("PerfLevelSrc", false);
                        key.DeleteValue("PowerMizerEnable", false);
                        key.DeleteValue("PowerMizerLevel", false);
                        key.DeleteValue("PowerMizerLevelAC", false);
                    }
                }

                _logger.Log("GPU settings reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting GPU settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts thermal settings to default
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertThermalSettings()
        {
            try
            {
                _logger.Log("Reverting thermal settings to default...", LogLevel.INFO);

                // Reset cooling policy to default (passive)
                RunProcessAsAdmin("powercfg", "-setacvalueindex scheme_current sub_processor 94d3a615-a899-4ac5-ae2b-e4d8f634367f 0");
                RunProcessAsAdmin("powercfg", "-setdcvalueindex scheme_current sub_processor 94d3a615-a899-4ac5-ae2b-e4d8f634367f 0");

                // Apply changes
                RunProcessAsAdmin("powercfg", "-setactive scheme_current");

                _logger.Log("Thermal settings reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting thermal settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts mouse settings to default
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertMouseSettings()
        {
            try
            {
                _logger.Log("Reverting mouse settings to default...", LogLevel.INFO);

                // Reset mouse acceleration settings
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Mouse", true))
                {
                    if (key != null)
                    {
                        // Default Windows values
                        key.SetValue("MouseSpeed", "1", RegistryValueKind.String);
                        key.SetValue("MouseThreshold1", "6", RegistryValueKind.String);
                        key.SetValue("MouseThreshold2", "10", RegistryValueKind.String);
                    }
                }

                // Reset mouse polling rate (requires admin)
                RunProcessAsAdmin("devcon.exe", "setprop \"HID\\VID*&PID*&MI*\" DeviceParameters\\PollingRate 0 0");

                _logger.Log("Mouse settings reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting mouse settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts keyboard settings to default
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertKeyboardSettings()
        {
            try
            {
                _logger.Log("Reverting keyboard settings to default...", LogLevel.INFO);

                // Reset keyboard repeat rate
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Keyboard", true))
                {
                    if (key != null)
                    {
                        // Default Windows values
                        key.SetValue("KeyboardDelay", "1", RegistryValueKind.String);
                        key.SetValue("KeyboardSpeed", "31", RegistryValueKind.String);
                    }
                }

                // Reset keyboard polling rate (requires admin)
                RunProcessAsAdmin("devcon.exe", "setprop \"HID\\VID*&PID*&MI*\" DeviceParameters\\PollingRate 0 0");

                _logger.Log("Keyboard settings reverted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting keyboard settings: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets the optimization score (0-100)
        /// </summary>
        /// <returns>The optimization score</returns>
        public double GetOptimizationScore()
        {
            try
            {
                _logger.Log("Calculating optimization score...", LogLevel.INFO);

                // Calculate score based on applied optimizations
                double score = 0;
                double maxScore = 0;

                // Network optimization (20 points)
                maxScore += 20;
                try
                {
                    using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"))
                    {
                        if (key != null)
                        {
                            if (key.GetValue("DefaultTTL") != null) score += 5;
                            if (key.GetValue("Tcp1323Opts") != null) score += 5;
                            if (key.GetValue("TcpMaxDupAcks") != null) score += 5;
                            if (key.GetValue("SackOpts") != null) score += 5;
                        }
                    }
                }
                catch { /* Ignore errors */ }

                // Power optimization (20 points)
                maxScore += 20;
                try
                {
                    // Check if high performance power plan is active
                    Process process = new Process();
                    process.StartInfo.FileName = "powercfg";
                    process.StartInfo.Arguments = "/getactivescheme";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.Start();
                    string output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    if (output.Contains("8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"))
                    {
                        score += 20;
                    }
                }
                catch { /* Ignore errors */ }

                // Input delay optimization (20 points)
                maxScore += 20;
                try
                {
                    using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Mouse"))
                    {
                        if (key != null)
                        {
                            if (key.GetValue("MouseSpeed")?.ToString() == "0") score += 7;
                            if (key.GetValue("MouseThreshold1")?.ToString() == "0") score += 7;
                            if (key.GetValue("MouseThreshold2")?.ToString() == "0") score += 6;
                        }
                    }
                }
                catch { /* Ignore errors */ }

                // GPU optimization (20 points)
                maxScore += 20;
                try
                {
                    using (RegistryKey key = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000"))
                    {
                        if (key != null)
                        {
                            if (key.GetValue("PerfLevelSrc") != null) score += 5;
                            if (key.GetValue("PowerMizerEnable") != null) score += 5;
                            if (key.GetValue("PowerMizerLevel") != null) score += 5;
                            if (key.GetValue("PowerMizerLevelAC") != null) score += 5;
                        }
                    }
                }
                catch { /* Ignore errors */ }

                // Thermal optimization (20 points)
                maxScore += 20;
                try
                {
                    // Check if active cooling is enabled
                    Process process = new Process();
                    process.StartInfo.FileName = "powercfg";
                    process.StartInfo.Arguments = "/q scheme_current sub_processor 94d3a615-a899-4ac5-ae2b-e4d8f634367f";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.Start();
                    string output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    if (output.Contains("AC=0x00000001") || output.Contains("DC=0x00000001"))
                    {
                        score += 20;
                    }
                }
                catch { /* Ignore errors */ }

                // Calculate final score (0-100)
                double finalScore = (score / maxScore) * 100;

                _logger.Log($"Optimization score calculated: {finalScore:F0}", LogLevel.INFO);
                return finalScore;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error calculating optimization score: {ex.Message}", LogLevel.ERROR);
                return 50; // Default to 50% if there's an error
            }
        }

        /// <summary>
        /// Runs all optimizations
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RunOptimization()
        {
            try
            {
                _logger.Log("Running all optimizations...", LogLevel.INFO);

                // Run optimizations one by one to avoid UI freezing
                bool networkResult = OptimizeNetworkSettings();

                // Allow UI to update
                DoEvents();

                bool powerResult = OptimizePowerSettings();

                // Allow UI to update
                DoEvents();

                bool inputResult = OptimizeInputDelay();

                // Allow UI to update
                DoEvents();

                bool gpuResult = OptimizeGpuSettings();

                // Allow UI to update
                DoEvents();

                bool thermalResult = OptimizeThermalSettings();

                bool success = networkResult && powerResult && inputResult && gpuResult && thermalResult;

                if (success)
                {
                    _logger.Log("All optimizations completed successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some optimizations failed", LogLevel.WARNING);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Runs all optimizations asynchronously
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The value of the TResult parameter contains true if successful, false otherwise</returns>
        public async Task<bool> RunOptimizationAsync()
        {
            try
            {
                _logger.Log("Running all optimizations asynchronously...", LogLevel.INFO);

                // Create tasks for all optimizations to run in parallel
                Task<bool> networkTask = Task.Run(() => OptimizeNetworkSettings());
                Task<bool> powerTask = Task.Run(() => OptimizePowerSettings());
                Task<bool> inputTask = Task.Run(() => OptimizeInputDelay());
                Task<bool> gpuTask = Task.Run(() => OptimizeGpuSettings());
                Task<bool> thermalTask = Task.Run(() => OptimizeThermalSettings());

                // Wait for all tasks to complete
                await Task.WhenAll(networkTask, powerTask, inputTask, gpuTask, thermalTask);

                // Get results
                bool networkResult = networkTask.Result;
                bool powerResult = powerTask.Result;
                bool inputResult = inputTask.Result;
                bool gpuResult = gpuTask.Result;
                bool thermalResult = thermalTask.Result;

                bool success = networkResult && powerResult && inputResult && gpuResult && thermalResult;

                if (success)
                {
                    _logger.Log("All optimizations completed successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some optimizations failed", LogLevel.WARNING);

                    // Log which optimizations failed
                    if (!networkResult) _logger.Log("Network optimization failed", LogLevel.WARNING);
                    if (!powerResult) _logger.Log("Power optimization failed", LogLevel.WARNING);
                    if (!inputResult) _logger.Log("Input delay optimization failed", LogLevel.WARNING);
                    if (!gpuResult) _logger.Log("GPU optimization failed", LogLevel.WARNING);
                    if (!thermalResult) _logger.Log("Thermal optimization failed", LogLevel.WARNING);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running optimizations asynchronously: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts all optimizations
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertOptimization()
        {
            try
            {
                _logger.Log("Reverting all optimizations...", LogLevel.INFO);

                // Revert optimizations one by one to avoid UI freezing
                bool networkResult = RevertNetworkSettings();

                // Allow UI to update
                DoEvents();

                bool powerResult = RevertPowerSettings();

                // Allow UI to update
                DoEvents();

                bool inputResult = RevertInputDelay();

                // Allow UI to update
                DoEvents();

                bool gpuResult = RevertGpuSettings();

                // Allow UI to update
                DoEvents();

                bool thermalResult = RevertThermalSettings();

                bool success = networkResult && powerResult && inputResult && gpuResult && thermalResult;

                if (success)
                {
                    _logger.Log("All optimizations reverted successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some optimizations failed to revert", LogLevel.WARNING);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts all optimizations asynchronously
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The value of the TResult parameter contains true if successful, false otherwise</returns>
        public async Task<bool> RevertOptimizationAsync()
        {
            try
            {
                _logger.Log("Reverting all optimizations asynchronously...", LogLevel.INFO);

                // Create tasks for all revert operations to run in parallel
                Task<bool> networkTask = Task.Run(() => RevertNetworkSettings());
                Task<bool> powerTask = Task.Run(() => RevertPowerSettings());
                Task<bool> inputTask = Task.Run(() => RevertInputDelay());
                Task<bool> gpuTask = Task.Run(() => RevertGpuSettings());
                Task<bool> thermalTask = Task.Run(() => RevertThermalSettings());

                // Wait for all tasks to complete
                await Task.WhenAll(networkTask, powerTask, inputTask, gpuTask, thermalTask);

                // Get results
                bool networkResult = networkTask.Result;
                bool powerResult = powerTask.Result;
                bool inputResult = inputTask.Result;
                bool gpuResult = gpuTask.Result;
                bool thermalResult = thermalTask.Result;

                bool success = networkResult && powerResult && inputResult && gpuResult && thermalResult;

                if (success)
                {
                    _logger.Log("All optimizations reverted successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some optimizations failed to revert", LogLevel.WARNING);

                    // Log which optimizations failed to revert
                    if (!networkResult) _logger.Log("Network optimization revert failed", LogLevel.WARNING);
                    if (!powerResult) _logger.Log("Power optimization revert failed", LogLevel.WARNING);
                    if (!inputResult) _logger.Log("Input delay optimization revert failed", LogLevel.WARNING);
                    if (!gpuResult) _logger.Log("GPU optimization revert failed", LogLevel.WARNING);
                    if (!thermalResult) _logger.Log("Thermal optimization revert failed", LogLevel.WARNING);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting optimizations asynchronously: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Allows the UI to process events
        /// </summary>
        private void DoEvents()
        {
            // Create an empty dispatcher frame
            var frame = new System.Windows.Threading.DispatcherFrame();

            // Queue an action to stop the dispatcher frame
            System.Windows.Threading.Dispatcher.CurrentDispatcher.BeginInvoke(
                System.Windows.Threading.DispatcherPriority.Background,
                new System.Windows.Threading.DispatcherOperationCallback(ExitFrame),
                frame);

            // Process events until the frame is stopped
            System.Windows.Threading.Dispatcher.PushFrame(frame);
        }

        /// <summary>
        /// Exits the dispatcher frame
        /// </summary>
        /// <param name="frame">The frame to exit</param>
        /// <returns>Always returns null</returns>
        private object ExitFrame(object frame)
        {
            ((System.Windows.Threading.DispatcherFrame)frame).Continue = false;
            return null;
        }
    }
}

