using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for hardware compatibility service
    /// </summary>
    public interface IHardwareCompatibilityService
    {
        /// <summary>
        /// Gets whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Initializes the service asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task InitializeAsync();

        /// <summary>
        /// Checks hardware compatibility
        /// </summary>
        /// <param name="hardware">Hardware to check</param>
        /// <returns>Compatibility result</returns>
        object CheckCompatibility(object hardware);

        /// <summary>
        /// Gets compatibility report
        /// </summary>
        /// <returns>Compatibility report</returns>
        object GetCompatibilityReport();

        /// <summary>
        /// Validates optimization asynchronously
        /// </summary>
        /// <param name="optimization">The optimization to validate</param>
        /// <returns>Task with validation result</returns>
        Task<CompatibilityResult> ValidateOptimizationAsync(HardwareSpecificOptimization optimization);

        /// <summary>
        /// Validates optimization asynchronously with force option
        /// </summary>
        /// <param name="optimization">The optimization to validate</param>
        /// <param name="force">Whether to force validation</param>
        /// <returns>Task with validation result</returns>
        Task<CompatibilityResult> ValidateOptimizationAsync(HardwareSpecificOptimization optimization, bool force);

        /// <summary>
        /// Validates power profile asynchronously
        /// </summary>
        /// <param name="profile">The power profile to validate</param>
        /// <returns>Task with validation result</returns>
        Task<CompatibilityResult> ValidatePowerProfileAsync(PowerManagementProfile profile);

        /// <summary>
        /// Validates power profile asynchronously with force option
        /// </summary>
        /// <param name="profile">The power profile to validate</param>
        /// <param name="force">Whether to force validation</param>
        /// <returns>Task with validation result</returns>
        Task<CompatibilityResult> ValidatePowerProfileAsync(PowerManagementProfile profile, bool force);
    }
}
