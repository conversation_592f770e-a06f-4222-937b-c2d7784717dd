using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using CircleUtility.Services;
using CircleUtility.Models;

namespace CircleUtility.Controls
{
    public partial class AnimatedPerformanceSlider : UserControl
    {
        private readonly LoggingService _logger;
        private readonly Storyboard _sliderGlowAnimation;
        private readonly Storyboard _thumbGlowAnimation;
        private readonly Storyboard _valueChangeAnimation;
        private readonly Storyboard _profileChangeAnimation;
        private bool _isInitialized;
        private bool _suppressEvents;

        public event EventHandler<PerformanceProfileChangedEventArgs> ProfileChanged;

        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register(nameof(Title), typeof(string), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata("Performance"));

        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register(nameof(Value), typeof(double), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata(50.0, OnValueChanged));

        public static readonly DependencyProperty CurrentProfileProperty =
            DependencyProperty.Register(nameof(CurrentProfile), typeof(string), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata("Balanced"));

        public static readonly DependencyProperty ComponentTypeProperty =
            DependencyProperty.Register(nameof(ComponentType), typeof(PerformanceComponentType), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata(PerformanceComponentType.System));

        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }

        public double Value
        {
            get => (double)GetValue(ValueProperty);
            set => SetValue(ValueProperty, value);
        }

        public string CurrentProfile
        {
            get => (string)GetValue(CurrentProfileProperty);
            set => SetValue(CurrentProfileProperty, value);
        }

        public PerformanceComponentType ComponentType
        {
            get => (PerformanceComponentType)GetValue(ComponentTypeProperty);
            set => SetValue(ComponentTypeProperty, value);
        }

        public AnimatedPerformanceSlider()
        {
            InitializeComponent();

            _logger = LoggingService.Instance;

            _sliderGlowAnimation = (Storyboard)FindResource("SliderGlowAnimation");
            _thumbGlowAnimation = (Storyboard)FindResource("ThumbGlowAnimation");
            _valueChangeAnimation = (Storyboard)FindResource("ValueChangeAnimation");
            _profileChangeAnimation = (Storyboard)FindResource("ProfileChangeAnimation");

            Loaded += UserControl_Loaded;
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (_isInitialized) return;

            _sliderGlowAnimation?.Begin(this, true);
            _thumbGlowAnimation?.Begin(this, true);
            _profileChangeAnimation?.Begin(this);

            _isInitialized = true;
        }

        private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AnimatedPerformanceSlider slider && !slider._suppressEvents)
            {
                slider.UpdateProfileFromValue((double)e.NewValue);
            }
        }

        private void MainSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_isInitialized && !_suppressEvents)
            {
                _valueChangeAnimation?.Begin(this);
            }
        }

        private void UpdateProfileFromValue(double value)
        {
            string newProfile = value switch
            {
                < 25 => "Power Saver",
                < 75 => "Balanced",
                _ => "Performance"
            };

            if (newProfile == CurrentProfile) return;

            CurrentProfile = newProfile;
            _profileChangeAnimation?.Begin(this);

            ProfileChanged?.Invoke(this, new PerformanceProfileChangedEventArgs
            {
                ComponentType = ComponentType,
                ProfileName = CurrentProfile,
                Value = Value
            });

            _logger.Log($"{ComponentType} profile changed to {CurrentProfile} ({Value}%)", LogLevel.INFO);
        }

        private void PowerSaverButton_Click(object sender, RoutedEventArgs e) => SetProfile(0);
        private void BalancedButton_Click(object sender, RoutedEventArgs e) => SetProfile(50);
        private void PerformanceButton_Click(object sender, RoutedEventArgs e) => SetProfile(100);

        private void SetProfile(double value)
        {
            _suppressEvents = true;
            Value = value;
            _suppressEvents = false;

            UpdateProfileFromValue(value);
            _valueChangeAnimation?.Begin(this);
        }
    }

    public class PerformanceProfileChangedEventArgs : EventArgs
    {
        public PerformanceComponentType ComponentType { get; set; }
        public string ProfileName { get; set; }
        public double Value { get; set; }
    }
}
