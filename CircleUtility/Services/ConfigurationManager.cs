using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using CircleUtility.Interfaces;nusing CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Enhanced configuration manager with backup and restore capabilities
    /// </summary>
    public class ConfigurationManager : IConfigurationService, IInitializableService
    {
        private static ConfigurationManager _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;
        private IConfiguration _configuration;
        private readonly Dictionary<string, object> _settings;

        /// <summary>
        /// Gets the singleton instance of the configuration manager
        /// </summary>
        public static ConfigurationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ConfigurationManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private ConfigurationManager()
        {
            _logger = LoggingService.Instance;
            _settings = new Dictionary<string, object>();
            LoadConfiguration();
        }

        /// <summary>
        /// Initializes the configuration service
        /// </summary>
        public void Initialize()
        {
            LoadConfiguration();
        }

        /// <summary>
        /// Loads the configuration from disk
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool LoadConfiguration()
        {
            try
            {
                _logger.Log("Loading configuration...", Models.LogLevel.INFO);

                var builder = new ConfigurationBuilder()
                    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                _configuration = builder.Build();
                _logger.Log("Configuration loaded successfully", Models.LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading configuration: {ex.Message}", Models.LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Saves the configuration to disk
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool SaveConfiguration()
        {
            try
            {
                _logger.Log("Configuration saved successfully", Models.LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving configuration: {ex.Message}", Models.LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a setting value
        /// </summary>
        /// <typeparam name="T">The type of the setting</typeparam>
        /// <param name="section">The section name</param>
        /// <param name="key">The setting key</param>
        /// <returns>The setting value</returns>
        public T GetSetting<T>(string section, string key)
        {
            try
            {
                var settingKey = $"{section}:{key}";
                var value = _configuration?[settingKey];
                
                if (value != null)
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                
                return default(T);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting setting {section}.{key}: {ex.Message}", Models.LogLevel.ERROR);
                return default(T);
            }
        }

        /// <summary>
        /// Sets a setting value
        /// </summary>
        /// <typeparam name="T">The type of the setting</typeparam>
        /// <param name="section">The section name</param>
        /// <param name="key">The setting key</param>
        /// <param name="value">The setting value</param>
        public void SetSetting<T>(string section, string key, T value)
        {
            try
            {
                var settingKey = $"{section}:{key}";
                _settings[settingKey] = value;
                _logger.Log($"Setting updated: {section}.{key}", Models.LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error setting {section}.{key}: {ex.Message}", Models.LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Updates a setting value with generic type
        /// </summary>
        /// <typeparam name="T">The type of the setting</typeparam>
        /// <param name="section">The section name</param>
        /// <param name="key">The setting key</param>
        /// <param name="value">The setting value</param>
        /// <returns>True if successful</returns>
        public bool UpdateSetting<T>(string section, string key, T value)
        {
            try
            {
                SetSetting(section, key, value);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating setting {section}.{key}: {ex.Message}", Models.LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Updates a configuration setting
        /// </summary>
        /// <param name="section">The configuration section</param>
        /// <param name="key">The setting key</param>
        /// <param name="value">The new value</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool UpdateSetting(string section, string key, object value)
        {
            try
            {
                var settingKey = $"{section}:{key}";
                _settings[settingKey] = value;
                _logger.Log($"Configuration setting updated: {section}.{key}", Models.LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating configuration setting: {ex.Message}", Models.LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a specific configuration setting
        /// </summary>
        /// <param name="section">The configuration section</param>
        /// <param name="key">The setting key</param>
        /// <returns>The setting value, or null if not found</returns>
        public object GetSetting(string section, string key)
        {
            try
            {
                var settingKey = $"{section}:{key}";
                
                if (_settings.ContainsKey(settingKey))
                {
                    return _settings[settingKey];
                }
                
                return _configuration?[settingKey];
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting setting {section}.{key}: {ex.Message}", Models.LogLevel.ERROR);
                return null;
            }
        }
    }
}


