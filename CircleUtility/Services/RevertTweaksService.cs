using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for reverting tweaks and optimizations to default settings
    /// </summary>
    public class RevertTweaksService : IRevertTweaksService
    {
        private static RevertTweaksService _instance;
        private readonly LoggingService _logger;
        private readonly HardwareOptimizationService _hardwareOptimizationService;
        private readonly string _tweakHistoryDirectory;
        private readonly string _tweakHistoryFile;
        private List<TweakHistoryEntry> _tweakHistory;
        private bool _isInitialized;

        /// <summary>
        /// Gets the singleton instance of the RevertTweaksService
        /// </summary>
        public static RevertTweaksService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new RevertTweaksService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Event raised when tweaks are reverted
        /// </summary>
        public event EventHandler<TweaksRevertedEventArgs> TweaksReverted;

        /// <summary>
        /// Initializes a new instance of the RevertTweaksService class
        /// </summary>
        private RevertTweaksService()
        {
            _logger = LoggingService.Instance;
            _hardwareOptimizationService = HardwareOptimizationService.Instance;
            _tweakHistoryDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TweakHistory");
            _tweakHistoryFile = Path.Combine(_tweakHistoryDirectory, "tweak_history.json");
            _tweakHistory = new List<TweakHistoryEntry>();

            // Ensure tweak history directory exists
            if (!Directory.Exists(_tweakHistoryDirectory))
            {
                Directory.CreateDirectory(_tweakHistoryDirectory);
            }

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Initializes the service
        /// </summary>
        private void Initialize()
        {
            try
            {
                _logger.Log("Initializing RevertTweaksService...", LogLevel.INFO);

                // Load tweak history
                LoadTweakHistory();

                // Subscribe to optimization events
                _hardwareOptimizationService.OptimizationApplied += OnOptimizationApplied;
                _hardwareOptimizationService.OptimizationReverted += OnOptimizationReverted;
                _hardwareOptimizationService.PowerProfileApplied += OnPowerProfileApplied;
                _hardwareOptimizationService.PowerProfileReverted += OnPowerProfileReverted;

                _isInitialized = true;
                _logger.Log("RevertTweaksService initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing RevertTweaksService: {ex.Message}", LogLevel.ERROR);
                _isInitialized = false;
            }
        }

        /// <summary>
        /// Loads the tweak history from the history file
        /// </summary>
        private void LoadTweakHistory()
        {
            try
            {
                if (File.Exists(_tweakHistoryFile))
                {
                    string json = File.ReadAllText(_tweakHistoryFile);
                    _tweakHistory = JsonSerializer.Deserialize<List<TweakHistoryEntry>>(json) ?? new List<TweakHistoryEntry>();
                    _logger.Log($"Loaded {_tweakHistory.Count} tweak history entries", LogLevel.INFO);
                }
                else
                {
                    _tweakHistory = new List<TweakHistoryEntry>();
                    _logger.Log("No tweak history file found, starting with empty history", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading tweak history: {ex.Message}", LogLevel.ERROR);
                _tweakHistory = new List<TweakHistoryEntry>();
            }
        }

        /// <summary>
        /// Saves the tweak history to the history file
        /// </summary>
        private void SaveTweakHistory()
        {
            try
            {
                string json = JsonSerializer.Serialize(_tweakHistory, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_tweakHistoryFile, json);
                _logger.Log($"Saved {_tweakHistory.Count} tweak history entries", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving tweak history: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Handles the OptimizationApplied event
        /// </summary>
        private void OnOptimizationApplied(object sender, OptimizationEventArgs e)
        {
            try
            {
                // Add to history if not already present
                if (!_tweakHistory.Any(t => t.Type == OptimizationType.Performance && t.Name == e.Optimization.Name))
                {
                    _tweakHistory.Add(new TweakHistoryEntry
                    {
                        Type = OptimizationType.Performance,
                        Name = e.Optimization.Name,
                        AppliedDate = DateTime.Now,
                        IsActive = true
                    });

                    SaveTweakHistory();
                }
                else
                {
                    // Update existing entry
                    var entry = _tweakHistory.First(t => t.Type == OptimizationType.Performance && t.Name == e.Optimization.Name);
                    entry.AppliedDate = DateTime.Now;
                    entry.IsActive = true;
                    SaveTweakHistory();
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling optimization applied event: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Handles the OptimizationReverted event
        /// </summary>
        private void OnOptimizationReverted(object sender, OptimizationEventArgs e)
        {
            try
            {
                // Update history if present
                var entry = _tweakHistory.FirstOrDefault(t => t.Type == OptimizationType.Performance && t.Name == e.Optimization.Name);
                if (entry != null)
                {
                    entry.IsActive = false;
                    entry.RevertedDate = DateTime.Now;
                    SaveTweakHistory();
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling optimization reverted event: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Handles the PowerProfileApplied event
        /// </summary>
        private void OnPowerProfileApplied(object sender, PowerProfileEventArgs e)
        {
            try
            {
                // Add to history if not already present
                if (!_tweakHistory.Any(t => t.Type == OptimizationType.PowerEfficiency && t.Name == e.Profile.Name))
                {
                    _tweakHistory.Add(new TweakHistoryEntry
                    {
                        Type = OptimizationType.PowerEfficiency,
                        Name = e.Profile.Name,
                        AppliedDate = DateTime.Now,
                        IsActive = true
                    });

                    SaveTweakHistory();
                }
                else
                {
                    // Update existing entry
                    var entry = _tweakHistory.First(t => t.Type == OptimizationType.PowerEfficiency && t.Name == e.Profile.Name);
                    entry.AppliedDate = DateTime.Now;
                    entry.IsActive = true;
                    SaveTweakHistory();
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling power profile applied event: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Handles the PowerProfileReverted event
        /// </summary>
        private void OnPowerProfileReverted(object sender, PowerProfileEventArgs e)
        {
            try
            {
                // Update history if present
                var entry = _tweakHistory.FirstOrDefault(t => t.Type == OptimizationType.PowerEfficiency && t.Name == e.Profile.Name);
                if (entry != null)
                {
                    entry.IsActive = false;
                    entry.RevertedDate = DateTime.Now;
                    SaveTweakHistory();
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling power profile reverted event: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets all active tweaks
        /// </summary>
        /// <returns>The list of active tweaks</returns>
        public List<TweakHistoryEntry> GetActiveTweaks()
        {
            return _tweakHistory.Where(t => t.IsActive).ToList();
        }

        /// <summary>
        /// Gets all tweaks (active and inactive)
        /// </summary>
        /// <returns>The list of all tweaks</returns>
        public List<TweakHistoryEntry> GetAllTweaks()
        {
            return _tweakHistory.ToList();
        }

        /// <summary>
        /// Reverts all active tweaks
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertAllTweaksAsync()
        {
            try
            {
                _logger.Log("Reverting all tweaks...", LogLevel.INFO);

                // Get active tweaks
                var activeTweaks = GetActiveTweaks();
                int revertedCount = 0;

                // Revert optimizations
                foreach (var tweak in activeTweaks.Where(t => t.Type == OptimizationType.Performance))
                {
                    bool result = _hardwareOptimizationService.RevertOptimization(tweak.Name);
                    if (result)
                    {
                        revertedCount++;
                        _logger.Log($"Reverted optimization: {tweak.Name}", LogLevel.SUCCESS);
                    }
                    else
                    {
                        _logger.Log($"Failed to revert optimization: {tweak.Name}", LogLevel.ERROR);
                    }
                }

                // Revert power profiles
                foreach (var tweak in activeTweaks.Where(t => t.Type == OptimizationType.PowerEfficiency))
                {
                    bool result = await _hardwareOptimizationService.RevertPowerProfileAsync(tweak.Name);
                    if (result)
                    {
                        revertedCount++;
                        _logger.Log($"Reverted power profile: {tweak.Name}", LogLevel.SUCCESS);
                    }
                    else
                    {
                        _logger.Log($"Failed to revert power profile: {tweak.Name}", LogLevel.ERROR);
                    }
                }

                // Raise event
                TweaksReverted?.Invoke(this, new TweaksRevertedEventArgs
                {
                    RevertedCount = revertedCount,
                    TotalCount = activeTweaks.Count
                });

                _logger.Log($"Reverted {revertedCount} of {activeTweaks.Count} tweaks", LogLevel.INFO);
                return revertedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting all tweaks: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts tweaks by category
        /// </summary>
        /// <param name="tweakType">The type of tweaks to revert</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RevertTweaksByCategoryAsync(OptimizationType tweakType)
        {
            try
            {
                _logger.Log($"Reverting {tweakType} tweaks...", LogLevel.INFO);

                // Get active tweaks of the specified type
                var activeTweaks = GetActiveTweaks().Where(t => t.Type == tweakType).ToList();
                int revertedCount = 0;

                if (tweakType == OptimizationType.Performance)
                {
                    // Revert optimizations
                    foreach (var tweak in activeTweaks)
                    {
                        bool result = _hardwareOptimizationService.RevertOptimization(tweak.Name);
                        if (result)
                        {
                            revertedCount++;
                            _logger.Log($"Reverted optimization: {tweak.Name}", LogLevel.SUCCESS);
                        }
                        else
                        {
                            _logger.Log($"Failed to revert optimization: {tweak.Name}", LogLevel.ERROR);
                        }
                    }
                }
                else if (tweakType == OptimizationType.PowerEfficiency)
                {
                    // Revert power profiles
                    foreach (var tweak in activeTweaks)
                    {
                        bool result = await _hardwareOptimizationService.RevertPowerProfileAsync(tweak.Name);
                        if (result)
                        {
                            revertedCount++;
                            _logger.Log($"Reverted power profile: {tweak.Name}", LogLevel.SUCCESS);
                        }
                        else
                        {
                            _logger.Log($"Failed to revert power profile: {tweak.Name}", LogLevel.ERROR);
                        }
                    }
                }

                // Raise event
                TweaksReverted?.Invoke(this, new TweaksRevertedEventArgs
                {
                    RevertedCount = revertedCount,
                    TotalCount = activeTweaks.Count,
                    TweakType = tweakType
                });

                _logger.Log($"Reverted {revertedCount} of {activeTweaks.Count} {tweakType} tweaks", LogLevel.INFO);
                return revertedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting {tweakType} tweaks: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }
    }
}



