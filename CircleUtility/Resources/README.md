# CircleUtility Resource Management

This directory contains the centralized resource management system for CircleUtility.

## Structure

```
Resources/
├── ResourceDictionary.xaml          # Master resource dictionary
├── Colors/
│   └── ColorResources.xaml          # Color definitions and brushes
├── Converters/
│   └── ConverterResources.xaml      # Value converter instances
├── Styles/
│   └── ButtonStyles.xaml            # Button style definitions
└── README.md                        # This file
```

## Usage

### In XAML Files

Resources are automatically available in all XAML files through App.xaml. Simply reference them by key:

```xaml
<!-- Using colors -->
<Border Background="{StaticResource BackgroundDarkGrayBrush}"/>

<!-- Using button styles -->
<Button Style="{StaticResource GlowButton}" Content="Click Me"/>

<!-- Using converters -->
<TextBlock Text="{Binding IsEnabled, Converter={StaticResource BoolToYesNoConverter}}"/>
```

### Available Resources

#### Colors
- **Primary Colors**: PrimaryBlue, PrimaryDarkBlue, PrimaryLightBlue
- **Background Colors**: BackgroundBlack, BackgroundDarkGray, BackgroundMediumGray, BackgroundLightGray
- **Border Colors**: BorderBlue, BorderLightBlue, BorderGray
- **Text Colors**: TextWhite, TextLightGray, TextBlue, TextLightBlue
- **Status Colors**: SuccessGreen, WarningYellow, ErrorRed
- **Interaction Colors**: HoverBlue, PressedBlue, HoverGray, PressedGray

#### Button Styles
- **GlowButton**: Animated glowing button for primary actions
- **StandardButton**: Standard button for secondary actions
- **MenuButton**: Button style for navigation menu items

#### Converters
- **BoolToYesNoConverter**: Converts boolean to "Yes"/"No" text
- **BoolToColorConverter**: Converts boolean to colors (configurable)
- **BoolToVisibilityConverter**: Converts boolean to Visibility
- **InverseBoolToVisibilityConverter**: Inverse boolean to Visibility
- **BoolToBackgroundConverter**: Boolean to background color
- **BoolToForegroundConverter**: Boolean to foreground color
- **ToolToDescriptionConverter**: Tool name to description text

#### Common Values
- **Font Families**: ConsoleFont (Consolas), UIFont (Segoe UI)
- **Font Sizes**: HeaderFontSize (24), SubHeaderFontSize (18), NormalFontSize (14), SmallFontSize (12)
- **Margins**: StandardMargin (10), LargeMargin (20), SmallMargin (5)
- **Paddings**: StandardPadding (10,5), LargePadding (15,8), SmallPadding (5,3)
- **Border Thickness**: StandardBorderThickness (1), ThickBorderThickness (2)
- **Corner Radius**: StandardCornerRadius (3), RoundedCornerRadius (5)
- **Animation Durations**: FastAnimation (0.2s), NormalAnimation (0.3s), SlowAnimation (0.5s)

## Adding New Resources

### Adding Colors
1. Add color definition to `Colors/ColorResources.xaml`
2. Create corresponding brush if needed
3. Follow naming convention: `[Purpose][Shade]` (e.g., `PrimaryBlue`, `BackgroundDark`)

### Adding Styles
1. Add style to appropriate file in `Styles/` directory
2. Use descriptive key names
3. Base styles on existing color resources
4. Include hover/pressed states for interactive elements

### Adding Converters
1. Create converter class in `CircleUtility.Converters` namespace
2. Add instance to `Converters/ConverterResources.xaml`
3. Use descriptive key names

## Best Practices

1. **Always use resource references** instead of hardcoded values
2. **Maintain consistent naming** conventions
3. **Group related resources** in appropriate files
4. **Document new resources** in this README
5. **Test resource changes** across all XAML files

## Migration Notes

This centralized resource system replaces:
- Scattered resource definitions in individual XAML files
- Inline styles and hardcoded values
- Duplicate converter instances
- Inconsistent color usage

All existing functionality is preserved while improving maintainability and consistency.
