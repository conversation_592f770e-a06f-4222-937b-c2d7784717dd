using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using CircleUtility.Models;
using CircleUtility.Services;
using CircleUtility.Commands;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// ViewModel for the RevertTweaks view
    /// </summary>
    public class RevertTweaksViewModel : ViewModelBase
    {
        private readonly IRevertTweaksService _revertTweaksService;
        private readonly IConfigurationService _configManager;
        private readonly ITweakChangeTracker _changeTracker;
        private readonly IHardwareOptimizationService _hardwareOptimizationService;
        private bool _isLoading;
        private bool _isReverting;
        private string _statusMessage;
        private bool _isStatusVisible;
        private int _activeTweaksCount;
        private bool _isDevMode;
        private ObservableCollection<TweakHistoryEntry> _activeTweaks;
        private ObservableCollection<TweakHistoryEntry> _recentlyRevertedTweaks;

        /// <summary>
        /// Gets or sets a value indicating whether the view is loading
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether tweaks are being reverted
        /// </summary>
        public bool IsReverting
        {
            get => _isReverting;
            set => SetProperty(ref _isReverting, value);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status message is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets the number of active tweaks
        /// </summary>
        public int ActiveTweaksCount
        {
            get => _activeTweaksCount;
            set => SetProperty(ref _activeTweaksCount, value);
        }

        /// <summary>
        /// Gets or sets the active tweaks
        /// </summary>
        public ObservableCollection<TweakHistoryEntry> ActiveTweaks
        {
            get => _activeTweaks;
            set => SetProperty(ref _activeTweaks, value);
        }

        /// <summary>
        /// Gets or sets the recently reverted tweaks
        /// </summary>
        public ObservableCollection<TweakHistoryEntry> RecentlyRevertedTweaks
        {
            get => _recentlyRevertedTweaks;
            set => SetProperty(ref _recentlyRevertedTweaks, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether dev mode is enabled
        /// </summary>
                private readonly ILoggerService _logger;

        /// <summary>
        /// Gets or sets whether dev mode is enabled
        /// </summary>
        public bool IsDevMode
        {
            get => _isDevMode;
            set => SetProperty(ref _isDevMode, value);
        }

        /// <summary>
        /// Gets the command to revert all tweaks
        /// </summary>
        public ICommand RevertAllTweaksCommand { get; }

        /// <summary>
        /// Gets the command to revert tweaks by category
        /// </summary>
        public ICommand RevertTweaksByCategoryCommand { get; }

        /// <summary>
        /// Gets the command to revert a specific tweak
        /// </summary>
        public ICommand RevertTweakCommand { get; }

        /// <summary>
        /// Gets the command to refresh the tweaks list
        /// </summary>
        public ICommand RefreshTweaksCommand { get; }

        /// <summary>
        /// Initializes a new instance of the RevertTweaksViewModel class
        /// </summary>        private readonly ILoggerService _logger;


        public RevertTweaksViewModel(ILoggerService logger, IHardwareOptimizationService hardwareOptimizationService, IRevertTweaksService revertTweaksService, IConfigurationService configManager, ITweakChangeTracker changeTracker) : base(logger)
        {
            _hardwareOptimizationService = hardwareOptimizationService;
            _revertTweaksService = revertTweaksService;
            _configManager = configManager;
            _changeTracker = changeTracker;
            _activeTweaks = new ObservableCollection<TweakHistoryEntry>();
            _recentlyRevertedTweaks = new ObservableCollection<TweakHistoryEntry>();

            // Check if we're in dev mode
            IsDevMode = (bool)_configManager.GetSetting("advanced", "enabledebugmode");

            // Log the mode we're running in
            _logger.Log($"RevertTweaksViewModel initialized in {(IsDevMode ? "DEV" : "LIVE")} mode", LogLevel.INFO);

            // Initialize commands
            RevertAllTweaksCommand = new RelayCommand(async () => await RevertAllTweaksAsync());
            RevertTweaksByCategoryCommand = new RelayCommand<OptimizationType>(async (tweakType) => await RevertTweaksByCategoryAsync(tweakType));
            RevertTweakCommand = new RelayCommand<TweakHistoryEntry>(async (tweak) => await RevertTweakAsync(tweak));
            RefreshTweaksCommand = new RelayCommand(async () => await LoadTweaksAsync());

            // Subscribe to events
            _revertTweaksService.TweaksReverted += OnTweaksReverted;

            // Load tweaks
            LoadTweaksAsync().ConfigureAwait(false);
        }

        /// <summary>
        /// Loads the tweaks
        /// </summary>        private readonly ILoggerService _logger;
                foreach (var tweak in activeTweaks)
                {
                    ActiveTweaks.Add(tweak);
                }

                // Get all tweaks - use Task.Run to make this operation async
                var allTweaks = await Task.Run(() => _revertTweaksService.GetAllTweaks());
                foreach (var tweak in allTweaks)
                {
                    if (!tweak.IsActive && tweak.RevertedDate.HasValue && tweak.RevertedDate.Value > DateTime.Now.AddDays(-1))
                    {
                        RecentlyRevertedTweaks.Add(tweak);
                    }
                }

                // Update count
                ActiveTweaksCount = ActiveTweaks.Count;

                // Update status
                if (ActiveTweaksCount > 0)
                {
                    StatusMessage = $"Found {ActiveTweaksCount} active tweaks.";
                }
                else
                {
                    StatusMessage = "No active tweaks found.";
                }

                IsStatusVisible = true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading tweaks: {ex.Message}", LogLevel.ERROR);
                StatusMessage = "Error loading tweaks. Please try again.";
                IsStatusVisible = true;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Reverts all tweaks
        /// </summary>        private readonly ILoggerService _logger;


        private async Task RevertAllTweaksAsync()
        {
            try
            {
                IsReverting = true;
                StatusMessage = "Reverting all tweaks...";
                IsStatusVisible = true;

                // Revert all tweaks
                bool result = await _revertTweaksService.RevertAllTweaksAsync();

                // Update status
                if (result)
                {
                    StatusMessage = "All tweaks reverted successfully.";

                    // Mark all active changes as reverted in the change tracker
                    var activeChanges = _changeTracker.GetActiveChanges();
                    foreach (var change in activeChanges)
                    {
                        change.IsActive = false;
                        change.RevertedDate = DateTime.Now;
                    }
                }
                else
                {
                    StatusMessage = "No tweaks were reverted.";
                }

                IsStatusVisible = true;

                // Refresh tweaks
                await LoadTweaksAsync();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting all tweaks: {ex.Message}", LogLevel.ERROR);
                StatusMessage = "Error reverting tweaks. Please try again.";
                IsStatusVisible = true;
            }
            finally
            {
                IsReverting = false;
            }
        }

        /// <summary>
        /// Reverts tweaks by category
        /// </summary>
        /// <param name="tweakType">The type of tweaks to revert</param>        private readonly ILoggerService _logger;


        private async Task RevertTweaksByCategoryAsync(OptimizationType tweakType)
        {
            try
            {
                IsReverting = true;
                StatusMessage = $"Reverting {tweakType} tweaks...";
                IsStatusVisible = true;

                // Revert tweaks by category
                bool result = await _revertTweaksService.RevertTweaksByCategoryAsync(tweakType);

                // Update status
                if (result)
                {
                    StatusMessage = $"{tweakType} tweaks reverted successfully.";

                    // Mark all active changes of this type as reverted in the change tracker
                    var activeChanges = _changeTracker.GetChangesByType(tweakType).Where(c => c.IsActive).ToList();
                    foreach (var change in activeChanges)
                    {
                        change.IsActive = false;
                        change.RevertedDate = DateTime.Now;
                    }
                }
                else
                {
                    StatusMessage = $"No {tweakType} tweaks were reverted.";
                }

                IsStatusVisible = true;

                // Refresh tweaks
                await LoadTweaksAsync();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting {tweakType} tweaks: {ex.Message}", LogLevel.ERROR);
                StatusMessage = "Error reverting tweaks. Please try again.";
                IsStatusVisible = true;
            }
            finally
            {
                IsReverting = false;
            }
        }

        /// <summary>
        /// Reverts a specific tweak
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>        private readonly ILoggerService _logger;


        private async Task RevertTweakAsync(TweakHistoryEntry tweak)
        {
            if (tweak == null)
            {
                return;
            }

            try
            {
                IsReverting = true;
                StatusMessage = $"Reverting {tweak.Name}...";
                IsStatusVisible = true;

                bool result = false;

                // Revert the tweak based on its type
                if (tweak.Type == OptimizationType.Performance)
                {
                    result = _hardwareOptimizationService.RevertOptimization(tweak.Name);
                }
                else if (tweak.Type == OptimizationType.PowerEfficiency)
                {
                    result = await _hardwareOptimizationService.RevertPowerProfileAsync(tweak.Name);
                }

                // Update status
                if (result)
                {
                    StatusMessage = $"{tweak.Name} reverted successfully.";

                    // Track the revert in the change tracker
                    var changes = _changeTracker.GetChangesByTweak(tweak.Name);
                    foreach (var change in changes)
                    {
                        if (change.IsActive)
                        {
                            // Mark the change as reverted
                            change.IsActive = false;
                            change.RevertedDate = DateTime.Now;
                        }
                    }
                }
                else
                {
                    StatusMessage = $"Failed to revert {tweak.Name}.";
                }

                IsStatusVisible = true;

                // Refresh tweaks
                await LoadTweaksAsync();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting tweak {tweak.Name}: {ex.Message}", LogLevel.ERROR);
                StatusMessage = "Error reverting tweak. Please try again.";
                IsStatusVisible = true;
            }
            finally
            {
                IsReverting = false;
            }
        }

        /// <summary>
        /// Handles the TweaksReverted event
        /// </summary>        private readonly ILoggerService _logger;
        }
    }
}





