using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// A stylized card control that supports reveal and hover animations.
    /// </summary>
    public partial class AnimatedCard : UserControl
    {
        private Storyboard _revealAnimation;
        private Storyboard _hoverAnimation;
        private Storyboard _unhoverAnimation;

        // Dependency: Title (used for header text)
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register(nameof(Title), typeof(string), typeof(AnimatedCard), new PropertyMetadata(string.Empty));

        // Dependency: Content (overrides base Content to bind in XAML)
        public static new readonly DependencyProperty ContentProperty =
            DependencyProperty.Register(nameof(Content), typeof(object), typeof(AnimatedCard), new PropertyMetadata(null));

        // Dependency: Delay before playing reveal animation
        public static readonly DependencyProperty AnimationDelayProperty =
            DependencyProperty.Register(nameof(AnimationDelay), typeof(double), typeof(AnimatedCard), new PropertyMetadata(0.0));

        /// <summary>
        /// Title displayed in the card header
        /// </summary>
        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }

        /// <summary>
        /// Content displayed in the card body
        /// </summary>
        public new object Content
        {
            get => GetValue(ContentProperty);
            set => SetValue(ContentProperty, value);
        }

        /// <summary>
        /// Delay (in seconds) before the card reveal animation begins
        /// </summary>
        public double AnimationDelay
        {
            get => (double)GetValue(AnimationDelayProperty);
            set => SetValue(AnimationDelayProperty, value);
        }

        /// <summary>
        /// Initializes a new AnimatedCard control
        /// </summary>
        public AnimatedCard()
        {
            InitializeComponent();

            _revealAnimation = TryFindResource("RevealAnimation") as Storyboard;
            _hoverAnimation = TryFindResource("HoverAnimation") as Storyboard;
            _unhoverAnimation = TryFindResource("UnhoverAnimation") as Storyboard;
        }

        /// <summary>
        /// Starts reveal animation when control is loaded
        /// </summary>
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (_revealAnimation == null) return;

            // Clone animation to safely set delay
            var animation = _revealAnimation.Clone();

            if (AnimationDelay > 0)
            {
                foreach (var timeline in animation.Children)
                {
                    timeline.BeginTime = TimeSpan.FromSeconds(AnimationDelay);
                }
            }

            animation.Begin(this);
        }

        /// <summary>
        /// Handles hover-in animation
        /// </summary>
        private void MainBorder_MouseEnter(object sender, MouseEventArgs e)
        {
            _hoverAnimation?.Begin(this);
        }

        /// <summary>
        /// Handles hover-out animation
        /// </summary>
        private void MainBorder_MouseLeave(object sender, MouseEventArgs e)
        {
            _unhoverAnimation?.Begin(this);
        }
    }
}
