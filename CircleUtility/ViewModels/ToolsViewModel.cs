using CircleUtility.Commands;
using System;
using CircleUtility.Models;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Helpers;
using CircleUtility.Services;
using System.Windows;
using System.Diagnostics;
using System.IO;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the tools view
    /// </summary>
    public class ToolsViewModel : ViewModelBase
    {
        private readonly IUserTrackingService _userTracking;
        private string _selectedTool;

        /// <summary>
        /// Initializes a new instance of the ToolsViewModel class
        /// </summary>        private readonly ILoggerService _logger;


        public ToolsViewModel(ILoggerService logger, IUserTrackingService userTrackingService) : base(logger)
        {
            _userTracking = userTrackingService;
            
            // Initialize commands
            RunToolCommand = new RelayCommand(RunTool);
            
            // Initialize tools
            InitializeTools();
            
            // Set default selected tool
            SelectedTool = Tools[0];
            
            _logger.Log("ToolsViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of tools
        /// </summary>        private readonly ILoggerService _logger;


        public ObservableCollection<string> Tools { get; } = new ObservableCollection<string>();

        /// <summary>
        /// Gets or sets the selected tool
        /// </summary>
        public string SelectedTool
        {
            get => _selectedTool;
            set => SetProperty(ref _selectedTool, value);
        }

        /// <summary>
        /// Gets the run tool command
        /// </summary>
        public ICommand RunToolCommand { get; }

        /// <summary>
        /// Initializes the tools
        /// </summary>        private readonly ILoggerService _logger;


        private void InitializeTools()
        {
            Tools.Add("Disk Cleanup");
            Tools.Add("Registry Cleaner");
            Tools.Add("Driver Updater");
            Tools.Add("Network Optimizer");
            Tools.Add("Startup Manager");
            Tools.Add("Process Priority Manager");
            Tools.Add("Memory Optimizer");
            Tools.Add("Temp File Cleaner");
            Tools.Add("System File Checker");
            Tools.Add("DirectX Diagnostic");
            Tools.Add("GPU Driver Cleaner");
            Tools.Add("Windows Update Reset");
        }

        /// <summary>
        /// Runs the selected tool
        /// </summary>        private readonly ILoggerService _logger;


        private void RunTool()
        {
            if (string.IsNullOrEmpty(SelectedTool))
                return;

            _logger.Log($"Running tool: {SelectedTool}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Tool", $"Ran {SelectedTool}");
            
            // Run the selected tool
            switch (SelectedTool)
            {
                case "Disk Cleanup":
                    RunDiskCleanup();
                    break;
                case "Registry Cleaner":
                    RunRegistryCleaner();
                    break;
                case "Driver Updater":
                    RunDriverUpdater();
                    break;
                case "Network Optimizer":
                    RunNetworkOptimizer();
                    break;
                case "Startup Manager":
                    RunStartupManager();
                    break;
                case "Process Priority Manager":
                    RunProcessPriorityManager();
                    break;
                case "Memory Optimizer":
                    RunMemoryOptimizer();
                    break;
                case "Temp File Cleaner":
                    RunTempFileCleaner();
                    break;
                case "System File Checker":
                    RunSystemFileChecker();
                    break;
                case "DirectX Diagnostic":
                    RunDirectXDiagnostic();
                    break;
                case "GPU Driver Cleaner":
                    RunGpuDriverCleaner();
                    break;
                case "Windows Update Reset":
                    RunWindowsUpdateReset();
                    break;
                default:
                    _logger.Log($"Unknown tool: {SelectedTool}", LogLevel.WARNING);
                    break;
            }
        }        private readonly ILoggerService _logger;



        private void RunDiskCleanup()
        {
            try
            {
                Process.Start("cleanmgr.exe");
                MessageBox.Show("Disk Cleanup launched.", "Disk Cleanup", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to launch Disk Cleanup: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunRegistryCleaner()
        {
            try
            {
                var script = "Get-ChildItem -Path HKCU: | Out-Null; Write-Output 'Registry scan complete. No changes made.'";
                Process.Start(new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-NoProfile -Command \"{script}\"",
                    UseShellExecute = true
                });
                MessageBox.Show("Registry scan complete. (No changes made for safety)", "Registry Cleaner", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to run Registry Cleaner: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunDriverUpdater()
        {
            try
            {
                Process.Start("devmgmt.msc");
                MessageBox.Show("Device Manager opened. Update drivers from there.", "Driver Updater", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open Device Manager: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunNetworkOptimizer()
        {
            try
            {
                var script = "netsh int tcp set global autotuninglevel=normal; netsh int tcp set global rss=enabled; netsh int tcp set global chimney=enabled";
                Process.Start(new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c {script}",
                    Verb = "runas",
                    UseShellExecute = true
                });
                MessageBox.Show("Network optimizations applied.", "Network Optimizer", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to optimize network: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunStartupManager()
        {
            try
            {
                Process.Start("taskmgr.exe");
                MessageBox.Show("Task Manager opened. Go to the Startup tab.", "Startup Manager", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open Task Manager: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunProcessPriorityManager()
        {
            try
            {
                MessageBox.Show("To change process priority, open Task Manager, right-click a process, and set priority.", "Process Priority Manager", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to show process priority info: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunMemoryOptimizer()
        {
            try
            {
                var script = "[System.GC]::Collect(); Clear-Host; Write-Output 'Memory optimization attempted.'";
                Process.Start(new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-NoProfile -Command \"{script}\"",
                    UseShellExecute = true
                });
                MessageBox.Show("Memory optimization attempted.", "Memory Optimizer", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to optimize memory: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunTempFileCleaner()
        {
            try
            {
                int filesDeleted = 0;
                long bytesFreed = 0;
                string[] tempDirs = { Path.GetTempPath(), @"C:\\Windows\\Temp" };
                foreach (var dir in tempDirs)
                {
                    if (Directory.Exists(dir))
                    {
                        foreach (var file in Directory.GetFiles(dir, "*", SearchOption.AllDirectories))
                        {
                            try
                            {
                                var info = new FileInfo(file);
                                bytesFreed += info.Length;
                                File.Delete(file);
                                filesDeleted++;
                            }
                            catch { }
                        }
                    }
                }
                MessageBox.Show($"Deleted {filesDeleted} temp files. Freed {bytesFreed / (1024 * 1024)} MB.", "Temp File Cleaner", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to clean temp files: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunSystemFileChecker()
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = "/c sfc /scannow",
                    Verb = "runas",
                    UseShellExecute = true
                });
                MessageBox.Show("System File Checker started. This may take a while.", "System File Checker", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to start System File Checker: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunDirectXDiagnostic()
        {
            try
            {
                Process.Start("dxdiag.exe");
                MessageBox.Show("DirectX Diagnostic Tool launched.", "DirectX Diagnostic", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to launch DirectX Diagnostic Tool: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunGpuDriverCleaner()
        {
            try
            {
                Process.Start("devmgmt.msc");
                MessageBox.Show("Device Manager opened. Uninstall GPU drivers from Display Adapters.", "GPU Driver Cleaner", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open Device Manager: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }        private readonly ILoggerService _logger;



        private void RunWindowsUpdateReset()
        {
            try
            {
                var script = "net stop wuauserv && net stop bits && net stop cryptsvc && ren C:\\Windows\\SoftwareDistribution SoftwareDistribution.old && ren C:\\Windows\\System32\\catroot2 catroot2.old && net start wuauserv && net start bits && net start cryptsvc";
                Process.Start(new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c {script}",
                    Verb = "runas",
                    UseShellExecute = true
                });
                MessageBox.Show("Windows Update components reset.", "Windows Update Reset", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to reset Windows Update: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}



