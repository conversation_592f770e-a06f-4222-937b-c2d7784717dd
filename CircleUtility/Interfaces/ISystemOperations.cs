using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.Win32;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for system operations that may require administrator privileges
    /// </summary>
    public interface ISystemOperations
    {
        /// <summary>
        /// Checks if the application is running with administrator privileges
        /// </summary>
        /// <returns>True if running as administrator, false otherwise</returns>
        bool IsRunningAsAdmin();

        /// <summary>
        /// Restarts the application with administrator privileges
        /// </summary>
        /// <param name="commandLineArgs">Optional command line arguments to pass to the new process</param>
        /// <returns>True if successful, false otherwise</returns>
        bool RestartAsAdmin(string commandLineArgs = "");

        /// <summary>
        /// Executes a function that requires administrator privileges
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="showPrompt">Whether to show a UAC prompt if not running as admin</param>
        /// <returns>True if successful, false otherwise</returns>
        bool ExecuteAsAdmin(Action action, bool showPrompt = true);

        /// <summary>
        /// Executes a function that requires administrator privileges asynchronously
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="showPrompt">Whether to show a UAC prompt if not running as admin</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        Task<bool> ExecuteAsAdminAsync(Func<Task> action, bool showPrompt = true);

        /// <summary>
        /// Runs a process with administrator privileges
        /// </summary>
        /// <param name="fileName">The process file name</param>
        /// <param name="arguments">The process arguments</param>
        /// <param name="waitForExit">Whether to wait for the process to exit</param>
        /// <returns>The process exit code, or -1 if an error occurred</returns>
        int RunProcessAsAdmin(string fileName, string arguments, bool waitForExit = true);

        /// <summary>
        /// Sets a registry value
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <param name="value">The value to set</param>
        /// <param name="valueKind">The value kind</param>
        /// <returns>True if successful, false otherwise</returns>
        bool SetRegistryValue(string keyPath, string valueName, object value, RegistryValueKind valueKind);

        /// <summary>
        /// Gets a registry value
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <returns>The registry value, or null if not found</returns>
        object GetRegistryValue(string keyPath, string valueName);

        /// <summary>
        /// Deletes a registry value
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <returns>True if successful, false otherwise</returns>
        bool DeleteRegistryValue(string keyPath, string valueName);

        /// <summary>
        /// Creates a registry key
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>True if successful, false otherwise</returns>
        bool CreateRegistryKey(string keyPath);

        /// <summary>
        /// Deletes a registry key
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>True if successful, false otherwise</returns>
        bool DeleteRegistryKey(string keyPath);

        /// <summary>
        /// Checks if a registry key exists
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <returns>True if the key exists, false otherwise</returns>
        bool RegistryKeyExists(string keyPath);

        /// <summary>
        /// Checks if a registry value exists
        /// </summary>
        /// <param name="keyPath">The registry key path</param>
        /// <param name="valueName">The value name</param>
        /// <returns>True if the value exists, false otherwise</returns>
        bool RegistryValueExists(string keyPath, string valueName);

        /// <summary>
        /// Gets all changes that would be made in dev mode
        /// </summary>
        /// <returns>A dictionary of changes</returns>
        Dictionary<string, object> GetPendingChanges();

        /// <summary>
        /// Clears all pending changes
        /// </summary>
        void ClearPendingChanges();
    }
}
