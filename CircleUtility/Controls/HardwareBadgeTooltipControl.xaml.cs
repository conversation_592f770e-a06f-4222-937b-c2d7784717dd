using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for HardwareBadgeTooltipControl.xaml
    /// </summary>
    public partial class HardwareBadgeTooltipControl : UserControl
    {
        private readonly HardwareDetectionBadgeService _badgeService;
        
        /// <summary>
        /// Gets or sets the hardware type
        /// </summary>
        public HardwareType HardwareType
        {
            get { return (HardwareType)GetValue(HardwareTypeProperty); }
            set { SetValue(HardwareTypeProperty, value); }
        }

        /// <summary>
        /// Dependency property for HardwareType
        /// </summary>
        public static readonly DependencyProperty HardwareTypeProperty =
            DependencyProperty.Register("HardwareType", typeof(HardwareType), typeof(HardwareBadgeTooltipControl),
                new PropertyMetadata(HardwareType.CPU, OnHardwareTypeChanged));

        private static void OnHardwareTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HardwareBadgeTooltipControl control && e.NewValue is HardwareType type)
            {
                control.UpdateTooltipContent(type);
            }
        }

        /// <summary>
        /// Initializes a new instance of the HardwareBadgeTooltipControl class
        /// </summary>
        public HardwareBadgeTooltipControl()
        {
            InitializeComponent();
            _badgeService = HardwareDetectionBadgeService.Instance;
        }

        /// <summary>
        /// Updates the tooltip content based on hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        private void UpdateTooltipContent(HardwareType hardwareType)
        {
            var badgeInfo = _badgeService.GetBadgeInfo(hardwareType);
            if (badgeInfo == null || !badgeInfo.IsDetected)
            {
                // Hardware not detected
                TitleText.Text = $"{hardwareType}: Not Detected";
                ManufacturerText.Text = "Unknown";
                DetailsText.Text = "No hardware information available";
                DetectionTimeText.Text = "Never";
                ScoreText.Text = "N/A";
                ScoreText.Foreground = new SolidColorBrush(Colors.Gray);
                return;
            }

            // Set title
            TitleText.Text = $"{hardwareType}: {badgeInfo.Name}";

            // Set manufacturer
            ManufacturerText.Text = badgeInfo.Manufacturer ?? "Unknown";

            // Set details
            DetailsText.Text = badgeInfo.Details ?? "No details available";

            // Set detection time
            DateTime detectionTime = _badgeService.GetDetectionTime(hardwareType);
            DetectionTimeText.Text = detectionTime != DateTime.MinValue
                ? detectionTime.ToString("MMM dd, yyyy hh:mm tt")
                : "Unknown";

            // Set compatibility score
            ScoreText.Text = $"{badgeInfo.CompatibilityScore}% ({badgeInfo.CompatibilityLevel})";

            // Set score color based on compatibility level
            if (badgeInfo.CompatibilityScore >= 90)
            {
                ScoreText.Foreground = new SolidColorBrush(Color.FromRgb(0, 255, 0)); // Green
            }
            else if (badgeInfo.CompatibilityScore >= 75)
            {
                ScoreText.Foreground = new SolidColorBrush(Color.FromRgb(144, 238, 144)); // Light Green
            }
            else if (badgeInfo.CompatibilityScore >= 50)
            {
                ScoreText.Foreground = new SolidColorBrush(Color.FromRgb(255, 255, 0)); // Yellow
            }
            else if (badgeInfo.CompatibilityScore >= 25)
            {
                ScoreText.Foreground = new SolidColorBrush(Color.FromRgb(255, 165, 0)); // Orange
            }
            else
            {
                ScoreText.Foreground = new SolidColorBrush(Color.FromRgb(255, 0, 0)); // Red
            }
        }
    }
}
