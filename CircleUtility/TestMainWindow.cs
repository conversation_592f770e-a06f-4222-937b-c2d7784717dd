using System;
using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Services;

namespace CircleUtility
{
    public class TestMainWindow
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                // Set up logging
                string logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logsDirectory))
                {
                    Directory.CreateDirectory(logsDirectory);
                }

                string logPath = Path.Combine(logsDirectory, "TestMainWindow.log");
                File.AppendAllText(logPath, $"[{DateTime.Now}] Test application starting\n");

                // Initialize services
                File.AppendAllText(logPath, $"[{DateTime.Now}] Initializing services\n");
                var serviceProvider = InitializeServices(logPath);

                // Create application
                Application app = new Application();

                // Get required services
                var hardwareDetectionService = serviceProvider.GetRequiredService<IHardwareDetectionService>();
                var hardwareOptimizationService = serviceProvider.GetRequiredService<IHardwareOptimizationService>();
                var hardwareCompatibilityService = serviceProvider.GetRequiredService<IHardwareCompatibilityService>();
                var performanceMonitoringService = serviceProvider.GetRequiredService<IPerformanceMonitoringService>();
                var benchmarkingService = serviceProvider.GetRequiredService<IBenchmarkingService>();

                // Create main window directly (skip welcome screen)
                File.AppendAllText(logPath, $"[{DateTime.Now}] Creating main window\n");
                MainWindow mainWindow = new MainWindow(
                    hardwareDetectionService,
                    hardwareOptimizationService,
                    hardwareCompatibilityService,
                    performanceMonitoringService,
                    benchmarkingService);

                // Set as main window
                File.AppendAllText(logPath, $"[{DateTime.Now}] Setting main window\n");
                app.MainWindow = mainWindow;

                // Show the window
                File.AppendAllText(logPath, $"[{DateTime.Now}] Showing main window\n");
                mainWindow.Show();

                // Run the application
                File.AppendAllText(logPath, $"[{DateTime.Now}] Running application\n");
                app.Run();
            }
            catch (Exception ex)
            {
                string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestMainWindowError.log");
                File.AppendAllText(logPath, $"[{DateTime.Now}] Error: {ex.Message}\n");
                File.AppendAllText(logPath, $"[{DateTime.Now}] Stack trace: {ex.StackTrace}\n");

                MessageBox.Show($"Error: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private static ServiceProvider InitializeServices(string logPath)
        {
            try
            {
                File.AppendAllText(logPath, $"[{DateTime.Now}] Setting up service collection\n");

                // Create a service collection
                var services = new ServiceCollection();

                // Register all services using standardized extension method
                File.AppendAllText(logPath, $"[{DateTime.Now}] Registering all CircleUtility services\n");
                services.AddCircleUtilityServices(
                    includeUIServices: false,  // No UI services for test window
                    includeSecurityServices: false);  // No security services for test

                // Build the service provider
                File.AppendAllText(logPath, $"[{DateTime.Now}] Building service provider\n");
                var serviceProvider = services.BuildServiceProvider();

                // Initialize all services
                File.AppendAllText(logPath, $"[{DateTime.Now}] Initializing all services\n");
                serviceProvider.InitializeAllServices();

                File.AppendAllText(logPath, $"[{DateTime.Now}] Services initialized successfully\n");

                return serviceProvider;
            }
            catch (Exception ex)
            {
                File.AppendAllText(logPath, $"[{DateTime.Now}] Error initializing services: {ex.Message}\n");
                File.AppendAllText(logPath, $"[{DateTime.Now}] Stack trace: {ex.StackTrace}\n");
                throw;
            }
        }
    }
}

