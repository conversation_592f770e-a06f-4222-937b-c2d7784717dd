using System;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using CircleUtility.Services;
using CircleUtility.Models;
using CircleUtility.Tests.Helpers;

namespace CircleUtility.Tests.Services
{
    [TestFixture]
    public class WindowLoadingManagerTests
    {
        private WindowLoadingManager _windowLoadingManager = null!;

        [SetUp]
        public void Setup()
        {
            // Enable test mode
            TestHelper.EnableTestMode();
            
            // Get the instance of WindowLoadingManager
            _windowLoadingManager = WindowLoadingManager.Instance;
        }

        [TearDown]
        public void TearDown()
        {
            // Disable test mode
            TestHelper.DisableTestMode();
        }

        [Test]
        public async Task StartLoadingSequence_ShouldCompleteSuccessfully()
        {
            // Arrange
            bool stepCompletedEventRaised = false;
            bool loadingCompletedEventRaised = false;
            
            _windowLoadingManager.StepCompleted += (sender, args) =>
            {
                stepCompletedEventRaised = true;
                Console.WriteLine($"Step completed: {args.Step.StepName} - {args.Result.IsSuccessful}");
            };
            
            _windowLoadingManager.LoadingCompleted += (sender, args) =>
            {
                loadingCompletedEventRaised = true;
                Console.WriteLine($"Loading completed in {args.TotalTime.TotalMilliseconds}ms");
                Console.WriteLine($"Successful steps: {args.SuccessfulSteps}, Failed steps: {args.FailedSteps}");
            };

            // Act
            bool result = await _windowLoadingManager.StartLoadingSequence();

            // Assert
            ClassicAssert.IsTrue(result, "Loading sequence should complete successfully");
            ClassicAssert.IsTrue(stepCompletedEventRaised, "Step completed event should be raised");
            ClassicAssert.IsTrue(loadingCompletedEventRaised, "Loading completed event should be raised");

            // Verify validation results
            var validationResults = _windowLoadingManager.GetValidationResults();
            ClassicAssert.IsTrue(validationResults.Count > 0, "Should have validation results");

            foreach (var kvp in validationResults)
            {
                Console.WriteLine($"Validation result for {kvp.Key}: {kvp.Value.IsSuccessful} ({kvp.Value.LoadingTime.TotalMilliseconds:F0}ms)");
                if (!kvp.Value.IsSuccessful)
                {
                    Console.WriteLine($"  Error: {kvp.Value.ErrorMessage}");
                }
            }
        }

        [Test]
        public async Task StartLoadingSequence_ShouldHandleFailuresGracefully()
        {
            // Arrange
            bool loadingFailedEventRaised = false;
            
            _windowLoadingManager.LoadingFailed += (sender, args) =>
            {
                loadingFailedEventRaised = true;
                Console.WriteLine($"Loading failed for step: {args.Step.StepName}");
                Console.WriteLine($"Error: {args.Result.ErrorMessage}");
                Console.WriteLine($"Is recoverable: {args.IsRecoverable}");
                Console.WriteLine($"Recovery action: {args.RecoveryAction}");
            };

            // Act
            bool result = await _windowLoadingManager.StartLoadingSequence();

            // Assert
            // Note: This test might pass or fail depending on the actual window state
            // The important thing is that it handles failures gracefully
            Console.WriteLine($"Loading sequence result: {result}");
            
            if (!result)
            {
                ClassicAssert.IsTrue(loadingFailedEventRaised, "Loading failed event should be raised on failure");
            }

            // Verify we get some validation results even on failure
            var validationResults = _windowLoadingManager.GetValidationResults();
            Console.WriteLine($"Got {validationResults.Count} validation results");
        }

        [Test]
        public void GetValidationResults_ShouldReturnEmptyInitially()
        {
            // Act
            var results = _windowLoadingManager.GetValidationResults();

            // Assert
            ClassicAssert.IsNotNull(results, "Validation results should not be null");
            ClassicAssert.AreEqual(0, results.Count, "Should have no validation results initially");
        }

        [Test]
        public void GetTotalLoadingTime_ShouldReturnZeroInitially()
        {
            // Act
            var totalTime = _windowLoadingManager.GetTotalLoadingTime();

            // Assert
            ClassicAssert.AreEqual(TimeSpan.Zero, totalTime, "Total loading time should be zero initially");
        }

        [Test]
        public async Task WindowLoadingManager_ShouldProvideDetailedTiming()
        {
            // Act
            bool result = await _windowLoadingManager.StartLoadingSequence();

            // Assert
            var totalTime = _windowLoadingManager.GetTotalLoadingTime();
            var validationResults = _windowLoadingManager.GetValidationResults();

            Console.WriteLine($"Total loading time: {totalTime.TotalMilliseconds:F0}ms");
            
            TimeSpan sumOfSteps = TimeSpan.Zero;
            foreach (var kvp in validationResults)
            {
                sumOfSteps = sumOfSteps.Add(kvp.Value.LoadingTime);
                Console.WriteLine($"{kvp.Key}: {kvp.Value.LoadingTime.TotalMilliseconds:F0}ms");
            }
            
            Console.WriteLine($"Sum of individual steps: {sumOfSteps.TotalMilliseconds:F0}ms");
            
            // Total time should be at least as long as the longest step
            // (since steps run sequentially)
            ClassicAssert.IsTrue(totalTime.TotalMilliseconds >= 0, "Total time should be non-negative");
        }

        [Test]
        public async Task WindowLoadingManager_ShouldValidateAllCriticalComponents()
        {
            // Act
            bool result = await _windowLoadingManager.StartLoadingSequence();

            // Assert
            var validationResults = _windowLoadingManager.GetValidationResults();
            
            // Check that all expected components were validated
            string[] expectedComponents = { "LoginWindow", "WelcomeScreen", "MainWindow", "Services", "UI_Components" };
            
            foreach (string component in expectedComponents)
            {
                ClassicAssert.IsTrue(validationResults.ContainsKey(component), 
                    $"Should validate {component}");
                
                var result_component = validationResults[component];
                Console.WriteLine($"{component}: {(result_component.IsSuccessful ? "PASS" : "FAIL")}");
                
                if (!result_component.IsSuccessful)
                {
                    Console.WriteLine($"  Error: {result_component.ErrorMessage}");
                }
            }
        }

        [Test]
        public void WindowLoadingEventArgs_ShouldContainCorrectInformation()
        {
            // Arrange
            var step = new WindowLoadingStep
            {
                StepName = "TestStep",
                Description = "Test step description",
                TimeoutMs = 5000,
                IsRequired = true
            };

            var result = new WindowValidationResult
            {
                IsSuccessful = true,
                Message = "Test successful",
                LoadingTime = TimeSpan.FromMilliseconds(100),
                StepName = "TestStep"
            };

            // Act
            var eventArgs = new WindowLoadingEventArgs(step, result);

            // Assert
            ClassicAssert.AreEqual(step, eventArgs.Step);
            ClassicAssert.AreEqual(result, eventArgs.Result);
        }

        [Test]
        public void WindowLoadingErrorEventArgs_ShouldSetRecoveryInformation()
        {
            // Arrange
            var requiredStep = new WindowLoadingStep
            {
                StepName = "RequiredStep",
                IsRequired = true
            };

            var optionalStep = new WindowLoadingStep
            {
                StepName = "OptionalStep",
                IsRequired = false
            };

            var failureResult = new WindowValidationResult
            {
                IsSuccessful = false,
                ErrorMessage = "Test failure"
            };

            // Act
            var requiredErrorArgs = new WindowLoadingErrorEventArgs(requiredStep, failureResult);
            var optionalErrorArgs = new WindowLoadingErrorEventArgs(optionalStep, failureResult);

            // Assert
            ClassicAssert.IsFalse(requiredErrorArgs.IsRecoverable, "Required step failure should not be recoverable");
            ClassicAssert.IsTrue(optionalErrorArgs.IsRecoverable, "Optional step failure should be recoverable");
            
            ClassicAssert.AreEqual("Application cannot continue", requiredErrorArgs.RecoveryAction);
            ClassicAssert.AreEqual("Continue with reduced functionality", optionalErrorArgs.RecoveryAction);
        }

        [Test]
        public void WindowLoadingCompletedEventArgs_ShouldCalculateStatistics()
        {
            // Arrange
            var results = new Dictionary<string, WindowValidationResult>
            {
                ["Step1"] = new WindowValidationResult { IsSuccessful = true },
                ["Step2"] = new WindowValidationResult { IsSuccessful = true },
                ["Step3"] = new WindowValidationResult { IsSuccessful = false }
            };

            var totalTime = TimeSpan.FromSeconds(5);

            // Act
            var completedArgs = new WindowLoadingCompletedEventArgs(results, totalTime);

            // Assert
            ClassicAssert.AreEqual(2, completedArgs.SuccessfulSteps);
            ClassicAssert.AreEqual(1, completedArgs.FailedSteps);
            ClassicAssert.AreEqual(totalTime, completedArgs.TotalTime);
            ClassicAssert.IsFalse(completedArgs.AllRequiredStepsSuccessful);
        }
    }
}
