using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedTabControl.xaml
    /// </summary>
    public partial class AnimatedTabControl : UserControl
    {
        private ContentPresenter _currentContentPresenter;
        private ContentPresenter _newContentPresenter;
        private int _previousIndex = -1;
        private bool _isAnimating = false;
        private Dictionary<string, Storyboard> _storyboards = new Dictionary<string, Storyboard>();

        /// <summary>
        /// Gets the inner tab control
        /// </summary>
        public TabControl TabControl => InnerTabControl;

        /// <summary>
        /// Gets or sets the animation type
        /// </summary>
        public AnimationType AnimationType { get; set; } = AnimationType.Slide;

        /// <summary>
        /// Initializes a new instance of the AnimatedTabControl class
        /// </summary>
        public AnimatedTabControl()
        {
            InitializeComponent();

            // Create animations programmatically
            CreateAnimations();
        }

        /// <summary>
        /// Creates the animations programmatically
        /// </summary>
        private void CreateAnimations()
        {
            // Create slide in from right animation
            Storyboard slideInFromRight = new Storyboard();

            ThicknessAnimation marginAnimationRight = new ThicknessAnimation
            {
                From = new Thickness(800, 0, -800, 0),
                To = new Thickness(0, 0, 0, 0),
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTargetProperty(marginAnimationRight, new PropertyPath("Margin"));
            slideInFromRight.Children.Add(marginAnimationRight);

            DoubleAnimation opacityAnimationRight = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTargetProperty(opacityAnimationRight, new PropertyPath("Opacity"));
            slideInFromRight.Children.Add(opacityAnimationRight);

            _storyboards["SlideInFromRight"] = slideInFromRight;

            // Create slide out to left animation
            Storyboard slideOutToLeft = new Storyboard();

            ThicknessAnimation marginAnimationLeft = new ThicknessAnimation
            {
                From = new Thickness(0, 0, 0, 0),
                To = new Thickness(-800, 0, 800, 0),
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTargetProperty(marginAnimationLeft, new PropertyPath("Margin"));
            slideOutToLeft.Children.Add(marginAnimationLeft);

            DoubleAnimation opacityAnimationLeft = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTargetProperty(opacityAnimationLeft, new PropertyPath("Opacity"));
            slideOutToLeft.Children.Add(opacityAnimationLeft);

            _storyboards["SlideOutToLeft"] = slideOutToLeft;

            // Create slide in from left animation
            Storyboard slideInFromLeft = new Storyboard();

            ThicknessAnimation marginAnimationInLeft = new ThicknessAnimation
            {
                From = new Thickness(-800, 0, 800, 0),
                To = new Thickness(0, 0, 0, 0),
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTargetProperty(marginAnimationInLeft, new PropertyPath("Margin"));
            slideInFromLeft.Children.Add(marginAnimationInLeft);

            DoubleAnimation opacityAnimationInLeft = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTargetProperty(opacityAnimationInLeft, new PropertyPath("Opacity"));
            slideInFromLeft.Children.Add(opacityAnimationInLeft);

            _storyboards["SlideInFromLeft"] = slideInFromLeft;

            // Create slide out to right animation
            Storyboard slideOutToRight = new Storyboard();

            ThicknessAnimation marginAnimationOutRight = new ThicknessAnimation
            {
                From = new Thickness(0, 0, 0, 0),
                To = new Thickness(800, 0, -800, 0),
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTargetProperty(marginAnimationOutRight, new PropertyPath("Margin"));
            slideOutToRight.Children.Add(marginAnimationOutRight);

            DoubleAnimation opacityAnimationOutRight = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTargetProperty(opacityAnimationOutRight, new PropertyPath("Opacity"));
            slideOutToRight.Children.Add(opacityAnimationOutRight);

            _storyboards["SlideOutToRight"] = slideOutToRight;

            // Create fade in animation
            Storyboard fadeIn = new Storyboard();

            DoubleAnimation fadeInAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = new Duration(TimeSpan.FromSeconds(0.2)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTargetProperty(fadeInAnimation, new PropertyPath("Opacity"));
            fadeIn.Children.Add(fadeInAnimation);

            _storyboards["FadeIn"] = fadeIn;

            // Create fade out animation
            Storyboard fadeOut = new Storyboard();

            DoubleAnimation fadeOutAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = new Duration(TimeSpan.FromSeconds(0.2)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTargetProperty(fadeOutAnimation, new PropertyPath("Opacity"));
            fadeOut.Children.Add(fadeOutAnimation);

            _storyboards["FadeOut"] = fadeOut;
        }

        /// <summary>
        /// Handles the selection changed event for the inner tab control
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void InnerTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isAnimating || InnerTabControl.SelectedIndex == -1)
                return;

            // Get the content presenter for the selected tab
            if (InnerTabControl.SelectedContent != null)
            {
                var tabItem = InnerTabControl.ItemContainerGenerator.ContainerFromItem(InnerTabControl.SelectedItem) as TabItem;
                if (tabItem != null)
                {
                    _newContentPresenter = FindVisualChild<ContentPresenter>(tabItem);

                    if (_newContentPresenter != null && _currentContentPresenter != null && _previousIndex != -1)
                    {
                        // Determine animation direction
                        bool isMovingRight = InnerTabControl.SelectedIndex > _previousIndex;

                        // Animate tab change
                        AnimateTabChange(isMovingRight);
                    }
                    else
                    {
                        // First tab selection, no animation needed
                        _currentContentPresenter = _newContentPresenter;
                    }

                    _previousIndex = InnerTabControl.SelectedIndex;
                }
            }
        }

        /// <summary>
        /// Animates the tab change
        /// </summary>
        /// <param name="isMovingRight">Whether the tab change is moving right</param>
        private void AnimateTabChange(bool isMovingRight)
        {
            _isAnimating = true;

            // Set initial state
            _newContentPresenter.Opacity = 0;

            // Determine which animations to use
            string inAnimation, outAnimation;

            if (AnimationType == AnimationType.Slide)
            {
                inAnimation = isMovingRight ? "SlideInFromRight" : "SlideInFromLeft";
                outAnimation = isMovingRight ? "SlideOutToLeft" : "SlideOutToRight";
            }
            else
            {
                inAnimation = "FadeIn";
                outAnimation = "FadeOut";
            }

            // Clone the storyboards to avoid reuse issues
            Storyboard inStoryboard = _storyboards[inAnimation].Clone();
            Storyboard outStoryboard = _storyboards[outAnimation].Clone();

            // Set the target for the animations
            Storyboard.SetTarget(inStoryboard, _newContentPresenter);
            Storyboard.SetTarget(outStoryboard, _currentContentPresenter);

            // Handle completion
            EventHandler completionHandler = null;
            completionHandler = (s, e) => {
                outStoryboard.Completed -= completionHandler;
                _currentContentPresenter = _newContentPresenter;
                _isAnimating = false;
            };

            outStoryboard.Completed += completionHandler;

            // Start animations
            inStoryboard.Begin();
            outStoryboard.Begin();
        }

        /// <summary>
        /// Finds a visual child of the specified type
        /// </summary>
        /// <typeparam name="T">The type of child to find</typeparam>
        /// <param name="parent">The parent object</param>
        /// <returns>The child of the specified type</returns>
        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);

                if (child != null && child is T)
                    return (T)child;

                T childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }
    }

    /// <summary>
    /// Animation type for tab transitions
    /// </summary>
    public enum AnimationType
    {
        /// <summary>
        /// Slide animation
        /// </summary>
        Slide,

        /// <summary>
        /// Fade animation
        /// </summary>
        Fade
    }
}
