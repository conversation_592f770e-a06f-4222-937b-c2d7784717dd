// Created by Arsenal on 5-17-25 12:15PM
using System;
using CircleUtility.Models;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Controls.Primitives;
using CircleUtility.Services;

namespace CircleUtility.Helpers
{
    /// <summary>
    /// Helper class for security dialogs
    /// </summary>
    public static class SecurityDialogHelper
    {
        private static readonly LoggingService _logger = LoggingService.Instance;
        private static readonly SecurityService _securityService = SecurityService.Instance;

        /// <summary>
        /// Shows a password dialog
        /// </summary>
        /// <param name="title">The dialog title</param>
        /// <param name="message">The dialog message</param>
        /// <param name="password">The entered password</param>
        /// <returns>True if the user clicked OK, false otherwise</returns>
        public static bool ShowPasswordDialog(string title, string message, out string password)
        {
            password = null;

            try
            {
                // Create password dialog
                Window passwordDialog = new Window
                {
                    Title = title,
                    Width = 400,
                    Height = 200,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Application.Current.MainWindow,
                    ResizeMode = ResizeMode.NoResize,
                    Background = System.Windows.Media.Brushes.Black
                };

                // Create layout
                Grid grid = new Grid
                {
                    Margin = new Thickness(20)
                };

                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                // Create header
                TextBlock headerText = new TextBlock
                {
                    Text = message,
                    Foreground = System.Windows.Media.Brushes.White,
                    FontSize = 14,
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 0, 0, 20)
                };
                Grid.SetRow(headerText, 0);

                // Create password box
                PasswordBox passwordBox = new PasswordBox
                {
                    Margin = new Thickness(0, 0, 0, 20),
                    Height = 30,
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(10, 20, 30)),
                    Foreground = System.Windows.Media.Brushes.White,
                    BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 200, 255)),
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(5, 0, 5, 0),
                    VerticalContentAlignment = VerticalAlignment.Center
                };
                Grid.SetRow(passwordBox, 1);

                // Create button panel
                StackPanel buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Right
                };
                Grid.SetRow(buttonPanel, 2);

                // Create buttons
                Button submitButton = new Button
                {
                    Content = "Submit",
                    Width = 100,
                    Height = 30,
                    Margin = new Thickness(0, 0, 10, 0),
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 200, 255)),
                    Foreground = System.Windows.Media.Brushes.Black,
                    BorderThickness = new Thickness(0),
                    IsDefault = true
                };

                Button cancelButton = new Button
                {
                    Content = "Cancel",
                    Width = 100,
                    Height = 30,
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(50, 50, 50)),
                    Foreground = System.Windows.Media.Brushes.White,
                    BorderThickness = new Thickness(0),
                    IsCancel = true
                };

                buttonPanel.Children.Add(submitButton);
                buttonPanel.Children.Add(cancelButton);

                grid.Children.Add(headerText);
                grid.Children.Add(passwordBox);
                grid.Children.Add(buttonPanel);

                passwordDialog.Content = grid;

                // Handle button clicks
                bool dialogResult = false;

                string enteredPassword = null;
                submitButton.Click += (sender, e) =>
                {
                    enteredPassword = passwordBox.Password;
                    dialogResult = true;
                    passwordDialog.Close();
                };

                cancelButton.Click += (sender, e) =>
                {
                    passwordDialog.Close();
                };

                // Show dialog
                passwordDialog.ShowDialog();

                // Set the out parameter
                if (dialogResult)
                {
                    password = enteredPassword;
                }

                return dialogResult;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing password dialog: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Shows an admin authentication dialog
        /// </summary>
        /// <returns>True if authentication is successful, false otherwise</returns>
        public static bool ShowAdminAuthenticationDialog()
        {
            try
            {
                // Show password dialog
                if (ShowPasswordDialog("Admin Authentication", "Please enter the admin password:", out string password))
                {
                    // Authenticate admin
                    bool isAuthenticated = _securityService.AuthenticateUser("admincp123", password);

                    if (isAuthenticated)
                    {
                        _logger.Log("Admin authentication successful", LogLevel.SUCCESS);
                        return true;
                    }
                    else
                    {
                        _logger.Log("Admin authentication failed", LogLevel.WARNING);
                        MessageBox.Show("Authentication failed. Please check your password and try again.",
                            "Authentication Failed", MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing admin authentication dialog: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Shows a user authentication dialog
        /// </summary>
        /// <param name="username">The username</param>
        /// <returns>True if authentication is successful, false otherwise</returns>
        public static bool ShowUserAuthenticationDialog(string username)
        {
            try
            {
                // Show password dialog
                if (ShowPasswordDialog("User Authentication", $"Please enter the password for user '{username}':", out string password))
                {
                    // Authenticate user
                    bool isAuthenticated = _securityService.AuthenticateUser(username, password);

                    if (isAuthenticated)
                    {
                        _logger.Log($"User authentication successful: {username}", LogLevel.SUCCESS);
                        return true;
                    }
                    else
                    {
                        _logger.Log($"User authentication failed: {username}", LogLevel.WARNING);
                        MessageBox.Show("Authentication failed. Please check your password and try again.",
                            "Authentication Failed", MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing user authentication dialog: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Shows a security confirmation dialog
        /// </summary>
        /// <param name="title">The dialog title</param>
        /// <param name="message">The dialog message</param>
        /// <param name="requireAuthentication">Whether to require authentication</param>
        /// <returns>True if confirmed, false otherwise</returns>
        public static bool ShowSecurityConfirmationDialog(string title, string message, bool requireAuthentication = false)
        {
            try
            {
                // Show confirmation dialog
                MessageBoxResult result = MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    // Check if authentication is required
                    if (requireAuthentication)
                    {
                        return ShowAdminAuthenticationDialog();
                    }

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing security confirmation dialog: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }
    }
}

