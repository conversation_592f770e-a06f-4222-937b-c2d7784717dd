using System;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Event arguments for login events
    /// </summary>
    public class LoginEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the username
        /// </summary>
        public string Username { get; }

        /// <summary>
        /// Gets the password
        /// </summary>
        public string Password { get; }

        /// <summary>
        /// Gets a value indicating whether to remember the login
        /// </summary>
        public bool RememberMe { get; }

        /// <summary>
        /// Initializes a new instance of the LoginEventArgs class
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="password">The password</param>
        /// <param name="rememberMe">Whether to remember the login</param>
        public LoginEventArgs(string username, string password, bool rememberMe)
        {
            Username = username;
            Password = password;
            RememberMe = rememberMe;
        }
    }

    /// <summary>
    /// Interface for login screen
    /// </summary>
    public interface ILoginScreen : IScreen
    {
        /// <summary>
        /// Event raised when a login is attempted
        /// </summary>
        event EventHandler<LoginEventArgs> LoginAttempted;

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        string Username { get; set; }

        /// <summary>
        /// Gets or sets the password
        /// </summary>
        string Password { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to remember the login
        /// </summary>
        bool RememberMe { get; set; }

        /// <summary>
        /// Attempts to login with the current credentials
        /// </summary>
        void AttemptLogin();

        /// <summary>
        /// Resets the login fields
        /// </summary>
        void ResetFields();
    }
}
