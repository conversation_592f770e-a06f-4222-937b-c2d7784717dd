using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Text;
using CircleUtility.Models;
using Microsoft.Extensions.Logging;
using Discord;
using Discord.WebSocket;
using Microsoft.Extensions.Configuration;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Discord Bot Service for reading messages and processing commands
    /// Note: This is a simplified implementation. For full Discord Bot functionality,
    /// you would need Discord.NET library and proper bot token
    /// </summary>
    public class DiscordBotService : IDiscordService, IDisposable
    {
        private static readonly HttpClient _httpClient = new();
        private readonly string _commandsWebhookUrl;
        private readonly EnhancedDiscordUserService _userService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DiscordBotService> _logger;
        private System.Timers.Timer _messageCheckTimer;
        private readonly string _lastMessageFile;
        private DateTime _lastCheckedTime;
        private readonly SemaphoreSlim _processingLock = new(1, 1);
        private bool _isDisposed;
        private readonly DiscordSocketClient _client;
        private bool _isInitialized;
        private bool _isConnected;
        private readonly string _botToken;
        private readonly string _emergencyUserId;

        public bool IsConnected => _isConnected;

        public DiscordBotService(
            EnhancedDiscordUserService userService, 
            IConfiguration configuration,
            ILogger<DiscordBotService> logger)
        {
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Get webhook URL from configuration
            _commandsWebhookUrl = _configuration["Discord:WebhookUrl"];
            if (string.IsNullOrEmpty(_commandsWebhookUrl))
            {
                _logger.LogError("Discord webhook URL is not configured");
                throw new InvalidOperationException("Discord webhook URL is not configured");
            }
            
            if (!Uri.TryCreate(_commandsWebhookUrl, UriKind.Absolute, out _))
            {
                _logger.LogError("Invalid Discord webhook URL format");
                throw new InvalidOperationException("Invalid Discord webhook URL format");
            }
            
            // File to track last checked message
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var appFolder = Path.Combine(appDataPath, "CircleUtility");
                Directory.CreateDirectory(appFolder);
                
                _lastMessageFile = Path.Combine(appFolder, "last_discord_check.txt");
                _lastCheckedTime = LoadLastCheckedTime();
                
                StartMessageMonitoring();
                _logger.LogInformation("Discord Bot Service initialized");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Discord Bot Service");
                throw;
            }

            var config = new DiscordSocketConfig
            {
                GatewayIntents = GatewayIntents.AllUnprivileged | GatewayIntents.MessageContent,
                AlwaysDownloadUsers = true,
                MessageCacheSize = 100
            };
            
            _client = new DiscordSocketClient(config);
            
            // Set up event handlers
            _client.Log += LogAsync;
            _client.Ready += ReadyAsync;
            _client.MessageReceived += MessageReceivedAsync;
            _client.SlashCommandExecuted += SlashCommandExecutedAsync;

            _botToken = _configuration["Discord:BotToken"];
            _emergencyUserId = _configuration["Discord:EmergencyUserId"] ?? string.Empty;
            if (string.IsNullOrWhiteSpace(_botToken))
                throw new InvalidOperationException("Discord bot token is not configured in appsettings.json");
        }

        // Public constructor for DI compatibility
        public DiscordBotService(EnhancedDiscordUserService userService, IConfiguration configuration, ILogger<DiscordBotService> logger, bool forDI = false) : this(userService, configuration, logger) { }

        /// <summary>
        /// Starts monitoring for Discord messages every 30 seconds
        /// </summary>
        private void StartMessageMonitoring()
        {
            try
            {
                _messageCheckTimer = new System.Timers.Timer(30 * 1000); // 30 seconds
                _messageCheckTimer.Elapsed += OnMessageCheckTimer;
                _messageCheckTimer.AutoReset = true;
                _messageCheckTimer.Start();

                Console.WriteLine("👁️ Discord message monitoring started (30-second intervals)");
                
                // Send startup notification
                _ = Task.Run(() => SendNotification("🚀 Circle Utility Discord Bot is now online and monitoring for .addprofile commands!"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error starting message monitoring: {ex.Message}");
            }
        }

        /// <summary>
        /// Timer event handler
        /// </summary>
        private async void OnMessageCheckTimer(object sender, ElapsedEventArgs e)
        {
            // Prevent reentrancy
            if (!_processingLock.Wait(0))
            {
                _logger.LogWarning("Previous command check is still in progress");
                return;
            }

            try
            {
                await CheckForCommands();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in message check timer");
            }
            finally
            {
                _processingLock.Release();
            }
        }

        /// <summary>
        /// Checks for new .addprofile commands
        /// Note: This is a simulation since we can't read Discord messages with webhooks
        /// In a real implementation, you'd use Discord Bot API
        /// </summary>
        private async Task CheckForCommands()
        {
            try
            {
                Console.WriteLine("🔍 Checking for Discord commands...");

                // Simulate checking for commands
                // In a real implementation, you would:
                // 1. Use Discord Bot API to read messages from the channel
                // 2. Filter messages since last check time
                // 3. Process .addprofile commands

                // For demonstration, let's create a sample command file that users can create
                await CheckForCommandFile();
                
                // Update last checked time
                _lastCheckedTime = DateTime.Now;
                await SaveLastCheckedTime();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error checking for commands: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks for a command file that users can create to simulate Discord commands
        /// </summary>
        private async Task CheckForCommandFile()
        {
            string commandFile = null;
            string archiveFile = null;
            
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var appFolder = Path.Combine(appDataPath, "CircleUtility");
                commandFile = Path.Combine(appFolder, "discord_commands.txt");
                
                if (!File.Exists(commandFile)) return;
                
                _logger.LogInformation("Found Discord commands file, processing...");
                
                // Read all commands first
                var commands = (await File.ReadAllLinesAsync(commandFile))
                    .Where(cmd => !string.IsNullOrWhiteSpace(cmd) && cmd.Trim().Length > 0)
                    .Select(cmd => cmd.Trim())
                    .Distinct()
                    .ToList();
                
                if (commands.Count == 0) return;
                
                // Create archive directory if it doesn't exist
                var archiveDir = Path.Combine(appFolder, "ProcessedCommands");
                Directory.CreateDirectory(archiveDir);
                
                // Create archive file with timestamp
                archiveFile = Path.Combine(archiveDir, $"commands_{DateTime.UtcNow:yyyyMMdd_HHmmss}.txt");
                
                // Process each command with rate limiting
                foreach (var command in commands)
                {
                    try
                    {
                        await ProcessCommand(command);
                        await Task.Delay(500); // Rate limiting
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing command: {Command}", command);
                    }
                }
                
                // Move the processed file to archive
                File.Move(commandFile, archiveFile, true);
                _logger.LogInformation("Commands processed and archived to: {ArchiveFile}", archiveFile);
            }
            catch (Exception ex) when (ex is IOException || ex is UnauthorizedAccessException)
            {
                _logger.LogError(ex, "File operation failed while processing commands");
                
                // Try to recover by moving the file to a different name if it still exists
                try
                {
                    if (File.Exists(commandFile) && !string.IsNullOrEmpty(archiveFile))
                    {
                        var errorFile = $"{archiveFile}.error";
                        File.Move(commandFile, errorFile, true);
                        _logger.LogWarning("Recovered command file to: {ErrorFile}", errorFile);
                    }
                }
                catch (Exception moveEx)
                {
                    _logger.LogError(moveEx, "Failed to recover command file");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error processing command file");
            }
        }

        /// <summary>
        /// Processes a Discord command
        /// </summary>
        private async Task ProcessCommand(string command)
        {
            try
            {
                Console.WriteLine($"⚡ Processing command: {command}");

                if (command.StartsWith(".addprofile", StringComparison.OrdinalIgnoreCase))
                {
                    await ProcessAddProfileCommand(command);
                }
                else if (command.StartsWith(".listusers", StringComparison.OrdinalIgnoreCase))
                {
                    await ProcessListUsersCommand();
                }
                else if (command.StartsWith(".help", StringComparison.OrdinalIgnoreCase))
                {
                    await ProcessHelpCommand();
                }
                else
                {
                    await SendResponse($"❓ Unknown command: {command}. Type .help for available commands.");
                }
            }
            catch (Exception ex)
            {
                await SendResponse($"❌ Error processing command: {ex.Message}");
                Console.WriteLine($"🚨 Error processing command: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes .addprofile command
        /// Format: .addprofile username password displayname [role]
        /// </summary>
        private async Task ProcessAddProfileCommand(string command)
        {
            try
            {
                var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                
                if (parts.Length < 4)
                {
                    await SendResponse("❌ **Invalid format!**\n📝 **Usage:** `.addprofile username password displayname [role]`\n🔹 **Role:** `user` or `admin` (default: user)");
                    return;
                }

                string username = parts[1].Trim();
                string password = parts[2]; // In a real app, passwords should be hashed
                string displayName = string.Join(" ", parts.Skip(3).Take(parts.Length - 4));
                string role = parts.Length > 4 ? parts[^1].Trim() : "User";

                // Input validation
                if (string.IsNullOrWhiteSpace(username) || username.Length < 3 || username.Length > 20)
                {
                    await SendResponse("❌ **Invalid username!** Must be 3-20 characters long.");
                    return;
                }

                if (password.Length < 8)
                {
                    await SendResponse("❌ **Password too short!** Must be at least 8 characters.");
                    return;
                }

                // Validate role
                if (!role.Equals("User", StringComparison.OrdinalIgnoreCase) && 
                    !role.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                {
                    role = "User";
                }

                // Check if user already exists
                var existingUser = (await _userService.LoadUsersFromFileAsync())
                    .FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));

                if (existingUser != null)
                {
                    await SendResponse($"❌ **User already exists:** `{username}`");
                    return;
                }


                // Create new user
                var newUser = new DiscordUser
                {
                    Username = username,
                    Password = password, // In a real app, this should be hashed
                    DisplayName = string.IsNullOrWhiteSpace(displayName) ? username : displayName,
                    Role = role,
                    IsActive = true,
                    DiscordId = $"discord_{Guid.NewGuid()}",
                    JoinDate = DateTime.UtcNow,
                    Email = $"{username}@discord.local",
                    LastModified = DateTime.UtcNow
                };

                // Add user with rate limiting
                bool success = await _userService.AddUserAsync(newUser);
                
                if (success)
                {
                    _logger.LogInformation("Added new user via Discord: {Username} ({Role})", username, role);
                    await SendResponse(
                        $"✅ **User Added Successfully!**\n" +
                        $"👤 **Username:** `{username}`\n" +
                        $"🏷️ **Role:** `{role}`\n" +
                        $"📝 **Display Name:** `{newUser.DisplayName}`\n" +
                        $"📅 **Added:** {DateTime.UtcNow:yyyy-MM-dd HH:mm} UTC");
                }
                else
                {
                    _logger.LogWarning("Failed to add user: {Username}", username);
                    await SendResponse($"❌ **Failed to add user:** `{username}`\n⚠️ Please try again or contact support.");
                }
            }
            catch (Exception ex)
            {
                await SendResponse($"❌ **Error:** {ex.Message}");
                Console.WriteLine($"🚨 Error in .addprofile: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes .listusers command
        /// </summary>
        private async Task ProcessListUsersCommand()
        {
            try
            {
                var users = await _userService.LoadUsersFromFileAsync(forceRefresh: true);
                
                var response = new StringBuilder();
                response.AppendLine("📋 **Current Users:**");
                response.AppendLine($"👥 **Total:** {users.Count} users");
                response.AppendLine($"🔹 **Regular:** {users.Count(u => !u.IsAdmin)}");
                response.AppendLine($"🔸 **Admin:** {users.Count(u => u.IsAdmin)}");
                response.AppendLine();

                response.AppendLine("**👑 Admin Users:**");
                foreach (var admin in users.Where(u => u.IsAdmin).Take(10))
                {
                    response.AppendLine($"• `{admin.Username}` - {admin.DisplayName}");
                }

                response.AppendLine();
                response.AppendLine("**👤 Regular Users:**");
                foreach (var user in users.Where(u => !u.IsAdmin).Take(10))
                {
                    response.AppendLine($"• `{user.Username}` - {user.DisplayName}");
                }

                if (users.Count > 20)
                {
                    response.AppendLine($"\n*... and {users.Count - 20} more users*");
                }

                await SendResponse(response.ToString());
            }
            catch (Exception ex)
            {
                await SendResponse($"❌ Error listing users: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes .help command
        /// </summary>
        private async Task ProcessHelpCommand()
        {
            var help = @"🤖 **Circle Utility Discord Bot Commands**

**📝 Available Commands:**
• `.addprofile username password displayname [role]` - Add new user
• `.listusers` - Show current users
• `.help` - Show this help message

**📋 Examples:**
• `.addprofile john mypass123 ""John Doe"" user`
• `.addprofile admin adminpass ""Administrator"" admin`

**📁 Alternative Method:**
Create a file: `Documents/CircleUtility/discord_commands.txt`
Add commands (one per line), the bot will process them automatically.

**⏰ Bot checks for commands every 30 seconds.**";

            await SendResponse(help);
        }

        /// <summary>
        /// Sends response to Discord
        /// </summary>
        private async Task SendResponse(string message, int maxRetries = 2)
        {
            if (string.IsNullOrEmpty(message) || message.Length > 2000)
            {
                _logger.LogWarning("Discord message is empty or too long");
                return;
            }

            var payload = new
            {
                content = message,
                allowed_mentions = new { parse = Array.Empty<string>() }
            };

            var json = JsonSerializer.Serialize(payload);
            using var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

            int attempt = 0;
            Exception lastError = null;

            while (attempt <= maxRetries)
            {
                attempt++;
                
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    var response = await _httpClient.PostAsync(
                        _commandsWebhookUrl, 
                        httpContent, 
                        cts.Token);
                    
                    response.EnsureSuccessStatusCode();
                    _logger.LogDebug("Successfully sent message to Discord");
                    return;
                }
                catch (HttpRequestException ex) when (ex.StatusCode.HasValue && (int)ex.StatusCode.Value >= 500)
                {
                    // Server error, retry with backoff
                    lastError = ex;
                    if (attempt <= maxRetries)
                    {
                        var backoff = TimeSpan.FromSeconds(Math.Pow(2, attempt));
                        _logger.LogWarning(ex, "Discord server error, retrying in {Backoff}ms (attempt {Attempt}/{MaxAttempts})", 
                            backoff.TotalMilliseconds, attempt, maxRetries);
                        await Task.Delay(backoff);
                    }
                }
                catch (Exception ex) when (ex is TaskCanceledException || ex is OperationCanceledException)
                {
                    lastError = ex;
                    _logger.LogWarning("Request to Discord timed out");
                    if (attempt > maxRetries) break;
                }
                catch (Exception ex)
                {
                    lastError = ex;
                    _logger.LogError(ex, "Failed to send message to Discord");
                    break;
                }
            }

            // If we get here, all attempts failed
            _logger.LogError(lastError, "Failed to send message to Discord after {Attempts} attempts", attempt);
        }

        /// <summary>
        /// Sends notification to Discord
        /// </summary>
        private async Task SendNotification(string message)
        {
            await SendResponse(message);
        }

        /// <summary>
        /// Loads last checked time from file
        /// </summary>
        private DateTime LoadLastCheckedTime()
        {
            try
            {
                if (File.Exists(_lastMessageFile))
                {
                    var timeStr = File.ReadAllText(_lastMessageFile);
                    if (DateTime.TryParse(timeStr, out DateTime lastTime))
                    {
                        return lastTime;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error loading last checked time: {ex.Message}");
            }

            return DateTime.Now.AddMinutes(-5); // Default to 5 minutes ago
        }

        /// <summary>
        /// Saves last checked time to file
        /// </summary>
        private async Task SaveLastCheckedTime()
        {
            try
            {
                await File.WriteAllTextAsync(_lastMessageFile, _lastCheckedTime.ToString("O"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error saving last checked time: {ex.Message}");
            }
        }

        public void Initialize()
        {
            if (_isInitialized) return;

            // Initialization logic (e.g., connect to Discord)
            Connect().ConfigureAwait(false).GetAwaiter().GetResult(); 
            _isInitialized = true;
            _logger.LogInformation("DiscordBotService initialized.");
        }

        public async Task<bool> Connect()
        {
            if (IsConnected) return true;
            try
            {
                await _client.LoginAsync(TokenType.Bot, _botToken);
                await _client.StartAsync();
                _isConnected = true;
                _logger.LogInformation("Discord client connected.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting to Discord");
                _isConnected = false;
                return false;
            }
        }

        public async Task<bool> Disconnect()
        {
            if (!IsConnected) return true;
            try
            {
                await _client.StopAsync();
                await _client.LogoutAsync();
                _isConnected = false;
                _logger.LogInformation("Discord client disconnected.");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from Discord");
                return false;
            }
        }

        public async Task SendMessage(string message)
        {
            // This method is a bit tricky with webhooks only for commands.
            // For now, let's use the SendNotification logic which uses the command webhook.
            // In a real bot, you'd send to a specific channel.
            await SendNotification(message);
        }

        public async Task StopAsync()
        {
            await Disconnect();
            Dispose();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed) return;

            if (disposing)
            {
                // Dispose managed state (managed objects)
                _messageCheckTimer?.Stop();
                _messageCheckTimer?.Dispose();
                _processingLock?.Dispose();
                _client?.Dispose();
            }

            _isDisposed = true;
            _logger?.LogInformation("Discord Bot Service disposed");
        }

        ~DiscordBotService()
        {
            Dispose(false);
        }

        // Event Handlers (stubs - implement actual logic)
        private Task LogAsync(Discord.LogMessage msg)
        {
            _logger.LogInformation(msg.ToString());
            return Task.CompletedTask;
        }

        private Task ReadyAsync()
        {
            _logger.LogInformation($"Bot is connected as {_client.CurrentUser.Username}#{_client.CurrentUser.Discriminator}");
            _isConnected = true;
            return Task.CompletedTask;
        }

        private async Task MessageReceivedAsync(Discord.WebSocket.SocketMessage message)
        {
            if (message.Author.IsBot) return;
            var content = message.Content.Trim();
            if (content.Equals("?alive", StringComparison.OrdinalIgnoreCase))
            {
                await message.Channel.SendMessageAsync("Yes, The Circle's Utility is alive and running! 🟢");
            }
            else if (content.StartsWith("?emergency", StringComparison.OrdinalIgnoreCase))
            {
                var mention = !string.IsNullOrEmpty(_emergencyUserId) ? $"<@{_emergencyUserId}> " : string.Empty;
                var msg = content.Length > 10 ? content.Substring(10).Trim() : "Emergency alert triggered from The Circle's Utility!";
                await message.Channel.SendMessageAsync($"{mention}**EMERGENCY ALERT** {mention}\n{msg}");
            }
            else if (content.Equals("?about", StringComparison.OrdinalIgnoreCase))
            {
                await message.Channel.SendMessageAsync("The Circle's Utility - System optimization and management tool. Version 1.1.2");
            }
        }

        private async Task SlashCommandExecutedAsync(Discord.WebSocket.SocketSlashCommand command)
        {
            // Handle slash commands
            // Example: await command.RespondAsync($"You executed {command.Data.Name}");
            await Task.CompletedTask; // Placeholder
        }
    }
}
