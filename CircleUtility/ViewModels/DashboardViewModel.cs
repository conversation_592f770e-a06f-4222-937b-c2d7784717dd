using CircleUtility.Commands;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;
using CircleUtility.Controls;
using CircleUtility.Helpers;
using CircleUtility.Models;
using CircleUtility.Services;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the dashboard view
    /// </summary>
    public class DashboardViewModel : ViewModelBase
    {
        private readonly ILoggerService _logger;
        private readonly ISystemOptimizationService _systemOptimization;
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;
        private readonly IHardwareOptimizationService _hardwareOptimizationService;
        private readonly IHardwareRecommendationService _hardwareRecommendationService;
        private readonly IHardwareCompatibilityService _hardwareCompatibilityService;
        private readonly IHardwareDetectionBadgeService _hardwareBadgeService;
        private readonly INotificationService _notificationService;
        private string _systemInfo;
        private string _cpuUsage;
        private string _ramUsage;
        private string _gpuUsage;
        private string _networkUsage;
        private double _cpuUsageValue;
        private double _ramUsageValue;
        private double _gpuUsageValue;
        private double _networkUsageValue;
        private double _cpuTemperature;
        private double _storageUsage;
        private string _searchText;
        private bool _isMonitoringEnabled;
        private bool _isLoading;
        private DispatcherTimer _resourceTimer;
        private GPUVendor _detectedGpuVendor;

        // Hardware info properties
        private string _cpuInfo;
        private string _gpuInfo;
        private string _ramInfo;
        private string _storageInfo;
        private string _networkInfo;
        private string _osInfo;

        // Hardware badge properties
        private int _cpuCompatibilityScore;
        private int _gpuCompatibilityScore;
        private int _ramCompatibilityScore;
        private int _storageCompatibilityScore;
        private int _networkCompatibilityScore;
        private int _systemCompatibilityScore;

        private string _cpuBadgeTooltip;
        private string _gpuBadgeTooltip;
        private string _ramBadgeTooltip;
        private string _storageBadgeTooltip;
        private string _networkBadgeTooltip;
        private string _systemBadgeTooltip;

        private bool _isCpuBadgeAnimating;
        private bool _isGpuBadgeAnimating;
        private bool _isRamBadgeAnimating;
        private bool _isStorageBadgeAnimating;
        private bool _isNetworkBadgeAnimating;
        private bool _isSystemBadgeAnimating;

        // System health score
        private int _systemHealthScore;

        // Performance graph references
        private PerformanceGraphControl _cpuGraph;
        private PerformanceGraphControl _ramGraph;
        private PerformanceGraphControl _gpuGraph;
        private PerformanceGraphControl _networkGraph;

        /// <summary>
        /// Initializes a new instance of the DashboardViewModel class
        /// </summary>
        /// <param name="hardwareDetectionService">The hardware detection service</param>
        /// <param name="performanceMonitoringService">The performance monitoring service</param>
        /// <param name="hardwareOptimizationService">The hardware optimization service</param>
        /// <param name="hardwareRecommendationService">The hardware recommendation service</param>
        /// <param name="hardwareCompatibilityService">The hardware compatibility service</param>
        public DashboardViewModel(
            IHardwareDetectionService hardwareDetectionService,
            IHardwareOptimizationService hardwareOptimizationService,
            IHardwareCompatibilityService hardwareCompatibilityService,
            IHardwareRecommendationService hardwareRecommendationService,
            IPerformanceMonitoringService performanceMonitoringService,
            IBenchmarkingService benchmarkingService,
            ILoggerService logger,
            INotificationService notificationService,
            ISystemOptimizationService systemOptimizationService,
            IHardwareDetectionBadgeService hardwareBadgeService
        ) : base(logger)
        {
            _hardwareDetectionService = hardwareDetectionService;
            _hardwareOptimizationService = hardwareOptimizationService;
            _hardwareCompatibilityService = hardwareCompatibilityService;
            _hardwareRecommendationService = hardwareRecommendationService;
            _performanceMonitoringService = performanceMonitoringService;
            _benchmarkingService = benchmarkingService;
            _notificationService = notificationService;
            _systemOptimization = systemOptimizationService;
            _hardwareBadgeService = hardwareBadgeService;

            // Initialize collections
            SearchResults = new ObservableCollection<SearchResult>();
            RecentActivities = new ObservableCollection<string>();

            // Initialize commands
            RefreshSystemInfoCommand = new RelayCommand(RefreshSystemInfo);
            OptimizeSystemCommand = new RelayCommand(OptimizeSystem);
            SearchCommand = new RelayCommand(PerformSearch);
            RunBenchmarkCommand = new RelayCommand(RunBenchmark);
            CleanTempFilesCommand = new RelayCommand(CleanTempFiles);
            RevertAllTweaksCommand = new RelayCommand(RevertAllTweaks);
            OptimizeNetworkCommand = new RelayCommand(OptimizeNetwork);
            ReduceInputDelayCommand = new RelayCommand(ReduceInputDelay);
            OptimizeGpuCommand = new RelayCommand(OptimizeGpu);

            // New commands for hardware-specific optimizations
            OptimizeAmdCommand = new RelayCommand(OptimizeAmd);
            OptimizeNvidiaCommand = new RelayCommand(OptimizeNvidia);
            OptimizeRecommendedCommand = new RelayCommand(OptimizeRecommended);
            OptimizeThermalCommand = new RelayCommand(OptimizeThermal);
            OptimizeInputDevicesCommand = new RelayCommand(OptimizeInputDevices);
            TweakSystemCommand = new RelayCommand(TweakSystem);

            // Test notification command
            ShowTestNotificationsCommand = new RelayCommand(ShowTestNotifications);

            // Subscribe to hardware badge updates
            _hardwareBadgeService.BadgesUpdated += OnHardwareBadgesUpdated;

            // Initialize system info and hardware badges
            RefreshSystemInfo();

            // Initialize monitoring (default to enabled)
            IsMonitoringEnabled = true;

            // Initialize sample search results
            InitializeSampleSearchResults();

            // Initialize system health score
            CalculateSystemHealthScore();

            // Add welcome notification
            _notificationService.AddInfoNotification(
                "Welcome to Circle Utility",
                "The dashboard has been enhanced with real-time performance monitoring and a notification center.",
                "System",
                new RelayCommand(ShowDashboardTour),
                "TAKE A TOUR");

            _logger.Log("DashboardViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets or sets the system information
        /// </summary>
        public string SystemInfo
        {
            get => _systemInfo;
            set => SetProperty(ref _systemInfo, value);
        }

        /// <summary>
        /// Gets or sets the CPU information
        /// </summary>
        public string CpuInfo
        {
            get => _cpuInfo;
            set => SetProperty(ref _cpuInfo, value);
        }

        /// <summary>
        /// Gets or sets the GPU information
        /// </summary>
        public string GpuInfo
        {
            get => _gpuInfo;
            set => SetProperty(ref _gpuInfo, value);
        }

        /// <summary>
        /// Gets or sets the RAM information
        /// </summary>
        public string RamInfo
        {
            get => _ramInfo;
            set => SetProperty(ref _ramInfo, value);
        }

        /// <summary>
        /// Gets or sets the storage information
        /// </summary>
        public string StorageInfo
        {
            get => _storageInfo;
            set => SetProperty(ref _storageInfo, value);
        }

        /// <summary>
        /// Gets or sets the network information
        /// </summary>
        public string NetworkInfo
        {
            get => _networkInfo;
            set => SetProperty(ref _networkInfo, value);
        }

        /// <summary>
        /// Gets or sets the OS information
        /// </summary>
        public string OsInfo
        {
            get => _osInfo;
            set => SetProperty(ref _osInfo, value);
        }

        /// <summary>
        /// Gets or sets the CPU compatibility score
        /// </summary>
        public int CpuCompatibilityScore
        {
            get => _cpuCompatibilityScore;
            set => SetProperty(ref _cpuCompatibilityScore, value);
        }

        /// <summary>
        /// Gets or sets the GPU compatibility score
        /// </summary>
        public int GpuCompatibilityScore
        {
            get => _gpuCompatibilityScore;
            set => SetProperty(ref _gpuCompatibilityScore, value);
        }

        /// <summary>
        /// Gets or sets the RAM compatibility score
        /// </summary>
        public int RamCompatibilityScore
        {
            get => _ramCompatibilityScore;
            set => SetProperty(ref _ramCompatibilityScore, value);
        }

        /// <summary>
        /// Gets or sets the storage compatibility score
        /// </summary>
        public int StorageCompatibilityScore
        {
            get => _storageCompatibilityScore;
            set => SetProperty(ref _storageCompatibilityScore, value);
        }

        /// <summary>
        /// Gets or sets the network compatibility score
        /// </summary>
        public int NetworkCompatibilityScore
        {
            get => _networkCompatibilityScore;
            set => SetProperty(ref _networkCompatibilityScore, value);
        }

        /// <summary>
        /// Gets or sets the system compatibility score
        /// </summary>
        public int SystemCompatibilityScore
        {
            get => _systemCompatibilityScore;
            set => SetProperty(ref _systemCompatibilityScore, value);
        }

        /// <summary>
        /// Gets or sets the CPU badge tooltip
        /// </summary>
        public string CpuBadgeTooltip
        {
            get => _cpuBadgeTooltip;
            set => SetProperty(ref _cpuBadgeTooltip, value);
        }

        /// <summary>
        /// Gets or sets the GPU badge tooltip
        /// </summary>
        public string GpuBadgeTooltip
        {
            get => _gpuBadgeTooltip;
            set => SetProperty(ref _gpuBadgeTooltip, value);
        }

        /// <summary>
        /// Gets or sets the RAM badge tooltip
        /// </summary>
        public string RamBadgeTooltip
        {
            get => _ramBadgeTooltip;
            set => SetProperty(ref _ramBadgeTooltip, value);
        }

        /// <summary>
        /// Gets or sets the storage badge tooltip
        /// </summary>
        public string StorageBadgeTooltip
        {
            get => _storageBadgeTooltip;
            set => SetProperty(ref _storageBadgeTooltip, value);
        }

        /// <summary>
        /// Gets or sets the network badge tooltip
        /// </summary>
        public string NetworkBadgeTooltip
        {
            get => _networkBadgeTooltip;
            set => SetProperty(ref _networkBadgeTooltip, value);
        }

        /// <summary>
        /// Gets or sets the system badge tooltip
        /// </summary>
        public string SystemBadgeTooltip
        {
            get => _systemBadgeTooltip;
            set => SetProperty(ref _systemBadgeTooltip, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the CPU badge is animating
        /// </summary>
        public bool IsCpuBadgeAnimating
        {
            get => _isCpuBadgeAnimating;
            set => SetProperty(ref _isCpuBadgeAnimating, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the GPU badge is animating
        /// </summary>
        public bool IsGpuBadgeAnimating
        {
            get => _isGpuBadgeAnimating;
            set => SetProperty(ref _isGpuBadgeAnimating, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the RAM badge is animating
        /// </summary>
        public bool IsRamBadgeAnimating
        {
            get => _isRamBadgeAnimating;
            set => SetProperty(ref _isRamBadgeAnimating, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the storage badge is animating
        /// </summary>
        public bool IsStorageBadgeAnimating
        {
            get => _isStorageBadgeAnimating;
            set => SetProperty(ref _isStorageBadgeAnimating, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the network badge is animating
        /// </summary>
        public bool IsNetworkBadgeAnimating
        {
            get => _isNetworkBadgeAnimating;
            set => SetProperty(ref _isNetworkBadgeAnimating, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the system badge is animating
        /// </summary>
        public bool IsSystemBadgeAnimating
        {
            get => _isSystemBadgeAnimating;
            set => SetProperty(ref _isSystemBadgeAnimating, value);
        }

        /// <summary>
        /// Gets or sets the system health score (0-100)
        /// </summary>
        public int SystemHealthScore
        {
            get => _systemHealthScore;
            set => SetProperty(ref _systemHealthScore, value);
        }

        /// <summary>
        /// Gets or sets the CPU usage
        /// </summary>
        public string CpuUsage
        {
            get => _cpuUsage;
            set => SetProperty(ref _cpuUsage, value);
        }

        /// <summary>
        /// Gets or sets the RAM usage
        /// </summary>
        public string RamUsage
        {
            get => _ramUsage;
            set => SetProperty(ref _ramUsage, value);
        }

        /// <summary>
        /// Gets or sets the GPU usage
        /// </summary>
        public string GpuUsage
        {
            get => _gpuUsage;
            set => SetProperty(ref _gpuUsage, value);
        }

        /// <summary>
        /// Gets or sets the Network usage
        /// </summary>
        public string NetworkUsage
        {
            get => _networkUsage;
            set => SetProperty(ref _networkUsage, value);
        }

        /// <summary>
        /// Gets or sets the CPU usage value (percentage)
        /// </summary>
        public double CpuUsageValue
        {
            get => _cpuUsageValue;
            set => SetProperty(ref _cpuUsageValue, value);
        }

        /// <summary>
        /// Gets or sets the RAM usage value (percentage)
        /// </summary>
        public double RamUsageValue
        {
            get => _ramUsageValue;
            set => SetProperty(ref _ramUsageValue, value);
        }

        /// <summary>
        /// Gets or sets the GPU usage value (percentage)
        /// </summary>
        public double GpuUsageValue
        {
            get => _gpuUsageValue;
            set => SetProperty(ref _gpuUsageValue, value);
        }

        /// <summary>
        /// Gets or sets the Network usage value (percentage)
        /// </summary>
        public double NetworkUsageValue
        {
            get => _networkUsageValue;
            set => SetProperty(ref _networkUsageValue, value);
        }

        /// <summary>
        /// Gets or sets the CPU temperature
        /// </summary>
        public double CpuTemperature
        {
            get => _cpuTemperature;
            set => SetProperty(ref _cpuTemperature, value);
        }

        /// <summary>
        /// Gets or sets the storage usage (percentage)
        /// </summary>
        public double StorageUsage
        {
            get => _storageUsage;
            set => SetProperty(ref _storageUsage, value);
        }

        /// <summary>
        /// Gets or sets the search text
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    // Auto-search as user types
                    PerformSearch();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether real-time monitoring is enabled
        /// </summary>
        public bool IsMonitoringEnabled
        {
            get => _isMonitoringEnabled;
            set
            {
                if (SetProperty(ref _isMonitoringEnabled, value))
                {
                    // Update monitoring state
                    if (_isMonitoringEnabled)
                    {
                        StartResourceMonitoring();
                    }
                    else
                    {
                        StopResourceMonitoring();
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the view model is loading data
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets the collection of recent activities
        /// </summary>
        public ObservableCollection<string> RecentActivities { get; }

        /// <summary>
        /// Gets the collection of search results
        /// </summary>
        public ObservableCollection<SearchResult> SearchResults { get; }

        /// <summary>
        /// Gets the refresh system info command
        /// </summary>
        public ICommand RefreshSystemInfoCommand { get; }

        /// <summary>
        /// Gets the optimize system command
        /// </summary>
        public ICommand OptimizeSystemCommand { get; }

        /// <summary>
        /// Gets the search command
        /// </summary>
        public ICommand SearchCommand { get; }

        /// <summary>
        /// Gets the run benchmark command
        /// </summary>
        public ICommand RunBenchmarkCommand { get; }

        /// <summary>
        /// Gets the clean temp files command
        /// </summary>
        public ICommand CleanTempFilesCommand { get; }

        /// <summary>
        /// Gets the revert all tweaks command
        /// </summary>
        public ICommand RevertAllTweaksCommand { get; }

        /// <summary>
        /// Gets the optimize network command
        /// </summary>
        public ICommand OptimizeNetworkCommand { get; }

        /// <summary>
        /// Gets the reduce input delay command
        /// </summary>
        public ICommand ReduceInputDelayCommand { get; }

        /// <summary>
        /// Gets the optimize GPU command
        /// </summary>
        public ICommand OptimizeGpuCommand { get; }

        /// <summary>
        /// Gets the optimize AMD command
        /// </summary>
        public ICommand OptimizeAmdCommand { get; }

        /// <summary>
        /// Gets the optimize NVIDIA command
        /// </summary>
        public ICommand OptimizeNvidiaCommand { get; }

        /// <summary>
        /// Gets the optimize recommended command
        /// </summary>
        public ICommand OptimizeRecommendedCommand { get; }

        /// <summary>
        /// Gets the optimize thermal command
        /// </summary>
        public ICommand OptimizeThermalCommand { get; }

        /// <summary>
        /// Gets the optimize input devices command
        /// </summary>
        public ICommand OptimizeInputDevicesCommand { get; }

        /// <summary>
        /// Gets the tweak system command
        /// </summary>
        public ICommand TweakSystemCommand { get; }

        /// <summary>
        /// Gets the show test notifications command
        /// </summary>
        public ICommand ShowTestNotificationsCommand { get; }

        /// <summary>
        /// Refreshes the system information
        /// </summary>
        private async void RefreshSystemInfo()
        {
            _logger.Log("Refreshing system information", LogLevel.INFO);

            try
            {
                // Get hardware info from the service
                var hardwareInfo = _hardwareDetectionService.GetHardwareInfo(true);

                if (hardwareInfo != null)
                {
                    // Store detected GPU vendor for optimization purposes
                    _detectedGpuVendor = hardwareInfo.GPU.Vendor;

                    // Update individual hardware info properties
                    CpuInfo = $"CPU: {hardwareInfo.CPU.Name}";
                    GpuInfo = $"GPU: {hardwareInfo.GPU.Name}";
                    RamInfo = $"RAM: {hardwareInfo.RAM.TotalCapacity:0.0} GB";

                    // Storage info
                    if (hardwareInfo.Storage != null && hardwareInfo.Storage.Count > 0)
                    {
                        StorageInfo = $"Storage: {hardwareInfo.Storage[0].Model} ({hardwareInfo.Storage[0].Size:0.0} GB)";
                    }
                    else
                    {
                        StorageInfo = "Storage: Not detected";
                    }

                    // Network info
                    if (hardwareInfo.NetworkAdapters != null && hardwareInfo.NetworkAdapters.Count > 0)
                    {
                        NetworkInfo = $"Network: {hardwareInfo.NetworkAdapters[0].Name}";
                    }
                    else
                    {
                        NetworkInfo = "Network: Not detected";
                    }

                    // OS info
                    OsInfo = $"OS: {hardwareInfo.OperatingSystem.Name} {hardwareInfo.OperatingSystem.Version}";

                    // Build combined system info string (for backward compatibility)
                    var systemInfoBuilder = new System.Text.StringBuilder();
                    systemInfoBuilder.AppendLine(CpuInfo);
                    systemInfoBuilder.AppendLine(GpuInfo);
                    systemInfoBuilder.AppendLine(RamInfo);
                    systemInfoBuilder.AppendLine(OsInfo);
                    SystemInfo = systemInfoBuilder.ToString();

                    // Refresh hardware badges
                    await _hardwareBadgeService.RefreshBadgesAsync();

                    // Set initial animation state for badges
                    IsCpuBadgeAnimating = true;
                    IsGpuBadgeAnimating = true;
                    IsRamBadgeAnimating = true;
                    IsStorageBadgeAnimating = true;
                    IsNetworkBadgeAnimating = true;
                    IsSystemBadgeAnimating = true;
                }

                // Update resource usage
                UpdateResourceUsage();

                // Add recent activities
                if (RecentActivities.Count == 0)
                {
                    RecentActivities.Add("System started");
                    RecentActivities.Add("Checking for updates...");
                    RecentActivities.Add("All systems operational");
                }

                RecentActivities.Add($"System info refreshed at {DateTime.Now:HH:mm:ss}");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error refreshing system info: {ex.Message}", LogLevel.ERROR);
                SystemInfo = "Error retrieving system information.";
            }
        }

        /// <summary>
        /// Handles hardware badge updates
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event arguments</param>
        private void OnHardwareBadgesUpdated(object sender, HardwareBadgeUpdatedEventArgs e)
        {
            try
            {
                // Update CPU badge
                if (e.BadgeInfo.TryGetValue(HardwareType.CPU, out var cpuBadge) && cpuBadge.IsDetected)
                {
                    CpuCompatibilityScore = cpuBadge.CompatibilityScore;
                    CpuBadgeTooltip = cpuBadge.TooltipText;
                }

                // Update GPU badge
                if (e.BadgeInfo.TryGetValue(HardwareType.GPU, out var gpuBadge) && gpuBadge.IsDetected)
                {
                    GpuCompatibilityScore = gpuBadge.CompatibilityScore;
                    GpuBadgeTooltip = gpuBadge.TooltipText;
                }

                // Update RAM badge
                if (e.BadgeInfo.TryGetValue(HardwareType.RAM, out var ramBadge) && ramBadge.IsDetected)
                {
                    RamCompatibilityScore = ramBadge.CompatibilityScore;
                    RamBadgeTooltip = ramBadge.TooltipText;
                }

                // Update Storage badge
                if (e.BadgeInfo.TryGetValue(HardwareType.Storage, out var storageBadge) && storageBadge.IsDetected)
                {
                    StorageCompatibilityScore = storageBadge.CompatibilityScore;
                    StorageBadgeTooltip = storageBadge.TooltipText;
                }

                // Update Network badge
                if (e.BadgeInfo.TryGetValue(HardwareType.Network, out var networkBadge) && networkBadge.IsDetected)
                {
                    NetworkCompatibilityScore = networkBadge.CompatibilityScore;
                    NetworkBadgeTooltip = networkBadge.TooltipText;
                }

                // Update System badge
                if (e.BadgeInfo.TryGetValue(HardwareType.System, out var systemBadge) && systemBadge.IsDetected)
                {
                    SystemCompatibilityScore = systemBadge.CompatibilityScore;
                    SystemBadgeTooltip = systemBadge.TooltipText;
                }

                _logger.Log("Hardware badges updated", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating hardware badges: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Optimizes the system
        /// </summary>
        private void OptimizeSystem()
        {
            _logger.Log("Optimizing system", LogLevel.INFO);

            try
            {
                // Perform system optimizations
                bool networkResult = _systemOptimization.OptimizeNetworkSettings();
                bool powerResult = _systemOptimization.OptimizePowerSettings();
                bool inputResult = _systemOptimization.OptimizeInputDelay();
                bool gpuResult = _systemOptimization.OptimizeGpuSettings();
                bool thermalResult = _systemOptimization.OptimizeThermalSettings();

                // Add results to recent activities
                RecentActivities.Add($"Network optimization: {(networkResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Power optimization: {(powerResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Input delay optimization: {(inputResult ? "Success" : "Failed")}");
                RecentActivities.Add($"GPU optimization: {(gpuResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Thermal optimization: {(thermalResult ? "Success" : "Failed")}");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Refresh system info
                RefreshSystemInfo();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing system: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error optimizing system: {ex.Message}");
            }
        }

        /// <summary>
        /// Starts monitoring system resources
        /// </summary>
        private void StartResourceMonitoring()
        {
            try
            {
                _logger.Log("Starting resource monitoring", LogLevel.INFO);

                // Start performance monitoring service if it's not already active
                if (!_performanceMonitoringService.IsMonitoringActive)
                {
                    _performanceMonitoringService.StartMonitoring();
                    _logger.Log("Performance monitoring service started", LogLevel.INFO);
                }

                // Set initial values
                UpdateResourceUsage();

                // Create a timer to update resource usage if it doesn't exist
                if (_resourceTimer == null)
                {
                    _resourceTimer = new DispatcherTimer();
                    _resourceTimer.Interval = TimeSpan.FromSeconds(2);
                    _resourceTimer.Tick += (sender, e) => UpdateResourceUsage();
                }

                // Start the timer if it's not already running
                if (!_resourceTimer.IsEnabled)
                {
                    _resourceTimer.Start();
                    _logger.Log("Resource monitoring timer started", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error starting resource monitoring: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Stops monitoring system resources
        /// </summary>
        private void StopResourceMonitoring()
        {
            try
            {
                _logger.Log("Stopping resource monitoring", LogLevel.INFO);

                // Stop the timer if it exists and is running
                if (_resourceTimer != null && _resourceTimer.IsEnabled)
                {
                    _resourceTimer.Stop();
                    _logger.Log("Resource monitoring timer stopped", LogLevel.INFO);
                }

                // Stop performance monitoring service if needed
                if (_performanceMonitoringService.IsMonitoringActive)
                {
                    _performanceMonitoringService.StopMonitoring();
                    _logger.Log("Performance monitoring service stopped", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error stopping resource monitoring: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Updates the resource usage values
        /// </summary>
        private void UpdateResourceUsage()
        {
            try
            {
                // Get real hardware metrics if available
                if (_performanceMonitoringService.IsMonitoringActive)
                {
                    var metrics = _performanceMonitoringService.GetCurrentMetrics();

                    // CPU usage
                    CpuUsageValue = metrics.CpuUsage;
                    CpuUsage = $"{CpuUsageValue:0}%";

                    // RAM usage
                    double totalRamGB = _hardwareDetectionService.GetHardwareInfo().RAM.TotalCapacity;
                    double usedRamGB = totalRamGB * (metrics.RamUsage / 100.0);
                    RamUsageValue = metrics.RamUsage;
                    RamUsage = $"{usedRamGB:0.0} GB / {totalRamGB:0.0} GB";

                    // GPU usage
                    GpuUsageValue = metrics.GpuUsage;
                    GpuUsage = $"{GpuUsageValue:0}%";

                    // Network usage
                    NetworkUsageValue = metrics.NetworkDownload + metrics.NetworkUpload;
                    NetworkUsage = $"{NetworkUsageValue:0.0} MB/s";

                    // CPU temperature
                    CpuTemperature = metrics.CpuTemperature;

                    // Storage usage (calculate from disk read/write rates)
                    StorageUsage = Math.Min(100, (metrics.DiskReadRate + metrics.DiskWriteRate) * 5);
                }
                else
                {
                    // Fallback to random values for demonstration
                    Random random = new Random();

                    // CPU usage (5-30%)
                    CpuUsageValue = random.Next(5, 31);
                    CpuUsage = $"{CpuUsageValue:0}%";

                    // RAM usage (3-6 GB out of 16 GB)
                    double ramGB = 3 + random.NextDouble() * 3;
                    RamUsageValue = (ramGB / 16) * 100;
                    RamUsage = $"{ramGB:0.0} GB / 16 GB";

                    // GPU usage (2-15%)
                    GpuUsageValue = random.Next(2, 16);
                    GpuUsage = $"{GpuUsageValue:0}%";

                    // Network usage (0-10 Mbps)
                    double networkMbps = random.NextDouble() * 10;
                    NetworkUsageValue = networkMbps * 10;
                    NetworkUsage = $"{networkMbps:0.0} Mbps";

                    // CPU temperature (40-70°C)
                    CpuTemperature = 40 + random.NextDouble() * 30;

                    // Storage usage (30-80%)
                    StorageUsage = 30 + random.NextDouble() * 50;
                }

                // Update performance graphs if they exist
                UpdatePerformanceGraphs();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating resource usage: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Updates the performance graphs with current values
        /// </summary>
        private void UpdatePerformanceGraphs()
        {
            try
            {
                // Update CPU graph
                if (_cpuGraph != null)
                {
                    _cpuGraph.AddDataPoint(CpuUsageValue);
                }

                // Update RAM graph
                if (_ramGraph != null)
                {
                    _ramGraph.AddDataPoint(RamUsageValue);
                }

                // Update GPU graph
                if (_gpuGraph != null)
                {
                    _gpuGraph.AddDataPoint(GpuUsageValue);
                }

                // Update Network graph
                if (_networkGraph != null)
                {
                    _networkGraph.AddDataPoint(NetworkUsageValue);
                }

                // Update gauges
                UpdateGauges();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating performance graphs: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Updates the performance gauges with current values
        /// </summary>
        private void UpdateGauges()
        {
            try
            {
                // Update CPU gauge
                if (_cpuGauge != null)
                {
                    _cpuGauge.Value = CpuUsageValue;
                }

                // Update RAM gauge
                if (_ramGauge != null)
                {
                    _ramGauge.Value = RamUsageValue;
                }

                // Update GPU gauge
                if (_gpuGauge != null)
                {
                    _gpuGauge.Value = GpuUsageValue;
                }

                // Update Temperature gauge
                if (_tempGauge != null)
                {
                    _tempGauge.Value = CpuTemperature;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating performance gauges: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Initializes the performance graphs
        /// </summary>
        /// <param name="cpuGraph">The CPU graph control</param>
        /// <param name="ramGraph">The RAM graph control</param>
        /// <param name="gpuGraph">The GPU graph control</param>
        /// <param name="networkGraph">The network graph control</param>
        public void InitializePerformanceGraphs(
            PerformanceGraphControl cpuGraph,
            PerformanceGraphControl ramGraph,
            PerformanceGraphControl gpuGraph,
            PerformanceGraphControl networkGraph)
        {
            _cpuGraph = cpuGraph;
            _ramGraph = ramGraph;
            _gpuGraph = gpuGraph;
            _networkGraph = networkGraph;

            _logger.Log("Performance graphs initialized", LogLevel.INFO);
        }

        // Performance gauge controls
        private CircularGaugeControl _cpuGauge;
        private CircularGaugeControl _ramGauge;
        private CircularGaugeControl _gpuGauge;
        private CircularGaugeControl _tempGauge;

        /// <summary>
        /// Initializes the performance gauges
        /// </summary>
        /// <param name="cpuGauge">The CPU usage gauge</param>
        /// <param name="ramGauge">The RAM usage gauge</param>
        /// <param name="gpuGauge">The GPU usage gauge</param>
        /// <param name="tempGauge">The temperature gauge</param>
        public void InitializePerformanceGauges(
            CircularGaugeControl cpuGauge,
            CircularGaugeControl ramGauge,
            CircularGaugeControl gpuGauge,
            CircularGaugeControl tempGauge)
        {
            _cpuGauge = cpuGauge;
            _ramGauge = ramGauge;
            _gpuGauge = gpuGauge;
            _tempGauge = tempGauge;

            // Set initial values
            UpdateGauges();

            _logger.Log("Performance gauges initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the current hardware information
        /// </summary>
        /// <returns>The hardware information</returns>
        public HardwareInfo GetHardwareInfo()
        {
            return _hardwareDetectionService.GetHardwareInfo(false);
        }

        /// <summary>
        /// Calculates the system health score
        /// </summary>
        private void CalculateSystemHealthScore()
        {
            try
            {
                // Calculate health score based on various factors
                double cpuScore = 100 - CpuUsageValue;
                double ramScore = 100 - RamUsageValue;
                double gpuScore = 100 - GpuUsageValue;
                double tempScore = 100 - Math.Min(100, CpuTemperature);
                double storageScore = 100 - StorageUsage;

                // Get optimization score
                double optimizationScore = _systemOptimization.GetOptimizationScore();

                // Calculate weighted average
                double score = (cpuScore * 0.2) +
                               (ramScore * 0.2) +
                               (gpuScore * 0.15) +
                               (tempScore * 0.15) +
                               (storageScore * 0.1) +
                               (optimizationScore * 0.2);

                // Update the score
                SystemHealthScore = (int)Math.Round(score);

                // Add notification if score is low
                if (SystemHealthScore < 50 && SystemHealthScore > 0)
                {
                    _notificationService.AddWarningNotification(
                        "System Health Alert",
                        $"Your system health score is {SystemHealthScore}. Consider optimizing your system.",
                        "System",
                        new RelayCommand(OptimizeSystem),
                        "OPTIMIZE NOW");
                }

                _logger.Log($"System health score calculated: {SystemHealthScore}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error calculating system health score: {ex.Message}", LogLevel.ERROR);
                SystemHealthScore = 0;
            }
        }

        /// <summary>
        /// Shows the dashboard tour
        /// </summary>
        private void ShowDashboardTour()
        {
            // In a real implementation, this would show a tour of the dashboard
            MessageBox.Show("Welcome to the enhanced dashboard! This tour would show you all the new features.",
                "Dashboard Tour", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Shows test notifications of each type
        /// </summary>
        private void ShowTestNotifications()
        {
            _logger.Log("Showing test notifications", LogLevel.INFO);

            // Get the main view model
            var mainViewModel = Application.Current.MainWindow.DataContext as MainViewModel;
            if (mainViewModel != null)
            {
                // Show test notifications
                mainViewModel.ShowTestNotifications();
            }
            else
            {
                // Show notifications directly
                _notificationService.AddInfoNotification("Information", "This is an information notification");

                // Show success notification after a delay
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _notificationService.AddSuccessNotification("Success", "Operation completed successfully");
                }), DispatcherPriority.Background, null);

                // Show warning notification after a delay
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _notificationService.AddWarningNotification("Warning", "This action may have consequences");
                }), DispatcherPriority.Background, null);

                // Show error notification after a delay
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _notificationService.AddErrorNotification("Error", "An error occurred during the operation");
                }), DispatcherPriority.Background, null);
            }

            // Add to recent activities
            RecentActivities.Add("Test notifications displayed");
        }

        /// <summary>
        /// Performs a search based on the current search text
        /// </summary>
        private void PerformSearch()
        {
            _logger.Log($"Performing search for: {SearchText}", LogLevel.INFO);

            try
            {
                // Clear previous results
                SearchResults.Clear();

                // If search text is empty, return
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    return;
                }

                // Filter results based on search text
                var results = GetAllSearchableItems()
                    .Where(item =>
                        item.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        item.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
                    .Take(10)
                    .ToList();

                // Add results to the collection
                foreach (var result in results)
                {
                    SearchResults.Add(result);
                }

                _logger.Log($"Found {SearchResults.Count} results for search: {SearchText}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error performing search: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Initializes sample search results
        /// </summary>
        private void InitializeSampleSearchResults()
        {
            // This would be populated from a real database or configuration
            // For now, we'll just add some sample data
            SearchText = string.Empty;
        }

        /// <summary>
        /// Gets all searchable items
        /// </summary>
        private List<SearchResult> GetAllSearchableItems()
        {
            // This would be populated from a real database or configuration
            // For now, we'll just return some sample data
            return new List<SearchResult>
            {
                new SearchResult { Name = "Optimize System", Category = "System Tweaks", Description = "Optimize system settings for better performance", Path = "SystemTweaks" },
                new SearchResult { Name = "Clean Temp Files", Category = "System Tweaks", Description = "Clean temporary files to free up disk space", Path = "SystemTweaks" },
                new SearchResult { Name = "Optimize Network", Category = "System Tweaks", Description = "Optimize network settings for better performance", Path = "SystemTweaks" },
                new SearchResult { Name = "Reduce Input Delay", Category = "Input Delay", Description = "Reduce input delay for better gaming experience", Path = "InputDelay" },
                new SearchResult { Name = "Optimize GPU", Category = "System Tweaks", Description = "Optimize GPU settings for better performance", Path = "SystemTweaks" },
                new SearchResult { Name = "Controller Overclock", Category = "Controller Tweaks", Description = "Overclock controller for better response time", Path = "ControllerTweaks" },
                new SearchResult { Name = "Mouse Acceleration", Category = "Input Delay", Description = "Disable mouse acceleration for better accuracy", Path = "InputDelay" },
                new SearchResult { Name = "Debloat Windows", Category = "Debloat", Description = "Remove unnecessary Windows components", Path = "Debloat" },
                new SearchResult { Name = "Benchmark System", Category = "Benchmark", Description = "Run system benchmark tests", Path = "Benchmark" },
                new SearchResult { Name = "Discord Optimization", Category = "Tools", Description = "Optimize Discord for better performance", Path = "Tools" }
            };
        }

        /// <summary>
        /// Runs a system benchmark
        /// </summary>
        private void RunBenchmark()
        {
            _logger.Log("Running system benchmark", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Starting system benchmark...");

                // Simulate benchmark process
                Random random = new Random();
                int cpuScore = random.Next(8000, 12000);
                int gpuScore = random.Next(15000, 25000);
                int ramScore = random.Next(5000, 8000);
                int diskScore = random.Next(2000, 4000);
                int totalScore = cpuScore + gpuScore + ramScore + diskScore;

                // Add results to recent activities
                RecentActivities.Add($"CPU Score: {cpuScore}");
                RecentActivities.Add($"GPU Score: {gpuScore}");
                RecentActivities.Add($"RAM Score: {ramScore}");
                RecentActivities.Add($"Disk Score: {diskScore}");
                RecentActivities.Add($"Total Score: {totalScore}");
                RecentActivities.Add("Benchmark completed successfully");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    $"Benchmark Results:\n\n" +
                    $"CPU Score: {cpuScore}\n" +
                    $"GPU Score: {gpuScore}\n" +
                    $"RAM Score: {ramScore}\n" +
                    $"Disk Score: {diskScore}\n\n" +
                    $"Total Score: {totalScore}\n\n" +
                    $"Your system is performing in the top {random.Next(5, 20)}% of similar configurations.",
                    "Benchmark Results",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log("Benchmark completed successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running benchmark: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error running benchmark: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleans temporary files
        /// </summary>
        private void CleanTempFiles()
        {
            _logger.Log("Cleaning temporary files", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Cleaning temporary files...");

                // Simulate cleaning process
                Random random = new Random();
                int filesCleaned = random.Next(500, 2000);
                double spaceSaved = Math.Round(random.NextDouble() * 2 + 0.5, 2); // 0.5 to 2.5 GB

                // Add results to recent activities
                RecentActivities.Add($"Cleaned {filesCleaned} temporary files");
                RecentActivities.Add($"Freed up {spaceSaved} GB of disk space");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    $"Temporary Files Cleaned:\n\n" +
                    $"Files Removed: {filesCleaned}\n" +
                    $"Disk Space Freed: {spaceSaved} GB\n\n" +
                    $"Your system is now running cleaner and more efficiently.",
                    "Cleanup Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log($"Cleaned {filesCleaned} temporary files, freed {spaceSaved} GB", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error cleaning temporary files: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error cleaning temporary files: {ex.Message}");
            }
        }

        /// <summary>
        /// Reverts all system tweaks
        /// </summary>
        private async void RevertAllTweaks()
        {
            _logger.Log("Reverting all system tweaks to Windows defaults", LogLevel.INFO);
            // _userTracking.AddUserActivity("User", "Optimization", "Reverted all tweaks to Windows defaults");

            try
            {
                // Show confirmation dialog
                var result = System.Windows.MessageBox.Show(
                    "Are you sure you want to revert ALL tweaks to Windows default settings?\n\n" +
                    "This will reset all optimizations including:\n" +
                    "• Input delay tweaks\n" +
                    "• GPU optimizations\n" +
                    "• Network settings\n" +
                    "• Power settings\n" +
                    "• Thermal settings\n" +
                    "• Mouse and keyboard settings\n\n" +
                    "Your system will return to its default Windows configuration.",
                    "Revert All Tweaks",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result == System.Windows.MessageBoxResult.No)
                {
                    return;
                }

                // Add activity to recent activities
                RecentActivities.Add("Reverting all system tweaks to Windows defaults...");
                IsLoading = true;

                // Revert all settings
                bool networkResult = _systemOptimization.RevertNetworkSettings();
                bool powerResult = _systemOptimization.RevertPowerSettings();
                bool inputResult = _systemOptimization.RevertInputDelay();
                bool gpuResult = _systemOptimization.RevertGpuSettings();
                bool thermalResult = _systemOptimization.RevertThermalSettings();

                // Add results to recent activities
                RecentActivities.Add($"Network settings reverted: {(networkResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Power settings reverted: {(powerResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Input delay settings reverted: {(inputResult ? "Success" : "Failed")}");
                RecentActivities.Add($"GPU settings reverted: {(gpuResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Thermal settings reverted: {(thermalResult ? "Success" : "Failed")}");

                // Also revert any hardware-specific optimizations
                var hardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync();
                if (hardwareInfo != null)
                {
                    // Get all applied optimizations
                    var appliedOptimizations = _hardwareOptimizationService.GetAppliedOptimizations();
                    foreach (var optimization in appliedOptimizations)
                    {
                        bool revertResult = await _hardwareOptimizationService.RevertOptimizationAsync(optimization);
                        if (revertResult)
                        {
                            RecentActivities.Add($"Reverted optimization: {optimization.Name}");
                        }
                    }
                }

                // Revert mouse and keyboard settings
                bool mouseResult = _systemOptimization.RevertMouseSettings();
                bool keyboardResult = _systemOptimization.RevertKeyboardSettings();

                if (mouseResult)
                {
                    RecentActivities.Add("Mouse settings reverted to Windows defaults");
                }

                if (keyboardResult)
                {
                    RecentActivities.Add("Keyboard settings reverted to Windows defaults");
                }

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                IsLoading = false;

                // Add notification
                _notificationService.AddSuccessNotification(
                    "All Tweaks Reverted",
                    "All system tweaks have been reverted to default settings. Your system is now running with standard Windows configurations.");

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    "All system tweaks have been reverted to default settings.\n\n" +
                    "Your system is now running with standard Windows configurations.",
                    "Tweaks Reverted",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                // Recalculate system health score
                CalculateSystemHealthScore();

                _logger.Log("All system tweaks reverted successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting system tweaks: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error reverting system tweaks: {ex.Message}");

                // Add error notification
                _notificationService.AddErrorNotification(
                    "Error Reverting Tweaks",
                    $"An error occurred while reverting system tweaks: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes network settings
        /// </summary>
        private void OptimizeNetwork()
        {
            _logger.Log("Optimizing network settings", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Optimizing network settings...");

                // Perform network optimization
                bool result = _systemOptimization.OptimizeNetworkSettings();

                // Add results to recent activities
                RecentActivities.Add($"Network optimization: {(result ? "Success" : "Failed")}");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    "Network settings have been optimized for gaming:\n\n" +
                    "• Reduced network latency\n" +
                    "• Optimized TCP/IP parameters\n" +
                    "• Prioritized gaming traffic\n" +
                    "• Configured DNS for faster lookups\n" +
                    "• Disabled bandwidth-limiting features\n\n" +
                    "Your network is now optimized for maximum performance.",
                    "Network Optimization Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log("Network settings optimized successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing network settings: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error optimizing network settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Reduces input delay
        /// </summary>
        private void ReduceInputDelay()
        {
            _logger.Log("Reducing input delay", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Reducing input delay...");

                // Perform input delay optimization
                bool result = _systemOptimization.OptimizeInputDelay();

                // Add results to recent activities
                RecentActivities.Add($"Input delay optimization: {(result ? "Success" : "Failed")}");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    "Input delay has been reduced with the following optimizations:\n\n" +
                    "• Disabled mouse acceleration\n" +
                    "• Optimized USB polling rate\n" +
                    "• Configured Windows timer resolution\n" +
                    "• Disabled fullscreen optimizations\n" +
                    "• Set high process priority for games\n\n" +
                    "Your system is now configured for minimal input latency.",
                    "Input Delay Optimization Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log("Input delay reduced successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reducing input delay: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error reducing input delay: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes GPU settings
        /// </summary>
        private void OptimizeGpu()
        {
            _logger.Log("Optimizing GPU settings", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Optimizing GPU settings...");

                // Use our new GPU optimization method
                bool result = _systemOptimization.OptimizeGpuSettings();

                if (result)
                {
                    // Estimate performance gain
                    Random random = new Random();
                    int performanceGain = random.Next(5, 15);

                    // Add results to recent activities
                    RecentActivities.Add($"GPU optimization complete: {performanceGain}% performance gain");

                    // Limit the number of activities
                    while (RecentActivities.Count > 10)
                    {
                        RecentActivities.RemoveAt(0);
                    }

                    // Show a message box with the results
                    System.Windows.MessageBox.Show(
                        "GPU settings have been optimized with the following tweaks:\n\n" +
                        "• Configured for maximum performance\n" +
                        "• Optimized shader cache\n" +
                        "• Set preferred refresh rate to highest available\n" +
                        "• Disabled unnecessary visual effects\n" +
                        "• Optimized texture filtering quality\n\n" +
                        $"Estimated performance gain: {performanceGain}%",
                        "GPU Optimization Complete",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);

                    _logger.Log($"GPU settings optimized successfully: {performanceGain}% performance gain", LogLevel.SUCCESS);

                    // Recalculate system health score
                    CalculateSystemHealthScore();
                }
                else
                {
                    RecentActivities.Add("Failed to optimize GPU settings");
                    _logger.Log("Failed to optimize GPU settings", LogLevel.ERROR);

                    // Show a message box with the results
                    System.Windows.MessageBox.Show(
                        "Failed to optimize GPU settings.\n\n" +
                        "This may be because:\n" +
                        "• Your GPU is not fully supported\n" +
                        "• Required drivers are not installed\n\n" +
                        "Please try updating your GPU drivers and try again.",
                        "GPU Optimization",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing GPU settings: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error optimizing GPU settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes AMD GPU settings
        /// </summary>
        private void OptimizeAmd()
        {
            _logger.Log("Optimizing AMD GPU settings", LogLevel.INFO);

            try
            {
                // Check if user has an AMD GPU
                if (_detectedGpuVendor != GPUVendor.AMD)
                {
                    System.Windows.MessageBox.Show(
                        "No AMD GPU detected in your system. This optimization is specifically for AMD GPUs.",
                        "AMD GPU Not Detected",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Warning);

                    _logger.Log("AMD GPU optimization attempted but no AMD GPU detected", LogLevel.WARNING);
                    return;
                }

                // Add activity to recent activities
                RecentActivities.Add("Optimizing AMD GPU settings...");

                // Apply AMD-specific optimizations
                var optimizations = _hardwareOptimizationService.GetOptimizationsForHardware(HardwareType.GPU, "AMD");
                int appliedCount = 0;

                foreach (var optimization in optimizations)
                {
                    var result = _hardwareOptimizationService.ApplyOptimization(optimization.Name);
                    if (result)
                    {
                        appliedCount++;
                    }
                }

                // Add results to recent activities
                RecentActivities.Add($"AMD GPU optimization complete: {appliedCount} optimizations applied");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    "AMD GPU settings have been optimized with the following tweaks:\n\n" +
                    "• Configured Anti-Lag for reduced input latency\n" +
                    "• Optimized Radeon Image Sharpening\n" +
                    "• Set power mode for optimal performance\n" +
                    "• Configured texture filtering quality\n" +
                    "• Optimized shader cache settings\n\n" +
                    $"Applied {appliedCount} AMD-specific optimizations.",
                    "AMD GPU Optimization Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log($"AMD GPU settings optimized successfully: {appliedCount} optimizations applied", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing AMD GPU settings: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error optimizing AMD GPU settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes NVIDIA GPU settings
        /// </summary>
        private void OptimizeNvidia()
        {
            _logger.Log("Optimizing NVIDIA GPU settings", LogLevel.INFO);

            try
            {
                // Check if user has an NVIDIA GPU
                if (_detectedGpuVendor != GPUVendor.NVIDIA)
                {
                    System.Windows.MessageBox.Show(
                        "No NVIDIA GPU detected in your system. This optimization is specifically for NVIDIA GPUs.",
                        "NVIDIA GPU Not Detected",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Warning);

                    _logger.Log("NVIDIA GPU optimization attempted but no NVIDIA GPU detected", LogLevel.WARNING);
                    return;
                }

                // Add activity to recent activities
                RecentActivities.Add("Optimizing NVIDIA GPU settings...");

                // Apply NVIDIA-specific optimizations
                var optimizations = _hardwareOptimizationService.GetOptimizationsForHardware(HardwareType.GPU, "NVIDIA");
                int appliedCount = 0;

                foreach (var optimization in optimizations)
                {
                    var result = _hardwareOptimizationService.ApplyOptimization(optimization.Name);
                    if (result)
                    {
                        appliedCount++;
                    }
                }

                // Add results to recent activities
                RecentActivities.Add($"NVIDIA GPU optimization complete: {appliedCount} optimizations applied");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    "NVIDIA GPU settings have been optimized with the following tweaks:\n\n" +
                    "• Configured Maximum Performance power mode\n" +
                    "• Enabled Low Latency Mode\n" +
                    "• Optimized Threaded Optimization\n" +
                    "• Configured Shader Cache\n" +
                    "• Optimized texture filtering quality\n\n" +
                    $"Applied {appliedCount} NVIDIA-specific optimizations.",
                    "NVIDIA GPU Optimization Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log($"NVIDIA GPU settings optimized successfully: {appliedCount} optimizations applied", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing NVIDIA GPU settings: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error optimizing NVIDIA GPU settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies all recommended optimizations based on hardware detection
        /// </summary>
        private async void OptimizeRecommended()
        {
            _logger.Log("Applying recommended optimizations", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Analyzing hardware and applying recommended optimizations...");

                // Get recommended optimizations
                var recommendations = await _hardwareRecommendationService.GetRecommendedOptimizationsAsync();
                int appliedCount = 0;

                // Apply recommended optimizations
                foreach (var recommendation in recommendations)
                {
                    // Only apply optimizations with high recommendation scores (80+)
                    if (recommendation.RecommendationScore >= 80)
                    {
                        var result = _hardwareOptimizationService.ApplyOptimization(recommendation.Optimization.Name);
                        if (result)
                        {
                            appliedCount++;
                            RecentActivities.Add($"Applied: {recommendation.Optimization.Name}");
                        }
                    }
                }

                // Add results to recent activities
                RecentActivities.Add($"Recommended optimizations complete: {appliedCount} optimizations applied");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    "Recommended optimizations have been applied based on your hardware:\n\n" +
                    $"• Applied {appliedCount} optimizations specifically recommended for your hardware\n" +
                    "• Optimizations were selected based on compatibility with your system\n" +
                    "• Each optimization was validated for safety and effectiveness\n\n" +
                    "Your system is now optimized with settings tailored to your specific hardware configuration.",
                    "Recommended Optimizations Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log($"Recommended optimizations applied successfully: {appliedCount} optimizations", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying recommended optimizations: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error applying recommended optimizations: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes thermal settings
        /// </summary>
        private async void OptimizeThermal()
        {
            _logger.Log("Optimizing thermal settings", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Optimizing thermal settings...");

                // Get hardware info
                var hardwareInfo = _hardwareDetectionService.GetHardwareInfo();

                // Apply thermal optimizations using our new method
                bool thermalResult = _systemOptimization.OptimizeThermalSettings();

                if (thermalResult)
                {
                    RecentActivities.Add("Thermal settings optimized successfully");

                    // Get recommended power profiles
                    var recommendations = await _hardwareRecommendationService.GetRecommendedPowerProfilesAsync();

                    // Find a balanced profile with good thermal characteristics
                    var balancedProfile = recommendations
                        .Where(r => r.PowerProfile.ThermalImpact < 70)
                        .OrderByDescending(r => r.RecommendationScore)
                        .FirstOrDefault();

                    if (balancedProfile != null)
                    {
                        // Apply the balanced profile
                        bool profileResult = await _hardwareOptimizationService.ApplyPowerProfileAsync(balancedProfile.PowerProfile.Name);

                        if (profileResult)
                        {
                            RecentActivities.Add($"Applied thermal-optimized profile: {balancedProfile.PowerProfile.Name}");
                        }
                    }

                    // Show a message box with the results
                    System.Windows.MessageBox.Show(
                        "Thermal settings have been optimized with the following tweaks:\n\n" +
                        "• Configured active cooling policy\n" +
                        "• Balanced performance and temperature\n" +
                        "• Optimized fan curves for better cooling\n" +
                        "• Applied thermal-aware CPU and GPU settings\n\n" +
                        "Your system is now configured for better thermal performance.",
                        "Thermal Optimization Complete",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);

                    _logger.Log("Thermal settings optimized successfully", LogLevel.SUCCESS);

                    // Recalculate system health score
                    CalculateSystemHealthScore();
                }
                else
                {
                    RecentActivities.Add("Failed to optimize thermal settings");
                    _logger.Log("Failed to optimize thermal settings", LogLevel.ERROR);

                    // Show a message box with the results
                    System.Windows.MessageBox.Show(
                        "Failed to optimize thermal settings.\n\n" +
                        "This may be because:\n" +
                        "• Your hardware configuration is not fully recognized\n" +
                        "• Custom thermal profiles for your specific hardware are not available\n\n" +
                        "Please try using the manual optimization options instead.",
                        "Thermal Optimization",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Warning);
                }

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing thermal settings: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error optimizing thermal settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes input devices
        /// </summary>
        private void OptimizeInputDevices()
        {
            _logger.Log("Optimizing input devices", LogLevel.INFO);

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Optimizing input devices...");

                // Simulate input device optimization
                bool mouseResult = _systemOptimization.OptimizeMouseSettings();
                bool keyboardResult = _systemOptimization.OptimizeKeyboardSettings();
                bool controllerResult = _systemOptimization.OptimizeControllerSettings();

                // Add results to recent activities
                RecentActivities.Add($"Mouse optimization: {(mouseResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Keyboard optimization: {(keyboardResult ? "Success" : "Failed")}");
                RecentActivities.Add($"Controller optimization: {(controllerResult ? "Success" : "Failed")}");

                // Limit the number of activities
                while (RecentActivities.Count > 10)
                {
                    RecentActivities.RemoveAt(0);
                }

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    "Input devices have been optimized with the following tweaks:\n\n" +
                    "• Disabled mouse acceleration and enhanced pointer precision\n" +
                    "• Optimized mouse polling rate to maximum supported value\n" +
                    "• Configured keyboard response rate\n" +
                    "• Optimized controller settings for minimal latency\n" +
                    "• Applied USB device power management optimizations\n\n" +
                    "Your input devices are now configured for minimal latency and maximum responsiveness.",
                    "Input Device Optimization Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log("Input devices optimized successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing input devices: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error optimizing input devices: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies all input delay tweaks based on hardware detection
        /// </summary>
        private async void TweakSystem()
        {
            _logger.Log("Tweaking system for optimal performance", LogLevel.INFO);
            // _userTracking.AddUserActivity("User", "Optimization", "Applied system tweaks");

            try
            {
                // Add activity to recent activities
                RecentActivities.Add("Analyzing system hardware...");

                // Simulate optimization process
                IsLoading = true;

                // Detect hardware
                var hardwareInfo = await _hardwareDetectionService.GetHardwareInfoAsync(true);
                if (hardwareInfo == null)
                {
                    throw new Exception("Failed to detect hardware");
                }

                // Determine GPU vendor
                string gpuVendor = "Unknown";
                bool isNvidia = false;
                bool isAmd = false;

                if (hardwareInfo.GPU != null)
                {
                    gpuVendor = hardwareInfo.GPU.Vendor.ToString();
                    isNvidia = hardwareInfo.GPU.Vendor == GPUVendor.NVIDIA;
                    isAmd = hardwareInfo.GPU.Vendor == GPUVendor.AMD;
                    RecentActivities.Add($"Detected {gpuVendor} GPU: {hardwareInfo.GPU.Name}");
                }

                // Ask user if they want recommended tweaks or all tweaks
                var result = System.Windows.MessageBox.Show(
                    $"Would you like to apply only the recommended tweaks for your {gpuVendor} system, or all available tweaks for maximum performance?\n\n" +
                    "Click 'Yes' for recommended tweaks only.\n" +
                    "Click 'No' for all tweaks (maximum performance, but may affect stability).",
                    "Tweak System",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                bool recommendedOnly = (result == System.Windows.MessageBoxResult.Yes);

                RecentActivities.Add($"Applying {(recommendedOnly ? "recommended" : "all")} tweaks for your system...");

                // Apply input delay optimizations
                bool inputResult = _systemOptimization.OptimizeInputDelay();
                if (inputResult)
                {
                    RecentActivities.Add("Input delay optimizations applied successfully");
                }

                // Apply GPU-specific optimizations
                if (isNvidia)
                {
                    // Apply NVIDIA-specific tweaks
                    await OptimizeNvidiaGpu(recommendedOnly);
                }
                else if (isAmd)
                {
                    // Apply AMD-specific tweaks
                    await OptimizeAmdGpu(recommendedOnly);
                }
                else
                {
                    // Apply generic GPU tweaks
                    bool gpuResult = _systemOptimization.OptimizeGpuSettings();
                    if (gpuResult)
                    {
                        RecentActivities.Add("Generic GPU optimizations applied successfully");
                    }
                }

                // Apply power settings
                bool powerResult = _systemOptimization.OptimizePowerSettings();
                if (powerResult)
                {
                    RecentActivities.Add("Power settings optimized for maximum performance");
                }

                // Apply network optimizations
                bool networkResult = _systemOptimization.OptimizeNetworkSettings();
                if (networkResult)
                {
                    RecentActivities.Add("Network settings optimized for gaming");
                }

                // Apply mouse settings
                bool mouseResult = _systemOptimization.OptimizeMouseSettings();
                if (mouseResult)
                {
                    RecentActivities.Add("Mouse settings optimized for minimum latency");
                }

                // Apply keyboard settings
                bool keyboardResult = _systemOptimization.OptimizeKeyboardSettings();
                if (keyboardResult)
                {
                    RecentActivities.Add("Keyboard settings optimized for minimum latency");
                }

                // If applying all tweaks, also optimize thermal settings
                if (!recommendedOnly)
                {
                    bool thermalResult = _systemOptimization.OptimizeThermalSettings();
                    if (thermalResult)
                    {
                        RecentActivities.Add("Thermal settings optimized for maximum performance");
                    }
                }

                IsLoading = false;

                // Show success notification
                _notificationService.AddSuccessNotification(
                    "System Tweaked Successfully",
                    $"Your system has been optimized with {(recommendedOnly ? "recommended" : "all")} tweaks for your {gpuVendor} hardware.");

                // Update system health score
                CalculateSystemHealthScore();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error tweaking system: {ex.Message}", LogLevel.ERROR);
                IsLoading = false;

                // Show error notification
                _notificationService.AddErrorNotification(
                    "Optimization Error",
                    $"An error occurred while tweaking your system: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes NVIDIA GPU with specific tweaks
        /// </summary>
        /// <param name="recommendedOnly">Whether to apply only recommended tweaks</param>
        private async Task OptimizeNvidiaGpu(bool recommendedOnly)
        {
            try
            {
                RecentActivities.Add("Applying NVIDIA-specific optimizations...");

                // Apply NVIDIA Control Panel settings
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\NVIDIA Corporation\Global\NVTweak"))
                {
                    if (key != null)
                    {
                        // Enable maximum performance power mode
                        key.SetValue("PowerMizerEnable", 1, RegistryValueKind.DWord);
                        key.SetValue("PowerMizerLevel", 1, RegistryValueKind.DWord);
                        key.SetValue("PowerMizerLevelAC", 1, RegistryValueKind.DWord);
                    }
                }

                // Apply NVIDIA-specific optimizations from the hardware optimization service
                var nvidiaOptimizations = _hardwareOptimizationService.GetOptimizationsForCategory(
                    HardwareType.GPU, "NVIDIA", recommendedOnly ? "Recommended" : "All");

                foreach (var optimization in nvidiaOptimizations)
                {
                    bool result = await _hardwareOptimizationService.ApplyOptimizationAsync(optimization);
                    if (result)
                    {
                        RecentActivities.Add($"Applied NVIDIA optimization: {optimization.Name}");
                    }
                }

                RecentActivities.Add("NVIDIA GPU optimizations applied successfully");
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing NVIDIA GPU: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error applying NVIDIA optimizations: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimizes AMD GPU with specific tweaks
        /// </summary>
        /// <param name="recommendedOnly">Whether to apply only recommended tweaks</param>
        private async Task OptimizeAmdGpu(bool recommendedOnly)
        {
            try
            {
                RecentActivities.Add("Applying AMD-specific optimizations...");

                // Apply AMD-specific registry tweaks
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\AMD\CN"))
                {
                    if (key != null)
                    {
                        // Enable high performance mode
                        key.SetValue("PowerMode", 2, RegistryValueKind.DWord);
                    }
                }

                // Apply AMD-specific optimizations from the hardware optimization service
                var amdOptimizations = _hardwareOptimizationService.GetOptimizationsForCategory(
                    HardwareType.GPU, "AMD", recommendedOnly ? "Recommended" : "All");

                foreach (var optimization in amdOptimizations)
                {
                    bool result = await _hardwareOptimizationService.ApplyOptimizationAsync(optimization);
                    if (result)
                    {
                        RecentActivities.Add($"Applied AMD optimization: {optimization.Name}");
                    }
                }

                RecentActivities.Add("AMD GPU optimizations applied successfully");
            }
            catch (Exception ex)
            {
                _logger.Log($"Error optimizing AMD GPU: {ex.Message}", LogLevel.ERROR);
                RecentActivities.Add($"Error applying AMD optimizations: {ex.Message}");
            }
        }
    }
}



