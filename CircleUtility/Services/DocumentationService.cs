using CircleUtility.Interfaces;
// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Windows;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing application documentation
    /// </summary>
    public class DocumentationService : IDocumentationService
    {
        private static DocumentationService _instance;
        private readonly LoggingService _logger;
        private readonly string _documentationDirectory;
        private readonly Dictionary<string, DocumentationItem> _documentationItems;

        /// <summary>
        /// Initializes a new instance of the DocumentationService class
        /// </summary>
        private DocumentationService()
        {
            _logger = LoggingService.Instance;
            _documentationDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documentation");
            _documentationItems = new Dictionary<string, DocumentationItem>();

            // Ensure documentation directory exists
            if (!Directory.Exists(_documentationDirectory))
            {
                Directory.CreateDirectory(_documentationDirectory);
            }

            // Load documentation
            LoadDocumentation();
        }

        /// <summary>
        /// Public constructor for DI compatibility
        /// </summary>
        public DocumentationService(bool forDI = false) : this() { }

        /// <summary>
        /// Gets the singleton instance of the documentation service
        /// </summary>
        public static DocumentationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new DocumentationService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets all documentation items
        /// </summary>
        public IReadOnlyDictionary<string, DocumentationItem> DocumentationItems => _documentationItems;

        /// <summary>
        /// Loads documentation from disk
        /// </summary>
        private void LoadDocumentation()
        {
            try
            {
                _logger.Log("Loading documentation...", LogLevel.INFO);

                // Clear existing documentation
                _documentationItems.Clear();

                // Check if documentation file exists
                string documentationFilePath = Path.Combine(_documentationDirectory, "documentation.json");

                if (File.Exists(documentationFilePath))
                {
                    // Load documentation from file
                    string json = File.ReadAllText(documentationFilePath);
                    Dictionary<string, DocumentationItem> items = JsonSerializer.Deserialize<Dictionary<string, DocumentationItem>>(json);

                    if (items != null)
                    {
                        foreach (var item in items)
                        {
                            _documentationItems.Add(item.Key, item.Value);
                        }
                    }
                }
                else
                {
                    // Create default documentation
                    CreateDefaultDocumentation();
                    SaveDocumentation();
                }

                _logger.Log($"Loaded {_documentationItems.Count} documentation items", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading documentation: {ex.Message}", LogLevel.ERROR);

                // Create default documentation
                CreateDefaultDocumentation();
            }
        }

        /// <summary>
        /// Saves documentation to disk
        /// </summary>
        private void SaveDocumentation()
        {
            try
            {
                _logger.Log("Saving documentation...", LogLevel.INFO);

                // Save documentation to file
                string documentationFilePath = Path.Combine(_documentationDirectory, "documentation.json");
                string json = JsonSerializer.Serialize(_documentationItems, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(documentationFilePath, json);

                _logger.Log("Documentation saved successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving documentation: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Creates default documentation
        /// </summary>
        private void CreateDefaultDocumentation()
        {
            try
            {
                _logger.Log("Creating default documentation...", LogLevel.INFO);

                // Clear existing documentation
                _documentationItems.Clear();

                // Add documentation for main features
                AddDocumentationItem("Dashboard", new DocumentationItem
                {
                    Title = "Dashboard",
                    Category = "Main Features",
                    Content = "The Dashboard provides an overview of your system's performance and status. It displays real-time metrics for CPU, GPU, memory, and network usage, as well as system temperature and fan speeds. You can also see the status of applied optimizations and quick access to frequently used features.",
                    LastUpdated = DateTime.Now
                });

                AddDocumentationItem("SystemTweaks", new DocumentationItem
                {
                    Title = "System Tweaks",
                    Category = "Main Features",
                    Content = "System Tweaks allows you to optimize various aspects of your Windows system for gaming performance. This includes CPU optimizations, memory management tweaks, disk optimizations, and Windows service configurations. Each tweak is designed to reduce system overhead and improve gaming performance.",
                    LastUpdated = DateTime.Now
                });

                // Removed documentation for GameProfiles

                AddDocumentationItem("ControllerTweaks", new DocumentationItem
                {
                    Title = "Controller Tweaks",
                    Category = "Main Features",
                    Content = "Controller Tweaks allows you to optimize your gaming controllers for reduced input delay and improved responsiveness. Features include controller overclocking, dead zone adjustment, trigger stop configuration, and custom button mapping. You can create and save multiple controller profiles for different games or preferences.",
                    LastUpdated = DateTime.Now
                });

                AddDocumentationItem("GPUOptimization", new DocumentationItem
                {
                    Title = "GPU Optimization",
                    Category = "Main Features",
                    Content = "GPU Optimization provides tools to optimize your graphics card for gaming performance. This includes optimized driver settings, shader cache management, and power management configurations. The tool automatically detects your GPU and applies optimizations specific to your hardware.",
                    LastUpdated = DateTime.Now
                });

                AddDocumentationItem("InputDelay", new DocumentationItem
                {
                    Title = "Input Delay Reduction",
                    Category = "Main Features",
                    Content = "Input Delay Reduction focuses on minimizing the delay between your physical inputs and the corresponding actions in games. This includes optimizing USB polling rates, disabling mouse acceleration, adjusting Windows timer resolution, and configuring fullscreen optimizations. You can also measure your input delay to verify improvements.",
                    LastUpdated = DateTime.Now
                });

                AddDocumentationItem("Benchmark", new DocumentationItem
                {
                    Title = "Benchmark",
                    Category = "Main Features",
                    Content = "The Benchmark tool allows you to measure your system's performance before and after applying optimizations. It includes tests for CPU performance, GPU rendering, disk speed, and memory throughput. You can save and compare benchmark results to track improvements over time.",
                    LastUpdated = DateTime.Now
                });

                // Add documentation for settings
                AddDocumentationItem("Settings", new DocumentationItem
                {
                    Title = "Settings",
                    Category = "Configuration",
                    Content = "The Settings menu allows you to configure Circle Utility according to your preferences. This includes general settings, interface customization, performance monitoring options, logging configuration, and advanced settings. Changes are automatically saved and applied immediately.",
                    LastUpdated = DateTime.Now
                });

                // Add documentation for security features
                AddDocumentationItem("Security", new DocumentationItem
                {
                    Title = "Security Features",
                    Category = "Security",
                    Content = "Circle Utility includes several security features to protect your system and data. Administrative functions require authentication, and sensitive data is encrypted using industry-standard encryption. The application also respects Windows User Account Control (UAC) for operations that require elevated privileges.",
                    LastUpdated = DateTime.Now
                });

                // Add documentation for troubleshooting
                AddDocumentationItem("Troubleshooting", new DocumentationItem
                {
                    Title = "Troubleshooting",
                    Category = "Support",
                    Content = "If you encounter issues with Circle Utility, check the following:\n\n1. Ensure you're running the latest version\n2. Check the log files in the Logs directory\n3. Try running the application as administrator\n4. Disable any conflicting software\n5. Restore default settings if optimizations cause issues\n\nIf problems persist, contact support with your log files and system information.",
                    LastUpdated = DateTime.Now
                });

                _logger.Log("Default documentation created", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating default documentation: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Adds a documentation item
        /// </summary>
        /// <param name="key">The documentation key</param>
        /// <param name="item">The documentation item</param>
        public void AddDocumentationItem(string key, DocumentationItem item)
        {
            try
            {
                // Add or update documentation item
                _documentationItems[key] = item;

                // Save documentation
                SaveDocumentation();

                _logger.Log($"Documentation item added: {key}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error adding documentation item: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets a documentation item
        /// </summary>
        /// <param name="key">The documentation key</param>
        /// <returns>The documentation item, or null if not found</returns>
        public DocumentationItem GetDocumentationItem(string key)
        {
            try
            {
                // Check if documentation item exists
                if (_documentationItems.TryGetValue(key, out DocumentationItem item))
                {
                    return item;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting documentation item: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Gets documentation items by category
        /// </summary>
        /// <param name="category">The category</param>
        /// <returns>The documentation items</returns>
        public IEnumerable<DocumentationItem> GetDocumentationItemsByCategory(string category)
        {
            try
            {
                return _documentationItems.Values.Where(item => item.Category == category);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting documentation items by category: {ex.Message}", LogLevel.ERROR);
                return Enumerable.Empty<DocumentationItem>();
            }
        }

        /// <summary>
        /// Gets all documentation categories
        /// </summary>
        /// <returns>The documentation categories</returns>
        public IEnumerable<string> GetDocumentationCategories()
        {
            try
            {
                return _documentationItems.Values.Select(item => item.Category).Distinct();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting documentation categories: {ex.Message}", LogLevel.ERROR);
                return Enumerable.Empty<string>();
            }
        }

        /// <summary>
        /// Searches documentation
        /// </summary>
        /// <param name="searchText">The search text</param>
        /// <returns>The matching documentation items</returns>
        public IEnumerable<DocumentationItem> SearchDocumentation(string searchText)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    return _documentationItems.Values;
                }

                return _documentationItems.Values.Where(item =>
                    item.Title.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    item.Content.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    item.Category.Contains(searchText, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.Log($"Error searching documentation: {ex.Message}", LogLevel.ERROR);
                return Enumerable.Empty<DocumentationItem>();
            }
        }

        /// <summary>
        /// Shows a documentation tooltip
        /// </summary>
        /// <param name="key">The documentation key</param>
        /// <param name="element">The UI element</param>
        public void ShowDocumentationTooltip(string key, FrameworkElement element)
        {
            try
            {
                // Get documentation item
                DocumentationItem item = GetDocumentationItem(key);

                if (item != null && element != null)
                {
                    // Set tooltip
                    element.ToolTip = item.Content;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error showing documentation tooltip: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Exports documentation to HTML
        /// </summary>
        /// <param name="filePath">The file path</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ExportDocumentationToHtml(string filePath)
        {
            try
            {
                _logger.Log($"Exporting documentation to HTML: {filePath}", LogLevel.INFO);

                // Build HTML
                StringBuilder html = new StringBuilder();
                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html>");
                html.AppendLine("<head>");
                html.AppendLine("<title>Circle Utility Documentation</title>");
                html.AppendLine("<style>");
                html.AppendLine("body { font-family: Arial, sans-serif; margin: 20px; }");
                html.AppendLine("h1 { color: #00C8FF; }");
                html.AppendLine("h2 { color: #0088CC; margin-top: 30px; }");
                html.AppendLine("h3 { color: #006699; }");
                html.AppendLine(".category { background-color: #f0f0f0; padding: 10px; margin-top: 20px; }");
                html.AppendLine(".item { margin-left: 20px; margin-bottom: 20px; }");
                html.AppendLine(".timestamp { color: #888; font-size: 0.8em; }");
                html.AppendLine("</style>");
                html.AppendLine("</head>");
                html.AppendLine("<body>");
                html.AppendLine("<h1>Circle Utility Documentation</h1>");

                // Add categories and items
                foreach (string category in GetDocumentationCategories())
                {
                    html.AppendLine($"<div class=\"category\">");
                    html.AppendLine($"<h2>{category}</h2>");

                    foreach (DocumentationItem item in GetDocumentationItemsByCategory(category))
                    {
                        html.AppendLine($"<div class=\"item\">");
                        html.AppendLine($"<h3>{item.Title}</h3>");
                        html.AppendLine($"<p>{item.Content.Replace("\n", "<br>")}</p>");
                        html.AppendLine($"<p class=\"timestamp\">Last updated: {item.LastUpdated:yyyy-MM-dd HH:mm:ss}</p>");
                        html.AppendLine($"</div>");
                    }

                    html.AppendLine($"</div>");
                }

                html.AppendLine("</body>");
                html.AppendLine("</html>");

                // Write HTML to file
                File.WriteAllText(filePath, html.ToString());

                _logger.Log("Documentation exported to HTML successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error exporting documentation to HTML: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }
    }
}


