using CircleUtility.Commands;
using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Helpers;
using CircleUtility.Models;
using CircleUtility.Services;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the system tweaks view
    /// </summary>
    public class SystemTweaksViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly IUserTrackingService _userTracking;
        private SystemTweak _selectedTweak;

        /// <summary>
        /// Initializes a new instance of the SystemTweaksViewModel class
        /// </summary>
        public SystemTweaksViewModel(IUserTrackingService userTracking)
        {
            _logger = LoggingService.Instance;
            _userTracking = userTracking;
            
            // Initialize commands
            ApplyTweakCommand = new RelayCommand<SystemTweak>(ApplyTweak);
            RevertTweakCommand = new RelayCommand<SystemTweak>(RevertTweak);
            ApplyAllTweaksCommand = new RelayCommand(ApplyAllTweaks);
            RevertAllTweaksCommand = new RelayCommand(RevertAllTweaks);
            
            // Initialize system tweaks
            InitializeSystemTweaks();
            
            _logger.Log("SystemTweaksViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of system tweaks
        /// </summary>
        public ObservableCollection<SystemTweak> SystemTweaks { get; } = new ObservableCollection<SystemTweak>();

        /// <summary>
        /// Gets or sets the selected system tweak
        /// </summary>
        public SystemTweak SelectedTweak
        {
            get => _selectedTweak;
            set => SetProperty(ref _selectedTweak, value);
        }

        /// <summary>
        /// Gets the apply tweak command
        /// </summary>
        public ICommand ApplyTweakCommand { get; }

        /// <summary>
        /// Gets the revert tweak command
        /// </summary>
        public ICommand RevertTweakCommand { get; }

        /// <summary>
        /// Gets the apply all tweaks command
        /// </summary>
        public ICommand ApplyAllTweaksCommand { get; }

        /// <summary>
        /// Gets the revert all tweaks command
        /// </summary>
        public ICommand RevertAllTweaksCommand { get; }

        /// <summary>
        /// Initializes the system tweaks
        /// </summary>
        private void InitializeSystemTweaks()
        {
            // Network tweaks
            SystemTweaks.Add(new SystemTweak
            {
                Name = "Optimize TCP/IP Settings",
                Description = "Optimizes TCP/IP settings for lower latency and better throughput in games.",
                Category = "Network",
                ApplyAction = () => OptimizeTcpIpSettings(),
                RevertAction = () => RevertTcpIpSettings(),
                RequiresAdmin = true
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Disable Nagle's Algorithm",
                Description = "Disables Nagle's algorithm to reduce latency for small packets, improving responsiveness in games.",
                Category = "Network",
                ApplyAction = () => DisableNaglesAlgorithm(),
                RevertAction = () => EnableNaglesAlgorithm(),
                RequiresAdmin = true
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Optimize Network Adapter",
                Description = "Configures network adapter settings for optimal gaming performance.",
                Category = "Network",
                ApplyAction = () => OptimizeNetworkAdapter(),
                RevertAction = () => RevertNetworkAdapter(),
                RequiresAdmin = true
            });

            // Power tweaks
            SystemTweaks.Add(new SystemTweak
            {
                Name = "High Performance Power Plan",
                Description = "Sets the power plan to High Performance for maximum CPU and GPU performance.",
                Category = "Power",
                ApplyAction = () => SetHighPerformancePowerPlan(),
                RevertAction = () => RevertPowerPlan(),
                RequiresAdmin = false
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Disable CPU Power Throttling",
                Description = "Disables CPU power throttling to maintain consistent performance.",
                Category = "Power",
                ApplyAction = () => DisableCpuPowerThrottling(),
                RevertAction = () => EnableCpuPowerThrottling(),
                RequiresAdmin = true
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Disable USB Power Saving",
                Description = "Disables USB power saving features to prevent input device latency.",
                Category = "Power",
                ApplyAction = () => DisableUsbPowerSaving(),
                RevertAction = () => EnableUsbPowerSaving(),
                RequiresAdmin = true
            });

            // Windows tweaks
            SystemTweaks.Add(new SystemTweak
            {
                Name = "Disable Visual Effects",
                Description = "Disables visual effects to improve system responsiveness and reduce input lag.",
                Category = "Windows",
                ApplyAction = () => DisableVisualEffects(),
                RevertAction = () => EnableVisualEffects(),
                RequiresAdmin = false
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Disable Windows Game Bar",
                Description = "Disables Windows Game Bar and Game DVR to improve performance and reduce input lag.",
                Category = "Windows",
                ApplyAction = () => DisableGameBar(),
                RevertAction = () => EnableGameBar(),
                RequiresAdmin = false
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Optimize Windows Services",
                Description = "Disables unnecessary Windows services to free up system resources.",
                Category = "Windows",
                ApplyAction = () => OptimizeWindowsServices(),
                RevertAction = () => RevertWindowsServices(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            // Input tweaks
            SystemTweaks.Add(new SystemTweak
            {
                Name = "Disable Mouse Acceleration",
                Description = "Disables mouse acceleration for more consistent aiming in games.",
                Category = "Input",
                ApplyAction = () => DisableMouseAcceleration(),
                RevertAction = () => EnableMouseAcceleration(),
                RequiresAdmin = false
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Optimize Keyboard Response",
                Description = "Optimizes keyboard response time for faster input recognition.",
                Category = "Input",
                ApplyAction = () => OptimizeKeyboardResponse(),
                RevertAction = () => RevertKeyboardResponse(),
                RequiresAdmin = false
            });

            SystemTweaks.Add(new SystemTweak
            {
                Name = "Set Timer Resolution",
                Description = "Sets the Windows timer resolution to 0.5ms for reduced input lag.",
                Category = "Input",
                ApplyAction = () => SetTimerResolution(),
                RevertAction = () => RevertTimerResolution(),
                RequiresAdmin = true
            });
        }

        /// <summary>
        /// Applies a system tweak
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        private void ApplyTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Applying system tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "System Tweak", $"Applied {tweak.Name}");
            
            // Execute the tweak's action
            tweak.ApplyAction?.Invoke();
            tweak.IsApplied = true;
        }

        /// <summary>
        /// Reverts a system tweak
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        private void RevertTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Reverting system tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "System Tweak", $"Reverted {tweak.Name}");
            
            // Execute the tweak's revert action
            tweak.RevertAction?.Invoke();
            tweak.IsApplied = false;
        }

        /// <summary>
        /// Applies all system tweaks
        /// </summary>
        private void ApplyAllTweaks()
        {
            _logger.Log("Applying all system tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "System Tweak", "Applied all tweaks");
            
            foreach (var tweak in SystemTweaks)
            {
                tweak.ApplyAction?.Invoke();
                tweak.IsApplied = true;
            }
        }

        /// <summary>
        /// Reverts all system tweaks
        /// </summary>
        private void RevertAllTweaks()
        {
            _logger.Log("Reverting all system tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "System Tweak", "Reverted all tweaks");
            
            foreach (var tweak in SystemTweaks)
            {
                tweak.RevertAction?.Invoke();
                tweak.IsApplied = false;
            }
        }

        // Tweak implementation methods (these would be implemented in a full version)
        private void OptimizeTcpIpSettings() { }
        private void RevertTcpIpSettings() { }
        private void DisableNaglesAlgorithm() { }
        private void EnableNaglesAlgorithm() { }
        private void OptimizeNetworkAdapter() { }
        private void RevertNetworkAdapter() { }
        private void SetHighPerformancePowerPlan() { }
        private void RevertPowerPlan() { }
        private void DisableCpuPowerThrottling() { }
        private void EnableCpuPowerThrottling() { }
        private void DisableUsbPowerSaving() { }
        private void EnableUsbPowerSaving() { }
        private void DisableVisualEffects() { }
        private void EnableVisualEffects() { }
        private void DisableGameBar() { }
        private void EnableGameBar() { }
        private void OptimizeWindowsServices() { }
        private void RevertWindowsServices() { }
        private void DisableMouseAcceleration() { }
        private void EnableMouseAcceleration() { }
        private void OptimizeKeyboardResponse() { }
        private void RevertKeyboardResponse() { }
        private void SetTimerResolution() { }
        private void RevertTimerResolution() { }
    }
}


