using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for ApplyRevertButtonPair.xaml
    /// </summary>
    public partial class ApplyRevertButtonPair : UserControl
    {
        /// <summary>
        /// Gets or sets the apply button text
        /// </summary>
        public string ApplyButtonText
        {
            get { return (string)GetValue(ApplyButtonTextProperty); }
            set { SetValue(ApplyButtonTextProperty, value); }
        }

        /// <summary>
        /// Dependency property for ApplyButtonText
        /// </summary>
        public static readonly DependencyProperty ApplyButtonTextProperty =
            DependencyProperty.Register("ApplyButtonText", typeof(string), typeof(ApplyRevertButtonPair), 
                new PropertyMetadata("APPLY", OnApplyButtonTextChanged));

        private static void OnApplyButtonTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ApplyRevertButtonPair control && e.NewValue is string text)
            {
                control.ApplyButton.ButtonText = text;
            }
        }

        /// <summary>
        /// Gets or sets the revert button text
        /// </summary>
        public string RevertButtonText
        {
            get { return (string)GetValue(RevertButtonTextProperty); }
            set { SetValue(RevertButtonTextProperty, value); }
        }

        /// <summary>
        /// Dependency property for RevertButtonText
        /// </summary>
        public static readonly DependencyProperty RevertButtonTextProperty =
            DependencyProperty.Register("RevertButtonText", typeof(string), typeof(ApplyRevertButtonPair), 
                new PropertyMetadata("REVERT", OnRevertButtonTextChanged));

        private static void OnRevertButtonTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ApplyRevertButtonPair control && e.NewValue is string text)
            {
                control.RevertButton.ButtonText = text;
            }
        }

        /// <summary>
        /// Gets or sets the apply command
        /// </summary>
        public ICommand ApplyCommand
        {
            get { return (ICommand)GetValue(ApplyCommandProperty); }
            set { SetValue(ApplyCommandProperty, value); }
        }

        /// <summary>
        /// Dependency property for ApplyCommand
        /// </summary>
        public static readonly DependencyProperty ApplyCommandProperty =
            DependencyProperty.Register("ApplyCommand", typeof(ICommand), typeof(ApplyRevertButtonPair), 
                new PropertyMetadata(null, OnApplyCommandChanged));

        private static void OnApplyCommandChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ApplyRevertButtonPair control && e.NewValue is ICommand command)
            {
                control.ApplyButton.Command = command;
            }
        }

        /// <summary>
        /// Gets or sets the revert command
        /// </summary>
        public ICommand RevertCommand
        {
            get { return (ICommand)GetValue(RevertCommandProperty); }
            set { SetValue(RevertCommandProperty, value); }
        }

        /// <summary>
        /// Dependency property for RevertCommand
        /// </summary>
        public static readonly DependencyProperty RevertCommandProperty =
            DependencyProperty.Register("RevertCommand", typeof(ICommand), typeof(ApplyRevertButtonPair), 
                new PropertyMetadata(null, OnRevertCommandChanged));

        private static void OnRevertCommandChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ApplyRevertButtonPair control && e.NewValue is ICommand command)
            {
                control.RevertButton.Command = command;
            }
        }

        /// <summary>
        /// Gets or sets the apply command parameter
        /// </summary>
        public object ApplyCommandParameter
        {
            get { return GetValue(ApplyCommandParameterProperty); }
            set { SetValue(ApplyCommandParameterProperty, value); }
        }

        /// <summary>
        /// Dependency property for ApplyCommandParameter
        /// </summary>
        public static readonly DependencyProperty ApplyCommandParameterProperty =
            DependencyProperty.Register("ApplyCommandParameter", typeof(object), typeof(ApplyRevertButtonPair), 
                new PropertyMetadata(null, OnApplyCommandParameterChanged));

        private static void OnApplyCommandParameterChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ApplyRevertButtonPair control)
            {
                control.ApplyButton.CommandParameter = e.NewValue;
            }
        }

        /// <summary>
        /// Gets or sets the revert command parameter
        /// </summary>
        public object RevertCommandParameter
        {
            get { return GetValue(RevertCommandParameterProperty); }
            set { SetValue(RevertCommandParameterProperty, value); }
        }

        /// <summary>
        /// Dependency property for RevertCommandParameter
        /// </summary>
        public static readonly DependencyProperty RevertCommandParameterProperty =
            DependencyProperty.Register("RevertCommandParameter", typeof(object), typeof(ApplyRevertButtonPair), 
                new PropertyMetadata(null, OnRevertCommandParameterChanged));

        private static void OnRevertCommandParameterChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ApplyRevertButtonPair control)
            {
                control.RevertButton.CommandParameter = e.NewValue;
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the revert button is enabled
        /// </summary>
        public bool IsRevertEnabled
        {
            get { return (bool)GetValue(IsRevertEnabledProperty); }
            set { SetValue(IsRevertEnabledProperty, value); }
        }

        /// <summary>
        /// Dependency property for IsRevertEnabled
        /// </summary>
        public static readonly DependencyProperty IsRevertEnabledProperty =
            DependencyProperty.Register("IsRevertEnabled", typeof(bool), typeof(ApplyRevertButtonPair), 
                new PropertyMetadata(true, OnIsRevertEnabledChanged));

        private static void OnIsRevertEnabledChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ApplyRevertButtonPair control && e.NewValue is bool isEnabled)
            {
                control.RevertButton.IsEnabled = isEnabled;
            }
        }

        /// <summary>
        /// Event raised when the apply button is clicked
        /// </summary>
        public event RoutedEventHandler ApplyClicked;

        /// <summary>
        /// Event raised when the revert button is clicked
        /// </summary>
        public event RoutedEventHandler RevertClicked;

        /// <summary>
        /// Initializes a new instance of the ApplyRevertButtonPair class
        /// </summary>
        public ApplyRevertButtonPair()
        {
            InitializeComponent();

            // Set initial values
            ApplyButton.ButtonText = ApplyButtonText;
            RevertButton.ButtonText = RevertButtonText;
            RevertButton.IsEnabled = IsRevertEnabled;
        }

        /// <summary>
        /// Handles the Click event for the apply button
        /// </summary>
        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyClicked?.Invoke(this, e);
        }

        /// <summary>
        /// Handles the Click event for the revert button
        /// </summary>
        private void RevertButton_Click(object sender, RoutedEventArgs e)
        {
            RevertClicked?.Invoke(this, e);
        }

        /// <summary>
        /// Shows the success animation for the apply button
        /// </summary>
        public void ShowApplySuccess()
        {
            ApplyButton.ShowSuccess();
            IsRevertEnabled = true;
        }

        /// <summary>
        /// Shows the success animation for the revert button
        /// </summary>
        public void ShowRevertSuccess()
        {
            RevertButton.ShowSuccess();
            IsRevertEnabled = false;
        }

        /// <summary>
        /// Shows the success animation for the apply button and then resets after a delay
        /// </summary>
        /// <param name="resetDelayMs">The delay in milliseconds before resetting</param>
        public void ShowApplySuccessAndReset(int resetDelayMs = 1500)
        {
            ApplyButton.ShowSuccessAndReset(resetDelayMs);
            IsRevertEnabled = true;
        }

        /// <summary>
        /// Shows the success animation for the revert button and then resets after a delay
        /// </summary>
        /// <param name="resetDelayMs">The delay in milliseconds before resetting</param>
        public void ShowRevertSuccessAndReset(int resetDelayMs = 1500)
        {
            RevertButton.ShowSuccessAndReset(resetDelayMs);
            IsRevertEnabled = false;
        }

        /// <summary>
        /// Resets both buttons to their default state
        /// </summary>
        public void Reset()
        {
            ApplyButton.Reset();
            RevertButton.Reset();
        }
    }
}
