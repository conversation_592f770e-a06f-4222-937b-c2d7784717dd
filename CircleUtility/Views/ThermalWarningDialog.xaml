<Window x:Class="CircleUtility.Views.ThermalWarningDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CircleUtility.Views"
        mc:Ignorable="d"
        Title="Thermal Warning" 
        Height="350" 
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#0F1620"
        BorderBrush="#1E90FF"
        BorderThickness="1"
        WindowStyle="None">
    
    <Window.Resources>
        <Style x:Key="GlowingButton" TargetType="Button">
            <Setter Property="Background" Value="#1E90FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <Border x:Name="border" Background="{TemplateBinding Background}" CornerRadius="4">
                                <Border.Effect>
                                    <DropShadowEffect Color="#3AA0FF" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                                </Border.Effect>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3AA0FF"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#3AA0FF" BlurRadius="20" ShadowDepth="0" Opacity="0.8"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0078D7"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="CancelButton" TargetType="Button">
            <Setter Property="Background" Value="#2A3441"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <Border x:Name="border" Background="{TemplateBinding Background}" CornerRadius="4">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3A4451"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1A2431"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Border Padding="20" Background="#0F1620">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <StackPanel Grid.Row="0" Margin="0,0,0,15">
                <TextBlock Text="THERMAL MANAGEMENT WARNING" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Foreground="#FF4040"
                           HorizontalAlignment="Center">
                    <TextBlock.Effect>
                        <DropShadowEffect Color="#FF4040" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                    </TextBlock.Effect>
                </TextBlock>
                
                <Border Height="2" Background="#1E90FF" Margin="0,10,0,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#1E90FF" BlurRadius="10" ShadowDepth="0" Opacity="0.6"/>
                    </Border.Effect>
                </Border>
            </StackPanel>
            
            <!-- Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Warning Icon -->
                <Path Grid.Column="0" 
                      Width="60" 
                      Height="60" 
                      Margin="0,0,15,0"
                      Data="M32,2.667c-16.217,0-29.333,13.117-29.333,29.333s13.117,29.333 29.333,29.333c16.217,0 29.333,-13.117 29.333,-29.333s-13.117,-29.333 -29.333,-29.333zM32,53.333c-11.783,0-21.333-9.55-21.333-21.333s9.55-21.333 21.333,-21.333c11.783,0 21.333,9.55 21.333,21.333s-9.55,21.333 -21.333,21.333zM32,18.667c-2.217,0-4,1.783-4,4v13.333c0,2.217 1.783,4 4,4c2.217,0 4,-1.783 4,-4v-13.333c0,-2.217 -1.783,-4 -4,-4zM32,42.667c-2.217,0-4,1.783-4,4c0,2.217 1.783,4 4,4c2.217,0 4,-1.783 4,-4c0,-2.217 -1.783,-4 -4,-4z" 
                      Fill="#FF4040" 
                      Stretch="Uniform" 
                      VerticalAlignment="Top">
                    <Path.Effect>
                        <DropShadowEffect Color="#FF4040" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                    </Path.Effect>
                </Path>
                
                <!-- Warning Text -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock x:Name="ProfileNameText" 
                               Text="Ultimate Performance Gaming" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="#1E90FF" 
                               Margin="0,0,0,10"/>
                    
                    <TextBlock TextWrapping="Wrap" 
                               Foreground="White" 
                               Margin="0,0,0,15"
                               LineHeight="22">
                        <Run Text="This power profile is designed for maximum performance and may generate "/>
                        <Run Text="significant heat" FontWeight="Bold" Foreground="#FF4040"/>
                        <Run Text=". Before proceeding, please ensure:"/>
                    </TextBlock>
                    
                    <StackPanel Margin="10,0,0,0">
                        <TextBlock TextWrapping="Wrap" 
                                   Foreground="White" 
                                   Margin="0,0,0,8"
                                   LineHeight="20">
                            <Run Text="• Your system has "/>
                            <Run Text="adequate cooling capacity" FontWeight="Bold"/>
                            <Run Text=" to handle increased thermal load"/>
                        </TextBlock>
                        
                        <TextBlock TextWrapping="Wrap" 
                                   Foreground="White" 
                                   Margin="0,0,0,8"
                                   LineHeight="20">
                            <Run Text="• All "/>
                            <Run Text="cooling vents are unobstructed" FontWeight="Bold"/>
                            <Run Text=" and free from dust"/>
                        </TextBlock>
                        
                        <TextBlock TextWrapping="Wrap" 
                                   Foreground="White" 
                                   Margin="0,0,0,8"
                                   LineHeight="20">
                            <Run Text="• Your system is placed on a "/>
                            <Run Text="hard, flat surface" FontWeight="Bold"/>
                            <Run Text=" with good airflow"/>
                        </TextBlock>
                    </StackPanel>
                </StackPanel>
            </Grid>
            
            <!-- Buttons -->
            <StackPanel Grid.Row="2" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right" 
                        Margin="0,15,0,0">
                <Button x:Name="CancelButton" 
                        Content="CANCEL" 
                        Style="{StaticResource CancelButton}"
                        Width="120"
                        Margin="0,0,10,0"
                        Click="CancelButton_Click"/>
                
                <Button x:Name="ProceedButton" 
                        Content="PROCEED" 
                        Style="{StaticResource GlowingButton}"
                        Width="120"
                        Click="ProceedButton_Click"/>
            </StackPanel>
            
            <!-- Close Button -->
            <Button Grid.Row="0" 
                    Content="✕" 
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Top" 
                    Width="24" 
                    Height="24"
                    Margin="0,-10,-10,0"
                    Background="Transparent"
                    BorderThickness="0"
                    Foreground="#AAAAAA"
                    FontSize="14"
                    Click="CloseButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Grid Background="Transparent">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Grid>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Foreground" Value="White"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Border>
</Window>
