using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for hardware recommendations
    /// </summary>
    public class HardwareRecommendationService : IHardwareRecommendationService
    {
        private static HardwareRecommendationService _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of the hardware recommendation service
        /// </summary>
        public static HardwareRecommendationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new HardwareRecommendationService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private HardwareRecommendationService()
        {
            // Initialize hardware recommendation service
        }

        /// <summary>
        /// Gets hardware recommendations
        /// </summary>
        /// <returns>Hardware recommendations</returns>
        public object GetRecommendations()
        {
            return new { Recommendations = new string[] { "Upgrade RAM", "Update GPU drivers" } };
        }

        /// <summary>
        /// Analyzes hardware compatibility
        /// </summary>
        /// <param name="hardware">Hardware to analyze</param>
        /// <returns>Compatibility analysis</returns>
        public object AnalyzeCompatibility(object hardware)
        {
            return new { IsCompatible = true, Score = 95 };
        }
    }
}
