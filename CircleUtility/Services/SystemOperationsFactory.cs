using System;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Factory for creating system operations instances
    /// </summary>
    public static class SystemOperationsFactory
    {
        private static ISystemOperations _instance;

        /// <summary>
        /// Creates a system operations instance based on the current mode
        /// </summary>
        /// <returns>A system operations instance</returns>
        public static ISystemOperations Create()
        {
            if (_instance != null)
            {
                return _instance;
            }

            // Check if we're in dev mode
            bool isDevMode = false;
            try
            {
                var configManager = CircleUtility.Services.ConfigurationManager.Instance;
                isDevMode = configManager.GetSetting("advanced", "enabledebugmode") as bool? ?? false;
            }
            catch (Exception)
            {
                // If we can't get the setting, assume we're not in dev mode
                isDevMode = false;
            }

            bool isTestMode = Environment.GetEnvironmentVariable("CIRCLE_UTILITY_TEST_MODE") == "true";

            // Create the appropriate implementation
            if (isDevMode || isTestMode)
            {
                _instance = new MockSystemOperations();
                LoggingService.Instance.Log("Using MockSystemOperations for dev/test mode", Models.LogLevel.INFO);
            }
            else
            {
                _instance = new RealSystemOperations();
                LoggingService.Instance.Log("Using RealSystemOperations for production mode", Models.LogLevel.INFO);
            }

            return _instance;
        }

        /// <summary>
        /// Sets the system operations instance (for testing)
        /// </summary>
        /// <param name="systemOperations">The system operations instance</param>
        public static void SetInstance(ISystemOperations systemOperations)
        {
            _instance = systemOperations ?? throw new ArgumentNullException(nameof(systemOperations));
        }

        /// <summary>
        /// Resets the system operations instance
        /// </summary>
        public static void ResetInstance()
        {
            _instance = null;
        }
    }
}


