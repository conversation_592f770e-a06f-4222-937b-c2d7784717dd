using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace CircleUtility.Controls
{
    public partial class AnimatedNotification : UserControl
    {
        private Storyboard _showAnimation;
        private Storyboard _hideAnimation;
        private Storyboard _progressAnimation;
        private DispatcherTimer _autoCloseTimer;

        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(AnimatedNotification), new PropertyMetadata(string.Empty));

        public static readonly DependencyProperty MessageProperty =
            DependencyProperty.Register("Message", typeof(string), typeof(AnimatedNotification), new PropertyMetadata(string.Empty));

        public static readonly DependencyProperty TypeProperty =
            DependencyProperty.Register("Type", typeof(NotificationType), typeof(AnimatedNotification), new PropertyMetadata(NotificationType.Info, OnTypeChanged));

        public static readonly DependencyProperty AutoCloseProperty =
            DependencyProperty.Register("AutoClose", typeof(bool), typeof(AnimatedNotification), new PropertyMetadata(true));

        public static readonly DependencyProperty AutoCloseTimeProperty =
            DependencyProperty.Register("AutoCloseTime", typeof(double), typeof(AnimatedNotification), new PropertyMetadata(5.0));

        public event EventHandler Closed;

        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }

        public string Message
        {
            get => (string)GetValue(MessageProperty);
            set => SetValue(MessageProperty, value);
        }

        public NotificationType Type
        {
            get => (NotificationType)GetValue(TypeProperty);
            set => SetValue(TypeProperty, value);
        }

        public bool AutoClose
        {
            get => (bool)GetValue(AutoCloseProperty);
            set => SetValue(AutoCloseProperty, value);
        }

        public double AutoCloseTime
        {
            get => (double)GetValue(AutoCloseTimeProperty);
            set => SetValue(AutoCloseTimeProperty, value);
        }

        public AnimatedNotification()
        {
            InitializeComponent();
            CreateAnimations();

            _autoCloseTimer = new DispatcherTimer();
            _autoCloseTimer.Tick += AutoCloseTimer_Tick;

            _hideAnimation.Completed += (_, _) => Closed?.Invoke(this, EventArgs.Empty);
        }

        private void CreateAnimations()
        {
            _showAnimation = new Storyboard
            {
                Children =
                {
                    new ThicknessAnimation
                    {
                        From = new Thickness(0, -80, 0, 0),
                        To = new Thickness(0),
                        Duration = TimeSpan.FromSeconds(0.3),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                    }.WithTarget(MainBorder, new PropertyPath("Margin")),

                    new DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = TimeSpan.FromSeconds(0.3),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                    }.WithTarget(MainBorder, new PropertyPath("Opacity"))
                }
            };

            _hideAnimation = new Storyboard
            {
                Children =
                {
                    new ThicknessAnimation
                    {
                        From = new Thickness(0),
                        To = new Thickness(0, -80, 0, 0),
                        Duration = TimeSpan.FromSeconds(0.3),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
                    }.WithTarget(MainBorder, new PropertyPath("Margin")),

                    new DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        Duration = TimeSpan.FromSeconds(0.3),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
                    }.WithTarget(MainBorder, new PropertyPath("Opacity"))
                }
            };

            _progressAnimation = new Storyboard
            {
                Children =
                {
                    new DoubleAnimation
                    {
                        To = 0,
                        Duration = TimeSpan.FromSeconds(AutoCloseTime),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
                    }.WithTarget(ProgressBar, new PropertyPath("Width"))
                }
            };
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            UpdateAppearance();
            _showAnimation.Begin(this);

            if (AutoClose)
            {
                foreach (var timeline in _progressAnimation.Children)
                {
                    timeline.Duration = TimeSpan.FromSeconds(AutoCloseTime);
                    if (timeline is DoubleAnimation widthAnimation)
                    {
                        widthAnimation.From = MainBorder.ActualWidth;
                    }
                }

                _progressAnimation.Begin(this, true);
                _autoCloseTimer.Interval = TimeSpan.FromSeconds(AutoCloseTime);
                _autoCloseTimer.Start();
            }
        }

        private void AutoCloseTimer_Tick(object sender, EventArgs e)
        {
            _autoCloseTimer.Stop();
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e) => Close();

        public void Close()
        {
            _autoCloseTimer.Stop();
            _progressAnimation.Stop(this);
            _hideAnimation.Begin(this);
        }

        private void UpdateAppearance()
        {
            (Color color, string iconData) = Type switch
            {
                NotificationType.Success => (Color.FromRgb(0, 200, 83), "M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"),
                NotificationType.Warning => (Color.FromRgb(255, 193, 7), "M1,21h22L12,2L1,21z M13,18h-2v-2h2V18z M13,14h-2V9h2V14z"),
                NotificationType.Error => (Color.FromRgb(255, 80, 80), "M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41z"),
                _ => (Color.FromRgb(0, 200, 255), "M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,17h-2v-6h2V17z M13,9h-2V7h2V9z")
            };

            NotificationBorderBrush.Color = color;
            GlowEffect.Color = color;
            TitleText.Foreground = new SolidColorBrush(color);
            ProgressBar.Background = new SolidColorBrush(color);
            NotificationIcon.Fill = new SolidColorBrush(color);

            try
            {
                NotificationIcon.Data = Geometry.Parse(iconData);
            }
            catch
            {
                NotificationIcon.Data = Geometry.Empty;
            }
        }

        private static void OnTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AnimatedNotification control)
                control.UpdateAppearance();
        }
    }

    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }

    internal static class AnimationExtensions
    {
        public static T WithTarget<T>(this T animation, DependencyObject target, PropertyPath path) where T : Timeline
        {
            Storyboard.SetTarget(animation, target);
            Storyboard.SetTargetProperty(animation, path);
            return animation;
        }
    }
}
