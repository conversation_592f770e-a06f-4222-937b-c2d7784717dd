<Window x:Class="CircleUtility.WelcomeScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="THE CIRCLE UTILITY" Height="450" Width="650"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None" ResizeMode="NoResize"
        AllowsTransparency="True" Background="Transparent">

    <!-- Resources for animations and styles -->
    <Window.Resources>
        <!-- Glowing button style -->
        <Style x:Key="GlowButton" TargetType="Button">
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- Outer glow effect -->
                            <Border x:Name="GlowBorder"
                                    Background="#3300C8FF"
                                    CornerRadius="3"
                                    Margin="-2"
                                    Opacity="0">
                                <Border.Effect>
                                    <BlurEffect Radius="10"/>
                                </Border.Effect>
                            </Border>

                            <!-- Button background -->
                            <Border x:Name="ButtonBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="2">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF002A50" TargetName="ButtonBorder"/>
                                <Setter Property="BorderBrush" Value="#FF00E8FF" TargetName="ButtonBorder"/>
                                <Setter Property="Opacity" Value="1" TargetName="GlowBorder"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF003A6A" TargetName="ButtonBorder"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5" TargetName="ButtonBorder"/>
                                <Setter Property="BorderBrush" Value="#FF004080" TargetName="ButtonBorder"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Futuristic TextBox style -->
        <Style x:Key="FuturisticTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#FF0A141E"/>
            <Setter Property="Foreground" Value="#FF00FFFF"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FF006080"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost" Margin="0" />
                                <TextBlock x:Name="WatermarkText"
                                           Text="Enter username..."
                                           Foreground="#FF004080"
                                           Visibility="Collapsed"
                                           Margin="{TemplateBinding Padding}"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#FF00A0C0" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF00C8FF" TargetName="border"/>
                                <Setter Property="Background" Value="#FF0C1824" TargetName="border"/>
                            </Trigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="Text" Value=""/>
                                </MultiTrigger.Conditions>
                                <Setter Property="Visibility" Value="Visible" TargetName="WatermarkText"/>
                            </MultiTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Animation for the loading bar -->
        <Storyboard x:Key="LoadingAnimation">
            <DoubleAnimation
                Storyboard.TargetName="LoadingBar"
                Storyboard.TargetProperty="Width"
                From="0" To="400" Duration="0:0:5" />
        </Storyboard>

        <!-- Pulse animation for title -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <ColorAnimation
                Storyboard.TargetName="TitleText"
                Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                From="#FF00C8FF" To="#FF0060A0" Duration="0:0:1.5"
                AutoReverse="True" />
        </Storyboard>
    </Window.Resources>

    <!-- Main container -->
    <Border CornerRadius="5" Background="#FF050A0F" BorderThickness="1" BorderBrush="#FF00C8FF">
        <Grid>
            <!-- Background elements -->
            <Canvas>
                <!-- Tech patterns - left side -->
                <Rectangle Canvas.Left="30" Canvas.Top="50" Width="3" Height="50" Fill="#FF00B4F0"/>
                <Rectangle Canvas.Left="40" Canvas.Top="60" Width="3" Height="30" Fill="#FF00A0DC"/>
                <Rectangle Canvas.Left="50" Canvas.Top="70" Width="3" Height="15" Fill="#FF008CC8"/>

                <!-- Tech patterns - right side -->
                <Rectangle Canvas.Right="30" Canvas.Top="50" Width="3" Height="50" Fill="#FF00B4F0"/>
                <Rectangle Canvas.Right="40" Canvas.Top="60" Width="3" Height="30" Fill="#FF00A0DC"/>
                <Rectangle Canvas.Right="50" Canvas.Top="70" Width="3" Height="15" Fill="#FF008CC8"/>

                <!-- Diagonal corner effects -->
                <Path Canvas.Left="0" Canvas.Top="0" Fill="#FF050A0F" Data="M0,0 L20,0 L0,20 Z"/>
                <Path Canvas.Right="0" Canvas.Top="0" Fill="#FF050A0F" Data="M0,0 L20,0 L20,20 Z"/>
                <Path Canvas.Left="0" Canvas.Bottom="0" Fill="#FF050A0F" Data="M0,0 L0,20 L20,20 Z"/>
                <Path Canvas.Right="0" Canvas.Bottom="0" Fill="#FF050A0F" Data="M20,0 L0,20 L20,20 Z"/>

                <!-- Circuit-like patterns -->
                <Path Stroke="#FF006080" StrokeThickness="1" Data="M50,120 L100,120 L100,150 L150,150" StrokeDashArray="1,2"/>
                <Path Stroke="#FF006080" StrokeThickness="1" Data="M550,120 L500,120 L500,150 L450,150" StrokeDashArray="1,2"/>
                <Ellipse Canvas.Left="147" Canvas.Top="147" Width="6" Height="6" Fill="#FF00A0C0"/>
                <Ellipse Canvas.Left="447" Canvas.Top="147" Width="6" Height="6" Fill="#FF00A0C0"/>
            </Canvas>

            <!-- Content layout -->
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Title with shadow effect -->
                <Grid Grid.Row="0" Margin="0,20,0,0">
                    <TextBlock x:Name="TitleShadow"
                               Text="WELCOME TO THE CIRCLE"
                               FontFamily="Consolas"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="#FF004060"
                               HorizontalAlignment="Center"
                               Margin="2,2,0,0"/>

                    <TextBlock x:Name="TitleText"
                               Text="WELCOME TO THE CIRCLE"
                               FontFamily="Consolas"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="#FF00C8FF"
                               HorizontalAlignment="Center"/>
                </Grid>

                <!-- Subtitle -->
                <TextBlock Grid.Row="1"
                           Text="ADVANCED OPTIMIZATION SYSTEM v2025"
                           FontFamily="Consolas"
                           FontSize="14"
                           FontWeight="Bold"
                           Foreground="#FF0096C0"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"/>

                <!-- Status message -->
                <TextBlock x:Name="StatusMessage"
                           Grid.Row="2"
                           Text="Initializing system optimization protocols..."
                           FontFamily="Consolas"
                           FontSize="12"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           Margin="0,20,0,0"/>

                <!-- Username input section -->
                <StackPanel Grid.Row="3" VerticalAlignment="Center" Margin="50,20">
                    <TextBlock Text=">> ENTER USERNAME:"
                               FontFamily="Consolas"
                               FontSize="14"
                               FontWeight="Bold"
                               Foreground="#FF00DCFF"
                               Margin="0,0,0,10"/>

                    <TextBox x:Name="UsernameInput"
                             Style="{StaticResource FuturisticTextBox}"
                             Height="40"/>
                </StackPanel>

                <!-- Loading bar -->
                <Grid Grid.Row="4" Margin="50,0">
                    <Border Background="#FF001020"
                            BorderBrush="#FF004080"
                            BorderThickness="1"
                            Height="6"
                            CornerRadius="3"/>

                    <Border x:Name="LoadingBar"
                            Background="#FF00C8FF"
                            HorizontalAlignment="Left"
                            Width="0"
                            Height="4"
                            CornerRadius="2"
                            Margin="1">
                        <Border.Effect>
                            <BlurEffect Radius="2"/>
                        </Border.Effect>
                    </Border>
                </Grid>

                <!-- Loading status -->
                <TextBlock x:Name="LoadingStatus"
                           Grid.Row="4"
                           Text="INITIALIZING..."
                           FontFamily="Consolas"
                           FontSize="10"
                           Foreground="#FF00C8FF"
                           HorizontalAlignment="Left"
                           Margin="50,10,0,0"/>

                <!-- Access button -->
                <Button x:Name="AccessButton"
                        Grid.Row="5"
                        Content="ACCESS SYSTEM"
                        Style="{StaticResource GlowButton}"
                        Width="200"
                        Height="40"
                        Margin="0,30,0,0"
                        HorizontalAlignment="Center"
                        IsEnabled="False"/>
            </Grid>

            <!-- Close button -->
            <Button x:Name="CloseButton"
                    Content="×"
                    FontSize="16"
                    Width="30"
                    Height="30"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Margin="0"
                    Background="Transparent"
                    BorderThickness="0"
                    Foreground="#FF00A0C0"
                    Cursor="Hand"
                    Click="CloseButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Grid Background="Transparent">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="#FF00E8FF"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Border>
</Window>


