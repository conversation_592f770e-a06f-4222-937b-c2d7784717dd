<UserControl x:Class="CircleUtility.Controls.EnhancedLoginScreen"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             xmlns:models="clr-namespace:CircleUtility.Models"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="400">
    
    <UserControl.Resources>
        <!-- Animations -->
        <Storyboard x:Key="PulseAnimation">
            <DoubleAnimation Storyboard.TargetName="TitleText" 
                             Storyboard.TargetProperty="Opacity"
                             From="0.8" To="1.0" Duration="0:0:1.5"
                             AutoReverse="True" RepeatBehavior="Forever"/>
        </Storyboard>
        
        <Storyboard x:Key="SuccessAnimation">
            <DoubleAnimation Storyboard.TargetName="SuccessOverlay" 
                             Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.3"/>
            <DoubleAnimation Storyboard.TargetName="SuccessIcon" 
                             Storyboard.TargetProperty="RenderTransform.ScaleX"
                             From="0" To="1" Duration="0:0:0.5"
                             BeginTime="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="SuccessIcon" 
                             Storyboard.TargetProperty="RenderTransform.ScaleY"
                             From="0" To="1" Duration="0:0:0.5"
                             BeginTime="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
        
        <Storyboard x:Key="LoadingAnimation">
            <DoubleAnimation Storyboard.TargetName="LoadingSpinner" 
                             Storyboard.TargetProperty="RenderTransform.Angle"
                             From="0" To="360" Duration="0:0:1.5"
                             RepeatBehavior="Forever"/>
        </Storyboard>
        
        <Storyboard x:Key="ShowTwoFactorAnimation">
            <DoubleAnimation Storyboard.TargetName="LoginPanel" 
                             Storyboard.TargetProperty="Opacity"
                             From="1" To="0" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="TwoFactorPanel" 
                             Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.2"
                             BeginTime="0:0:0.2"/>
            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LoginPanel" 
                                          Storyboard.TargetProperty="Visibility"
                                          BeginTime="0:0:0.2">
                <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}"/>
            </ObjectAnimationUsingKeyFrames>
            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TwoFactorPanel" 
                                          Storyboard.TargetProperty="Visibility"
                                          BeginTime="0:0:0.2">
                <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Visible}"/>
            </ObjectAnimationUsingKeyFrames>
        </Storyboard>
        
        <Storyboard x:Key="ShowForgotPasswordAnimation">
            <DoubleAnimation Storyboard.TargetName="LoginPanel" 
                             Storyboard.TargetProperty="Opacity"
                             From="1" To="0" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="ForgotPasswordPanel" 
                             Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.2"
                             BeginTime="0:0:0.2"/>
            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LoginPanel" 
                                          Storyboard.TargetProperty="Visibility"
                                          BeginTime="0:0:0.2">
                <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}"/>
            </ObjectAnimationUsingKeyFrames>
            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ForgotPasswordPanel" 
                                          Storyboard.TargetProperty="Visibility"
                                          BeginTime="0:0:0.2">
                <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Visible}"/>
            </ObjectAnimationUsingKeyFrames>
        </Storyboard>
        
        <Storyboard x:Key="ReturnToLoginAnimation">
            <DoubleAnimation Storyboard.TargetName="TwoFactorPanel" 
                             Storyboard.TargetProperty="Opacity"
                             From="1" To="0" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="ForgotPasswordPanel" 
                             Storyboard.TargetProperty="Opacity"
                             From="1" To="0" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetName="LoginPanel" 
                             Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.2"
                             BeginTime="0:0:0.2"/>
            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TwoFactorPanel" 
                                          Storyboard.TargetProperty="Visibility"
                                          BeginTime="0:0:0.2">
                <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}"/>
            </ObjectAnimationUsingKeyFrames>
            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ForgotPasswordPanel" 
                                          Storyboard.TargetProperty="Visibility"
                                          BeginTime="0:0:0.2">
                <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Collapsed}"/>
            </ObjectAnimationUsingKeyFrames>
            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LoginPanel" 
                                          Storyboard.TargetProperty="Visibility"
                                          BeginTime="0:0:0.2">
                <DiscreteObjectKeyFrame KeyTime="0:0:0" Value="{x:Static Visibility.Visible}"/>
            </ObjectAnimationUsingKeyFrames>
        </Storyboard>
        
        <!-- Styles -->
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="SubtitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="InputLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
        
        <Style x:Key="InputBoxStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Height" Value="35"/>
        </Style>
        
        <Style x:Key="PasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Height" Value="35"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="0,10,0,0"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF002A57" TargetName="border"/>
                                <Setter Property="BorderBrush" Value="#FF00E1FF" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF003C7A" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#FF0A141E" TargetName="border"/>
                                <Setter Property="BorderBrush" Value="#FF004080" TargetName="border"/>
                                <Setter Property="Opacity" Value="0.7" TargetName="border"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="LinkStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10,0,0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Foreground" Value="#FF00E1FF"/>
                    <Setter Property="TextDecorations" Value="Underline"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="ErrorMessageStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFFF3232"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,10,0,10"/>
            <Setter Property="Visibility" Value="Collapsed"/>
        </Style>
        
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FF808080"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,10,0,0"/>
        </Style>
        
        <Style x:Key="CheckBoxStyle" TargetType="CheckBox">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
    </UserControl.Resources>
    
    <Border Background="#FF0A141E" 
            BorderBrush="#FF00C8FF" 
            BorderThickness="1" 
            CornerRadius="5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Title -->
            <TextBlock x:Name="TitleText" 
                       Grid.Row="0" 
                       Text="ADMIN LOGIN" 
                       Style="{StaticResource TitleStyle}"/>
            
            <!-- Content Area -->
            <Grid Grid.Row="1">
                <!-- Login Panel -->
                <Grid x:Name="LoginPanel" Visibility="Visible">
                    <StackPanel>
                        <TextBlock Text="Enter your credentials to access admin features" 
                                   Style="{StaticResource SubtitleStyle}"/>
                        
                        <!-- Username -->
                        <TextBlock Text="USERNAME" 
                                   Style="{StaticResource InputLabelStyle}"/>
                        <TextBox x:Name="UsernameBox" 
                                 Style="{StaticResource InputBoxStyle}"
                                 KeyDown="InputBox_KeyDown"/>
                        
                        <!-- Password -->
                        <TextBlock Text="PASSWORD" 
                                   Style="{StaticResource InputLabelStyle}"/>
                        <PasswordBox x:Name="PasswordBox" 
                                     Style="{StaticResource PasswordBoxStyle}"
                                     KeyDown="InputBox_KeyDown"/>
                        
                        <!-- Remember Me -->
                        <CheckBox x:Name="RememberMeCheckBox" 
                                  Content="Remember username" 
                                  Style="{StaticResource CheckBoxStyle}"/>
                        
                        <!-- Error Message -->
                        <TextBlock x:Name="ErrorMessage" 
                                   Style="{StaticResource ErrorMessageStyle}"/>
                        
                        <!-- Login Button -->
                        <Button x:Name="LoginButton" 
                                Content="LOGIN" 
                                Style="{StaticResource ButtonStyle}"
                                Click="LoginButton_Click"/>
                        
                        <!-- Forgot Password Link -->
                        <TextBlock x:Name="ForgotPasswordLink" 
                                   Text="Forgot Password?" 
                                   Style="{StaticResource LinkStyle}"
                                   MouseDown="ForgotPasswordLink_MouseDown"/>
                        
                        <!-- Status Text -->
                        <TextBlock x:Name="StatusText" 
                                   Text="Enter your credentials to access admin features" 
                                   Style="{StaticResource StatusTextStyle}"/>
                    </StackPanel>
                </Grid>
                
                <!-- Two-Factor Authentication Panel -->
                <Grid x:Name="TwoFactorPanel" Visibility="Collapsed" Opacity="0">
                    <StackPanel>
                        <TextBlock Text="Two-Factor Authentication" 
                                   Style="{StaticResource SubtitleStyle}"/>
                        
                        <!-- Verification Code -->
                        <TextBlock Text="VERIFICATION CODE" 
                                   Style="{StaticResource InputLabelStyle}"/>
                        <TextBox x:Name="VerificationCodeBox" 
                                 Style="{StaticResource InputBoxStyle}"
                                 KeyDown="InputBox_KeyDown"/>
                        
                        <!-- Error Message -->
                        <TextBlock x:Name="TwoFactorErrorMessage" 
                                   Style="{StaticResource ErrorMessageStyle}"/>
                        
                        <!-- Verify Button -->
                        <Button x:Name="VerifyButton" 
                                Content="VERIFY" 
                                Style="{StaticResource ButtonStyle}"
                                Click="VerifyButton_Click"/>
                        
                        <!-- Resend Code Link -->
                        <TextBlock x:Name="ResendCodeLink" 
                                   Text="Resend Code" 
                                   Style="{StaticResource LinkStyle}"
                                   MouseDown="ResendCodeLink_MouseDown"/>
                        
                        <!-- Back to Login Link -->
                        <TextBlock x:Name="BackToLoginLink" 
                                   Text="Back to Login" 
                                   Style="{StaticResource LinkStyle}"
                                   MouseDown="BackToLoginLink_MouseDown"/>
                        
                        <!-- Status Text -->
                        <TextBlock x:Name="TwoFactorStatusText" 
                                   Text="A verification code has been sent to your email" 
                                   Style="{StaticResource StatusTextStyle}"/>
                    </StackPanel>
                </Grid>
                
                <!-- Forgot Password Panel -->
                <Grid x:Name="ForgotPasswordPanel" Visibility="Collapsed" Opacity="0">
                    <StackPanel>
                        <TextBlock Text="Reset Password" 
                                   Style="{StaticResource SubtitleStyle}"/>
                        
                        <!-- Username -->
                        <TextBlock Text="USERNAME" 
                                   Style="{StaticResource InputLabelStyle}"/>
                        <TextBox x:Name="ResetUsernameBox" 
                                 Style="{StaticResource InputBoxStyle}"
                                 KeyDown="InputBox_KeyDown"/>
                        
                        <!-- Error Message -->
                        <TextBlock x:Name="ResetErrorMessage" 
                                   Style="{StaticResource ErrorMessageStyle}"/>
                        
                        <!-- Reset Button -->
                        <Button x:Name="ResetButton" 
                                Content="RESET PASSWORD" 
                                Style="{StaticResource ButtonStyle}"
                                Click="ResetButton_Click"/>
                        
                        <!-- Back to Login Link -->
                        <TextBlock x:Name="ResetBackToLoginLink" 
                                   Text="Back to Login" 
                                   Style="{StaticResource LinkStyle}"
                                   MouseDown="BackToLoginLink_MouseDown"/>
                        
                        <!-- Status Text -->
                        <TextBlock x:Name="ResetStatusText" 
                                   Text="Enter your username to reset your password" 
                                   Style="{StaticResource StatusTextStyle}"/>
                    </StackPanel>
                </Grid>
                
                <!-- Loading Spinner -->
                <Grid x:Name="LoadingOverlay"
                      Background="#80000000"
                      Visibility="Collapsed">
                    <Grid Width="60"
                          Height="60"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center">
                        <Ellipse Width="60"
                                 Height="60"
                                 Stroke="#FF00C8FF"
                                 StrokeThickness="4"
                                 StrokeDashArray="0.75,0.25"
                                 StrokeDashCap="Round"
                                 x:Name="LoadingSpinner">
                            <Ellipse.RenderTransform>
                                <RotateTransform Angle="0" CenterX="30" CenterY="30"/>
                            </Ellipse.RenderTransform>
                        </Ellipse>
                        <TextBlock Text="LOADING"
                                   FontFamily="Consolas"
                                   FontSize="10"
                                   Foreground="White"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                    </Grid>
                </Grid>
                
                <!-- Success Overlay -->
                <Grid x:Name="SuccessOverlay"
                      Background="#80000000"
                      Visibility="Collapsed"
                      Opacity="0">
                    <Grid Width="100"
                          Height="100"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center">
                        <Ellipse Width="100"
                                 Height="100"
                                 Fill="#FF001428"
                                 Stroke="#FF00C8FF"
                                 StrokeThickness="4"/>
                        <Path x:Name="SuccessIcon"
                              Data="M38,65 L50,77 L75,52"
                              Stroke="#FF00FF00"
                              StrokeThickness="4"
                              StrokeEndLineCap="Round"
                              StrokeStartLineCap="Round"
                              StrokeLineJoin="Round">
                            <Path.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1" CenterX="50" CenterY="50"/>
                            </Path.RenderTransform>
                        </Path>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    </Border>
</UserControl>
