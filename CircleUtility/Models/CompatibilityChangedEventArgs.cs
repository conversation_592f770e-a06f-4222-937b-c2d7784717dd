using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Event arguments for compatibility changed events
    /// </summary>
    public class CompatibilityChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the compatibility status
        /// </summary>
        public bool IsCompatible { get; set; }

        /// <summary>
        /// Gets or sets the compatibility score
        /// </summary>
        public int CompatibilityScore { get; set; }

        /// <summary>
        /// Gets or sets the hardware component
        /// </summary>
        public string HardwareComponent { get; set; }

        /// <summary>
        /// Initializes a new instance of the CompatibilityChangedEventArgs class
        /// </summary>
        public CompatibilityChangedEventArgs()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CompatibilityChangedEventArgs class
        /// </summary>
        /// <param name="isCompatible">Whether the hardware is compatible</param>
        /// <param name="score">The compatibility score</param>
        /// <param name="component">The hardware component</param>
        public CompatibilityChangedEventArgs(bool isCompatible, int score, string component)
        {
            IsCompatible = isCompatible;
            CompatibilityScore = score;
            HardwareComponent = component;
        }
    }
}
