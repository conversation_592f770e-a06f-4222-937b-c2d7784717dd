using System;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Base interface for all UI components
    /// </summary>
    public interface IUIComponent
    {
        /// <summary>
        /// Initializes the UI component
        /// </summary>
        void Initialize();

        /// <summary>
        /// Gets a value indicating whether the component is visible
        /// </summary>
        bool IsVisible { get; }

        /// <summary>
        /// Shows the UI component
        /// </summary>
        void Show();

        /// <summary>
        /// Hides the UI component
        /// </summary>
        void Hide();
    }
}
