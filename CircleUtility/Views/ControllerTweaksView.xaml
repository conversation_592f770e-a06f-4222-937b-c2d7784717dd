<UserControl x:Class="CircleUtility.Views.ControllerTweaksView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="Black">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="Controller Tweaks"
                   FontFamily="Consolas"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20"/>

        <!-- Description -->
        <TextBlock Grid.Row="1"
                   Text="Apply controller optimizations to reduce input latency and improve responsiveness. These tweaks are designed for Xbox, PlayStation, and other controllers."
                   FontFamily="Segoe UI"
                   FontSize="12"
                   Foreground="White"
                   TextWrapping="Wrap"
                   Margin="0,0,0,10"/>

        <!-- Controller Overclocking Panel -->
        <Border Grid.Row="1"
                Background="#FF050A0F"
                BorderThickness="1"
                BorderBrush="#FF00C8FF"
                Margin="0,40,0,20">
            <Grid Margin="15">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <TextBlock Text="Controller Overclocking"
                           FontFamily="Consolas"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#FF00C8FF"
                           Margin="0,0,0,10"/>

                <!-- Description -->
                <TextBlock Grid.Row="1"
                           Text="Increase your controller polling rate to reduce input latency. Higher polling rates result in faster response times but may increase CPU usage slightly."
                           FontFamily="Segoe UI"
                           FontSize="12"
                           Foreground="White"
                           TextWrapping="Wrap"
                           Margin="0,0,0,15"/>

                <!-- Polling Rate Slider -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Text="Polling Rate:"
                               FontFamily="Consolas"
                               FontSize="12"
                               Foreground="White"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>

                    <Slider Grid.Column="1"
                            Minimum="125"
                            Maximum="1000"
                            Value="{Binding ControllerPollingRate}"
                            TickFrequency="125"
                            IsSnapToTickEnabled="True"
                            TickPlacement="BottomRight"
                            Margin="0,0,10,0"/>

                    <TextBlock Grid.Column="2"
                               Text="{Binding ControllerPollingRate, StringFormat='{}{0} Hz'}"
                               FontFamily="Consolas"
                               FontSize="12"
                               Foreground="#FF00C8FF"
                               VerticalAlignment="Center"
                               MinWidth="60"/>
                </Grid>

                <!-- Apply Button -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,15,0,0">
                    <Button Content="Apply Overclock"
                            Command="{Binding ApplyControllerOverclockCommand}"
                            Width="150"
                            Height="35"
                            Margin="0,0,15,0"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="#FF00C8FF"
                            BorderThickness="1"/>

                    <Button Content="Reset to Default"
                            Command="{Binding ResetControllerOverclockCommand}"
                            Width="150"
                            Height="35"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="#FFFF3232"
                            BorderThickness="1"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Tweaks list -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="0,20,0,0">
            <ItemsControl ItemsSource="{Binding ControllerTweaks}">
                <ItemsControl.GroupStyle>
                    <GroupStyle>
                        <GroupStyle.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}"
                                           FontFamily="Consolas"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#FF00C8FF"
                                           Margin="0,10,0,5"/>
                            </DataTemplate>
                        </GroupStyle.HeaderTemplate>
                    </GroupStyle>
                </ItemsControl.GroupStyle>

                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Background="#FF050A0F"
                                BorderThickness="0,0,0,1"
                                BorderBrush="#FF004080"
                                Padding="10"
                                Margin="0,0,0,5">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Tweak info -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding Name}"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="White"/>

                                    <TextBlock Text="{Binding Description}"
                                               FontFamily="Segoe UI"
                                               FontSize="12"
                                               Foreground="#FFC0C0C0"
                                               TextWrapping="Wrap"
                                               Margin="0,5,0,0"/>

                                    <!-- Requirements -->
                                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                        <TextBlock Text="Admin: "
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF808080"/>
                                        <TextBlock Text="{Binding RequiresAdmin, Converter={StaticResource BoolToYesNoConverter}}"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="{Binding RequiresAdmin, Converter={StaticResource BoolToColorConverter}}"/>

                                        <TextBlock Text=" | Restart: "
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF808080"
                                                   Margin="10,0,0,0"/>
                                        <TextBlock Text="{Binding RequiresRestart, Converter={StaticResource BoolToYesNoConverter}}"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="{Binding RequiresRestart, Converter={StaticResource BoolToColorConverter}}"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Action buttons with status indicator -->
                                <Grid Grid.Column="1" VerticalAlignment="Center">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Status indicator -->
                                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,5">
                                        <TextBlock Text="Status: "
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF808080"/>

                                        <TextBlock Text="Not Applied"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FFFF6060"
                                                   Visibility="{Binding IsApplied, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>

                                        <TextBlock Text="Applied"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF60FF60"
                                                   Visibility="{Binding IsApplied, Converter={StaticResource BoolToVisibilityConverter}}"/>

                                        <Ellipse Width="8" Height="8" Margin="5,0,0,0"
                                                 Fill="#FFFF6060"
                                                 Visibility="{Binding IsApplied, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                            <Ellipse.Effect>
                                                <BlurEffect Radius="1"/>
                                            </Ellipse.Effect>
                                        </Ellipse>

                                        <Ellipse Width="8" Height="8" Margin="5,0,0,0"
                                                 Fill="#FF60FF60"
                                                 Visibility="{Binding IsApplied, Converter={StaticResource BoolToVisibilityConverter}}">
                                            <Ellipse.Effect>
                                                <BlurEffect Radius="1"/>
                                            </Ellipse.Effect>
                                        </Ellipse>
                                    </StackPanel>

                                    <!-- Buttons -->
                                    <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                                        <Button Content="Apply"
                                                Command="{Binding DataContext.ApplyTweakCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                CommandParameter="{Binding}"
                                                Width="80"
                                                Height="30"
                                                Margin="0,0,10,0"
                                                Background="#FF001428"
                                                Foreground="White"
                                                BorderBrush="#FF00C8FF"
                                                BorderThickness="1"
                                                Visibility="{Binding IsApplied, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>

                                        <Button Content="Revert"
                                                Command="{Binding DataContext.RevertTweakCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                CommandParameter="{Binding}"
                                                Width="80"
                                                Height="30"
                                                Background="#FF001428"
                                                Foreground="White"
                                                BorderBrush="#FFFF3232"
                                                BorderThickness="1"
                                                Visibility="{Binding IsApplied, Converter={StaticResource BoolToVisibilityConverter}}"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
