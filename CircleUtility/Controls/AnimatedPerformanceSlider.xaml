<UserControl x:Class="CircleUtility.Controls.AnimatedPerformanceSlider"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="120" d:DesignWidth="500"
             Loaded="UserControl_Loaded">

    <UserControl.Resources>
        <!-- Ensure this converter is registered in App.xaml or the same scope -->
        <!-- <local:SliderValueToWidthConverter x:Key="SliderValueToWidthConverter"/> -->

        <!-- Animations -->
        <Storyboard x:Key="SliderGlowAnimation">
            <DoubleAnimation Storyboard.TargetName="SliderGlow"
                             Storyboard.TargetProperty="Opacity"
                             From="0.4" To="0.8"
                             Duration="0:0:1.5"
                             AutoReverse="True"
                             RepeatBehavior="Forever" />
        </Storyboard>

        <Storyboard x:Key="ThumbGlowAnimation">
            <DoubleAnimation Storyboard.TargetName="ThumbGlow"
                             Storyboard.TargetProperty="Opacity"
                             From="0.6" To="1.0"
                             Duration="0:0:1"
                             AutoReverse="True"
                             RepeatBehavior="Forever" />
        </Storyboard>

        <Storyboard x:Key="ValueChangeAnimation">
            <DoubleAnimation Storyboard.TargetName="ValueDisplay"
                             Storyboard.TargetProperty="Opacity"
                             From="0.7" To="1.0"
                             Duration="0:0:0.2"
                             AutoReverse="True" />
            <DoubleAnimation Storyboard.TargetName="ValueDisplay"
                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                             From="1.0" To="1.2"
                             Duration="0:0:0.2"
                             AutoReverse="True" />
            <DoubleAnimation Storyboard.TargetName="ValueDisplay"
                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                             From="1.0" To="1.2"
                             Duration="0:0:0.2"
                             AutoReverse="True" />
        </Storyboard>

        <Storyboard x:Key="ProfileChangeAnimation">
            <DoubleAnimation Storyboard.TargetName="ProfileDisplay"
                             Storyboard.TargetProperty="Opacity"
                             From="0" To="1.0"
                             Duration="0:0:0.3" />
            <DoubleAnimation Storyboard.TargetName="ProfileDisplay"
                             Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                             From="10" To="0"
                             Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.5" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Thumb Style -->
        <Style x:Key="CustomThumbStyle" TargetType="Thumb">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Thumb">
                        <Grid>
                            <Ellipse x:Name="ThumbGlow"
                                     Width="30" Height="30"
                                     Fill="#4000C8FF"
                                     Opacity="0.6">
                                <Ellipse.Effect>
                                    <BlurEffect Radius="10" />
                                </Ellipse.Effect>
                            </Ellipse>
                            <Ellipse x:Name="ThumbCircle"
                                     Width="20" Height="20"
                                     Fill="#FF00C8FF">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#FF00C8FF"
                                                      BlurRadius="10"
                                                      ShadowDepth="0"
                                                      Opacity="0.7" />
                                </Ellipse.Effect>
                            </Ellipse>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ThumbCircle" Property="Fill" Value="#FF40E0FF" />
                            </Trigger>
                            <Trigger Property="IsDragging" Value="True">
                                <Setter TargetName="ThumbCircle" Property="Fill" Value="#FF80FFFF" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Slider Style -->
        <Style x:Key="CustomSliderStyle" TargetType="Slider">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Slider">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <Border x:Name="SliderGlow"
                                    Grid.Row="1"
                                    Height="8"
                                    Background="#4000C8FF"
                                    CornerRadius="4"
                                    Opacity="0.4">
                                <Border.Effect>
                                    <BlurEffect Radius="8" />
                                </Border.Effect>
                            </Border>

                            <Border x:Name="TrackBackground"
                                    Grid.Row="1"
                                    Height="4"
                                    Background="#FF0A141E"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"
                                    CornerRadius="2" />

                            <Border x:Name="TrackFill"
                                    Grid.Row="1"
                                    Height="4"
                                    Background="#FF00C8FF"
                                    CornerRadius="2"
                                    HorizontalAlignment="Left"
                                    Width="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Value, Converter={StaticResource SliderValueToWidthConverter}}" />

                            <Track x:Name="PART_Track" Grid.Row="1">
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Command="Slider.DecreaseLarge" Opacity="0" />
                                </Track.DecreaseRepeatButton>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Command="Slider.IncreaseLarge" Opacity="0" />
                                </Track.IncreaseRepeatButton>
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomThumbStyle}" />
                                </Track.Thumb>
                            </Track>

                            <TickBar x:Name="TopTick"
                                     Grid.Row="0"
                                     Fill="#FF808080"
                                     Height="4"
                                     Placement="Top"
                                     Visibility="Collapsed" />
                            <TickBar x:Name="BottomTick"
                                     Grid.Row="2"
                                     Fill="#FF808080"
                                     Height="4"
                                     Placement="Bottom"
                                     Visibility="Collapsed" />
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="TickPlacement" Value="TopLeft">
                                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
                            </Trigger>
                            <Trigger Property="TickPlacement" Value="BottomRight">
                                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
                            </Trigger>
                            <Trigger Property="TickPlacement" Value="Both">
                                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
                                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <!-- Main Layout -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock x:Name="TitleText"
                       Grid.Column="0"
                       Text="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       FontFamily="Consolas"
                       FontSize="14"
                       FontWeight="Bold"
                       Foreground="#FF00C8FF" />

            <TextBlock x:Name="ValueDisplay"
                       Grid.Column="1"
                       Text="{Binding Value, RelativeSource={RelativeSource AncestorType=UserControl}, StringFormat='{}{0}%'}"
                       FontFamily="Consolas"
                       FontSize="14"
                       FontWeight="Bold"
                       Foreground="#FF00C8FF"
                       RenderTransformOrigin="0.5,0.5">
                <TextBlock.RenderTransform>
                    <ScaleTransform ScaleX="1" ScaleY="1" />
                </TextBlock.RenderTransform>
            </TextBlock>
        </Grid>

        <Slider x:Name="MainSlider"
                Grid.Row="1"
                Style="{StaticResource CustomSliderStyle}"
                Minimum="0"
                Maximum="100"
                Value="{Binding Value, RelativeSource={RelativeSource AncestorType=UserControl}, Mode=TwoWay}"
                TickFrequency="25"
                TickPlacement="BottomRight"
                IsSnapToTickEnabled="True"
                ValueChanged="MainSlider_ValueChanged" />

        <TextBlock x:Name="ProfileDisplay"
                   Grid.Row="2"
                   Text="{Binding CurrentProfile, RelativeSource={RelativeSource AncestorType=UserControl}}"
                   FontFamily="Consolas"
                   FontSize="12"
                   Foreground="#FFA0A0A0"
                   HorizontalAlignment="Center"
                   Margin="0,5,0,0"
                   Opacity="0"
                   RenderTransformOrigin="0.5,0.5">
            <TextBlock.RenderTransform>
                <TranslateTransform Y="0" />
            </TextBlock.RenderTransform>
        </TextBlock>

        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,5,0,0">
            <Button x:Name="PowerSaverButton"
                    Content="POWER SAVER"
                    FontFamily="Consolas"
                    FontSize="10"
                    Padding="5,2"
                    Margin="0,0,5,0"
                    Background="#FF001428"
                    Foreground="#FFA0A0A0"
                    BorderBrush="#FF404040"
                    BorderThickness="1"
                    Click="PowerSaverButton_Click" />

            <Button x:Name="BalancedButton"
                    Content="BALANCED"
                    FontFamily="Consolas"
                    FontSize="10"
                    Padding="5,2"
                    Margin="0,0,5,0"
                    Background="#FF001428"
                    Foreground="#FFA0A0A0"
                    BorderBrush="#FF404040"
                    BorderThickness="1"
                    Click="BalancedButton_Click" />

            <Button x:Name="PerformanceButton"
                    Content="PERFORMANCE"
                    FontFamily="Consolas"
                    FontSize="10"
                    Padding="5,2"
                    Background="#FF001428"
                    Foreground="#FFA0A0A0"
                    BorderBrush="#FF404040"
                    BorderThickness="1"
                    Click="PerformanceButton_Click" />
        </StackPanel>
    </Grid>
</UserControl>
