using System;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for hardware detection badge service
    /// </summary>
    public interface IHardwareDetectionBadgeService
    {
        /// <summary>
        /// Gets whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Initializes the service asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        System.Threading.Tasks.Task InitializeAsync();

        /// <summary>
        /// Gets badge information for hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>Badge information</returns>
        object GetBadgeInfo(HardwareType hardwareType);

        /// <summary>
        /// Gets detection time for hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>Detection time</returns>
        DateTime GetDetectionTime(HardwareType hardwareType);
    }
}
