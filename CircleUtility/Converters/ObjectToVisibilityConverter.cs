using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts an object to a visibility value
    /// </summary>
    public class ObjectToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts an object to a visibility value
        /// </summary>
        /// <param name="value">The object to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>Visibility.Visible if the object is not null, Visibility.Collapsed otherwise</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isInverted = parameter is string paramString && 
                              paramString.Equals("Invert", StringComparison.OrdinalIgnoreCase);
                              
            bool isVisible = value != null;
            
            if (isInverted)
            {
                isVisible = !isVisible;
            }
            
            return isVisible ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// Converts a visibility value back to an object
        /// </summary>
        /// <param name="value">The visibility value to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>Not implemented</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
