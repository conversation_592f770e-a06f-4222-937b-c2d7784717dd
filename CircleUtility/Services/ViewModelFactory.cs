using System;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.ViewModels;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Factory for creating ViewModels with proper dependency injection
    /// Replaces the ServiceLocator anti-pattern
    /// </summary>
    public class ViewModelFactory : IViewModelFactory
    {
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initializes a new instance of the ViewModelFactory
        /// </summary>
        /// <param name="serviceProvider">The service provider for dependency injection</param>
        public ViewModelFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// Creates a DashboardViewModel with injected dependencies
        /// </summary>
        public DashboardViewModel CreateDashboardViewModel()
        {
            return new DashboardViewModel(
                _serviceProvider.GetRequiredService<IHardwareDetectionService>(),
                _serviceProvider.GetRequiredService<IPerformanceMonitoringService>(),
                _serviceProvider.GetRequiredService<IHardwareOptimizationService>(),
                _serviceProvider.GetRequiredService<IHardwareRecommendationService>(),
                _serviceProvider.GetRequiredService<IHardwareCompatibilityService>()
            );
        }

        /// <summary>
        /// Creates a DebloatViewModel with injected dependencies
        /// </summary>
        public DebloatViewModel CreateDebloatViewModel()
        {
            return new DebloatViewModel();
        }

        /// <summary>
        /// Creates a SettingsViewModel with injected dependencies
        /// </summary>
        public SettingsViewModel CreateSettingsViewModel()
        {
            return new SettingsViewModel();
        }

        /// <summary>
        /// Creates a DiscordViewModel with injected dependencies
        /// </summary>
        public DiscordViewModel CreateDiscordViewModel()
        {
            return new DiscordViewModel();
        }

        /// <summary>
        /// Creates a RevertTweaksViewModel with injected dependencies
        /// </summary>
        public RevertTweaksViewModel CreateRevertTweaksViewModel()
        {
            return new RevertTweaksViewModel();
        }

        /// <summary>
        /// Creates an AdminViewModel with injected dependencies
        /// </summary>
        public AdminViewModel CreateAdminViewModel()
        {
            return new AdminViewModel();
        }

        /// <summary>
        /// Creates a PerformanceOptimizerViewModel with injected dependencies
        /// </summary>
        public PerformanceOptimizerViewModel CreatePerformanceOptimizerViewModel()
        {
            return new PerformanceOptimizerViewModel();
        }

        /// <summary>
        /// Creates a BenchmarkViewModel with injected dependencies
        /// </summary>
        public BenchmarkViewModel CreateBenchmarkViewModel()
        {
            return new BenchmarkViewModel();
        }

        /// <summary>
        /// Creates an InputDelayViewModel with injected dependencies
        /// </summary>
        public InputDelayViewModel CreateInputDelayViewModel()
        {
            return new InputDelayViewModel();
        }

        /// <summary>
        /// Creates a SystemTweaksViewModel with injected dependencies
        /// </summary>
        public SystemTweaksViewModel CreateSystemTweaksViewModel()
        {
            return new SystemTweaksViewModel();
        }

        /// <summary>
        /// Creates a MainViewModel with injected dependencies
        /// </summary>
        public MainViewModel CreateMainViewModel(string username)
        {
            return new MainViewModel(
                username,
                _serviceProvider.GetRequiredService<IHardwareDetectionService>(),
                _serviceProvider.GetRequiredService<IHardwareOptimizationService>(),
                _serviceProvider.GetRequiredService<IHardwareCompatibilityService>(),
                _serviceProvider.GetRequiredService<IHardwareRecommendationService>(),
                _serviceProvider.GetRequiredService<IPerformanceMonitoringService>(),
                _serviceProvider.GetRequiredService<IBenchmarkingService>(),
                _serviceProvider.GetRequiredService<EnhancedDiscordUserService>()
            );
        }

        /// <summary>
        /// Creates a ViewModel of the specified type with dependency injection
        /// </summary>
        /// <typeparam name="T">The ViewModel type</typeparam>
        /// <returns>The created ViewModel instance</returns>
        public T CreateViewModel<T>() where T : class
        {
            // Use reflection to find the appropriate factory method
            var methodName = $"Create{typeof(T).Name}";
            var method = GetType().GetMethod(methodName);
            
            if (method != null)
            {
                return (T)method.Invoke(this, null);
            }

            // Fallback: try to create using service provider
            try
            {
                return _serviceProvider.GetService<T>();
            }
            catch
            {
                throw new InvalidOperationException($"Cannot create ViewModel of type {typeof(T).Name}. No factory method found and type not registered in DI container.");
            }
        }
    }
}

