using System;
using System.Collections.Generic;

namespace CircleUtility.Models
{
    /// <summary>
    /// Event arguments for badge updates
    /// </summary>
    public class HardwareBadgeUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the badge info
        /// </summary>
        public Dictionary<HardwareType, HardwareBadgeInfo> BadgeInfo { get; }

        /// <summary>
        /// Initializes a new instance of the HardwareBadgeUpdatedEventArgs class
        /// </summary>
        /// <param name="badgeInfo">The badge info</param>
        public HardwareBadgeUpdatedEventArgs(Dictionary<HardwareType, HardwareBadgeInfo> badgeInfo)
        {
            BadgeInfo = badgeInfo;
        }
    }
} 