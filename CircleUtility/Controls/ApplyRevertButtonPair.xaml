<UserControl x:Class="CircleUtility.Controls.ApplyRevertButtonPair"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="250">
    
    <UserControl.Resources>
        <!-- Apply button style -->
        <Style x:Key="ApplyButtonStyle" TargetType="local:AnimatedButton">
            <Setter Property="Width" Value="120"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="ButtonText" Value="APPLY"/>
        </Style>
        
        <!-- Revert button style -->
        <Style x:Key="RevertButtonStyle" TargetType="local:AnimatedButton">
            <Setter Property="Width" Value="120"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="ButtonText" Value="REVERT"/>
        </Style>
    </UserControl.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="10"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- Apply Button -->
        <local:AnimatedButton x:Name="ApplyButton"
                              Grid.Column="0"
                              Style="{StaticResource ApplyButtonStyle}"
                              Click="ApplyButton_Click"/>
        
        <!-- Spacer -->
        <Rectangle Grid.Column="1" Fill="Transparent"/>
        
        <!-- Revert Button -->
        <local:AnimatedButton x:Name="RevertButton"
                              Grid.Column="2"
                              Style="{StaticResource RevertButtonStyle}"
                              Click="RevertButton_Click"/>
    </Grid>
</UserControl>
