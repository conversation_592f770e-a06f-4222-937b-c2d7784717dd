using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    public class GitHubUserService
    {
        private readonly HttpClient _httpClient;
        private readonly string _githubToken;
        private readonly string _githubRepo;
        private readonly string _githubOwner;
        private readonly string _usersFilePath;
        private readonly LoggingService _logger;

        public GitHubUserService(string githubToken, string githubOwner, string githubRepo)
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "CircleUtility");
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"token {githubToken}");
            
            _githubToken = githubToken;
            _githubOwner = githubOwner;
            _githubRepo = githubRepo;
            _usersFilePath = "users/users.json";
            _logger = LoggingService.Instance;
        }

        public async Task<List<DiscordUser>> LoadUsersAsync()
        {
            try
            {
                var url = $"https://api.github.com/repos/{_githubOwner}/{_githubRepo}/contents/{_usersFilePath}";
                var response = await _httpClient.GetStringAsync(url);
                var content = JsonSerializer.Deserialize<GitHubContent>(response);
                
                if (content != null && !string.IsNullOrEmpty(content.Content))
                {
                    var jsonContent = System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(content.Content));
                    var users = JsonSerializer.Deserialize<List<DiscordUser>>(jsonContent);
                    return users ?? new List<DiscordUser>();
                }
                
                return new List<DiscordUser>();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading users from GitHub: {ex.Message}", LogLevel.ERROR);
                return new List<DiscordUser>();
            }
        }

        public async Task<bool> AddUserAsync(DiscordUser user)
        {
            try
            {
                var users = await LoadUsersAsync();
                
                // Check if user already exists
                if (users.Any(u => u.Username.Equals(user.Username, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.Log($"User {user.Username} already exists", LogLevel.WARNING);
                    return false;
                }

                users.Add(user);
                return await SaveUsersAsync(users);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error adding user: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(DiscordUser user)
        {
            try
            {
                var users = await LoadUsersAsync();
                var existingUser = users.FirstOrDefault(u => u.Username.Equals(user.Username, StringComparison.OrdinalIgnoreCase));
                
                if (existingUser == null)
                {
                    _logger.Log($"User {user.Username} not found", LogLevel.WARNING);
                    return false;
                }

                // Update user properties
                existingUser.DisplayName = user.DisplayName;
                existingUser.Role = user.Role;
                existingUser.IsActive = user.IsActive;
                existingUser.LastModified = DateTime.UtcNow;

                return await SaveUsersAsync(users);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating user: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(string username)
        {
            try
            {
                var users = await LoadUsersAsync();
                var user = users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                
                if (user == null)
                {
                    _logger.Log($"User {username} not found", LogLevel.WARNING);
                    return false;
                }

                users.Remove(user);
                return await SaveUsersAsync(users);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error deleting user: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        private async Task<bool> SaveUsersAsync(List<DiscordUser> users)
        {
            try
            {
                var jsonContent = JsonSerializer.Serialize(users, new JsonSerializerOptions { WriteIndented = true });
                var base64Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(jsonContent));

                // Get the current file SHA
                var url = $"https://api.github.com/repos/{_githubOwner}/{_githubRepo}/contents/{_usersFilePath}";
                var response = await _httpClient.GetStringAsync(url);
                var content = JsonSerializer.Deserialize<GitHubContent>(response);

                var updateRequest = new
                {
                    message = $"Update users.json - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC",
                    content = base64Content,
                    sha = content?.Sha
                };

                var updateResponse = await _httpClient.PutAsync(url, 
                    new StringContent(JsonSerializer.Serialize(updateRequest), 
                    System.Text.Encoding.UTF8, "application/json"));

                return updateResponse.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving users to GitHub: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        private class GitHubContent
        {
            public string Content { get; set; }
            public string Sha { get; set; }
        }
    }
} 