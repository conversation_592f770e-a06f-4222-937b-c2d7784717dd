// Created by Arsenal on 5-17-25 12:15PM
using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for GPU information
    /// </summary>
    public class GPUInfo
    {
        /// <summary>
        /// Gets or sets the name of the GPU
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the driver version
        /// </summary>
        public string DriverVersion { get; set; }

        /// <summary>
        /// Gets or sets the video memory in MB
        /// </summary>
        public long VideoMemory { get; set; }

        /// <summary>
        /// Gets or sets the memory in GB
        /// </summary>
        public int MemoryGB { get; set; }

        /// <summary>
        /// Gets or sets the current resolution
        /// </summary>
        public string CurrentResolution { get; set; }

        /// <summary>
        /// Gets or sets the refresh rate in Hz
        /// </summary>
        public int RefreshRate { get; set; }

        /// <summary>
        /// Gets or sets the device ID
        /// </summary>
        public string DeviceID { get; set; }

        /// <summary>
        /// Gets or sets the status
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the vendor
        /// </summary>
        public GPUVendor Vendor { get; set; }

        /// <summary>
        /// Gets or sets the core clock in MHz
        /// </summary>
        public int CoreClock { get; set; }

        /// <summary>
        /// Gets or sets the memory clock in MHz
        /// </summary>
        public int MemoryClock { get; set; }

        /// <summary>
        /// Gets or sets the temperature in Celsius
        /// </summary>
        public double Temperature { get; set; }

        /// <summary>
        /// Gets or sets the fan speed percentage
        /// </summary>
        public int FanSpeed { get; set; }

        /// <summary>
        /// Gets or sets the power usage in watts
        /// </summary>
        public double PowerUsage { get; set; }

        /// <summary>
        /// Gets or sets the usage percentage
        /// </summary>
        public double Usage { get; set; }

        /// <summary>
        /// Gets or sets the memory usage percentage
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// Gets or sets the CUDA cores (NVIDIA) or Stream Processors (AMD)
        /// </summary>
        public int Cores { get; set; }

        /// <summary>
        /// Gets or sets the DirectX version
        /// </summary>
        public string DirectXVersion { get; set; }

        /// <summary>
        /// Gets or sets the OpenGL version
        /// </summary>
        public string OpenGLVersion { get; set; }

        /// <summary>
        /// Gets or sets the Vulkan version
        /// </summary>
        public string VulkanVersion { get; set; }

        /// <summary>
        /// Gets or sets the PCIe version
        /// </summary>
        public string PCIeVersion { get; set; }

        /// <summary>
        /// Gets or sets the PCIe lanes
        /// </summary>
        public int PCIeLanes { get; set; }

        /// <summary>
        /// Gets or sets the BIOS version
        /// </summary>
        public string BIOSVersion { get; set; }

        /// <summary>
        /// Gets or sets the architecture
        /// </summary>
        public string Architecture { get; set; }

        /// <summary>
        /// Gets or sets the manufacturing process in nm
        /// </summary>
        public int Process { get; set; }

        /// <summary>
        /// Gets or sets the release date
        /// </summary>
        public DateTime? ReleaseDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the GPU is overclocked
        /// </summary>
        public bool IsOverclocked { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the GPU is the primary display adapter
        /// </summary>
        public bool IsPrimary { get; set; }

        /// <summary>
        /// Gets the formatted video memory
        /// </summary>
        public string FormattedVideoMemory
        {
            get
            {
                if (VideoMemory >= 1024)
                {
                    return $"{VideoMemory / 1024.0:F1} GB";
                }
                return $"{VideoMemory} MB";
            }
        }

        /// <summary>
        /// Gets the formatted core clock
        /// </summary>
        public string FormattedCoreClock => $"{CoreClock} MHz";

        /// <summary>
        /// Gets the formatted memory clock
        /// </summary>
        public string FormattedMemoryClock => $"{MemoryClock} MHz";

        /// <summary>
        /// Gets the formatted temperature
        /// </summary>
        public string FormattedTemperature => $"{Temperature:F1}°C";

        /// <summary>
        /// Gets the formatted fan speed
        /// </summary>
        public string FormattedFanSpeed => $"{FanSpeed}%";

        /// <summary>
        /// Gets the formatted power usage
        /// </summary>
        public string FormattedPowerUsage => $"{PowerUsage:F1}W";

        /// <summary>
        /// Gets the formatted usage
        /// </summary>
        public string FormattedUsage => $"{Usage:F1}%";

        /// <summary>
        /// Gets the formatted memory usage
        /// </summary>
        public string FormattedMemoryUsage => $"{MemoryUsage:F1}%";

        /// <summary>
        /// Gets the formatted vendor
        /// </summary>
        public string FormattedVendor
        {
            get
            {
                switch (Vendor)
                {
                    case GPUVendor.NVIDIA:
                        return "NVIDIA";
                    case GPUVendor.AMD:
                        return "AMD";
                    case GPUVendor.Intel:
                        return "Intel";
                    case GPUVendor.Other:
                    default:
                        return "Other";
                }
            }
        }

        /// <summary>
        /// Gets the vendor color code
        /// </summary>
        public string VendorColorCode
        {
            get
            {
                switch (Vendor)
                {
                    case GPUVendor.NVIDIA:
                        return "#76B900"; // NVIDIA Green
                    case GPUVendor.AMD:
                        return "#ED1C24"; // AMD Red
                    case GPUVendor.Intel:
                        return "#0071C5"; // Intel Blue
                    case GPUVendor.Other:
                    default:
                        return "#FFFFFF"; // White
                }
            }
        }

        /// <summary>
        /// Gets the temperature color code
        /// </summary>
        public string TemperatureColorCode
        {
            get
            {
                if (Temperature >= 80)
                {
                    return "#FF0000"; // Red
                }
                else if (Temperature >= 70)
                {
                    return "#FF8800"; // Orange
                }
                else if (Temperature >= 60)
                {
                    return "#FFFF00"; // Yellow
                }
                else
                {
                    return "#00FF00"; // Green
                }
            }
        }

        /// <summary>
        /// Gets the usage color code
        /// </summary>
        public string UsageColorCode
        {
            get
            {
                if (Usage >= 90)
                {
                    return "#FF0000"; // Red
                }
                else if (Usage >= 75)
                {
                    return "#FF8800"; // Orange
                }
                else if (Usage >= 50)
                {
                    return "#FFFF00"; // Yellow
                }
                else
                {
                    return "#00FF00"; // Green
                }
            }
        }

        /// <summary>
        /// Gets the performance rating (0-100)
        /// </summary>
        public int PerformanceRating
        {
            get
            {
                // This is a simplified rating calculation
                // In a real implementation, this would be more sophisticated
                int memoryRating = (int)Math.Min(100, VideoMemory / 100);
                int clockRating = (int)Math.Min(100, CoreClock / 20);

                return (memoryRating + clockRating) / 2;
            }
        }

        /// <summary>
        /// Gets the performance tier
        /// </summary>
        public string PerformanceTier
        {
            get
            {
                if (PerformanceRating >= 90)
                {
                    return "Enthusiast";
                }
                else if (PerformanceRating >= 75)
                {
                    return "High-End";
                }
                else if (PerformanceRating >= 50)
                {
                    return "Mid-Range";
                }
                else if (PerformanceRating >= 25)
                {
                    return "Entry-Level";
                }
                else
                {
                    return "Integrated";
                }
            }
        }

        /// <summary>
        /// Gets the optimization recommendation
        /// </summary>
        public string OptimizationRecommendation
        {
            get
            {
                switch (Vendor)
                {
                    case GPUVendor.NVIDIA:
                        return "Set Power Management Mode to 'Prefer Maximum Performance' and Low Latency Mode to 'Ultra'";
                    case GPUVendor.AMD:
                        return "Set Power State to 'Performance' and Anti-Lag to 'Enabled'";
                    case GPUVendor.Intel:
                        return "Set Power Mode to 'Maximum Performance'";
                    case GPUVendor.Other:
                    default:
                        return "No specific optimization available";
                }
            }
        }
    }

    /// <summary>
    /// Enum for GPU vendors
    /// </summary>
    public enum GPUVendor
    {
        /// <summary>
        /// NVIDIA
        /// </summary>
        NVIDIA,

        /// <summary>
        /// AMD
        /// </summary>
        AMD,

        /// <summary>
        /// Intel
        /// </summary>
        Intel,

        /// <summary>
        /// Other vendor
        /// </summary>
        Other
    }
}
