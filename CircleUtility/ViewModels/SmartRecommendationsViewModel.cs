using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using CircleUtility.Models;
using CircleUtility.Services;
using CircleUtility.Interfaces;
// Assuming you have a RelayCommand implementation. If not, you'll need to create or add one.
// For example, using Microsoft.Toolkit.Mvvm.Input.RelayCommand or similar.

namespace CircleUtility.ViewModels
{
    public class SmartRecommendationsViewModel : BaseViewModel // Assuming a BaseViewModel for INotifyPropertyChanged
    {
        private readonly IHardwareOptimizationService _hardwareOptimizationService;

        private ObservableCollection<SmartTweak> _recommendedTweaks;
        public ObservableCollection<SmartTweak> RecommendedTweaks
        {
            get => _recommendedTweaks;
            set => SetProperty(ref _recommendedTweaks, value);
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        private string _statusMessage;
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public ICommand LoadRecommendationsCommand { get; }
        public ICommand ApplySmartTweakCommand { get; }
        public ICommand RevertSmartTweakCommand { get; }

        public SmartRecommendationsViewModel(ILoggerService logger, IHardwareOptimizationService hardwareOptimizationService) : base(logger)
        {
            _hardwareOptimizationService = hardwareOptimizationService;

            RecommendedTweaks = new ObservableCollection<SmartTweak>();

            LoadRecommendationsCommand = new RelayCommand(async () => await LoadRecommendationsAsync());
            ApplySmartTweakCommand = new RelayCommand<SmartTweak>(async (tweak) => await ApplySmartTweakAsync(tweak), (tweak) => tweak != null && !tweak.IsCurrentlyApplied);
            RevertSmartTweakCommand = new RelayCommand<SmartTweak>(async (tweak) => await RevertSmartTweakAsync(tweak), (tweak) => tweak != null && tweak.IsCurrentlyApplied);

            // Initial load
            Task.Run(async () => await LoadRecommendationsAsync()); // Call explicitly from view's Loaded event if preferred
        }

        private async Task LoadRecommendationsAsync()
        {
            if (IsBusy) return;
            IsBusy = true;
            StatusMessage = "Loading smart recommendations...";
            _logger.LogInfo("SmartRecommendationsViewModel: Loading recommendations...");

            try
            {
                var tweaks = await _hardwareOptimizationService.GetRecommendedSmartTweaksAsync();
                RecommendedTweaks.Clear();
                foreach (var tweak in tweaks.OrderBy(t => t.Category).ThenBy(t => t.SettingName))
                {
                    RecommendedTweaks.Add(tweak);
                }
                StatusMessage = $"Found {RecommendedTweaks.Count} smart recommendations.";
                _logger.LogInfo($"SmartRecommendationsViewModel: Loaded {RecommendedTweaks.Count} recommendations.");
            }
            catch (System.Exception ex)
            {
                StatusMessage = "Error loading recommendations.";
                _logger.LogError("SmartRecommendationsViewModel: Error loading recommendations.", ex);
            }
            finally
            {
                IsBusy = false;
                RefreshCommandStates();
            }
        }

        private async Task ApplySmartTweakAsync(SmartTweak tweak)
        {
            if (tweak == null || IsBusy) return;

            IsBusy = true;
            StatusMessage = $"Applying {tweak.SettingName}...";
            _logger.LogInfo($"SmartRecommendationsViewModel: Applying tweak '{tweak.TweakId}' - {tweak.SettingName}");

            bool success = await _hardwareOptimizationService.ApplySmartTweakAsync(tweak);

            if (success)
            {
                StatusMessage = $"Successfully applied: {tweak.SettingName}.";
                _logger.LogInfo($"SmartRecommendationsViewModel: Successfully applied tweak '{tweak.TweakId}'.");
                // The service updates tweak.IsCurrentlyApplied, but we need to notify UI if command CanExecute depends on it.
                // One way is to re-fetch the specific tweak or refresh its state.
                var appliedTweak = RecommendedTweaks.FirstOrDefault(rt => rt.TweakId == tweak.TweakId);
                if (appliedTweak != null) { appliedTweak.IsCurrentlyApplied = true; } // Ensure local copy reflects
            }
            else
            {
                StatusMessage = $"Failed to apply: {tweak.SettingName}.";
                _logger.LogError($"SmartRecommendationsViewModel: Failed to apply tweak '{tweak.TweakId}'.");
            }
            
            IsBusy = false;
            RefreshCommandStates();
            if (tweak.RequiresRestart && success)
            {
                StatusMessage += " Restart recommended.";
                // Optionally, show a dialog or a more prominent restart notification.
            }
        }

        private async Task RevertSmartTweakAsync(SmartTweak tweak)
        {
            if (tweak == null || IsBusy) return;

            IsBusy = true;
            StatusMessage = $"Reverting {tweak.SettingName}...";
            _logger.LogInfo($"SmartRecommendationsViewModel: Reverting tweak '{tweak.TweakId}' - {tweak.SettingName}");

            bool success = await _hardwareOptimizationService.RevertSmartTweakAsync(tweak);
            if (success)
            {
                StatusMessage = $"Successfully reverted: {tweak.SettingName}.";
                _logger.LogInfo($"SmartRecommendationsViewModel: Successfully reverted tweak '{tweak.TweakId}'.");
                var revertedTweak = RecommendedTweaks.FirstOrDefault(rt => rt.TweakId == tweak.TweakId);
                if (revertedTweak != null) { revertedTweak.IsCurrentlyApplied = false; }
            }
            else
            {
                StatusMessage = $"Failed to revert: {tweak.SettingName}.";
                _logger.LogError($"SmartRecommendationsViewModel: Failed to revert tweak '{tweak.TweakId}'.");
            }

            IsBusy = false;
            RefreshCommandStates();
             if (tweak.RequiresRestart && success)
            {
                StatusMessage += " Restart recommended.";
            }
        }

        private void RefreshCommandStates()
        {
            // For RelayCommand, this manually triggers CanExecuteChanged.
            // If your RelayCommand implementation handles this automatically based on property changes,
            // this might not be strictly necessary, but it's safer.
            (ApplySmartTweakCommand as RelayCommand<SmartTweak>)?.RaiseCanExecuteChanged();
            (RevertSmartTweakCommand as RelayCommand<SmartTweak>)?.RaiseCanExecuteChanged();
             // For a non-generic RelayCommand: (LoadRecommendationsCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }
    }

    // Placeholder for RelayCommand if not already defined in your project.
    // A proper implementation would typically be in a shared MVVM utilities library.
    // For example, from Microsoft.Toolkit.Mvvm.Input:
    // using Microsoft.Toolkit.Mvvm.Input; 
    // Or a simple custom one:
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public bool CanExecute(object parameter) => _canExecute == null || _canExecute();
        public void Execute(object parameter) => _execute();
        public void RaiseCanExecuteChanged() => CommandManager.InvalidateRequerySuggested();
    }

    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Predicate<T> _canExecute;

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public RelayCommand(Action<T> execute, Predicate<T> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public bool CanExecute(object parameter) => _canExecute == null || _canExecute((T)parameter);
        public void Execute(object parameter) => _execute((T)parameter);
        public void RaiseCanExecuteChanged() => CommandManager.InvalidateRequerySuggested();
    }
} 