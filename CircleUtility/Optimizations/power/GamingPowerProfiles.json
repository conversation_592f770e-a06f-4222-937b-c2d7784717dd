[{"Name": "Ultimate Performance Gaming", "Description": "Maximum performance power profile optimized for competitive gaming with minimal input latency and maximum FPS. Prioritizes performance over power efficiency and noise.", "Category": "Gaming", "CpuSettings": {"PowerPlan": "Ultimate Performance", "MinProcessorState": 50, "MaxProcessorState": 100, "PowerLimit1": 0, "PowerLimit2": 0, "PowerLimit4": 0, "TauValue": 0, "CoreParkingEnabled": false, "CoreParkingMinCores": 100, "SpeedShiftEnabled": true, "SpeedShiftEppValue": 0, "PrecisionBoostOverdriveEnabled": true, "PrecisionBoostOverdriveScalar": 2, "CppcPreferredCoresEnabled": true, "ECoresEnabled": true, "GameProcessesPreferPCores": true, "CStateLimit": 1}, "GpuSettings": {"PowerManagementMode": "Prefer Maximum Performance", "PowerLimit": 105, "TemperatureTarget": 83, "CustomFanCurve": true, "FanCurvePoints": [{"Temperature": 30, "FanSpeed": 30}, {"Temperature": 50, "FanSpeed": 50}, {"Temperature": 70, "FanSpeed": 70}, {"Temperature": 80, "FanSpeed": 90}, {"Temperature": 85, "FanSpeed": 100}], "FanHysteresisEnabled": true, "FanHysteresisValue": 3, "LowLatencyModeEnabled": true, "LowLatencyModeValue": "Ultra", "AntiLagEnabled": true, "ResizableBarEnabled": true, "TextureFilteringQuality": "Performance", "ThreadedOptimizationEnabled": true, "ShaderCacheEnabled": true, "ShaderCacheSize": 0}, "SystemSettings": {"CoolingPolicy": "Active", "HardDiskTimeout": 0, "UsbSelectiveSuspendEnabled": false, "PciExpressPowerManagementEnabled": false, "ProcessorIdleStatesEnabled": false, "OptimizeBackgroundServices": true, "DisableNetworkThrottling": true, "DisableHpet": true, "DisableDynamicTick": true, "TimerResolution": 0.5}, "PerformanceImpact": 95, "PowerEfficiency": 20, "ThermalImpact": 90, "NoiseLevel": 80, "InputLatencyImpact": 95, "Risk": 1, "WarningMessage": "This profile maximizes performance at the cost of higher power consumption, heat output, and fan noise. It uses safe settings but requires good cooling.", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This profile is designed for competitive gaming where every frame and millisecond of input latency matters. It configures your system for maximum performance with minimal regard for power efficiency or noise. While all settings are safe, this profile will increase power consumption and heat output, so good cooling is recommended.", "HardwareCompatibility": [], "GameCompatibility": ["Fortnite", "Call of Duty", "Valorant", "Apex Legends", "Counter-Strike", "Overwatch"]}, {"Name": "Balanced Gaming", "Description": "Balanced power profile that provides excellent gaming performance while maintaining reasonable power efficiency and noise levels.", "Category": "Gaming", "CpuSettings": {"PowerPlan": "High Performance", "MinProcessorState": 10, "MaxProcessorState": 100, "PowerLimit1": 0, "PowerLimit2": 0, "PowerLimit4": 0, "TauValue": 0, "CoreParkingEnabled": false, "CoreParkingMinCores": 100, "SpeedShiftEnabled": true, "SpeedShiftEppValue": 40, "PrecisionBoostOverdriveEnabled": false, "PrecisionBoostOverdriveScalar": 0, "CppcPreferredCoresEnabled": true, "ECoresEnabled": true, "GameProcessesPreferPCores": true, "CStateLimit": 2}, "GpuSettings": {"PowerManagementMode": "Optimal Power", "PowerLimit": 100, "TemperatureTarget": 80, "CustomFanCurve": true, "FanCurvePoints": [{"Temperature": 30, "FanSpeed": 20}, {"Temperature": 50, "FanSpeed": 40}, {"Temperature": 70, "FanSpeed": 60}, {"Temperature": 80, "FanSpeed": 80}, {"Temperature": 85, "FanSpeed": 100}], "FanHysteresisEnabled": true, "FanHysteresisValue": 3, "LowLatencyModeEnabled": true, "LowLatencyModeValue": "On", "AntiLagEnabled": true, "ResizableBarEnabled": true, "TextureFilteringQuality": "Quality", "ThreadedOptimizationEnabled": true, "ShaderCacheEnabled": true, "ShaderCacheSize": 0}, "SystemSettings": {"CoolingPolicy": "Active", "HardDiskTimeout": 10, "UsbSelectiveSuspendEnabled": true, "PciExpressPowerManagementEnabled": false, "ProcessorIdleStatesEnabled": true, "OptimizeBackgroundServices": true, "DisableNetworkThrottling": true, "DisableHpet": false, "DisableDynamicTick": true, "TimerResolution": 0.5}, "PerformanceImpact": 80, "PowerEfficiency": 60, "ThermalImpact": 70, "NoiseLevel": 60, "InputLatencyImpact": 80, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This profile strikes a balance between performance and power efficiency. It provides excellent gaming performance with reduced input latency, while still allowing your system to run at reasonable power consumption and noise levels. This is the recommended profile for most gaming scenarios.", "HardwareCompatibility": [], "GameCompatibility": []}, {"Name": "Quiet Gaming", "Description": "Power profile optimized for quiet operation while still providing good gaming performance. Ideal for night gaming or noise-sensitive environments.", "Category": "Gaming", "CpuSettings": {"PowerPlan": "Balanced", "MinProcessorState": 5, "MaxProcessorState": 100, "PowerLimit1": 0, "PowerLimit2": 0, "PowerLimit4": 0, "TauValue": 0, "CoreParkingEnabled": true, "CoreParkingMinCores": 50, "SpeedShiftEnabled": true, "SpeedShiftEppValue": 80, "PrecisionBoostOverdriveEnabled": false, "PrecisionBoostOverdriveScalar": 0, "CppcPreferredCoresEnabled": true, "ECoresEnabled": true, "GameProcessesPreferPCores": true, "CStateLimit": 0}, "GpuSettings": {"PowerManagementMode": "Adaptive", "PowerLimit": 90, "TemperatureTarget": 75, "CustomFanCurve": true, "FanCurvePoints": [{"Temperature": 30, "FanSpeed": 0}, {"Temperature": 50, "FanSpeed": 30}, {"Temperature": 70, "FanSpeed": 50}, {"Temperature": 80, "FanSpeed": 70}, {"Temperature": 85, "FanSpeed": 90}], "FanHysteresisEnabled": true, "FanHysteresisValue": 5, "LowLatencyModeEnabled": true, "LowLatencyModeValue": "On", "AntiLagEnabled": true, "ResizableBarEnabled": true, "TextureFilteringQuality": "Quality", "ThreadedOptimizationEnabled": true, "ShaderCacheEnabled": true, "ShaderCacheSize": 0}, "SystemSettings": {"CoolingPolicy": "Passive", "HardDiskTimeout": 20, "UsbSelectiveSuspendEnabled": true, "PciExpressPowerManagementEnabled": true, "ProcessorIdleStatesEnabled": true, "OptimizeBackgroundServices": true, "DisableNetworkThrottling": true, "DisableHpet": false, "DisableDynamicTick": false, "TimerResolution": 1.0}, "PerformanceImpact": 65, "PowerEfficiency": 80, "ThermalImpact": 50, "NoiseLevel": 30, "InputLatencyImpact": 60, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This profile is designed for quiet operation while still providing good gaming performance. It's ideal for night gaming or noise-sensitive environments. While performance and input latency won't be as good as the other profiles, it still provides a good gaming experience with significantly reduced noise levels.", "HardwareCompatibility": [], "GameCompatibility": ["The Witcher 3", "Cyberpunk 2077", "Red Dead Redemption 2", "Assassin's Creed", "Skyrim", "Fallout"]}, {"Name": "Laptop Gaming", "Description": "Power profile optimized for gaming on laptops, balancing performance with battery life and thermal management.", "Category": "Gaming", "CpuSettings": {"PowerPlan": "Balanced", "MinProcessorState": 5, "MaxProcessorState": 100, "PowerLimit1": 0, "PowerLimit2": 0, "PowerLimit4": 0, "TauValue": 0, "CoreParkingEnabled": false, "CoreParkingMinCores": 100, "SpeedShiftEnabled": true, "SpeedShiftEppValue": 60, "PrecisionBoostOverdriveEnabled": false, "PrecisionBoostOverdriveScalar": 0, "CppcPreferredCoresEnabled": true, "ECoresEnabled": true, "GameProcessesPreferPCores": true, "CStateLimit": 3}, "GpuSettings": {"PowerManagementMode": "Optimal Power", "PowerLimit": 95, "TemperatureTarget": 75, "CustomFanCurve": true, "FanCurvePoints": [{"Temperature": 30, "FanSpeed": 0}, {"Temperature": 50, "FanSpeed": 40}, {"Temperature": 70, "FanSpeed": 70}, {"Temperature": 80, "FanSpeed": 100}, {"Temperature": 85, "FanSpeed": 100}], "FanHysteresisEnabled": true, "FanHysteresisValue": 3, "LowLatencyModeEnabled": true, "LowLatencyModeValue": "On", "AntiLagEnabled": true, "ResizableBarEnabled": true, "TextureFilteringQuality": "Performance", "ThreadedOptimizationEnabled": true, "ShaderCacheEnabled": true, "ShaderCacheSize": 0}, "SystemSettings": {"CoolingPolicy": "Active", "HardDiskTimeout": 10, "UsbSelectiveSuspendEnabled": true, "PciExpressPowerManagementEnabled": true, "ProcessorIdleStatesEnabled": true, "OptimizeBackgroundServices": true, "DisableNetworkThrottling": true, "DisableHpet": false, "DisableDynamicTick": true, "TimerResolution": 0.5}, "PerformanceImpact": 75, "PowerEfficiency": 70, "ThermalImpact": 65, "NoiseLevel": 70, "InputLatencyImpact": 75, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This profile is specifically designed for gaming on laptops. It balances performance with battery life and thermal management, which is crucial for laptop gaming. It provides good gaming performance with reduced input latency, while still managing heat and power consumption to prevent thermal throttling.", "HardwareCompatibility": ["Laptop"], "GameCompatibility": []}, {"Name": "Advanced Performance Tuning", "Description": "Advanced power profile with fine-tuned settings for maximum performance in CPU-limited games. Requires good cooling and stable hardware.", "Category": "Gaming", "CpuSettings": {"PowerPlan": "Ultimate Performance", "MinProcessorState": 100, "MaxProcessorState": 100, "PowerLimit1": 0, "PowerLimit2": 0, "PowerLimit4": 0, "TauValue": 0, "CoreParkingEnabled": false, "CoreParkingMinCores": 100, "SpeedShiftEnabled": true, "SpeedShiftEppValue": 0, "PrecisionBoostOverdriveEnabled": true, "PrecisionBoostOverdriveScalar": 3, "CppcPreferredCoresEnabled": true, "ECoresEnabled": true, "GameProcessesPreferPCores": true, "CStateLimit": 0}, "GpuSettings": {"PowerManagementMode": "Prefer Maximum Performance", "PowerLimit": 110, "TemperatureTarget": 85, "CustomFanCurve": true, "FanCurvePoints": [{"Temperature": 30, "FanSpeed": 40}, {"Temperature": 50, "FanSpeed": 60}, {"Temperature": 70, "FanSpeed": 80}, {"Temperature": 80, "FanSpeed": 100}, {"Temperature": 85, "FanSpeed": 100}], "FanHysteresisEnabled": true, "FanHysteresisValue": 2, "LowLatencyModeEnabled": true, "LowLatencyModeValue": "Ultra", "AntiLagEnabled": true, "ResizableBarEnabled": true, "TextureFilteringQuality": "Performance", "ThreadedOptimizationEnabled": true, "ShaderCacheEnabled": true, "ShaderCacheSize": 0}, "SystemSettings": {"CoolingPolicy": "Active", "HardDiskTimeout": 0, "UsbSelectiveSuspendEnabled": false, "PciExpressPowerManagementEnabled": false, "ProcessorIdleStatesEnabled": false, "OptimizeBackgroundServices": true, "DisableNetworkThrottling": true, "DisableHpet": true, "DisableDynamicTick": true, "TimerResolution": 0.5}, "PerformanceImpact": 100, "PowerEfficiency": 10, "ThermalImpact": 100, "NoiseLevel": 100, "InputLatencyImpact": 100, "Risk": 2, "WarningMessage": "This profile uses aggressive settings that push your hardware to its limits. It requires excellent cooling and stable hardware. Use at your own risk.", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": true, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This profile is for advanced users who want to extract every last bit of performance from their hardware. It uses aggressive settings that push your hardware to its limits, so excellent cooling is required. While all settings are within safe limits, they are at the upper end of those limits, so use this profile with caution.", "HardwareCompatibility": ["High-end desktop", "Custom water cooling"], "GameCompatibility": ["CPU-limited games", "Competitive games"]}]