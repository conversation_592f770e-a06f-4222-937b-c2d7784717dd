using System;
using System.Windows;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace CircleUtility.Windows
{
    /// <summary>
    /// Preview window for the loading screen
    /// </summary>
    public partial class LoadingScreenPreview : Window
    {
        private DispatcherTimer _demoTimer;
        private int _currentStep = 0;
        private readonly string[] _loadingMessages = new string[]
        {
            "INITIALIZING CIRCLE UTILITY...",
            "LOADING HARDWARE SERVICES...",
            "SCANNING SYSTEM CONFIGURATION...",
            "OPTIMIZING PERFORMANCE SETTINGS...",
            "CONFIGURING SECURITY PROTOCOLS...",
            "PREPARING USER INTERFACE...",
            "FINALIZING SETUP...",
            "SYSTEM READY"
        };

        private readonly string[] _detailMessages = new string[]
        {
            "Starting up Circle Utility framework...",
            "Loading hardware detection modules...",
            "Scanning system configuration and settings...",
            "Applying performance optimizations...",
            "Configuring security and authentication...",
            "Preparing user interface components...",
            "Finalizing system setup and validation...",
            "Circle Utility is ready to launch!"
        };

        public LoadingScreenPreview()
        {
            InitializeComponent();
            StartDemoAnimations();
        }

        /// <summary>
        /// Starts all the demo animations
        /// </summary>
        private void StartDemoAnimations()
        {
            // Start the loading spinner
            var loadingAnimation = (Storyboard)FindResource("LoadingAnimation");
            loadingAnimation.Begin();

            // Start the title pulse
            var pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            pulseAnimation.Begin();

            // Start the demo timer for cycling through messages
            _demoTimer = new DispatcherTimer();
            _demoTimer.Interval = TimeSpan.FromMilliseconds(1200);
            _demoTimer.Tick += DemoTimer_Tick;
            _demoTimer.Start();

            // Start with first message
            UpdateLoadingDisplay();
        }

        /// <summary>
        /// Demo timer tick to cycle through loading messages
        /// </summary>
        private void DemoTimer_Tick(object sender, EventArgs e)
        {
            if (_currentStep < _loadingMessages.Length - 1)
            {
                _currentStep++;
                UpdateLoadingDisplay();
            }
            else
            {
                // Reset to beginning for continuous demo
                _currentStep = 0;
                UpdateLoadingDisplay();
            }
        }

        /// <summary>
        /// Updates the loading display with current step
        /// </summary>
        private void UpdateLoadingDisplay()
        {
            // Update status text
            LoadingStatusText.Text = _loadingMessages[_currentStep];
            
            // Update details text
            LoadingDetailsText.Text = _detailMessages[_currentStep];
            
            // Update progress bar
            double progress = (double)_currentStep / (_loadingMessages.Length - 1) * 100;
            LoadingProgressBar.Value = progress;
            
            // Update percentage
            ProgressPercentage.Text = $"{progress:F0}%";

            // Show access button when complete
            if (_currentStep == _loadingMessages.Length - 1)
            {
                AccessButton.Visibility = Visibility.Visible;
            }
            else
            {
                AccessButton.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Handles the close button click
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            _demoTimer?.Stop();
            Close();
        }

        /// <summary>
        /// Handles the access button click
        /// </summary>
        private void AccessButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("🚀 This would transition to the main utility interface!\n\n" +
                          "In the actual implementation, this button would:\n" +
                          "• Complete the loading sequence\n" +
                          "• Close the login window\n" +
                          "• Open the main utility window", 
                          "Access System", 
                          MessageBoxButton.OK, 
                          MessageBoxImage.Information);
        }

        /// <summary>
        /// Window closing event
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            _demoTimer?.Stop();
        }
    }
}
