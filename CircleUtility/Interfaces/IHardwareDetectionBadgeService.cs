using System;
using System.Threading.Tasks;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for hardware detection badge service
    /// </summary>
    public interface IHardwareDetectionBadgeService
    {
        /// <summary>
        /// Gets hardware badges
        /// </summary>
        /// <returns>Hardware badges information</returns>
        object GetHardwareBadges();

        /// <summary>
        /// Updates hardware badges
        /// </summary>
        void UpdateBadges();

        /// <summary>
        /// Event raised when badges are updated
        /// </summary>
        event EventHandler BadgesUpdated;

        /// <summary>
        /// Refreshes badges asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task RefreshBadgesAsync();

        /// <summary>
        /// Checks if hardware is newly detected
        /// </summary>
        /// <param name="hardwareId">Hardware ID to check</param>
        /// <returns>True if newly detected</returns>
        bool IsNewlyDetected(string hardwareId);

        /// <summary>
        /// Resets newly detected status
        /// </summary>
        /// <param name="hardwareId">Hardware ID to reset</param>
        void ResetNewlyDetected(string hardwareId);
    }
}
