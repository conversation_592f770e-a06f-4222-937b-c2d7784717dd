<UserControl x:Class="CircleUtility.Controls.AnimatedProgressBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="20"
             d:DesignWidth="300">

    <UserControl.Resources>
        <!-- Progress animation -->
        <Storyboard x:Key="ProgressAnimation">
            <DoubleAnimation Storyboard.TargetName="ProgressIndicator"
                             Storyboard.TargetProperty="Width"
                             From="0"
                             Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Pulse animation -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="GlowEffect"
                             Storyboard.TargetProperty="Opacity"
                             From="0.5"
                             To="0.8"
                             Duration="0:0:1.5"
                             AutoReverse="True">
                <DoubleAnimation.EasingFunction>
                    <SineEase EasingMode="EaseInOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Indeterminate animation -->
        <Storyboard x:Key="IndeterminateAnimation" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="IndeterminateIndicator"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="-300" />
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="300">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut" />
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </UserControl.Resources>

    <Grid>
        <!-- Background -->
        <Border Background="#FF050A0F"
                BorderBrush="{Binding ProgressColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                BorderThickness="1"
                CornerRadius="3" />

        <!-- Determinate progress -->
        <Border x:Name="ProgressIndicator"
                Background="{Binding ProgressColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                HorizontalAlignment="Left"
                Width="0"
                Margin="1"
                CornerRadius="2">
            <Border.Effect>
                <DropShadowEffect x:Name="GlowEffect"
                                  Color="{Binding ProgressColor, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource ColorConverter}}"
                                  BlurRadius="10"
                                  ShadowDepth="0"
                                  Opacity="0.5" />
            </Border.Effect>
        </Border>

        <!-- Indeterminate progress -->
        <Border x:Name="IndeterminateIndicator"
                Background="{Binding ProgressColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Width="100"
                Opacity="0.5"
                CornerRadius="2"
                Margin="1"
                Visibility="Collapsed"
                RenderTransformOrigin="0.5,0.5">
            <Border.RenderTransform>
                <TranslateTransform X="0" />
            </Border.RenderTransform>
        </Border>

        <!-- Optional progress text -->
        <TextBlock Text="{Binding ProgressText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                   FontFamily="Consolas"
                   FontSize="12"
                   Foreground="White"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Visibility="{Binding ProgressText, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource StringToVisibilityConverter}}" />
    </Grid>
</UserControl>
