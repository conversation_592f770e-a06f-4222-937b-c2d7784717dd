using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for Discord integration
    /// </summary>
    public class DiscordService : IDiscordService
    {
        private static DiscordService _instance;
        private static readonly object _instanceLock = new object();
        private readonly LoggingService _logger;
        private readonly HttpClient _httpClient;
        private string _webhookUrl;
        private string _discordUserId;
        private bool _isEnabled;
        private bool _isListening;
        private readonly Dictionary<string, Func<string[], Task<bool>>> _commandHandlers;

        private DiscordService()
        {
            _logger = LoggingService.Instance;
            _httpClient = new HttpClient();

            // Default Discord settings
            _webhookUrl = string.Empty;
            _discordUserId = string.Empty;
            _isEnabled = true;
            _isListening = true;

            // Initialize command handlers
            _commandHandlers = new Dictionary<string, Func<string[], Task<bool>>>
            {
                { "?alive", HandleAliveCommand },
                { "?emergency", HandleEmergencyCommand },
                { "?about", HandleAboutCommand }
            };

            _logger.Log("Discord service initialized with default settings", LogLevel.INFO);
        }

        /// <summary>
        /// Public constructor for DI compatibility
        /// </summary>
        /// <param name="forDI">Indicates if this is a DI call</param>
        public DiscordService(bool forDI = false) : this() { }

        /// <summary>
        /// Gets the singleton instance of the Discord service
        /// </summary>
        public static DiscordService Instance
        {
            get
            {
                _instance ??= new DiscordService();
                return _instance;
            }
        }

        /// <summary>
        /// Initializes the Discord service with the webhook URL and user ID
        /// </summary>
        /// <param name="webhookUrl">The Discord webhook URL</param>
        /// <param name="userId">The Discord user ID</param>
        public void Initialize(string webhookUrl, string userId)
        {
            if (string.IsNullOrWhiteSpace(webhookUrl))
            {
                _logger.Log("Discord webhook URL is empty or null. Discord integration disabled.", LogLevel.WARNING);
                _isEnabled = false;
                return;
            }

            _webhookUrl = webhookUrl;
            _discordUserId = userId ?? string.Empty;
            _isEnabled = true;
            _logger.Log("Discord integration initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Initializes the Discord service with the webhook URL
        /// </summary>
        /// <param name="webhookUrl">The Discord webhook URL</param>
        public void Initialize(string webhookUrl)
        {
            Initialize(webhookUrl, string.Empty);
        }

        /// <summary>
        /// Starts listening for Discord commands
        /// </summary>
        public void StartListening()
        {
            if (!_isEnabled)
            {
                _logger.Log("Discord integration is not enabled. Cannot start listening.", LogLevel.WARNING);
                return;
            }

            _isListening = true;
            _logger.Log("Discord command listening started", LogLevel.INFO);

            // In a real implementation, this would connect to Discord's API
            // and listen for messages. For now, we'll just log that we're listening.
        }

        /// <summary>
        /// Stops listening for Discord commands
        /// </summary>
        public void StopListening()
        {
            _isListening = false;
            _logger.Log("Discord command listening stopped", LogLevel.INFO);
        }

        /// <summary>
        /// Gets a value indicating whether the Discord service is enabled
        /// </summary>
        public bool IsEnabled => _isEnabled;

        /// <summary>
        /// Gets a value indicating whether the Discord service is listening for commands
        /// </summary>
        public bool IsListening => _isListening;

        /// <summary>
        /// Gets the Discord webhook URL
        /// </summary>
        public string WebhookUrl => _webhookUrl;

        /// <summary>
        /// Gets the Discord user ID
        /// </summary>
        public string UserId => _discordUserId;

        /// <summary>
        /// Sends a message to Discord
        /// </summary>
        /// <param name="message">The message to send</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> SendMessageAsync(string message)
        {
            if (!_isEnabled)
            {
                _logger.Log("Discord integration is not enabled. Message not sent.", LogLevel.WARNING);
                return false;
            }

            try
            {
                // Create the payload
                var payload = new
                {
                    content = message
                };

                // Serialize to JSON
                string jsonPayload = JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // Send the request
                var response = await _httpClient.PostAsync(_webhookUrl, content);

                // Check if successful
                if (response.IsSuccessStatusCode)
                {
                    _logger.Log("Discord message sent successfully", LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    _logger.Log($"Failed to send Discord message. Status: {response.StatusCode}, Response: {errorContent}", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sending Discord message: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Sends a message to Discord synchronously
        /// </summary>
        /// <param name="message">The message to send</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool SendMessage(string message)
        {
            return SendMessageAsync(message).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Sends an emergency notification to Discord
        /// </summary>
        /// <param name="userId">The Discord user ID to mention</param>
        /// <param name="message">The emergency message</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> SendEmergencyNotificationAsync(string userId, string message)
        {
            if (!_isEnabled)
            {
                _logger.Log("Discord integration is not enabled. Emergency notification not sent.", LogLevel.WARNING);
                return false;
            }

            try
            {
                // Create the payload with user mention
                string mentionContent = $"<@{userId}> **EMERGENCY ALERT** <@{userId}> <@{userId}>\n{message}";
                var payload = new
                {
                    content = mentionContent
                };

                // Serialize to JSON
                string jsonPayload = JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // Send the request
                var response = await _httpClient.PostAsync(_webhookUrl, content);

                // Check if successful
                if (response.IsSuccessStatusCode)
                {
                    _logger.Log("Discord emergency notification sent successfully", LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    _logger.Log($"Failed to send Discord emergency notification. Status: {response.StatusCode}, Response: {errorContent}", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sending Discord emergency notification: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Processes a Discord message to check if it's a command
        /// </summary>
        /// <param name="message">The message to process</param>
        /// <returns>True if the message was a command and was processed, false otherwise</returns>
        public async Task<bool> ProcessMessageAsync(string message)
        {
            if (!_isEnabled || !_isListening)
            {
                return false;
            }

            try
            {
                // Split the message into command and arguments
                string[] parts = message.Split(' ');
                if (parts.Length == 0)
                {
                    return false;
                }

                string command = parts[0].ToLower();
                string[] args = parts.Length > 1 ? parts[1..] : Array.Empty<string>();

                // Check if the command exists
                if (_commandHandlers.TryGetValue(command, out var handler))
                {
                    _logger.Log($"Processing Discord command: {command}", LogLevel.INFO);
                    return await handler(args);
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error processing Discord message: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Handles the ?alive command
        /// </summary>
        private async Task<bool> HandleAliveCommand(string[] args)
        {
            try
            {
                string message = "Yes, The Circle's Utility is alive and running! 🟢";
                return await SendMessageAsync(message);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling ?alive command: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Handles the ?emergency command
        /// </summary>
        private async Task<bool> HandleEmergencyCommand(string[] args)
        {
            try
            {
                if (string.IsNullOrEmpty(_discordUserId))
                {
                    await SendMessageAsync("Error: No Discord user ID configured for emergency notifications.");
                    return false;
                }

                string message = args.Length > 0 ? string.Join(" ", args) : "Emergency alert triggered from The Circle's Utility!";
                return await SendEmergencyNotificationAsync(_discordUserId, message);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling ?emergency command: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Handles the ?about command
        /// </summary>
        private async Task<bool> HandleAboutCommand(string[] args)
        {
            try
            {
                string message = "**About The Circle's Utility**\n\n" +
                                "The Circle's Utility is a powerful Windows optimization tool designed to enhance your gaming experience. " +
                                "It offers advanced input delay reduction techniques, detailed game optimizations, and system tweaks " +
                                "to maximize performance.\n\n" +
                                "Created by Arsenal with contributions from the community.\n\n" +
                                "**Available Commands:**\n" +
                                "?alive - Check if the utility is running\n" +
                                "?emergency - Send an emergency notification\n" +
                                "?about - Show this information";

                return await SendMessageAsync(message);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error handling ?about command: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Sends a rich embed notification to Discord with optional role mention
        /// </summary>
        /// <param name="title">Embed title</param>
        /// <param name="description">Embed description</param>
        /// <param name="color">Embed color (decimal)</param>
        /// <param name="fields">Array of embed fields (name/value/inline)</param>
        /// <param name="roleIdToMention">Optional Discord role ID to mention</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> SendEmbedNotificationAsync(string title, string description, int color, object[] fields = null, string roleIdToMention = null)
        {
            if (!_isEnabled)
            {
                _logger.Log("Discord integration is not enabled. Embed notification not sent.", LogLevel.WARNING);
                return false;
            }

            try
            {
                var embed = new Dictionary<string, object>
                {
                    ["title"] = title,
                    ["description"] = description,
                    ["color"] = color,
                    ["timestamp"] = DateTime.UtcNow.ToString("o")
                };
                if (fields != null)
                {
                    embed["fields"] = fields;
                }
                var payload = new Dictionary<string, object>
                {
                    ["username"] = "The Circle Utility",
                    ["embeds"] = new[] { embed }
                };
                if (!string.IsNullOrEmpty(roleIdToMention))
                {
                    payload["content"] = $"<@&{roleIdToMention}>";
                }
                var jsonPayload = JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_webhookUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    _logger.Log("Discord embed notification sent successfully", LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    _logger.Log($"Failed to send Discord embed notification. Status: {response.StatusCode}, Response: {errorContent}", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sending Discord embed notification: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Sends a user login embed notification to Discord
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="isFirstTime">Whether this is the user's first login</param>
        /// <param name="systemInfo">A dictionary of system info fields</param>
        /// <param name="loginCount">The user's login count</param>
        /// <param name="roleIdToMention">Optional role ID to mention</param>
        public async Task<bool> SendUserLoginEmbedAsync(string username, bool isFirstTime, Dictionary<string, string> systemInfo, int loginCount = 1, string roleIdToMention = null)
        {
            string title = isFirstTime ? "New User Connected" : "User Login";
            string description = isFirstTime
                ? $"🎉 **{username}** connected to The Circle Utility for the first time!"
                : $"🔄 **{username}** logged in to The Circle Utility!";
            int color = isFirstTime ? 5763719 : 3447003; // Green or blue
            var fields = new List<object>();
            if (loginCount > 1)
            {
                fields.Add(new { name = "Login Count", value = loginCount.ToString(), inline = true });
            }
            if (systemInfo != null)
            {
                foreach (var kvp in systemInfo)
                {
                    fields.Add(new { name = kvp.Key, value = kvp.Value, inline = true });
                }
            }
            return await SendEmbedNotificationAsync(title, description, color, fields.ToArray(), roleIdToMention);
        }

        // IDiscordService implementation
        public bool IsConnected => IsEnabled; // Or implement a more accurate connection state if available
        public void Initialize() => StartListening();
        public async Task<bool> Connect() { StartListening(); return await Task.FromResult(IsEnabled); }
        public async Task<bool> Disconnect() { StopListening(); return await Task.FromResult(!IsListening); }
        public async Task StopAsync() { StopListening(); await Task.CompletedTask; }
        Task IDiscordService.SendMessage(string message) => SendMessageAsync(message);
    }
}

