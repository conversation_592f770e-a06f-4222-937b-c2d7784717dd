// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing application configuration
    /// </summary>
    public class ConfigurationManager : IConfigurationService
    {
        private readonly LoggingService _logger;
        private IConfiguration _configuration;
        private readonly string _configPath;

        /// <summary>
        /// Event raised when configuration is changed
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// Initializes a new instance of the ConfigurationManager class
        /// </summary>
        private ConfigurationManager(LoggingService logger, string configPath)
        {
            _logger = logger;
            _configPath = configPath;
            LoadConfiguration();
        }

        /// <summary>
        /// Loads the configuration from disk
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool LoadConfiguration()
        {
            try
            {
                _logger.Log("Loading configuration...", LogLevel.INFO);

                var builder = new ConfigurationBuilder()
                    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

                _configuration = builder.Build();
                _logger.Log("Configuration loaded successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Saves the configuration to disk
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool SaveConfiguration()
        {
            try
            {
                _logger.Log("Saving configuration...", LogLevel.INFO);

                string json = JsonConvert.SerializeObject(_configuration, Formatting.Indented);
                File.WriteAllText(_configPath, json);

                _logger.Log("Configuration saved successfully", LogLevel.SUCCESS);

                // Raise event
                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(_configuration));

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Creates a backup of the current configuration
        /// </summary>
        /// <param name="backupName">The name of the backup</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool CreateBackup(string backupName = null)
        {
            try
            {
                _logger.Log("Creating configuration backup...", LogLevel.INFO);

                // Generate backup name if not provided
                if (string.IsNullOrEmpty(backupName))
                {
                    backupName = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                }

                // Sanitize backup name
                backupName = string.Join("_", backupName.Split(Path.GetInvalidFileNameChars()));

                string backupFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups", $"{backupName}.json");

                // Save current configuration to backup file
                string json = JsonConvert.SerializeObject(_configuration, Formatting.Indented);
                File.WriteAllText(backupFilePath, json);

                _logger.Log($"Configuration backup created: {backupName}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating configuration backup: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restores a configuration from a backup
        /// </summary>
        /// <param name="backupName">The name of the backup</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool RestoreBackup(string backupName)
        {
            try
            {
                _logger.Log($"Restoring configuration from backup: {backupName}...", LogLevel.INFO);

                string backupFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups", $"{backupName}.json");

                if (!File.Exists(backupFilePath))
                {
                    _logger.Log($"Backup file not found: {backupName}", LogLevel.ERROR);
                    return false;
                }

                // Create a backup of the current configuration before restoring
                CreateBackup("BeforeRestore");

                // Load configuration from backup file
                string json = File.ReadAllText(backupFilePath);
                _configuration = JsonConvert.DeserializeObject<IConfiguration>(json);

                // Save the restored configuration
                SaveConfiguration();

                _logger.Log($"Configuration restored from backup: {backupName}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restoring configuration from backup: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a list of available backups
        /// </summary>
        /// <returns>A list of backup names</returns>
        public List<string> GetAvailableBackups()
        {
            try
            {
                List<string> backups = new List<string>();

                string[] backupFiles = Directory.GetFiles(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups"), "*.json");

                foreach (string file in backupFiles)
                {
                    string backupName = Path.GetFileNameWithoutExtension(file);
                    backups.Add(backupName);
                }

                return backups;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting available backups: {ex.Message}", LogLevel.ERROR);
                return new List<string>();
            }
        }

        /// <summary>
        /// Resets the configuration to default values
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool ResetConfiguration()
        {
            try
            {
                _logger.Log("Resetting configuration to defaults...", LogLevel.INFO);

                // Create a backup before resetting
                CreateBackup("BeforeReset");

                // Create default configuration
                CreateDefaultConfiguration();

                // Save the default configuration
                SaveConfiguration();

                _logger.Log("Configuration reset to defaults", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error resetting configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Exports the configuration to a file
        /// </summary>
        /// <param name="filePath">The file path to export to</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ExportConfiguration(string filePath)
        {
            try
            {
                _logger.Log($"Exporting configuration to: {filePath}...", LogLevel.INFO);

                string json = JsonConvert.SerializeObject(_configuration, Formatting.Indented);
                File.WriteAllText(filePath, json);

                _logger.Log($"Configuration exported to: {filePath}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error exporting configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Imports the configuration from a file
        /// </summary>
        /// <param name="filePath">The file path to import from</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ImportConfiguration(string filePath)
        {
            try
            {
                _logger.Log($"Importing configuration from: {filePath}...", LogLevel.INFO);

                if (!File.Exists(filePath))
                {
                    _logger.Log($"Import file not found: {filePath}", LogLevel.ERROR);
                    return false;
                }

                // Create a backup before importing
                CreateBackup("BeforeImport");

                // Load configuration from import file
                string json = File.ReadAllText(filePath);
                _configuration = JsonConvert.DeserializeObject<IConfiguration>(json);

                if (_configuration != null)
                {
                    SaveConfiguration();

                    _logger.Log($"Configuration imported from: {filePath}", LogLevel.SUCCESS);
                    return true;
                }
                else
                {
                    _logger.Log("Invalid configuration file", LogLevel.ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error importing configuration: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Updates a specific configuration setting
        /// </summary>
        /// <param name="section">The configuration section</param>
        /// <param name="key">The setting key</param>
        /// <param name="value">The setting value</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool UpdateSetting(string section, string key, object value)
        {
            try
            {
                _logger.Log($"Updating configuration setting: {section}.{key}...", LogLevel.INFO);

                // Update the setting based on the section
                switch (section.ToLower())
                {
                    case "general":
                        UpdateGeneralSetting(key, value);
                        break;
                    case "interface":
                        UpdateInterfaceSetting(key, value);
                        break;
                    case "performance":
                        UpdatePerformanceSetting(key, value);
                        break;
                    case "logging":
                        UpdateLoggingSetting(key, value);
                        break;
                    case "advanced":
                        UpdateAdvancedSetting(key, value);
                        break;
                    default:
                        _logger.Log($"Unknown configuration section: {section}", LogLevel.ERROR);
                        return false;
                }

                // Save the updated configuration
                SaveConfiguration();

                _logger.Log($"Configuration setting updated: {section}.{key}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating configuration setting: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a specific configuration setting
        /// </summary>
        /// <param name="section">The configuration section</param>
        /// <param name="key">The setting key</param>
        /// <returns>The setting value, or null if not found</returns>
        public object GetSetting(string section, string key)
        {
            try
            {
                // Get the setting based on the section
                switch (section.ToLower())
                {
                    case "general":
                        return GetGeneralSetting(key);
                    case "interface":
                        return GetInterfaceSetting(key);
                    case "performance":
                        return GetPerformanceSetting(key);
                    case "logging":
                        return GetLoggingSetting(key);
                    case "advanced":
                        return GetAdvancedSetting(key);
                    default:
                        _logger.Log($"Unknown configuration section: {section}", LogLevel.ERROR);
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting configuration setting: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private void UpdateGeneralSetting(string key, object value)
        {
            try
            {
                _logger.Log($"Updating general setting: {key}...", LogLevel.INFO);

                // Update the setting based on the key
                switch (key.ToLower())
                {
                    case "username":
                        _configuration["General:Username"] = value.ToString();
                        break;
                    case "startwithwindows":
                        _configuration["General:StartWithWindows"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "checkforupdatesatstartup":
                        _configuration["General:CheckForUpdatesAtStartup"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "minimizetotray":
                        _configuration["General:MinimizeToTray"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "confirmbeforeapplyingtweaks":
                        _configuration["General:ConfirmBeforeApplyingTweaks"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "showtipsatstartup":
                        _configuration["General:ShowTipsAtStartup"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "autoupdate":
                        _configuration["General:AutoUpdate"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "updatecheckinterval":
                        _configuration["General:UpdateCheckInterval"] = Convert.ToInt32(value).ToString();
                        break;
                    case "updateserverurl":
                        _configuration["General:UpdateServerUrl"] = value.ToString();
                        break;
                    case "lastupdatecheck":
                        // For DateTime?, it's trickier with IConfiguration indexer.
                        // Best to store as ISO 8601 string. Null if value is null.
                        _configuration["General:LastUpdateCheck"] = (value as DateTime?)?.ToString("o");
                        break;
                    default:
                        _logger.Log($"Unknown general setting: {key}", LogLevel.ERROR);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating general setting: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void UpdateInterfaceSetting(string key, object value)
        {
            try
            {
                _logger.Log($"Updating interface setting: {key}...", LogLevel.INFO);

                // Update the setting based on the key
                switch (key.ToLower())
                {
                    case "enabledarkmode":
                        _configuration["Interface:EnableDarkMode"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "animationspeedindex":
                        _configuration["Interface:AnimationSpeedIndex"] = Convert.ToInt32(value).ToString();
                        break;
                    case "fontsizeindex":
                        _configuration["Interface:FontSizeIndex"] = Convert.ToInt32(value).ToString();
                        break;
                    case "showtooltips":
                        _configuration["Interface:ShowTooltips"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "themeindex":
                        _configuration["Interface:ThemeIndex"] = Convert.ToInt32(value).ToString();
                        break;
                    default:
                        _logger.Log($"Unknown interface setting: {key}", LogLevel.ERROR);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating interface setting: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void UpdatePerformanceSetting(string key, object value)
        {
            try
            {
                _logger.Log($"Updating performance setting: {key}...", LogLevel.INFO);

                // Update the setting based on the key
                switch (key.ToLower())
                {
                    case "refreshintervalindex":
                        _configuration["Performance:RefreshIntervalIndex"] = Convert.ToInt32(value).ToString();
                        break;
                    case "enablehardwaremonitoring":
                        _configuration["Performance:EnableHardwareMonitoring"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enablebackgroundprocessing":
                        _configuration["Performance:EnableBackgroundProcessing"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enableautooptimization":
                        _configuration["Performance:EnableAutoOptimization"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "showfpscounter":
                        _configuration["Performance:ShowFpsCounter"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enablegamedetection":
                        _configuration["Performance:EnableGameDetection"] = Convert.ToBoolean(value).ToString();
                        break;
                    default:
                        _logger.Log($"Unknown performance setting: {key}", LogLevel.ERROR);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating performance setting: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void UpdateLoggingSetting(string key, object value)
        {
            try
            {
                _logger.Log($"Updating logging setting: {key}...", LogLevel.INFO);

                // Update the setting based on the key
                switch (key.ToLower())
                {
                    case "enablelogging":
                        _configuration["Logging:EnableLogging"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "loglevel":
                        _configuration["Logging:LogLevel:Default"] = value.ToString();
                        _configuration["Logging:LogLevel:Microsoft"] = value.ToString(); // Assuming these should also be updated if "LogLevel" is set
                        _configuration["Logging:LogLevel:MicrosoftHostingLifetime"] = value.ToString();
                        break;
                    case "maxlogfilesize":
                        _configuration["Logging:File:FileSizeLimitBytes"] = Convert.ToInt32(value).ToString();
                        break;
                    case "maxlogfiles":
                        _configuration["Logging:File:RetainedFileCountLimit"] = Convert.ToInt32(value).ToString();
                        break;
                    case "logfilepath":
                        _configuration["Logging:File:Path"] = value.ToString();
                        break;
                    default:
                        _logger.Log($"Unknown logging setting: {key}", LogLevel.ERROR);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating logging setting: {ex.Message}", LogLevel.ERROR);
            }
        }

        private void UpdateAdvancedSetting(string key, object value)
        {
            try
            {
                _logger.Log($"Updating advanced setting: {key}...", LogLevel.INFO);

                // Update the setting based on the key
                switch (key.ToLower())
                {
                    case "enabledebugmode":
                        _configuration["Advanced:EnableDebugMode"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enableexperimentalfeatures":
                        _configuration["Advanced:EnableExperimentalFeatures"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enableverboselogging":
                        _configuration["Advanced:EnableVerboseLogging"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enablecrashreporting":
                        _configuration["Advanced:EnableCrashReporting"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enabletelemetry":
                        _configuration["Advanced:EnableTelemetry"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "enableautobackup":
                        _configuration["Advanced:EnableAutoBackup"] = Convert.ToBoolean(value).ToString();
                        break;
                    case "backupinterval":
                        _configuration["Advanced:BackupInterval"] = Convert.ToInt32(value).ToString();
                        break;
                    case "maxbackups":
                        _configuration["Advanced:MaxBackups"] = Convert.ToInt32(value).ToString();
                        break;
                    case "backuplocation":
                        _configuration["Advanced:BackupLocation"] = value.ToString();
                        break;
                    default:
                        _logger.Log($"Unknown advanced setting: {key}", LogLevel.ERROR);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating advanced setting: {ex.Message}", LogLevel.ERROR);
            }
        }

        private object GetGeneralSetting(string key)
        {
            try
            {
                _logger.Log($"Getting general setting: {key}...", LogLevel.INFO);

                // Get the setting based on the key
                switch (key.ToLower())
                {
                    case "username":
                        return _configuration["General:Username"];
                    case "startwithwindows":
                        return _configuration["General:StartWithWindows"];
                    case "checkforupdatesatstartup":
                        return _configuration["General:CheckForUpdatesAtStartup"];
                    case "minimizetotray":
                        return _configuration["General:MinimizeToTray"];
                    case "confirmbeforeapplyingtweaks":
                        return _configuration["General:ConfirmBeforeApplyingTweaks"];
                    case "showtipsatstartup":
                        return _configuration["General:ShowTipsAtStartup"];
                    case "autoupdate":
                        return _configuration["General:AutoUpdate"];
                    case "updatecheckinterval":
                        return _configuration["General:UpdateCheckInterval"];
                    case "updateserverurl":
                        return _configuration["General:UpdateServerUrl"];
                    case "lastupdatecheck":
                        return _configuration["General:LastUpdateCheck"];
                    default:
                        _logger.Log($"Unknown general setting: {key}", LogLevel.ERROR);
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting general setting: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private object GetInterfaceSetting(string key)
        {
            try
            {
                _logger.Log($"Getting interface setting: {key}...", LogLevel.INFO);

                // Get the setting based on the key
                switch (key.ToLower())
                {
                    case "enabledarkmode":
                        return _configuration["Interface:EnableDarkMode"];
                    case "animationspeedindex":
                        return _configuration["Interface:AnimationSpeedIndex"];
                    case "fontsizeindex":
                        return _configuration["Interface:FontSizeIndex"];
                    case "showtooltips":
                        return _configuration["Interface:ShowTooltips"];
                    case "themeindex":
                        return _configuration["Interface:ThemeIndex"];
                    default:
                        _logger.Log($"Unknown interface setting: {key}", LogLevel.ERROR);
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting interface setting: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private object GetPerformanceSetting(string key)
        {
            try
            {
                _logger.Log($"Getting performance setting: {key}...", LogLevel.INFO);

                // Get the setting based on the key
                switch (key.ToLower())
                {
                    case "refreshintervalindex":
                        return _configuration["Performance:RefreshIntervalIndex"];
                    case "enablehardwaremonitoring":
                        return _configuration["Performance:EnableHardwareMonitoring"];
                    case "enablebackgroundprocessing":
                        return _configuration["Performance:EnableBackgroundProcessing"];
                    case "enableautooptimization":
                        return _configuration["Performance:EnableAutoOptimization"];
                    case "showfpscounter":
                        return _configuration["Performance:ShowFpsCounter"];
                    case "enablegamedetection":
                        return _configuration["Performance:EnableGameDetection"];
                    default:
                        _logger.Log($"Unknown performance setting: {key}", LogLevel.ERROR);
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting performance setting: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private object GetLoggingSetting(string key)
        {
            try
            {
                _logger.Log($"Getting logging setting: {key}...", LogLevel.INFO);

                // Get the setting based on the key
                switch (key.ToLower())
                {
                    case "enablelogging":
                        return _configuration["Logging:EnableLogging"];
                    case "loglevel":
                        return _configuration["Logging:LogLevel:Default"];
                    case "maxlogfilesize":
                        return _configuration["Logging:File:FileSizeLimitBytes"];
                    case "maxlogfiles":
                        return _configuration["Logging:File:RetainedFileCountLimit"];
                    case "logfilepath":
                        return _configuration["Logging:File:Path"];
                    default:
                        _logger.Log($"Unknown logging setting: {key}", LogLevel.ERROR);
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting logging setting: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private object GetAdvancedSetting(string key)
        {
            try
            {
                _logger.Log($"Getting advanced setting: {key}...", LogLevel.INFO);

                // Get the setting based on the key
                switch (key.ToLower())
                {
                    case "enabledebugmode":
                        return _configuration["Advanced:EnableDebugMode"];
                    case "enableexperimentalfeatures":
                        return _configuration["Advanced:EnableExperimentalFeatures"];
                    case "enableverboselogging":
                        return _configuration["Advanced:EnableVerboseLogging"];
                    case "enablecrashreporting":
                        return _configuration["Advanced:EnableCrashReporting"];
                    case "enabletelemetry":
                        return _configuration["Advanced:EnableTelemetry"];
                    case "enableautobackup":
                        return _configuration["Advanced:EnableAutoBackup"];
                    case "backupinterval":
                        return _configuration["Advanced:BackupInterval"];
                    case "maxbackups":
                        return _configuration["Advanced:MaxBackups"];
                    case "backuplocation":
                        return _configuration["Advanced:BackupLocation"];
                    default:
                        _logger.Log($"Unknown advanced setting: {key}", LogLevel.ERROR);
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting advanced setting: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        private void CreateDefaultConfiguration()
        {
            try
            {
                _logger.Log("Creating default configuration...", LogLevel.INFO);

                var defaultConfig = new
                {
                    Logging = new
                    {
                        LogLevel = new
                        {
                            Default = "Information",
                            Microsoft = "Warning",
                            MicrosoftHostingLifetime = "Information"
                        },
                        File = new
                        {
                            Path = "logs/app.log",
                            FileSizeLimitBytes = 10485760,
                            RetainedFileCountLimit = 10
                        }
                    },
                    // Add other default settings
                };

                string json = JsonConvert.SerializeObject(defaultConfig, Formatting.Indented);
                File.WriteAllText(_configPath, json);
                _logger.Log("Default configuration created", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating default configuration: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        // IConfigurationService implementation
        void IInitializableService.Initialize() => LoadConfiguration();
        T IConfigurationService.GetSetting<T>(string section, string key) => (T)GetSetting(section, key);
        bool IConfigurationService.UpdateSetting<T>(string section, string key, T value) => UpdateSetting(section, key, value);
        bool IConfigurationService.SaveConfiguration() => SaveConfiguration();
        bool IConfigurationService.LoadConfiguration() => LoadConfiguration();
    }

    /// <summary>
    /// Event arguments for configuration changes
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the new configuration
        /// </summary>
        public IConfiguration NewConfiguration { get; }

        /// <summary>
        /// Initializes a new instance of the ConfigurationChangedEventArgs class
        /// </summary>
        /// <param name="newConfiguration">The new configuration</param>
        public ConfigurationChangedEventArgs(IConfiguration newConfiguration)
        {
            NewConfiguration = newConfiguration;
        }
    }
}


