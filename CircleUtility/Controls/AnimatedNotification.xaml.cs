using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedNotification.xaml
    /// </summary>
    public partial class AnimatedNotification : UserControl
    {
        private Storyboard _showAnimation;
        private Storyboard _hideAnimation;
        private Storyboard _progressAnimation;
        private DispatcherTimer _autoCloseTimer;

        /// <summary>
        /// Title dependency property
        /// </summary>
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(AnimatedNotification), new PropertyMetadata(string.Empty));

        /// <summary>
        /// Message dependency property
        /// </summary>
        public static readonly DependencyProperty MessageProperty =
            DependencyProperty.Register("Message", typeof(string), typeof(AnimatedNotification), new PropertyMetadata(string.Empty));

        /// <summary>
        /// Type dependency property
        /// </summary>
        public static readonly DependencyProperty TypeProperty =
            DependencyProperty.Register("Type", typeof(NotificationType), typeof(AnimatedNotification),
                new PropertyMetadata(NotificationType.Info, OnTypeChanged));

        /// <summary>
        /// Auto close dependency property
        /// </summary>
        public static readonly DependencyProperty AutoCloseProperty =
            DependencyProperty.Register("AutoClose", typeof(bool), typeof(AnimatedNotification), new PropertyMetadata(true));

        /// <summary>
        /// Auto close time dependency property
        /// </summary>
        public static readonly DependencyProperty AutoCloseTimeProperty =
            DependencyProperty.Register("AutoCloseTime", typeof(double), typeof(AnimatedNotification), new PropertyMetadata(5.0));

        /// <summary>
        /// Closed event
        /// </summary>
        public event EventHandler Closed;

        /// <summary>
        /// Gets or sets the title
        /// </summary>
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        /// <summary>
        /// Gets or sets the message
        /// </summary>
        public string Message
        {
            get { return (string)GetValue(MessageProperty); }
            set { SetValue(MessageProperty, value); }
        }

        /// <summary>
        /// Gets or sets the type
        /// </summary>
        public NotificationType Type
        {
            get { return (NotificationType)GetValue(TypeProperty); }
            set { SetValue(TypeProperty, value); }
        }

        /// <summary>
        /// Gets or sets a value indicating whether to auto close
        /// </summary>
        public bool AutoClose
        {
            get { return (bool)GetValue(AutoCloseProperty); }
            set { SetValue(AutoCloseProperty, value); }
        }

        /// <summary>
        /// Gets or sets the auto close time in seconds
        /// </summary>
        public double AutoCloseTime
        {
            get { return (double)GetValue(AutoCloseTimeProperty); }
            set { SetValue(AutoCloseTimeProperty, value); }
        }

        /// <summary>
        /// Initializes a new instance of the AnimatedNotification class
        /// </summary>
        public AnimatedNotification()
        {
            InitializeComponent();

            // Create animations programmatically
            CreateAnimations();

            // Set up auto close timer
            _autoCloseTimer = new DispatcherTimer();
            _autoCloseTimer.Tick += AutoCloseTimer_Tick;

            // Handle hide animation completed
            _hideAnimation.Completed += (s, e) => Closed?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Creates the animations programmatically
        /// </summary>
        private void CreateAnimations()
        {
            // Create show animation
            _showAnimation = new Storyboard();

            // Margin animation
            ThicknessAnimation marginAnimation = new ThicknessAnimation
            {
                From = new Thickness(0, -80, 0, 0),
                To = new Thickness(0, 0, 0, 0),
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTarget(marginAnimation, MainBorder);
            Storyboard.SetTargetProperty(marginAnimation, new PropertyPath("Margin"));
            _showAnimation.Children.Add(marginAnimation);

            // Opacity animation
            DoubleAnimation opacityAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTarget(opacityAnimation, MainBorder);
            Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));
            _showAnimation.Children.Add(opacityAnimation);

            // Create hide animation
            _hideAnimation = new Storyboard();

            // Margin animation
            ThicknessAnimation hideMarginAnimation = new ThicknessAnimation
            {
                From = new Thickness(0, 0, 0, 0),
                To = new Thickness(0, -80, 0, 0),
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(hideMarginAnimation, MainBorder);
            Storyboard.SetTargetProperty(hideMarginAnimation, new PropertyPath("Margin"));
            _hideAnimation.Children.Add(hideMarginAnimation);

            // Opacity animation
            DoubleAnimation hideOpacityAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = new Duration(TimeSpan.FromSeconds(0.3)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(hideOpacityAnimation, MainBorder);
            Storyboard.SetTargetProperty(hideOpacityAnimation, new PropertyPath("Opacity"));
            _hideAnimation.Children.Add(hideOpacityAnimation);

            // Create progress animation
            _progressAnimation = new Storyboard();

            // Width animation
            DoubleAnimation widthAnimation = new DoubleAnimation
            {
                To = 0,
                Duration = new Duration(TimeSpan.FromSeconds(5)),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(widthAnimation, ProgressBar);
            Storyboard.SetTargetProperty(widthAnimation, new PropertyPath("Width"));
            _progressAnimation.Children.Add(widthAnimation);
        }

        /// <summary>
        /// Handles the loaded event
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Update appearance based on type
            UpdateAppearance();

            // Show the notification
            _showAnimation.Begin(this);

            // Start auto close timer if enabled
            if (AutoClose)
            {
                // Set up progress animation
                Duration duration = new Duration(TimeSpan.FromSeconds(AutoCloseTime));
                foreach (Timeline timeline in _progressAnimation.Children)
                {
                    timeline.Duration = duration;

                    // Set the From property for the width animation
                    if (timeline is DoubleAnimation widthAnimation)
                    {
                        widthAnimation.From = MainBorder.ActualWidth;
                    }
                }

                // Start progress animation
                _progressAnimation.Begin(this, true);

                // Start auto close timer
                _autoCloseTimer.Interval = TimeSpan.FromSeconds(AutoCloseTime);
                _autoCloseTimer.Start();
            }
        }

        /// <summary>
        /// Handles the auto close timer tick
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void AutoCloseTimer_Tick(object sender, EventArgs e)
        {
            _autoCloseTimer.Stop();
            Close();
        }

        /// <summary>
        /// Handles the close button click
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// Closes the notification
        /// </summary>
        public void Close()
        {
            // Stop auto close timer
            _autoCloseTimer.Stop();

            // Stop progress animation
            _progressAnimation.Stop(this);

            // Hide the notification
            _hideAnimation.Begin(this);
        }

        /// <summary>
        /// Updates the appearance based on the type
        /// </summary>
        private void UpdateAppearance()
        {
            Color color;
            string iconData;

            switch (Type)
            {
                case NotificationType.Success:
                    color = Color.FromRgb(0, 200, 83); // Green
                    iconData = "M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"; // Checkmark
                    break;
                case NotificationType.Warning:
                    color = Color.FromRgb(255, 193, 7); // Yellow
                    iconData = "M1,21h22L12,2L1,21z M13,18h-2v-2h2V18z M13,14h-2V9h2V14z"; // Warning triangle
                    break;
                case NotificationType.Error:
                    color = Color.FromRgb(255, 80, 80); // Red
                    iconData = "M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41z"; // X
                    break;
                case NotificationType.Info:
                default:
                    color = Color.FromRgb(0, 200, 255); // Blue
                    iconData = "M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,17h-2v-6h2V17z M13,9h-2V7h2V9z"; // Info
                    break;
            }

            // Update colors
            NotificationBorderBrush.Color = color;
            GlowEffect.Color = color;
            TitleText.Foreground = new SolidColorBrush(color);
            ProgressBar.Background = new SolidColorBrush(color);

            // Update icon
            NotificationIcon.Fill = new SolidColorBrush(color);
            NotificationIcon.Data = Geometry.Parse(iconData);
        }

        /// <summary>
        /// Handles the type property changed event
        /// </summary>
        /// <param name="d">The dependency object</param>
        /// <param name="e">The event args</param>
        private static void OnTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            AnimatedNotification notification = (AnimatedNotification)d;
            notification.UpdateAppearance();
        }
    }

    /// <summary>
    /// Notification type
    /// </summary>
    public enum NotificationType
    {
        /// <summary>
        /// Information notification
        /// </summary>
        Info,

        /// <summary>
        /// Success notification
        /// </summary>
        Success,

        /// <summary>
        /// Warning notification
        /// </summary>
        Warning,

        /// <summary>
        /// Error notification
        /// </summary>
        Error
    }
}
