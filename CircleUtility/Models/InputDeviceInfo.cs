// Created by Arsenal on 5-17-25 12:15PM
using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for input device information
    /// </summary>
    public class InputDeviceInfo
    {
        /// <summary>
        /// Gets or sets the device ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// Gets or sets the device name
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// Gets or sets the device type
        /// </summary>
        public InputDeviceType DeviceType { get; set; }

        /// <summary>
        /// Gets or sets the manufacturer
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the connection type (Wired, Wireless, Bluetooth)
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        /// Gets or sets the polling rate in Hz
        /// </summary>
        public int PollingRate { get; set; }

        /// <summary>
        /// Gets or sets the DPI (for mice)
        /// </summary>
        public int DPI { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the device is connected
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// Gets or sets the firmware version
        /// </summary>
        public string FirmwareVersion { get; set; }

        /// <summary>
        /// Gets or sets the driver version
        /// </summary>
        public string DriverVersion { get; set; }

        /// <summary>
        /// Gets or sets the battery level (0-100, for wireless devices)
        /// </summary>
        public int BatteryLevel { get; set; }

        /// <summary>
        /// Gets or sets the response time in milliseconds
        /// </summary>
        public double ResponseTime { get; set; }

        /// <summary>
        /// Gets or sets the debounce time in milliseconds (for mice and keyboards)
        /// </summary>
        public double DebounceTime { get; set; }

        /// <summary>
        /// Gets or sets the lift-off distance in millimeters (for mice)
        /// </summary>
        public double LiftOffDistance { get; set; }

        /// <summary>
        /// Gets or sets the angle snapping status (for mice)
        /// </summary>
        public bool AngleSnapping { get; set; }

        /// <summary>
        /// Gets or sets the acceleration status (for mice)
        /// </summary>
        public bool Acceleration { get; set; }

        /// <summary>
        /// Gets or sets the USB port
        /// </summary>
        public string UsbPort { get; set; }

        /// <summary>
        /// Gets or sets the last connected date
        /// </summary>
        public DateTime? LastConnected { get; set; }

        /// <summary>
        /// Gets or sets the device status
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets the formatted polling rate
        /// </summary>
        public string FormattedPollingRate => $"{PollingRate} Hz";

        /// <summary>
        /// Gets the formatted DPI
        /// </summary>
        public string FormattedDPI => DeviceType == InputDeviceType.Mouse ? $"{DPI} DPI" : "N/A";

        /// <summary>
        /// Gets the formatted battery level
        /// </summary>
        public string FormattedBatteryLevel
        {
            get
            {
                if (ConnectionType.ToLower() == "wired")
                {
                    return "N/A";
                }
                return $"{BatteryLevel}%";
            }
        }

        /// <summary>
        /// Gets the formatted response time
        /// </summary>
        public string FormattedResponseTime => $"{ResponseTime:F2} ms";

        /// <summary>
        /// Gets the formatted device type
        /// </summary>
        public string FormattedDeviceType
        {
            get
            {
                switch (DeviceType)
                {
                    case InputDeviceType.Mouse:
                        return "Mouse";
                    case InputDeviceType.Keyboard:
                        return "Keyboard";
                    case InputDeviceType.Controller:
                        return "Controller";
                    case InputDeviceType.Touchpad:
                        return "Touchpad";
                    case InputDeviceType.Tablet:
                        return "Tablet";
                    case InputDeviceType.Other:
                    default:
                        return "Other";
                }
            }
        }

        /// <summary>
        /// Gets the formatted connection type with icon
        /// </summary>
        public string FormattedConnectionType
        {
            get
            {
                switch (ConnectionType.ToLower())
                {
                    case "wired":
                        return "🔌 Wired";
                    case "wireless":
                        return "📡 Wireless";
                    case "bluetooth":
                        return "🔷 Bluetooth";
                    default:
                        return ConnectionType;
                }
            }
        }

        /// <summary>
        /// Gets the formatted status with icon
        /// </summary>
        public string FormattedStatus
        {
            get
            {
                if (IsConnected)
                {
                    return "✅ Connected";
                }
                return "❌ Disconnected";
            }
        }

        /// <summary>
        /// Gets the status color code
        /// </summary>
        public string StatusColorCode
        {
            get
            {
                if (IsConnected)
                {
                    if (ConnectionType.ToLower() == "wireless" || ConnectionType.ToLower() == "bluetooth")
                    {
                        if (BatteryLevel <= 20)
                        {
                            return "#FF3232"; // Red
                        }
                        else if (BatteryLevel <= 50)
                        {
                            return "#FFFF00"; // Yellow
                        }
                        else
                        {
                            return "#00FF00"; // Green
                        }
                    }
                    else
                    {
                        return "#00FF00"; // Green
                    }
                }
                else
                {
                    return "#FF3232"; // Red
                }
            }
        }

        /// <summary>
        /// Gets the polling rate color code
        /// </summary>
        public string PollingRateColorCode
        {
            get
            {
                if (PollingRate >= 1000)
                {
                    return "#00FF00"; // Green
                }
                else if (PollingRate >= 500)
                {
                    return "#FFFF00"; // Yellow
                }
                else
                {
                    return "#FF3232"; // Red
                }
            }
        }

        /// <summary>
        /// Gets the response time color code
        /// </summary>
        public string ResponseTimeColorCode
        {
            get
            {
                if (ResponseTime <= 1)
                {
                    return "#00FF00"; // Green
                }
                else if (ResponseTime <= 2)
                {
                    return "#FFFF00"; // Yellow
                }
                else
                {
                    return "#FF3232"; // Red
                }
            }
        }

        /// <summary>
        /// Gets the optimization recommendation
        /// </summary>
        public string OptimizationRecommendation
        {
            get
            {
                switch (DeviceType)
                {
                    case InputDeviceType.Mouse:
                        if (PollingRate < 1000)
                        {
                            return "Increase polling rate to 1000Hz";
                        }
                        if (Acceleration)
                        {
                            return "Disable mouse acceleration";
                        }
                        if (AngleSnapping)
                        {
                            return "Disable angle snapping";
                        }
                        if (DebounceTime > 10)
                        {
                            return "Reduce debounce time";
                        }
                        return "No optimization needed";

                    case InputDeviceType.Keyboard:
                        if (PollingRate < 1000)
                        {
                            return "Increase polling rate to 1000Hz";
                        }
                        if (DebounceTime > 5)
                        {
                            return "Reduce debounce time";
                        }
                        return "No optimization needed";

                    case InputDeviceType.Controller:
                        if (PollingRate < 1000)
                        {
                            return "Increase polling rate if supported";
                        }
                        if (ConnectionType.ToLower() == "wireless" || ConnectionType.ToLower() == "bluetooth")
                        {
                            return "Consider using wired connection for lowest latency";
                        }
                        return "No optimization needed";

                    default:
                        return "No specific optimization available";
                }
            }
        }
    }

    /// <summary>
    /// Enum for input device types
    /// </summary>
    public enum InputDeviceType
    {
        /// <summary>
        /// Mouse
        /// </summary>
        Mouse,

        /// <summary>
        /// Keyboard
        /// </summary>
        Keyboard,

        /// <summary>
        /// Controller
        /// </summary>
        Controller,

        /// <summary>
        /// Touchpad
        /// </summary>
        Touchpad,

        /// <summary>
        /// Tablet
        /// </summary>
        Tablet,

        /// <summary>
        /// Other input device
        /// </summary>
        Other
    }
}
