// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for hardware-specific optimizations
    /// </summary>
    public class HardwareSpecificOptimization
    {
        /// <summary>
        /// Gets or sets the name of the optimization
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the optimization
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the category of the optimization
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the hardware type this optimization applies to
        /// </summary>
        public HardwareType HardwareType { get; set; }

        /// <summary>
        /// Gets or sets the manufacturer this optimization applies to
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the model pattern this optimization applies to (supports wildcards)
        /// </summary>
        public string ModelPattern { get; set; }

        /// <summary>
        /// Gets or sets the specific models this optimization applies to
        /// </summary>
        public List<string> SpecificModels { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the required hardware for this optimization
        /// </summary>
        public List<HardwareRequirement> RequiredHardware { get; set; } = new List<HardwareRequirement>();

        /// <summary>
        /// Gets or sets the minimum driver version this optimization applies to
        /// </summary>
        public Version MinDriverVersion { get; set; }

        /// <summary>
        /// Gets or sets the maximum driver version this optimization applies to
        /// </summary>
        public Version MaxDriverVersion { get; set; }

        /// <summary>
        /// Gets or sets the implementation method
        /// </summary>
        public OptimizationMethod Method { get; set; }

        /// <summary>
        /// Gets or sets the registry key for registry-based optimizations
        /// </summary>
        public string RegistryKey { get; set; }

        /// <summary>
        /// Gets or sets the registry value name for registry-based optimizations
        /// </summary>
        public string RegistryValueName { get; set; }

        /// <summary>
        /// Gets or sets the registry value for registry-based optimizations
        /// </summary>
        public object RegistryValue { get; set; }

        /// <summary>
        /// Gets or sets the registry value kind for registry-based optimizations
        /// </summary>
        public string RegistryValueKind { get; set; }

        /// <summary>
        /// Gets or sets the command for command-based optimizations
        /// </summary>
        public string Command { get; set; }

        /// <summary>
        /// Gets or sets the arguments for command-based optimizations
        /// </summary>
        public string Arguments { get; set; }

        /// <summary>
        /// Gets or sets the file path for file-based optimizations
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// Gets or sets the file content for file-based optimizations
        /// </summary>
        public string FileContent { get; set; }

        /// <summary>
        /// Gets or sets the API call for API-based optimizations
        /// </summary>
        public string ApiCall { get; set; }

        /// <summary>
        /// Gets or sets the API parameters for API-based optimizations
        /// </summary>
        public Dictionary<string, object> ApiParameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Gets or sets the performance impact rating (0-100)
        /// </summary>
        public int PerformanceImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the stability impact rating (0-100, higher is more stable)
        /// </summary>
        public int StabilityImpact { get; set; } = 100;

        /// <summary>
        /// Gets or sets the power consumption impact rating (0-100, higher is more power)
        /// </summary>
        public int PowerConsumptionImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the thermal impact rating (0-100, higher is hotter)
        /// </summary>
        public int ThermalImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the input latency impact rating (0-100, higher is better reduction)
        /// </summary>
        public int InputLatencyImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the risk level of the optimization
        /// </summary>
        public RiskLevel Risk { get; set; } = RiskLevel.Safe;

        /// <summary>
        /// Gets or sets the warning message for risky optimizations
        /// </summary>
        public string WarningMessage { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this optimization requires a restart
        /// </summary>
        public bool RequiresRestart { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this optimization requires admin privileges
        /// </summary>
        public bool RequiresAdmin { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this optimization is enabled by default
        /// </summary>
        public bool EnabledByDefault { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether this optimization is currently enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether this optimization is advanced
        /// </summary>
        public bool IsAdvanced { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this optimization is experimental
        /// </summary>
        public bool IsExperimental { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this optimization is recommended
        /// </summary>
        public bool IsRecommended { get; set; } = true;

        /// <summary>
        /// Gets or sets the testing status of the optimization
        /// </summary>
        public TestingStatus TestingStatus { get; set; } = TestingStatus.Limited;

        /// <summary>
        /// Gets or sets the author of the optimization
        /// </summary>
        public string Author { get; set; } = "Circle Utility";

        /// <summary>
        /// Gets or sets the version of the optimization
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// Gets or sets the date the optimization was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets or sets the date the optimization was last modified
        /// </summary>
        public DateTime LastModifiedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets or sets the date the optimization was last applied
        /// </summary>
        public DateTime? LastAppliedDate { get; set; }

        /// <summary>
        /// Gets or sets the user notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets or sets the revert command for command-based optimizations
        /// </summary>
        public string RevertCommand { get; set; }

        /// <summary>
        /// Gets or sets the revert arguments for command-based optimizations
        /// </summary>
        public string RevertArguments { get; set; }

        /// <summary>
        /// Gets or sets the revert registry value for registry-based optimizations
        /// </summary>
        public object RevertRegistryValue { get; set; }

        /// <summary>
        /// Gets or sets the revert file content for file-based optimizations
        /// </summary>
        public string RevertFileContent { get; set; }

        /// <summary>
        /// Gets or sets the revert API parameters for API-based optimizations
        /// </summary>
        public Dictionary<string, object> RevertApiParameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Gets the formatted creation date
        /// </summary>
        [JsonIgnore]
        public string FormattedCreatedDate => CreatedDate.ToString("MMM dd, yyyy");

        /// <summary>
        /// Gets the formatted last modified date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastModifiedDate => LastModifiedDate.ToString("MMM dd, yyyy");

        /// <summary>
        /// Gets the formatted last applied date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastAppliedDate => LastAppliedDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the performance impact description
        /// </summary>
        [JsonIgnore]
        public string PerformanceImpactDescription
        {
            get
            {
                if (PerformanceImpact >= 80) return "Extreme Performance Boost";
                if (PerformanceImpact >= 60) return "High Performance Boost";
                if (PerformanceImpact >= 40) return "Moderate Performance Boost";
                if (PerformanceImpact >= 20) return "Slight Performance Boost";
                return "Minimal Performance Impact";
            }
        }

        /// <summary>
        /// Gets the stability impact description
        /// </summary>
        [JsonIgnore]
        public string StabilityImpactDescription
        {
            get
            {
                if (StabilityImpact >= 90) return "No Stability Impact";
                if (StabilityImpact >= 70) return "Minimal Stability Impact";
                if (StabilityImpact >= 50) return "Moderate Stability Impact";
                if (StabilityImpact >= 30) return "Significant Stability Risk";
                return "High Stability Risk";
            }
        }

        /// <summary>
        /// Gets the power consumption impact description
        /// </summary>
        [JsonIgnore]
        public string PowerConsumptionImpactDescription
        {
            get
            {
                if (PowerConsumptionImpact >= 80) return "Significantly Higher Power Usage";
                if (PowerConsumptionImpact >= 60) return "Higher Power Usage";
                if (PowerConsumptionImpact >= 40) return "Moderate Power Usage";
                if (PowerConsumptionImpact >= 20) return "Efficient Power Usage";
                return "Very Efficient Power Usage";
            }
        }

        /// <summary>
        /// Gets the thermal impact description
        /// </summary>
        [JsonIgnore]
        public string ThermalImpactDescription
        {
            get
            {
                if (ThermalImpact >= 80) return "Significantly Higher Temperatures";
                if (ThermalImpact >= 60) return "Higher Temperatures";
                if (ThermalImpact >= 40) return "Moderate Temperature Impact";
                if (ThermalImpact >= 20) return "Low Temperature Impact";
                return "Minimal Temperature Impact";
            }
        }

        /// <summary>
        /// Gets the input latency impact description
        /// </summary>
        [JsonIgnore]
        public string InputLatencyImpactDescription
        {
            get
            {
                if (InputLatencyImpact >= 80) return "Extreme Latency Reduction";
                if (InputLatencyImpact >= 60) return "High Latency Reduction";
                if (InputLatencyImpact >= 40) return "Moderate Latency Reduction";
                if (InputLatencyImpact >= 20) return "Slight Latency Reduction";
                return "Minimal Latency Impact";
            }
        }

        /// <summary>
        /// Gets the risk level description
        /// </summary>
        [JsonIgnore]
        public string RiskLevelDescription
        {
            get
            {
                switch (Risk)
                {
                    case RiskLevel.Safe:
                        return "Safe - No risk to hardware or system stability";
                    case RiskLevel.Low:
                        return "Low Risk - Minimal potential for system instability";
                    case RiskLevel.Medium:
                        return "Medium Risk - May cause instability in some systems";
                    case RiskLevel.High:
                        return "High Risk - Can cause system instability or reduced hardware lifespan";
                    case RiskLevel.VeryHigh:
                        return "Very High Risk - May cause hardware damage or system failure";
                    default:
                        return "Unknown Risk";
                }
            }
        }

        /// <summary>
        /// Gets the risk level color
        /// </summary>
        [JsonIgnore]
        public string RiskLevelColor
        {
            get
            {
                switch (Risk)
                {
                    case RiskLevel.Safe:
                        return "#00FF00"; // Green
                    case RiskLevel.Low:
                        return "#AAFF00"; // Light Green
                    case RiskLevel.Medium:
                        return "#FFFF00"; // Yellow
                    case RiskLevel.High:
                        return "#FF8800"; // Orange
                    case RiskLevel.VeryHigh:
                        return "#FF0000"; // Red
                    default:
                        return "#FFFFFF"; // White
                }
            }
        }
    }

    /// <summary>
    /// Enum for optimization methods
    /// </summary>
    public enum OptimizationMethod
    {
        /// <summary>
        /// Registry-based optimization
        /// </summary>
        Registry,

        /// <summary>
        /// Command-based optimization
        /// </summary>
        Command,

        /// <summary>
        /// File-based optimization
        /// </summary>
        File,

        /// <summary>
        /// API-based optimization
        /// </summary>
        Api,

        /// <summary>
        /// Multiple methods
        /// </summary>
        Multiple
    }

    /// <summary>
    /// Enum for risk levels
    /// </summary>
    public enum RiskLevel
    {
        /// <summary>
        /// Safe - No risk to hardware or system stability
        /// </summary>
        Safe,

        /// <summary>
        /// Low Risk - Minimal potential for system instability
        /// </summary>
        Low,

        /// <summary>
        /// Medium Risk - May cause instability in some systems
        /// </summary>
        Medium,

        /// <summary>
        /// High Risk - Can cause system instability or reduced hardware lifespan
        /// </summary>
        High,

        /// <summary>
        /// Very High Risk - May cause hardware damage or system failure
        /// </summary>
        VeryHigh
    }
}
