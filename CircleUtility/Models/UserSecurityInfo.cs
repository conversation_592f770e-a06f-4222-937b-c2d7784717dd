// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for user security information
    /// </summary>
    public class UserSecurityInfo
    {
        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Gets or sets the password hash
        /// </summary>
        public string PasswordHash { get; set; }

        /// <summary>
        /// Gets or sets the salt
        /// </summary>
        public string Salt { get; set; }

        /// <summary>
        /// Gets or sets the security level
        /// </summary>
        public int SecurityLevel { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is an admin
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the created date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the last login date
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// Gets or sets the failed login attempts
        /// </summary>
        public int FailedLoginAttempts { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is locked
        /// </summary>
        public bool IsLocked { get; set; }

        /// <summary>
        /// Gets or sets the password expiration date
        /// </summary>
        public DateTime? PasswordExpirationDate { get; set; }

        /// <summary>
        /// Gets or sets the last password change date
        /// </summary>
        public DateTime? LastPasswordChangeDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a password change is required
        /// </summary>
        public bool PasswordChangeRequired { get; set; }

        /// <summary>
        /// Gets or sets the user's email
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the email is verified
        /// </summary>
        public bool IsEmailVerified { get; set; }

        /// <summary>
        /// Gets or sets the user's phone number
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the phone number is verified
        /// </summary>
        public bool IsPhoneVerified { get; set; }

        /// <summary>
        /// Gets or sets the two-factor authentication type
        /// </summary>
        public TwoFactorType TwoFactorType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether two-factor authentication is enabled
        /// </summary>
        public bool IsTwoFactorEnabled { get; set; }

        /// <summary>
        /// Gets or sets the two-factor recovery codes
        /// </summary>
        public string[] RecoveryCodes { get; set; }

        /// <summary>
        /// Gets or sets the user's roles
        /// </summary>
        public string[] Roles { get; set; }

        /// <summary>
        /// Gets or sets the user's permissions
        /// </summary>
        public string[] Permissions { get; set; }

        /// <summary>
        /// Gets or sets the last IP address
        /// </summary>
        public string LastIpAddress { get; set; }

        /// <summary>
        /// Gets or sets the last user agent
        /// </summary>
        public string LastUserAgent { get; set; }

        /// <summary>
        /// Gets or sets the security questions
        /// </summary>
        public SecurityQuestion[] SecurityQuestions { get; set; }

        /// <summary>
        /// Gets or sets the account lockout end date
        /// </summary>
        public DateTime? LockoutEndDate { get; set; }

        /// <summary>
        /// Gets or sets the access token
        /// </summary>
        public string AccessToken { get; set; }

        /// <summary>
        /// Gets or sets the refresh token
        /// </summary>
        public string RefreshToken { get; set; }

        /// <summary>
        /// Gets or sets the token expiration date
        /// </summary>
        public DateTime? TokenExpirationDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is a super admin (Arsenal account)
        /// </summary>
        public bool IsSuperAdmin { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user cannot be deleted
        /// </summary>
        public bool CannotBeDeleted { get; set; }

        /// <summary>
        /// Gets or sets the hardware ID associated with this user
        /// </summary>
        public string HardwareId { get; set; }

        /// <summary>
        /// Gets or sets a list of authorized hardware IDs for this user
        /// </summary>
        public List<string> AuthorizedHardwareIds { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets a list of authorized IP addresses for this user
        /// </summary>
        public List<string> AuthorizedIpAddresses { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the punishment history for this user
        /// </summary>
        public List<UserPunishment> PunishmentHistory { get; set; } = new List<UserPunishment>();

        /// <summary>
        /// Gets or sets the user's notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets the formatted created date
        /// </summary>
        public string FormattedCreatedDate => CreatedDate.ToString("MMM dd, yyyy HH:mm:ss");

        /// <summary>
        /// Gets the formatted last login date
        /// </summary>
        public string FormattedLastLoginDate => LastLoginDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the formatted last password change date
        /// </summary>
        public string FormattedLastPasswordChangeDate => LastPasswordChangeDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the formatted password expiration date
        /// </summary>
        public string FormattedPasswordExpirationDate => PasswordExpirationDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the formatted lockout end date
        /// </summary>
        public string FormattedLockoutEndDate => LockoutEndDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the formatted token expiration date
        /// </summary>
        public string FormattedTokenExpirationDate => TokenExpirationDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the security level description
        /// </summary>
        public string SecurityLevelDescription
        {
            get
            {
                switch (SecurityLevel)
                {
                    case 1:
                        return "Basic";
                    case 2:
                        return "Advanced";
                    case 3:
                        return "Expert";
                    default:
                        return "Unknown";
                }
            }
        }

        /// <summary>
        /// Gets the status description
        /// </summary>
        public string StatusDescription
        {
            get
            {
                if (IsLocked)
                {
                    return "Locked";
                }
                else if (!IsEnabled)
                {
                    return "Disabled";
                }
                else
                {
                    return "Active";
                }
            }
        }

        /// <summary>
        /// Gets the status color
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (IsLocked)
                {
                    return "#FF0000"; // Red
                }
                else if (!IsEnabled)
                {
                    return "#FFA500"; // Orange
                }
                else
                {
                    return "#00FF00"; // Green
                }
            }
        }

        /// <summary>
        /// Gets the role description
        /// </summary>
        public string RoleDescription
        {
            get
            {
                if (IsAdmin)
                {
                    return "Administrator";
                }
                else if (SecurityLevel == 3)
                {
                    return "Expert User";
                }
                else if (SecurityLevel == 2)
                {
                    return "Advanced User";
                }
                else
                {
                    return "Basic User";
                }
            }
        }
    }

    /// <summary>
    /// Model for security questions
    /// </summary>
    public class SecurityQuestion
    {
        /// <summary>
        /// Gets or sets the question
        /// </summary>
        public string Question { get; set; }

        /// <summary>
        /// Gets or sets the answer hash
        /// </summary>
        public string AnswerHash { get; set; }

        /// <summary>
        /// Gets or sets the salt
        /// </summary>
        public string Salt { get; set; }
    }

    // TwoFactorType enum is defined elsewhere
}
