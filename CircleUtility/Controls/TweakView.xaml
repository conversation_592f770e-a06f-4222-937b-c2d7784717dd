<UserControl x:Class="CircleUtility.Controls.TweakView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="400">
    
    <UserControl.Resources>
        <Style x:Key="TweakTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
        
        <Style x:Key="TweakDescriptionStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
        
        <Style x:Key="TweakButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,5,5,0"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </UserControl.Resources>
    
    <Border Background="#FF050A0F" BorderBrush="#FF00C8FF" BorderThickness="1" Padding="10">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock x:Name="TitleTextBlock" 
                               Grid.Column="0"
                               Style="{StaticResource TweakTitleStyle}" 
                               Text="{Binding Title}"/>
                    
                    <!-- Recommendation Badge -->
                    <local:RecommendedBadgeControl x:Name="RecommendationBadge" 
                                                  Grid.Column="1"
                                                  Visibility="Collapsed"
                                                  Margin="5,0,0,0"
                                                  RecommendationReason="{Binding RecommendationReason}"
                                                  RecommendationScore="{Binding RecommendationScore}"/>
                </Grid>
                
                <TextBlock x:Name="DescriptionTextBlock" 
                           Style="{StaticResource TweakDescriptionStyle}" 
                           Text="{Binding Description}"/>
                
                <TextBlock x:Name="ImpactTextBlock" 
                           Style="{StaticResource TweakDescriptionStyle}" 
                           Text="{Binding ImpactText}"
                           Foreground="#FF00C8FF"/>
            </StackPanel>
            
            <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Bottom" HorizontalAlignment="Right">
                <Button x:Name="ApplyButton" 
                        Style="{StaticResource TweakButtonStyle}" 
                        Content="Apply" 
                        Click="ApplyButton_Click"/>
                
                <Button x:Name="RevertButton" 
                        Style="{StaticResource TweakButtonStyle}" 
                        Content="Revert" 
                        Click="RevertButton_Click"
                        BorderBrush="#FFFF4040"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
