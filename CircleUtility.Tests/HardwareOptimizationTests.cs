// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using CircleUtility.Models;
using CircleUtility.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace CircleUtility.Tests
{
    [TestClass]
    public class HardwareOptimizationTests
    {
        private string _testDirectory = null!;
        private string _optimizationsDirectory = null!;
        private string _hardwareDirectory = null!;
        private string _powerDirectory = null!;

        [TestInitialize]
        public void TestInitialize()
        {
            // Create test directories
            _testDirectory = Path.Combine(Path.GetTempPath(), "CircleUtilityTests", Guid.NewGuid().ToString());
            _optimizationsDirectory = Path.Combine(_testDirectory, "Optimizations");
            _hardwareDirectory = Path.Combine(_optimizationsDirectory, "hardware");
            _powerDirectory = Path.Combine(_optimizationsDirectory, "power");

            Directory.CreateDirectory(_testDirectory);
            Directory.CreateDirectory(_optimizationsDirectory);
            Directory.CreateDirectory(_hardwareDirectory);
            Directory.CreateDirectory(_powerDirectory);

            // Create test files
            CreateTestOptimizations();
            CreateTestPowerProfiles();
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Clean up test directories
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        [TestMethod]
        public void HardwareSpecificOptimization_Properties_ShouldWork()
        {
            // Arrange
            var optimization = new HardwareSpecificOptimization
            {
                Name = "Test Optimization",
                Description = "Test Description",
                Category = "Test Category",
                HardwareType = HardwareType.GPU,
                Manufacturer = "NVIDIA",
                ModelPattern = "RTX.*",
                SpecificModels = new List<string> { "RTX 3080", "RTX 3090" },
                Method = OptimizationMethod.Registry,
                RegistryKey = "HKEY_LOCAL_MACHINE\\Test",
                RegistryValueName = "TestValue",
                RegistryValue = 1,
                RegistryValueKind = "DWord",
                PerformanceImpact = 80,
                StabilityImpact = 90,
                PowerConsumptionImpact = 70,
                ThermalImpact = 60,
                InputLatencyImpact = 85,
                Risk = RiskLevel.Low,
                WarningMessage = "Test Warning",
                RequiresRestart = true,
                RequiresAdmin = true,
                EnabledByDefault = true,
                IsEnabled = true,
                IsAdvanced = false,
                IsExperimental = false,
                Author = "Test Author",
                Version = "1.0",
                CreatedDate = new DateTime(2025, 5, 17),
                LastModifiedDate = new DateTime(2025, 5, 17),
                LastAppliedDate = new DateTime(2025, 5, 17),
                Notes = "Test Notes",
                RevertRegistryValue = 0
            };

            // Act & Assert
            Assert.AreEqual("Test Optimization", optimization.Name);
            Assert.AreEqual("Test Description", optimization.Description);
            Assert.AreEqual("Test Category", optimization.Category);
            Assert.AreEqual(HardwareType.GPU, optimization.HardwareType);
            Assert.AreEqual("NVIDIA", optimization.Manufacturer);
            Assert.AreEqual("RTX.*", optimization.ModelPattern);
            Assert.AreEqual(2, optimization.SpecificModels.Count);
            Assert.AreEqual("RTX 3080", optimization.SpecificModels[0]);
            Assert.AreEqual("RTX 3090", optimization.SpecificModels[1]);
            Assert.AreEqual(OptimizationMethod.Registry, optimization.Method);
            Assert.AreEqual("HKEY_LOCAL_MACHINE\\Test", optimization.RegistryKey);
            Assert.AreEqual("TestValue", optimization.RegistryValueName);
            Assert.AreEqual(1, optimization.RegistryValue);
            Assert.AreEqual("DWord", optimization.RegistryValueKind);
            Assert.AreEqual(80, optimization.PerformanceImpact);
            Assert.AreEqual(90, optimization.StabilityImpact);
            Assert.AreEqual(70, optimization.PowerConsumptionImpact);
            Assert.AreEqual(60, optimization.ThermalImpact);
            Assert.AreEqual(85, optimization.InputLatencyImpact);
            Assert.AreEqual(RiskLevel.Low, optimization.Risk);
            Assert.AreEqual("Test Warning", optimization.WarningMessage);
            Assert.IsTrue(optimization.RequiresRestart);
            Assert.IsTrue(optimization.RequiresAdmin);
            Assert.IsTrue(optimization.EnabledByDefault);
            Assert.IsTrue(optimization.IsEnabled);
            Assert.IsFalse(optimization.IsAdvanced);
            Assert.IsFalse(optimization.IsExperimental);
            Assert.AreEqual("Test Author", optimization.Author);
            Assert.AreEqual("1.0", optimization.Version);
            Assert.AreEqual(new DateTime(2025, 5, 17), optimization.CreatedDate);
            Assert.AreEqual(new DateTime(2025, 5, 17), optimization.LastModifiedDate);
            Assert.AreEqual(new DateTime(2025, 5, 17), optimization.LastAppliedDate);
            Assert.AreEqual("Test Notes", optimization.Notes);
            Assert.AreEqual(0, optimization.RevertRegistryValue);

            // Derived properties
            Assert.AreEqual("May 17, 2025", optimization.FormattedCreatedDate);
            Assert.AreEqual("May 17, 2025", optimization.FormattedLastModifiedDate);
            Assert.AreEqual("May 17, 2025", optimization.FormattedLastAppliedDate);
            Assert.AreEqual("Extreme Performance Boost", optimization.PerformanceImpactDescription);
            Assert.AreEqual("No Stability Impact", optimization.StabilityImpactDescription);
            Assert.AreEqual("Higher Power Usage", optimization.PowerConsumptionImpactDescription);
            Assert.AreEqual("Higher Temperatures", optimization.ThermalImpactDescription);
            Assert.AreEqual("Extreme Latency Reduction", optimization.InputLatencyImpactDescription);
            Assert.AreEqual("Low Risk - Minimal potential for system instability", optimization.RiskLevelDescription);
            Assert.AreEqual("#AAFF00", optimization.RiskLevelColor);
        }

        [TestMethod]
        public void PowerManagementProfile_Properties_ShouldWork()
        {
            // Arrange
            var profile = new PowerManagementProfile
            {
                Name = "Test Profile",
                Description = "Test Description",
                Category = "Test Category",
                CpuSettings = new CpuPowerSettings
                {
                    PowerPlan = "High Performance",
                    MinProcessorState = 50,
                    MaxProcessorState = 100,
                    CoreParkingEnabled = false
                },
                GpuSettings = new GpuPowerSettings
                {
                    PowerManagementMode = "Prefer Maximum Performance",
                    PowerLimit = 105,
                    LowLatencyModeEnabled = true
                },
                SystemSettings = new SystemPowerSettings
                {
                    CoolingPolicy = "Active",
                    HardDiskTimeout = 0,
                    TimerResolution = 0.5
                },
                PerformanceImpact = 85,
                PowerEfficiency = 40,
                ThermalImpact = 75,
                NoiseLevel = 70,
                InputLatencyImpact = 90,
                Risk = RiskLevel.Low,
                WarningMessage = "Test Warning",
                RequiresRestart = true,
                RequiresAdmin = true,
                EnabledByDefault = true,
                IsEnabled = true,
                IsAdvanced = false,
                IsExperimental = false,
                Author = "Test Author",
                Version = "1.0",
                CreatedDate = new DateTime(2025, 5, 17),
                LastModifiedDate = new DateTime(2025, 5, 17),
                LastAppliedDate = new DateTime(2025, 5, 17),
                Notes = "Test Notes",
                HardwareCompatibility = new List<string> { "Test Hardware" },
                GameCompatibility = new List<string> { "Test Game" }
            };

            // Act & Assert
            Assert.AreEqual("Test Profile", profile.Name);
            Assert.AreEqual("Test Description", profile.Description);
            Assert.AreEqual("Test Category", profile.Category);
            Assert.AreEqual("High Performance", profile.CpuSettings.PowerPlan);
            Assert.AreEqual(50, profile.CpuSettings.MinProcessorState);
            Assert.AreEqual(100, profile.CpuSettings.MaxProcessorState);
            Assert.IsFalse(profile.CpuSettings.CoreParkingEnabled);
            Assert.AreEqual("Prefer Maximum Performance", profile.GpuSettings.PowerManagementMode);
            Assert.AreEqual(105, profile.GpuSettings.PowerLimit);
            Assert.IsTrue(profile.GpuSettings.LowLatencyModeEnabled);
            Assert.AreEqual("Active", profile.SystemSettings.CoolingPolicy);
            Assert.AreEqual(0, profile.SystemSettings.HardDiskTimeout);
            Assert.AreEqual(0.5, profile.SystemSettings.TimerResolution);
            Assert.AreEqual(85, profile.PerformanceImpact);
            Assert.AreEqual(40, profile.PowerEfficiency);
            Assert.AreEqual(75, profile.ThermalImpact);
            Assert.AreEqual(70, profile.NoiseLevel);
            Assert.AreEqual(90, profile.InputLatencyImpact);
            Assert.AreEqual(RiskLevel.Low, profile.Risk);
            Assert.AreEqual("Test Warning", profile.WarningMessage);
            Assert.IsTrue(profile.RequiresRestart);
            Assert.IsTrue(profile.RequiresAdmin);
            Assert.IsTrue(profile.EnabledByDefault);
            Assert.IsTrue(profile.IsEnabled);
            Assert.IsFalse(profile.IsAdvanced);
            Assert.IsFalse(profile.IsExperimental);
            Assert.AreEqual("Test Author", profile.Author);
            Assert.AreEqual("1.0", profile.Version);
            Assert.AreEqual(new DateTime(2025, 5, 17), profile.CreatedDate);
            Assert.AreEqual(new DateTime(2025, 5, 17), profile.LastModifiedDate);
            Assert.AreEqual(new DateTime(2025, 5, 17), profile.LastAppliedDate);
            Assert.AreEqual("Test Notes", profile.Notes);
            Assert.AreEqual(1, profile.HardwareCompatibility.Count);
            Assert.AreEqual("Test Hardware", profile.HardwareCompatibility[0]);
            Assert.AreEqual(1, profile.GameCompatibility.Count);
            Assert.AreEqual("Test Game", profile.GameCompatibility[0]);

            // Derived properties
            Assert.AreEqual("May 17, 2025", profile.FormattedCreatedDate);
            Assert.AreEqual("May 17, 2025", profile.FormattedLastModifiedDate);
            Assert.AreEqual("May 17, 2025", profile.FormattedLastAppliedDate);
            Assert.AreEqual("Extreme Performance Boost", profile.PerformanceImpactDescription);
            Assert.AreEqual("Moderately Efficient", profile.PowerEfficiencyDescription);
            Assert.AreEqual("Higher Temperatures", profile.ThermalImpactDescription);
            Assert.AreEqual("Loud", profile.NoiseLevelDescription);
            Assert.AreEqual("Extreme Latency Reduction", profile.InputLatencyImpactDescription);
            Assert.AreEqual("Low Risk - Minimal potential for system instability", profile.RiskLevelDescription);
            Assert.AreEqual("#AAFF00", profile.RiskLevelColor);
        }

        [TestMethod]
        public void LoadOptimizationsFromDisk_ShouldWork()
        {
            // Arrange
            string nvidiaOptimizationsPath = Path.Combine(_hardwareDirectory, "nvidia.json");
            string amdOptimizationsPath = Path.Combine(_hardwareDirectory, "amd.json");

            // Create test optimizations
            var nvidiaOptimization = new HardwareSpecificOptimization
            {
                Name = "NVIDIA Test Optimization",
                Description = "Test Description",
                Category = "Test Category",
                HardwareType = HardwareType.GPU,
                Manufacturer = "NVIDIA",
                ModelPattern = "RTX.*",
                Method = OptimizationMethod.Registry
            };

            var amdOptimization = new HardwareSpecificOptimization
            {
                Name = "AMD Test Optimization",
                Description = "Test Description",
                Category = "Test Category",
                HardwareType = HardwareType.GPU,
                Manufacturer = "AMD",
                ModelPattern = "RX.*",
                Method = OptimizationMethod.Registry
            };

            // Save test optimizations
            File.WriteAllText(nvidiaOptimizationsPath, JsonSerializer.Serialize(new[] { nvidiaOptimization }));
            File.WriteAllText(amdOptimizationsPath, JsonSerializer.Serialize(new[] { amdOptimization }));

            // Act
            var optimizations = LoadOptimizationsFromDisk(_hardwareDirectory);

            // Assert
            Assert.AreEqual(2, optimizations.Count);
            Assert.IsTrue(optimizations.Any(o => o.Name == "NVIDIA Test Optimization"));
            Assert.IsTrue(optimizations.Any(o => o.Name == "AMD Test Optimization"));
        }

        [TestMethod]
        public void LoadPowerProfilesFromDisk_ShouldWork()
        {
            // Arrange
            string gamingProfilesPath = Path.Combine(_powerDirectory, "gaming.json");
            string creativeProfilesPath = Path.Combine(_powerDirectory, "creative.json");

            // Create test profiles
            var gamingProfile = new PowerManagementProfile
            {
                Name = "Gaming Test Profile",
                Description = "Test Description",
                Category = "Gaming"
            };

            var creativeProfile = new PowerManagementProfile
            {
                Name = "Creative Test Profile",
                Description = "Test Description",
                Category = "Creative"
            };

            // Save test profiles
            File.WriteAllText(gamingProfilesPath, JsonSerializer.Serialize(new[] { gamingProfile }));
            File.WriteAllText(creativeProfilesPath, JsonSerializer.Serialize(new[] { creativeProfile }));

            // Act
            var profiles = LoadPowerProfilesFromDisk(_powerDirectory);

            // Assert
            Assert.AreEqual(2, profiles.Count);
            Assert.IsTrue(profiles.Any(p => p.Name == "Gaming Test Profile"));
            Assert.IsTrue(profiles.Any(p => p.Name == "Creative Test Profile"));
        }

        private void CreateTestOptimizations()
        {
            // Create test optimizations
            var nvidiaOptimization = new HardwareSpecificOptimization
            {
                Name = "NVIDIA Test Optimization",
                Description = "Test Description",
                Category = "Test Category",
                HardwareType = HardwareType.GPU,
                Manufacturer = "NVIDIA",
                ModelPattern = "RTX.*",
                Method = OptimizationMethod.Registry
            };

            var amdOptimization = new HardwareSpecificOptimization
            {
                Name = "AMD Test Optimization",
                Description = "Test Description",
                Category = "Test Category",
                HardwareType = HardwareType.GPU,
                Manufacturer = "AMD",
                ModelPattern = "RX.*",
                Method = OptimizationMethod.Registry
            };

            // Save test optimizations
            File.WriteAllText(
                Path.Combine(_hardwareDirectory, "nvidia.json"),
                JsonSerializer.Serialize(new[] { nvidiaOptimization }, new JsonSerializerOptions { WriteIndented = true }));

            File.WriteAllText(
                Path.Combine(_hardwareDirectory, "amd.json"),
                JsonSerializer.Serialize(new[] { amdOptimization }, new JsonSerializerOptions { WriteIndented = true }));
        }

        private void CreateTestPowerProfiles()
        {
            // Create test profiles
            var gamingProfile = new PowerManagementProfile
            {
                Name = "Gaming Test Profile",
                Description = "Test Description",
                Category = "Gaming"
            };

            var creativeProfile = new PowerManagementProfile
            {
                Name = "Creative Test Profile",
                Description = "Test Description",
                Category = "Creative"
            };

            // Save test profiles
            File.WriteAllText(
                Path.Combine(_powerDirectory, "gaming.json"),
                JsonSerializer.Serialize(new[] { gamingProfile }, new JsonSerializerOptions { WriteIndented = true }));

            File.WriteAllText(
                Path.Combine(_powerDirectory, "creative.json"),
                JsonSerializer.Serialize(new[] { creativeProfile }, new JsonSerializerOptions { WriteIndented = true }));
        }

        private List<HardwareSpecificOptimization> LoadOptimizationsFromDisk(string directory)
        {
            List<HardwareSpecificOptimization> optimizations = new List<HardwareSpecificOptimization>();

            foreach (string file in Directory.GetFiles(directory, "*.json"))
            {
                try
                {
                    string json = File.ReadAllText(file);
                    var fileOptimizations = JsonSerializer.Deserialize<HardwareSpecificOptimization[]>(json);

                    if (fileOptimizations != null)
                    {
                        optimizations.AddRange(fileOptimizations);
                    }
                }
                catch (Exception)
                {
                    // Ignore errors in tests
                }
            }

            return optimizations;
        }

        private List<PowerManagementProfile> LoadPowerProfilesFromDisk(string directory)
        {
            List<PowerManagementProfile> profiles = new List<PowerManagementProfile>();

            foreach (string file in Directory.GetFiles(directory, "*.json"))
            {
                try
                {
                    string json = File.ReadAllText(file);
                    var fileProfiles = JsonSerializer.Deserialize<PowerManagementProfile[]>(json);

                    if (fileProfiles != null)
                    {
                        profiles.AddRange(fileProfiles);
                    }
                }
                catch (Exception)
                {
                    // Ignore errors in tests
                }
            }

            return profiles;
        }
    }
}
