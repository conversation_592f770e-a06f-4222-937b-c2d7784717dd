// Created by Arsenal on 5-17-25 12:15PM
using System;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Interface for performance monitoring service
    /// </summary>
    public interface IPerformanceMonitoringService
    {
        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Gets a value indicating whether monitoring is active
        /// </summary>
        bool IsMonitoringActive { get; }

        /// <summary>
        /// Event raised when performance metrics are updated
        /// </summary>
        event EventHandler<PerformanceMetricsEventArgs> MetricsUpdated;

        /// <summary>
        /// Starts monitoring performance
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// Stops monitoring performance
        /// </summary>
        void StopMonitoring();

        /// <summary>
        /// Gets the current performance metrics
        /// </summary>
        /// <returns>The current performance metrics</returns>
        PerformanceMetrics GetCurrentMetrics();
    }
}
