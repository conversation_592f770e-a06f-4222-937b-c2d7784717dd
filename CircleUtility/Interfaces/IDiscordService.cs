namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for Discord service
    /// </summary>
    public interface IDiscordService
    {
        /// <summary>
        /// Connects to Discord
        /// </summary>
        /// <returns>True if successful</returns>
        bool Connect();

        /// <summary>
        /// Disconnects from Discord
        /// </summary>
        void Disconnect();

        /// <summary>
        /// Gets connection status
        /// </summary>
        /// <returns>True if connected</returns>
        bool IsConnected();
    }
}
