// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for security settings
    /// </summary>
    public class SecuritySettingsViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly SecurityService _securityService;
        private readonly InputValidationService _validationService;
        private readonly ConfigurationManager _configManager;
        private string _currentUsername;
        private string _currentPassword;
        private string _newPassword;
        private string _confirmPassword;
        private int _selectedSecurityLevel;
        private bool _isAdmin;
        private bool _isPasswordChangeEnabled;
        private bool _isSecurityLevelChangeEnabled;
        private bool _isAdminChangeEnabled;
        private string _errorMessage;
        private string _successMessage;
        private ObservableCollection<UserViewModel> _users;
        private UserViewModel _selectedUser;

        /// <summary>
        /// Initializes a new instance of the SecuritySettingsViewModel class
        /// </summary>
        public SecuritySettingsViewModel()
        {
            _logger = LoggingService.Instance;
            _securityService = SecurityService.Instance;
            _validationService = InputValidationService.Instance;
            _configManager = ConfigurationManager.Instance;
            
            // Initialize commands
            ChangePasswordCommand = new RelayCommand(ChangePassword, CanChangePassword);
            ChangeSecurityLevelCommand = new RelayCommand(ChangeSecurityLevel, CanChangeSecurityLevel);
            ChangeAdminStatusCommand = new RelayCommand(ChangeAdminStatus, CanChangeAdminStatus);
            CreateUserCommand = new RelayCommand(CreateUser, CanCreateUser);
            DeleteUserCommand = new RelayCommand(DeleteUser, CanDeleteUser);
            UnlockUserCommand = new RelayCommand(UnlockUser, CanUnlockUser);
            ResetPasswordCommand = new RelayCommand(ResetPassword, CanResetPassword);
            
            // Initialize properties
            _currentUsername = _configManager.CurrentConfig.General.Username;
            _selectedSecurityLevel = _securityService.GetSecurityLevel(_currentUsername);
            _isAdmin = _securityService.IsAdmin(_currentUsername);
            _isPasswordChangeEnabled = true;
            _isSecurityLevelChangeEnabled = _isAdmin;
            _isAdminChangeEnabled = _isAdmin;
            _users = new ObservableCollection<UserViewModel>();
            
            // Load users
            LoadUsers();
        }

        /// <summary>
        /// Gets or sets the current username
        /// </summary>
        public string CurrentUsername
        {
            get => _currentUsername;
            set
            {
                if (SetProperty(ref _currentUsername, value))
                {
                    OnPropertyChanged(nameof(IsCurrentUser));
                    OnPropertyChanged(nameof(IsAdmin));
                }
            }
        }

        /// <summary>
        /// Gets or sets the current password
        /// </summary>
        public string CurrentPassword
        {
            get => _currentPassword;
            set
            {
                if (SetProperty(ref _currentPassword, value))
                {
                    (ChangePasswordCommand as RelayCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets the new password
        /// </summary>
        public string NewPassword
        {
            get => _newPassword;
            set
            {
                if (SetProperty(ref _newPassword, value))
                {
                    (ChangePasswordCommand as RelayCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets the confirm password
        /// </summary>
        public string ConfirmPassword
        {
            get => _confirmPassword;
            set
            {
                if (SetProperty(ref _confirmPassword, value))
                {
                    (ChangePasswordCommand as RelayCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected security level
        /// </summary>
        public int SelectedSecurityLevel
        {
            get => _selectedSecurityLevel;
            set
            {
                if (SetProperty(ref _selectedSecurityLevel, value))
                {
                    (ChangeSecurityLevelCommand as RelayCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the user is an admin
        /// </summary>
        public bool IsAdmin
        {
            get => _isAdmin;
            set
            {
                if (SetProperty(ref _isAdmin, value))
                {
                    (ChangeAdminStatusCommand as RelayCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether password change is enabled
        /// </summary>
        public bool IsPasswordChangeEnabled
        {
            get => _isPasswordChangeEnabled;
            set => SetProperty(ref _isPasswordChangeEnabled, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether security level change is enabled
        /// </summary>
        public bool IsSecurityLevelChangeEnabled
        {
            get => _isSecurityLevelChangeEnabled;
            set => SetProperty(ref _isSecurityLevelChangeEnabled, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether admin change is enabled
        /// </summary>
        public bool IsAdminChangeEnabled
        {
            get => _isAdminChangeEnabled;
            set => SetProperty(ref _isAdminChangeEnabled, value);
        }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Gets or sets the success message
        /// </summary>
        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        /// <summary>
        /// Gets or sets the users
        /// </summary>
        public ObservableCollection<UserViewModel> Users
        {
            get => _users;
            set => SetProperty(ref _users, value);
        }

        /// <summary>
        /// Gets or sets the selected user
        /// </summary>
        public UserViewModel SelectedUser
        {
            get => _selectedUser;
            set
            {
                if (SetProperty(ref _selectedUser, value))
                {
                    OnPropertyChanged(nameof(IsCurrentUser));
                    (DeleteUserCommand as RelayCommand)?.RaiseCanExecuteChanged();
                    (UnlockUserCommand as RelayCommand)?.RaiseCanExecuteChanged();
                    (ResetPasswordCommand as RelayCommand)?.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets a value indicating whether the selected user is the current user
        /// </summary>
        public bool IsCurrentUser => SelectedUser != null && 
            string.Equals(SelectedUser.Username, CurrentUsername, StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// Gets the change password command
        /// </summary>
        public ICommand ChangePasswordCommand { get; }

        /// <summary>
        /// Gets the change security level command
        /// </summary>
        public ICommand ChangeSecurityLevelCommand { get; }

        /// <summary>
        /// Gets the change admin status command
        /// </summary>
        public ICommand ChangeAdminStatusCommand { get; }

        /// <summary>
        /// Gets the create user command
        /// </summary>
        public ICommand CreateUserCommand { get; }

        /// <summary>
        /// Gets the delete user command
        /// </summary>
        public ICommand DeleteUserCommand { get; }

        /// <summary>
        /// Gets the unlock user command
        /// </summary>
        public ICommand UnlockUserCommand { get; }

        /// <summary>
        /// Gets the reset password command
        /// </summary>
        public ICommand ResetPasswordCommand { get; }

        /// <summary>
        /// Loads users
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                // In a real implementation, this would load users from the security service
                // For now, we'll just add the current user
                Users.Clear();
                
                // Add current user
                Users.Add(new UserViewModel
                {
                    Username = CurrentUsername,
                    SecurityLevel = SelectedSecurityLevel,
                    IsAdmin = IsAdmin,
                    IsEnabled = true,
                    IsLocked = false,
                    LastLoginDate = DateTime.Now
                });
                
                // Add admin user if not the current user
                if (!string.Equals(CurrentUsername, "admincp123", StringComparison.OrdinalIgnoreCase))
                {
                    Users.Add(new UserViewModel
                    {
                        Username = "admincp123",
                        SecurityLevel = 3,
                        IsAdmin = true,
                        IsEnabled = true,
                        IsLocked = false,
                        LastLoginDate = DateTime.Now.AddDays(-1)
                    });
                }
                
                // Select current user
                SelectedUser = Users[0];
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading users: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error loading users. Please try again.";
            }
        }

        /// <summary>
        /// Changes the password
        /// </summary>
        private void ChangePassword()
        {
            try
            {
                ErrorMessage = null;
                SuccessMessage = null;
                
                // Validate input
                if (!ValidatePasswordChange())
                {
                    return;
                }
                
                // Change password
                bool result = _securityService.ChangePassword(CurrentUsername, CurrentPassword, NewPassword);
                
                if (result)
                {
                    SuccessMessage = "Password changed successfully.";
                    CurrentPassword = null;
                    NewPassword = null;
                    ConfirmPassword = null;
                }
                else
                {
                    ErrorMessage = "Failed to change password. Please check your current password and try again.";
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error changing password: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error changing password. Please try again.";
            }
        }

        /// <summary>
        /// Determines whether the password can be changed
        /// </summary>
        /// <returns>True if the password can be changed, false otherwise</returns>
        private bool CanChangePassword()
        {
            return IsPasswordChangeEnabled &&
                !string.IsNullOrEmpty(CurrentPassword) &&
                !string.IsNullOrEmpty(NewPassword) &&
                !string.IsNullOrEmpty(ConfirmPassword);
        }

        /// <summary>
        /// Validates the password change
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        private bool ValidatePasswordChange()
        {
            // Check if passwords match
            if (NewPassword != ConfirmPassword)
            {
                ErrorMessage = "New password and confirm password do not match.";
                return false;
            }
            
            // Validate new password
            if (!_validationService.ValidateProperty("Password", NewPassword, out string errorMessage))
            {
                ErrorMessage = errorMessage;
                return false;
            }
            
            return true;
        }

        /// <summary>
        /// Changes the security level
        /// </summary>
        private void ChangeSecurityLevel()
        {
            try
            {
                ErrorMessage = null;
                SuccessMessage = null;
                
                // In a real implementation, this would change the security level in the security service
                // For now, we'll just update the view model
                
                SuccessMessage = $"Security level changed to {SelectedSecurityLevel}.";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error changing security level: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error changing security level. Please try again.";
            }
        }

        /// <summary>
        /// Determines whether the security level can be changed
        /// </summary>
        /// <returns>True if the security level can be changed, false otherwise</returns>
        private bool CanChangeSecurityLevel()
        {
            return IsSecurityLevelChangeEnabled;
        }

        /// <summary>
        /// Changes the admin status
        /// </summary>
        private void ChangeAdminStatus()
        {
            try
            {
                ErrorMessage = null;
                SuccessMessage = null;
                
                // In a real implementation, this would change the admin status in the security service
                // For now, we'll just update the view model
                
                SuccessMessage = $"Admin status changed to {(IsAdmin ? "admin" : "non-admin")}.";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error changing admin status: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error changing admin status. Please try again.";
            }
        }

        /// <summary>
        /// Determines whether the admin status can be changed
        /// </summary>
        /// <returns>True if the admin status can be changed, false otherwise</returns>
        private bool CanChangeAdminStatus()
        {
            return IsAdminChangeEnabled;
        }

        /// <summary>
        /// Creates a user
        /// </summary>
        private void CreateUser()
        {
            try
            {
                ErrorMessage = null;
                SuccessMessage = null;
                
                // In a real implementation, this would create a user in the security service
                // For now, we'll just update the view model
                
                SuccessMessage = "User created successfully.";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating user: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error creating user. Please try again.";
            }
        }

        /// <summary>
        /// Determines whether a user can be created
        /// </summary>
        /// <returns>True if a user can be created, false otherwise</returns>
        private bool CanCreateUser()
        {
            return IsAdmin;
        }

        /// <summary>
        /// Deletes a user
        /// </summary>
        private void DeleteUser()
        {
            try
            {
                ErrorMessage = null;
                SuccessMessage = null;
                
                // In a real implementation, this would delete a user from the security service
                // For now, we'll just update the view model
                
                SuccessMessage = $"User {SelectedUser.Username} deleted successfully.";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error deleting user: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error deleting user. Please try again.";
            }
        }

        /// <summary>
        /// Determines whether a user can be deleted
        /// </summary>
        /// <returns>True if a user can be deleted, false otherwise</returns>
        private bool CanDeleteUser()
        {
            return IsAdmin && SelectedUser != null && !IsCurrentUser;
        }

        /// <summary>
        /// Unlocks a user
        /// </summary>
        private void UnlockUser()
        {
            try
            {
                ErrorMessage = null;
                SuccessMessage = null;
                
                // In a real implementation, this would unlock a user in the security service
                // For now, we'll just update the view model
                
                SuccessMessage = $"User {SelectedUser.Username} unlocked successfully.";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error unlocking user: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error unlocking user. Please try again.";
            }
        }

        /// <summary>
        /// Determines whether a user can be unlocked
        /// </summary>
        /// <returns>True if a user can be unlocked, false otherwise</returns>
        private bool CanUnlockUser()
        {
            return IsAdmin && SelectedUser != null && SelectedUser.IsLocked;
        }

        /// <summary>
        /// Resets a user's password
        /// </summary>
        private void ResetPassword()
        {
            try
            {
                ErrorMessage = null;
                SuccessMessage = null;
                
                // In a real implementation, this would reset a user's password in the security service
                // For now, we'll just update the view model
                
                SuccessMessage = $"Password for user {SelectedUser.Username} reset successfully.";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error resetting password: {ex.Message}", LogLevel.ERROR);
                ErrorMessage = "Error resetting password. Please try again.";
            }
        }

        /// <summary>
        /// Determines whether a user's password can be reset
        /// </summary>
        /// <returns>True if a user's password can be reset, false otherwise</returns>
        private bool CanResetPassword()
        {
            return IsAdmin && SelectedUser != null && !IsCurrentUser;
        }
    }

    /// <summary>
    /// View model for users
    /// </summary>
    public class UserViewModel : ViewModelBase
    {
        private string _username;
        private int _securityLevel;
        private bool _isAdmin;
        private bool _isEnabled;
        private bool _isLocked;
        private DateTime? _lastLoginDate;

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value);
        }

        /// <summary>
        /// Gets or sets the security level
        /// </summary>
        public int SecurityLevel
        {
            get => _securityLevel;
            set => SetProperty(ref _securityLevel, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the user is an admin
        /// </summary>
        public bool IsAdmin
        {
            get => _isAdmin;
            set => SetProperty(ref _isAdmin, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the user is enabled
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the user is locked
        /// </summary>
        public bool IsLocked
        {
            get => _isLocked;
            set => SetProperty(ref _isLocked, value);
        }

        /// <summary>
        /// Gets or sets the last login date
        /// </summary>
        public DateTime? LastLoginDate
        {
            get => _lastLoginDate;
            set => SetProperty(ref _lastLoginDate, value);
        }

        /// <summary>
        /// Gets the security level description
        /// </summary>
        public string SecurityLevelDescription
        {
            get
            {
                switch (SecurityLevel)
                {
                    case 1:
                        return "Basic";
                    case 2:
                        return "Advanced";
                    case 3:
                        return "Expert";
                    default:
                        return "Unknown";
                }
            }
        }

        /// <summary>
        /// Gets the status description
        /// </summary>
        public string StatusDescription
        {
            get
            {
                if (IsLocked)
                {
                    return "Locked";
                }
                else if (!IsEnabled)
                {
                    return "Disabled";
                }
                else
                {
                    return "Active";
                }
            }
        }

        /// <summary>
        /// Gets the status color
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (IsLocked)
                {
                    return "#FF0000"; // Red
                }
                else if (!IsEnabled)
                {
                    return "#FFA500"; // Orange
                }
                else
                {
                    return "#00FF00"; // Green
                }
            }
        }

        /// <summary>
        /// Gets the formatted last login date
        /// </summary>
        public string FormattedLastLoginDate => LastLoginDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the role description
        /// </summary>
        public string RoleDescription
        {
            get
            {
                if (IsAdmin)
                {
                    return "Administrator";
                }
                else if (SecurityLevel == 3)
                {
                    return "Expert User";
                }
                else if (SecurityLevel == 2)
                {
                    return "Advanced User";
                }
                else
                {
                    return "Basic User";
                }
            }
        }
    }
}

