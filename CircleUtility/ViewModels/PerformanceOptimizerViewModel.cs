using CircleUtility.Commands;
using System;
using System.Collections.Generic;
using System.Windows.Input;
using CircleUtility.Helpers;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the Performance Optimizer view
    /// </summary>
    public class PerformanceOptimizerViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly NotificationService _notificationService;
        private readonly Dictionary<PerformanceComponentType, string> _currentProfiles;
        private string _statusMessage;
        private bool _isStatusVisible;
        private bool _isStatusSuccess;

        /// <summary>
        /// Initializes a new instance of the PerformanceOptimizerViewModel class
        /// </summary>
        public PerformanceOptimizerViewModel()
        {
            // Initialize services
            _logger = LoggingService.Instance;
            _notificationService = NotificationService.Instance;
            _currentProfiles = new Dictionary<PerformanceComponentType, string>();

            // Initialize commands
            ApplySettingsCommand = new RelayCommand(ApplySettings);

            // Initialize properties
            StatusMessage = "";
            IsStatusVisible = false;
            IsStatusSuccess = false;

            // Initialize current profiles
            _currentProfiles[PerformanceComponentType.System] = "Balanced";
            _currentProfiles[PerformanceComponentType.CPU] = "Balanced";
            _currentProfiles[PerformanceComponentType.GPU] = "Performance";
            _currentProfiles[PerformanceComponentType.Memory] = "Balanced";
            _currentProfiles[PerformanceComponentType.Storage] = "Power Saver";

            _logger.Log("PerformanceOptimizerViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is a success
        /// </summary>
        public bool IsStatusSuccess
        {
            get => _isStatusSuccess;
            set => SetProperty(ref _isStatusSuccess, value);
        }

        /// <summary>
        /// Gets the command to apply settings
        /// </summary>
        public ICommand ApplySettingsCommand { get; }

        /// <summary>
        /// Updates the current profile for a component
        /// </summary>
        /// <param name="componentType">The component type</param>
        /// <param name="profileName">The profile name</param>
        /// <param name="value">The value</param>
        public void UpdateProfile(PerformanceComponentType componentType, string profileName, double value)
        {
            try
            {
                // Update current profile
                _currentProfiles[componentType] = profileName;

                // Log the change
                _logger.Log($"{componentType} profile changed to {profileName} ({value}%)", LogLevel.INFO);

                // Show status message
                ShowStatus($"{componentType} profile set to {profileName}", true);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating profile: {ex.Message}", LogLevel.ERROR);
                ShowStatus("Error updating profile", false);
            }
        }

        /// <summary>
        /// Applies all performance settings
        /// </summary>
        private void ApplySettings()
        {
            try
            {
                _logger.Log("Applying performance settings...", LogLevel.INFO);

                // Apply settings for each component
                foreach (var profile in _currentProfiles)
                {
                    ApplyProfileSettings(profile.Key, profile.Value);
                }

                // Show success notification
                _notificationService.AddSuccessNotification(
                    "Performance Settings Applied",
                    "Your performance settings have been successfully applied to the system.",
                    null,
                    null);

                ShowStatus("Performance settings applied successfully", true);
                _logger.Log("Performance settings applied successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying performance settings: {ex.Message}", LogLevel.ERROR);

                // Show error notification
                _notificationService.AddErrorNotification(
                    "Error Applying Settings",
                    $"An error occurred while applying performance settings: {ex.Message}",
                    "System",
                    null,
                    "RETRY");

                ShowStatus("Error applying performance settings", false);
            }
        }

        /// <summary>
        /// Applies profile settings for a component
        /// </summary>
        /// <param name="componentType">The component type</param>
        /// <param name="profileName">The profile name</param>
        private void ApplyProfileSettings(PerformanceComponentType componentType, string profileName)
        {
            // In a real implementation, this would call the appropriate service to apply the settings
            // For now, we'll just log the action
            _logger.Log($"Applied {profileName} profile to {componentType}", LogLevel.INFO);

            // Apply different settings based on the profile
            switch (profileName)
            {
                case "Performance":
                    // Apply performance-focused settings
                    break;
                case "Balanced":
                    // Apply balanced settings
                    break;
                case "Power Saver":
                    // Apply power-saving settings
                    break;
            }
        }

        /// <summary>
        /// Shows a status message
        /// </summary>
        /// <param name="message">The message to show</param>
        /// <param name="isSuccess">Whether the message is a success message</param>
        private void ShowStatus(string message, bool isSuccess)
        {
            StatusMessage = message;
            IsStatusSuccess = isSuccess;
            IsStatusVisible = true;

            // Hide the status after 5 seconds
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(5);
            timer.Tick += (sender, e) =>
            {
                IsStatusVisible = false;
                timer.Stop();
            };
            timer.Start();
        }
    }
}

