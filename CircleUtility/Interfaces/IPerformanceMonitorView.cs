using System;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for performance monitor view
    /// </summary>
    public interface IPerformanceMonitorView : IScreen
    {
        /// <summary>
        /// Updates the CPU usage display
        /// </summary>
        /// <param name="percentage">The CPU usage percentage</param>
        void UpdateCpuUsage(double percentage);

        /// <summary>
        /// Updates the memory usage display
        /// </summary>
        /// <param name="percentage">The memory usage percentage</param>
        void UpdateMemoryUsage(double percentage);

        /// <summary>
        /// Updates the GPU usage display
        /// </summary>
        /// <param name="percentage">The GPU usage percentage</param>
        void UpdateGpuUsage(double percentage);

        /// <summary>
        /// Starts monitoring system performance
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// Stops monitoring system performance
        /// </summary>
        void StopMonitoring();

        /// <summary>
        /// Gets a value indicating whether monitoring is active
        /// </summary>
        bool IsMonitoring { get; }

        /// <summary>
        /// Gets the current CPU usage percentage
        /// </summary>
        double CurrentCpuUsage { get; }

        /// <summary>
        /// Gets the current memory usage percentage
        /// </summary>
        double CurrentMemoryUsage { get; }

        /// <summary>
        /// Gets the current GPU usage percentage
        /// </summary>
        double CurrentGpuUsage { get; }
    }
}
