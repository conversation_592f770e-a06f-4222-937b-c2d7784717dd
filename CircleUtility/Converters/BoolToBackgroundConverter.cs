using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts a boolean value to a background color
    /// </summary>
    public class BoolToBackgroundConverter : IValueConverter
    {
        /// <summary>
        /// Converts a boolean value to a background color
        /// </summary>
        /// <param name="value">The boolean value to convert</param>
        /// <param name="targetType">The type of the target property</param>
        /// <param name="parameter">The converter parameter</param>
        /// <param name="culture">The culture information</param>
        /// <returns>A SolidColorBrush with the appropriate color</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return new SolidColorBrush(boolValue ? Color.FromRgb(0, 40, 80) : Color.FromRgb(10, 20, 30));
            }

            return new SolidColorBrush(Color.FromRgb(10, 20, 30));
        }

        /// <summary>
        /// Converts a background color back to a boolean value (not implemented)
        /// </summary>
        /// <param name="value">The color to convert</param>
        /// <param name="targetType">The type of the target property</param>
        /// <param name="parameter">The converter parameter</param>
        /// <param name="culture">The culture information</param>
        /// <returns>A boolean value</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
