using System.Windows.Media;

namespace CircleUtility.Models
{
    /// <summary>
    /// Information about a hardware badge
    /// </summary>
    public class HardwareBadgeInfo
    {
        public HardwareType Type { get; set; }
        public bool IsDetected { get; set; }
        public string Name { get; set; }
        public string Manufacturer { get; set; }
        public int CompatibilityScore { get; set; }
        public string Details { get; set; }
        public bool IsAnimating { get; set; }
        public double GlowIntensity => CalculateGlowIntensity();
        public double GlowRadius => CalculateGlowRadius();
        public Color BadgeColor => GetBadgeColor();
        public string TooltipText => $"{Type}: {Name}\nManufacturer: {Manufacturer}\n{Details}\nCompatibility Score: {CompatibilityScore}%";
        public string CompatibilityLevel
        {
            get
            {
                if (CompatibilityScore >= 90) return "Excellent";
                if (CompatibilityScore >= 75) return "Good";
                if (CompatibilityScore >= 50) return "Average";
                if (CompatibilityScore >= 25) return "Poor";
                return "Incompatible";
            }
        }
        private double CalculateGlowIntensity()
        {
            if (CompatibilityScore >= 90) return 1.0;
            if (CompatibilityScore >= 75) return 0.8;
            if (CompatibilityScore >= 50) return 0.6;
            if (CompatibilityScore >= 25) return 0.4;
            return 0.2;
        }
        private double CalculateGlowRadius()
        {
            if (CompatibilityScore >= 90) return 15;
            if (CompatibilityScore >= 75) return 12;
            if (CompatibilityScore >= 50) return 10;
            if (CompatibilityScore >= 25) return 8;
            return 5;
        }
        private Color GetBadgeColor()
        {
            switch (Type)
            {
                case HardwareType.CPU:
                    return Color.FromRgb(0, 200, 255); // Blue
                case HardwareType.GPU:
                    return Color.FromRgb(0, 255, 128); // Green
                case HardwareType.RAM:
                    return Color.FromRgb(255, 128, 0); // Orange
                case HardwareType.Storage:
                    return Color.FromRgb(255, 0, 128); // Pink
                case HardwareType.Network:
                    return Color.FromRgb(128, 0, 255); // Purple
                case HardwareType.System:
                    return Color.FromRgb(0, 128, 255); // Light Blue
                default:
                    return Color.FromRgb(200, 200, 200); // Gray
            }
        }
    }
} 