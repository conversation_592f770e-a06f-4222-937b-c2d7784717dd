using System.Threading.Tasks;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for system optimization service
    /// </summary>
    public interface ISystemOptimizationService
    {
        /// <summary>
        /// Gets whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Initializes the service asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task InitializeAsync();

        /// <summary>
        /// Applies system optimization
        /// </summary>
        /// <param name="optimizationName">Name of optimization to apply</param>
        /// <returns>True if successful</returns>
        bool ApplyOptimization(string optimizationName);

        /// <summary>
        /// Reverts system optimization
        /// </summary>
        /// <param name="optimizationName">Name of optimization to revert</param>
        /// <returns>True if successful</returns>
        bool RevertOptimization(string optimizationName);

        /// <summary>
        /// Gets the current optimization score
        /// </summary>
        /// <returns>Optimization score</returns>
        int GetOptimizationScore();
    }
}
