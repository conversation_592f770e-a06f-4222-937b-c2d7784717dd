using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for hardware optimization service
    /// </summary>
    public interface IHardwareOptimizationService
    {
        /// <summary>
        /// Gets whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Gets the list of available optimizations
        /// </summary>
        List<HardwareSpecificOptimization> Optimizations { get; }

        /// <summary>
        /// Gets the list of available power profiles
        /// </summary>
        List<PowerManagementProfile> PowerProfiles { get; }

        /// <summary>
        /// Initializes the service asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        Task InitializeAsync();

        /// <summary>
        /// Gets recommended smart tweaks asynchronously
        /// </summary>
        /// <returns>Task with recommended tweaks</returns>
        Task<IEnumerable<SmartTweak>> GetRecommendedSmartTweaksAsync();

        /// <summary>
        /// Applies smart tweak asynchronously
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        /// <returns>Task representing the async operation</returns>
        Task<bool> ApplySmartTweakAsync(object tweak);

        /// <summary>
        /// Reverts smart tweak asynchronously
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        /// <returns>Task representing the async operation</returns>
        Task<bool> RevertSmartTweakAsync(object tweak);

        /// <summary>
        /// Gets optimizations for a specific category
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="filter">The filter (e.g., "Recommended", "All")</param>
        /// <returns>List of optimizations</returns>
        List<HardwareSpecificOptimization> GetOptimizationsForCategory(HardwareType hardwareType, string manufacturer, string filter);

        /// <summary>
        /// Applies an optimization by name
        /// </summary>
        /// <param name="optimizationName">Name of optimization to apply</param>
        /// <returns>True if successful</returns>
        bool ApplyOptimization(string optimizationName);

        /// <summary>
        /// Applies optimization asynchronously
        /// </summary>
        /// <param name="optimization">The optimization to apply</param>
        /// <returns>Task representing the async operation</returns>
        Task<bool> ApplyOptimizationAsync(HardwareSpecificOptimization optimization);

        /// <summary>
        /// Reverts optimization
        /// </summary>
        /// <param name="optimizationName">Name of optimization to revert</param>
        /// <returns>True if successful</returns>
        bool RevertOptimization(string optimizationName);

        /// <summary>
        /// Reverts power profile asynchronously
        /// </summary>
        /// <param name="profileName">Name of power profile to revert</param>
        /// <returns>Task representing the async operation</returns>
        Task<bool> RevertPowerProfileAsync(string profileName);
    }
}
