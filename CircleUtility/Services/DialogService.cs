using System;
using System.Windows;
using Microsoft.Win32;
using CircleUtility.Interfaces;
using CircleUtility.Models;
using CircleUtility.Views;

namespace CircleUtility.Services
{
    /// <summary>
    /// Implementation of IDialogService
    /// </summary>
    public class DialogService : IDialogService
    {
        private readonly ILoggerService _logger;

        /// <summary>
        /// Initializes a new instance of the DialogService class
        /// </summary>
        /// <param name="logger">The logger service</param>
        public DialogService(ILoggerService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <returns>True if confirmed, false otherwise</returns>
        public bool ShowConfirmationDialog(Window owner, string title, string message)
        {
            var result = ShowMessageDialog(owner, title, message, MessageDialogButtons.YesNo);
            return result == MessageDialogResult.Yes;
        }

        /// <summary>
        /// Shows an error dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons to show</param>
        /// <returns>The dialog result</returns>
        public MessageDialogResult ShowErrorDialog(Window owner, string title, string message, MessageDialogButtons buttons = MessageDialogButtons.OK)
        {
            _logger.Log($"Error dialog shown: {title} - {message}", LogLevel.ERROR);
            return ShowMessageDialogInternal(owner, title, message, buttons, MessageBoxImage.Error);
        }

        /// <summary>
        /// Shows a message dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons to show</param>
        /// <returns>The dialog result</returns>
        public MessageDialogResult ShowMessageDialog(Window owner, string title, string message, MessageDialogButtons buttons = MessageDialogButtons.OK)
        {
            _logger.Log($"Message dialog shown: {title} - {message}", LogLevel.INFO);
            return ShowMessageDialogInternal(owner, title, message, buttons, MessageBoxImage.Information);
        }

        /// <summary>
        /// Shows a file open dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="filter">The file filter</param>
        /// <param name="initialDirectory">The initial directory</param>
        /// <returns>The selected file path, or null if cancelled</returns>
        public string ShowOpenFileDialog(Window owner, string filter, string initialDirectory = null)
        {
            var dialog = new OpenFileDialog
            {
                Filter = filter,
                InitialDirectory = initialDirectory ?? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
            };

            var result = dialog.ShowDialog(owner);
            if (result.HasValue && result.Value)
            {
                _logger.Log($"File selected: {dialog.FileName}", LogLevel.INFO);
                return dialog.FileName;
            }

            return null;
        }

        /// <summary>
        /// Shows a file save dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="filter">The file filter</param>
        /// <param name="initialDirectory">The initial directory</param>
        /// <param name="defaultFileName">The default file name</param>
        /// <returns>The selected file path, or null if cancelled</returns>
        public string ShowSaveFileDialog(Window owner, string filter, string initialDirectory = null, string defaultFileName = null)
        {
            var dialog = new SaveFileDialog
            {
                Filter = filter,
                InitialDirectory = initialDirectory ?? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                FileName = defaultFileName ?? string.Empty
            };

            var result = dialog.ShowDialog(owner);
            if (result.HasValue && result.Value)
            {
                _logger.Log($"File saved: {dialog.FileName}", LogLevel.INFO);
                return dialog.FileName;
            }

            return null;
        }

        /// <summary>
        /// Shows a thermal warning dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="profile">The power profile</param>
        /// <returns>True if the user chose to proceed, false otherwise</returns>
        public bool ShowThermalWarningDialog(Window owner, PowerManagementProfile profile)
        {
            var dialog = new ThermalWarningDialog();
            dialog.Owner = owner;
            dialog.ShowDialog();
            
            _logger.Log($"Thermal warning dialog result: {dialog.UserChoseToProceed}", LogLevel.INFO);
            return dialog.UserChoseToProceed;
        }

        /// <summary>
        /// Shows a warning dialog
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons to show</param>
        /// <returns>The dialog result</returns>
        public MessageDialogResult ShowWarningDialog(Window owner, string title, string message, MessageDialogButtons buttons = MessageDialogButtons.OK)
        {
            _logger.Log($"Warning dialog shown: {title} - {message}", LogLevel.WARNING);
            return ShowMessageDialogInternal(owner, title, message, buttons, MessageBoxImage.Warning);
        }

        private MessageDialogResult ShowMessageDialogInternal(Window owner, string title, string message, MessageDialogButtons buttons, MessageBoxImage icon)
        {
            var messageBoxButtons = MessageBoxButton.OK;
            switch (buttons)
            {
                case MessageDialogButtons.OK:
                    messageBoxButtons = MessageBoxButton.OK;
                    break;
                case MessageDialogButtons.OKCancel:
                    messageBoxButtons = MessageBoxButton.OKCancel;
                    break;
                case MessageDialogButtons.YesNo:
                    messageBoxButtons = MessageBoxButton.YesNo;
                    break;
                case MessageDialogButtons.YesNoCancel:
                    messageBoxButtons = MessageBoxButton.YesNoCancel;
                    break;
            }

            var result = MessageBox.Show(owner, message, title, messageBoxButtons, icon);
            return ConvertMessageBoxResult(result);
        }

        private MessageDialogResult ConvertMessageBoxResult(MessageBoxResult result)
        {
            switch (result)
            {
                case MessageBoxResult.OK:
                    return MessageDialogResult.OK;
                case MessageBoxResult.Cancel:
                    return MessageDialogResult.Cancel;
                case MessageBoxResult.Yes:
                    return MessageDialogResult.Yes;
                case MessageBoxResult.No:
                    return MessageDialogResult.No;
                default:
                    return MessageDialogResult.Cancel;
            }
        }
    }
}
