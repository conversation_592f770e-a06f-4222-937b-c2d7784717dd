using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedProgressBar.xaml
    /// </summary>
    public partial class AnimatedProgressBar : UserControl
    {
        private Storyboard _progressAnimation;
        private Storyboard _pulseAnimation;
        private Storyboard _indeterminateAnimation;
        private bool _isIndeterminate;
        
        /// <summary>
        /// Progress color dependency property
        /// </summary>
        public static readonly DependencyProperty ProgressColorProperty =
            DependencyProperty.Register("ProgressColor", typeof(Brush), typeof(AnimatedProgressBar), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(0, 200, 255))));
                
        /// <summary>
        /// Progress text dependency property
        /// </summary>
        public static readonly DependencyProperty ProgressTextProperty =
            DependencyProperty.Register("ProgressText", typeof(string), typeof(AnimatedProgressBar), 
                new PropertyMetadata(string.Empty));
                
        /// <summary>
        /// Progress value dependency property
        /// </summary>
        public static readonly DependencyProperty ProgressValueProperty =
            DependencyProperty.Register("ProgressValue", typeof(double), typeof(AnimatedProgressBar), 
                new PropertyMetadata(0.0, OnProgressValueChanged));
                
        /// <summary>
        /// Maximum value dependency property
        /// </summary>
        public static readonly DependencyProperty MaximumValueProperty =
            DependencyProperty.Register("MaximumValue", typeof(double), typeof(AnimatedProgressBar), 
                new PropertyMetadata(100.0));
                
        /// <summary>
        /// Is indeterminate dependency property
        /// </summary>
        public static readonly DependencyProperty IsIndeterminateProperty =
            DependencyProperty.Register("IsIndeterminate", typeof(bool), typeof(AnimatedProgressBar), 
                new PropertyMetadata(false, OnIsIndeterminateChanged));
                
        /// <summary>
        /// Gets or sets the progress color
        /// </summary>
        public Brush ProgressColor
        {
            get { return (Brush)GetValue(ProgressColorProperty); }
            set { SetValue(ProgressColorProperty, value); }
        }
        
        /// <summary>
        /// Gets or sets the progress text
        /// </summary>
        public string ProgressText
        {
            get { return (string)GetValue(ProgressTextProperty); }
            set { SetValue(ProgressTextProperty, value); }
        }
        
        /// <summary>
        /// Gets or sets the progress value
        /// </summary>
        public double ProgressValue
        {
            get { return (double)GetValue(ProgressValueProperty); }
            set { SetValue(ProgressValueProperty, value); }
        }
        
        /// <summary>
        /// Gets or sets the maximum value
        /// </summary>
        public double MaximumValue
        {
            get { return (double)GetValue(MaximumValueProperty); }
            set { SetValue(MaximumValueProperty, value); }
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether the progress bar is in indeterminate mode
        /// </summary>
        public bool IsIndeterminate
        {
            get { return (bool)GetValue(IsIndeterminateProperty); }
            set { SetValue(IsIndeterminateProperty, value); }
        }
        
        /// <summary>
        /// Initializes a new instance of the AnimatedProgressBar class
        /// </summary>
        public AnimatedProgressBar()
        {
            InitializeComponent();
            
            // Cache animations
            _progressAnimation = (Storyboard)FindResource("ProgressAnimation");
            _pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            _indeterminateAnimation = (Storyboard)FindResource("IndeterminateAnimation");
            
            // Start pulse animation
            _pulseAnimation.Begin(this, true);
            
            // Update progress
            UpdateProgress(ProgressValue, MaximumValue);
        }
        
        /// <summary>
        /// Updates the progress
        /// </summary>
        /// <param name="value">The progress value</param>
        /// <param name="maximum">The maximum value</param>
        private void UpdateProgress(double value, double maximum)
        {
            if (_isIndeterminate)
                return;
                
            // Calculate percentage
            double percentage = maximum > 0 ? Math.Min(value / maximum, 1.0) : 0;
            
            // Update progress text if not set
            if (string.IsNullOrEmpty(ProgressText))
            {
                ProgressText = $"{Math.Round(percentage * 100)}%";
            }
            
            // Calculate width
            double width = ActualWidth * percentage;
            
            // Clone the animation to avoid conflicts
            Storyboard animation = _progressAnimation.Clone();
            
            // Set the target width
            DoubleAnimation widthAnimation = animation.Children[0] as DoubleAnimation;
            if (widthAnimation != null)
            {
                widthAnimation.To = width;
            }
            
            // Start the animation
            animation.Begin(this);
        }
        
        /// <summary>
        /// Handles the progress value changed event
        /// </summary>
        /// <param name="d">The dependency object</param>
        /// <param name="e">The event args</param>
        private static void OnProgressValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            AnimatedProgressBar progressBar = (AnimatedProgressBar)d;
            double value = (double)e.NewValue;
            
            progressBar.UpdateProgress(value, progressBar.MaximumValue);
        }
        
        /// <summary>
        /// Handles the is indeterminate changed event
        /// </summary>
        /// <param name="d">The dependency object</param>
        /// <param name="e">The event args</param>
        private static void OnIsIndeterminateChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            AnimatedProgressBar progressBar = (AnimatedProgressBar)d;
            bool isIndeterminate = (bool)e.NewValue;
            
            progressBar._isIndeterminate = isIndeterminate;
            
            if (isIndeterminate)
            {
                // Show indeterminate indicator
                progressBar.IndeterminateIndicator.Visibility = Visibility.Visible;
                progressBar.ProgressIndicator.Visibility = Visibility.Collapsed;
                
                // Start indeterminate animation
                progressBar._indeterminateAnimation.Begin(progressBar, true);
            }
            else
            {
                // Show progress indicator
                progressBar.IndeterminateIndicator.Visibility = Visibility.Collapsed;
                progressBar.ProgressIndicator.Visibility = Visibility.Visible;
                
                // Stop indeterminate animation
                progressBar._indeterminateAnimation.Stop(progressBar);
                
                // Update progress
                progressBar.UpdateProgress(progressBar.ProgressValue, progressBar.MaximumValue);
            }
        }
    }
}
