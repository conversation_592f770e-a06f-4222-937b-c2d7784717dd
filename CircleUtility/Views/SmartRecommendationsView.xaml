<UserControl x:Class="CircleUtility.Views.SmartRecommendationsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Views"
             xmlns:viewModels="clr-namespace:CircleUtility.ViewModels"
             xmlns:models="clr-namespace:CircleUtility.Models" 
             xmlns:converters="clr-namespace:CircleUtility.Converters"
             mc:Ignorable="d" 
             d:DataContext="{d:DesignInstance viewModels:SmartRecommendationsViewModel, IsDesignTimeCreatable=True}"
             d:DesignHeight="600" d:DesignWidth="800">
    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
    </UserControl.Resources>
    <UserControl.DataContext>
        <viewModels:SmartRecommendationsViewModel/>
    </UserControl.DataContext>
    <Grid Background="#FF2D2D30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <StackPanel Orientation="Horizontal" Margin="15">
            <Button Content="Refresh Recommendations" 
                    Command="{Binding LoadRecommendationsCommand}" 
                    IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBooleanConverter}}" 
                    Padding="10,5" Background="#FF007ACC" Foreground="White" BorderThickness="0"/>
            <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center" Margin="15,0" Foreground="WhiteSmoke"/>
            <ProgressBar IsIndeterminate="True" Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}" Width="120" Height="20" VerticalAlignment="Center"/>
        </StackPanel>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="10,0,10,10">
            <ItemsControl ItemsSource="{Binding RecommendedTweaks}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate DataType="{x:Type models:SmartTweak}">
                        <Border BorderBrush="#FF4A4A4E" BorderThickness="1" Padding="15" Margin="0,0,0,10" Background="#FF3F3F46">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Text="{Binding SettingName}" FontWeight="Bold" FontSize="16" Grid.ColumnSpan="2" Foreground="WhiteSmoke"/>
                                
                                <TextBlock Text="{Binding Description}" TextWrapping="Wrap" Grid.Row="1" Grid.ColumnSpan="2" Margin="0,8,0,0" Foreground="#FFDCDCDC"/>
                                
                                <StackPanel Orientation="Horizontal" Grid.Row="2" Grid.ColumnSpan="2" Margin="0,8,0,0">
                                    <TextBlock Text="Category: " FontWeight="SemiBold" Foreground="#FFAAAAAA"/>
                                    <TextBlock Text="{Binding Category}" Foreground="#FFAAAAAA"/>
                                    <TextBlock Text=" | Requires Restart: " FontWeight="SemiBold" Margin="15,0,0,0" Foreground="#FFAAAAAA" Visibility="{Binding RequiresRestart, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    <TextBlock Text="Yes" Foreground="#FFFFC107" Visibility="{Binding RequiresRestart, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Grid.Row="3" Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Bottom" Margin="0,15,0,0">
                                    <Button Content="Apply Tweak" 
                                            Command="{Binding DataContext.ApplySmartTweakCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                            CommandParameter="{Binding}" 
                                            Visibility="{Binding IsCurrentlyApplied, Converter={StaticResource InverseBooleanToVisibilityConverter}}"
                                            IsEnabled="{Binding DataContext.IsBusy, RelativeSource={RelativeSource AncestorType=ItemsControl}, Converter={StaticResource InverseBooleanConverter}}" 
                                            Margin="5" Padding="12,6" Background="#FF28A745" Foreground="White" BorderThickness="0"/>
                                    <Button Content="Revert Tweak" 
                                            Command="{Binding DataContext.RevertSmartTweakCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                            CommandParameter="{Binding}" 
                                            Visibility="{Binding IsCurrentlyApplied, Converter={StaticResource BooleanToVisibilityConverter}}"
                                            IsEnabled="{Binding DataContext.IsBusy, RelativeSource={RelativeSource AncestorType=ItemsControl}, Converter={StaticResource InverseBooleanConverter}}" 
                                            Margin="5" Padding="12,6" Background="#FFDC3545" Foreground="White" BorderThickness="0"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
        
        <TextBlock Grid.Row="2" Text="Note: Some tweaks may require a system restart to take full effect. The application may also require administrator privileges for certain operations." 
                   TextWrapping="Wrap" Margin="15" FontStyle="Italic" Opacity="0.7" Foreground="#FFB0B0B0"/>
    </Grid>
</UserControl> 