using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedLoadingSpinner.xaml
    /// </summary>
    public partial class AnimatedLoadingSpinner : UserControl
    {
        private Storyboard _rotationAnimation;
        private Storyboard _pulseAnimation;
        
        /// <summary>
        /// Spinner color dependency property
        /// </summary>
        public static readonly DependencyProperty SpinnerColorProperty =
            DependencyProperty.Register("SpinnerColor", typeof(Brush), typeof(AnimatedLoadingSpinner), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(0, 200, 255))));
                
        /// <summary>
        /// Text dependency property
        /// </summary>
        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register("Text", typeof(string), typeof(AnimatedLoadingSpinner), 
                new PropertyMetadata(string.Empty));
                
        /// <summary>
        /// Is active dependency property
        /// </summary>
        public static readonly DependencyProperty IsActiveProperty =
            DependencyProperty.Register("IsActive", typeof(bool), typeof(AnimatedLoadingSpinner), 
                new PropertyMetadata(true, OnIsActiveChanged));
                
        /// <summary>
        /// Gets or sets the spinner color
        /// </summary>
        public Brush SpinnerColor
        {
            get { return (Brush)GetValue(SpinnerColorProperty); }
            set { SetValue(SpinnerColorProperty, value); }
        }
        
        /// <summary>
        /// Gets or sets the text
        /// </summary>
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether the spinner is active
        /// </summary>
        public bool IsActive
        {
            get { return (bool)GetValue(IsActiveProperty); }
            set { SetValue(IsActiveProperty, value); }
        }
        
        /// <summary>
        /// Initializes a new instance of the AnimatedLoadingSpinner class
        /// </summary>
        public AnimatedLoadingSpinner()
        {
            InitializeComponent();
            
            // Cache animations
            _rotationAnimation = (Storyboard)FindResource("RotationAnimation");
            _pulseAnimation = (Storyboard)FindResource("PulseAnimation");
        }
        
        /// <summary>
        /// Handles the loaded event
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (IsActive)
            {
                StartAnimations();
            }
        }
        
        /// <summary>
        /// Starts the animations
        /// </summary>
        public void StartAnimations()
        {
            _rotationAnimation.Begin(this, true);
            _pulseAnimation.Begin(this, true);
        }
        
        /// <summary>
        /// Stops the animations
        /// </summary>
        public void StopAnimations()
        {
            _rotationAnimation.Stop(this);
            _pulseAnimation.Stop(this);
        }
        
        /// <summary>
        /// Handles the IsActive property changed event
        /// </summary>
        /// <param name="d">The dependency object</param>
        /// <param name="e">The event args</param>
        private static void OnIsActiveChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            AnimatedLoadingSpinner spinner = (AnimatedLoadingSpinner)d;
            bool isActive = (bool)e.NewValue;
            
            if (isActive)
            {
                spinner.StartAnimations();
            }
            else
            {
                spinner.StopAnimations();
            }
        }
    }
    
    /// <summary>
    /// Converter that returns half the size of a value
    /// </summary>
    public class HalfSizeConverter : System.Windows.Data.IValueConverter
    {
        /// <summary>
        /// Converts a value to half its size
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">The parameter</param>
        /// <param name="culture">The culture</param>
        /// <returns>Half the size of the value</returns>
        public object Convert(object value, System.Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is double)
            {
                return (double)value / 2;
            }
            
            return 0;
        }
        
        /// <summary>
        /// Converts a value back
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">The parameter</param>
        /// <param name="culture">The culture</param>
        /// <returns>The original value</returns>
        public object ConvertBack(object value, System.Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new System.NotImplementedException();
        }
    }
    
    /// <summary>
    /// Converter that converts a brush to a color
    /// </summary>
    public class ColorConverter : System.Windows.Data.IValueConverter
    {
        /// <summary>
        /// Converts a brush to a color
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">The parameter</param>
        /// <param name="culture">The culture</param>
        /// <returns>The color</returns>
        public object Convert(object value, System.Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is SolidColorBrush)
            {
                return ((SolidColorBrush)value).Color;
            }
            
            return Colors.White;
        }
        
        /// <summary>
        /// Converts a color back to a brush
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">The parameter</param>
        /// <param name="culture">The culture</param>
        /// <returns>The brush</returns>
        public object ConvertBack(object value, System.Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new System.NotImplementedException();
        }
    }
    
    /// <summary>
    /// Converter that converts a string to visibility
    /// </summary>
    public class StringToVisibilityConverter : System.Windows.Data.IValueConverter
    {
        /// <summary>
        /// Converts a string to visibility
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">The parameter</param>
        /// <param name="culture">The culture</param>
        /// <returns>The visibility</returns>
        public object Convert(object value, System.Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is string && !string.IsNullOrEmpty((string)value))
            {
                return Visibility.Visible;
            }
            
            return Visibility.Collapsed;
        }
        
        /// <summary>
        /// Converts visibility back to a string
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">The parameter</param>
        /// <param name="culture">The culture</param>
        /// <returns>The string</returns>
        public object ConvertBack(object value, System.Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new System.NotImplementedException();
        }
    }
}
