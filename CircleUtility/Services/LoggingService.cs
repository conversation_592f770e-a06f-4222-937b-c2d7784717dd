using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using CircleUtility.Interfaces;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Enhanced LoggingService with improved error handling and shutdown support
    /// </summary>
    public class LoggingService : ILoggerService, IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly string _logDirectory;
        private string _logFilePath;
        private readonly ConcurrentQueue<LogEntry> _logEntries;
        private readonly SemaphoreSlim _logLock = new(1, 1);
        private readonly LogLevel _minimumLogLevel;
        private readonly long _maxFileSizeBytes;
        private readonly int _maxRetainedFiles;
        private readonly TimeSpan _maxLogAge;
        private bool _disposed;
        private bool _isShuttingDown;
        private readonly SemaphoreSlim _shutdownLock = new(1, 1);
        private readonly Timer _flushTimer;
        private readonly CancellationTokenSource _shutdownCts = new();

        /// <summary>
        /// Event raised when a new log entry is added
        /// </summary>
        public event EventHandler<LogEntryEventArgs> LogEntryAdded;

        /// <summary>
        /// Initializes the logging service with the specified configuration
        /// </summary>
        /// <param name="configuration">The configuration to use</param>
        public LoggingService(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            
            // Get log directory from configuration or use default
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                _configuration["FilePaths:LogsDirectory"] ?? "Logs");
                
            // Ensure log directory exists
            Directory.CreateDirectory(_logDirectory);
            
            // Set up log file path with rolling
            var logFileName = _configuration["Logging:File:Path"] ?? "CircleUtility.log";
            _logFilePath = GetCurrentLogFilePath(logFileName);
                
            _logEntries = new ConcurrentQueue<LogEntry>();
            
            // Parse configuration
            long maxFileSizeMB = 10;
            if (long.TryParse(_configuration["Logging:File:MaxFileSizeMB"], out var configuredMaxFileSizeMB)) maxFileSizeMB = configuredMaxFileSizeMB;
            _maxFileSizeBytes = maxFileSizeMB * 1024 * 1024;

            int maxRetainedFiles = 31;
            if (int.TryParse(_configuration["Logging:File:MaxRetainedFiles"], out var configuredMaxRetainedFiles)) maxRetainedFiles = configuredMaxRetainedFiles;
            _maxRetainedFiles = maxRetainedFiles;

            int maxAgeInDays = 30;
            if (int.TryParse(_configuration["Logging:File:MaxAgeInDays"], out var configuredMaxAgeInDays)) maxAgeInDays = configuredMaxAgeInDays;
            _maxLogAge = TimeSpan.FromDays(maxAgeInDays);
            
            // Parse minimum log level from config
            if (!Enum.TryParse<LogLevel>(
                _configuration["Logging:LogLevel:Default"] ?? "Information", 
                out _minimumLogLevel))
            {
                _minimumLogLevel = LogLevel.INFO;
            }
            
            // Clean up old log files
            Task.Run(CleanupOldLogs);
            
            // Start periodic flush timer (every 5 seconds)
            _flushTimer = new Timer(
                callback: _ => _ = FlushLogsAsync().ConfigureAwait(false),
                state: null,
                dueTime: TimeSpan.FromSeconds(5),
                period: TimeSpan.FromSeconds(5));
            
            Log("Logging service initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Shuts down the logging service, ensuring all pending logs are written
        /// </summary>
        public async Task ShutdownAsync(TimeSpan timeout = default)
        {
            if (_isShuttingDown)
                return;

            await _shutdownLock.WaitAsync();
            try
            {
                if (_isShuttingDown)
                    return;

                _isShuttingDown = true;
                _shutdownCts.Cancel();
                
                // Stop the flush timer
                _flushTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _flushTimer?.Dispose();

                // Flush any remaining logs with a timeout
                if (!_logEntries.IsEmpty)
                {
                    using var cts = new CancellationTokenSource(timeout == default ? TimeSpan.FromSeconds(5) : timeout);
                    await FlushLogsAsync(cts.Token);
                }
            }
            finally
            {
                _shutdownLock.Release();
            }
        }

        /// <summary>
        /// Flushes all pending log entries to disk
        /// </summary>
        public async Task FlushLogsAsync(CancellationToken cancellationToken = default)
        {
            if (_logEntries.IsEmpty || _isShuttingDown || cancellationToken.IsCancellationRequested)
                return;

            if (!await _logLock.WaitAsync(TimeSpan.FromSeconds(5), cancellationToken))
                return;

            try
            {
                var entries = new List<LogEntry>();
                while (_logEntries.TryDequeue(out var entry))
                {
                    entries.Add(entry);
                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                if (entries.Count > 0 && !cancellationToken.IsCancellationRequested)
                {
                    foreach (var entry in entries)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;
                            
                        await WriteToLogFileAsync(entry, cancellationToken);
                    }
                }
            }
            catch (Exception ex) when (ex is OperationCanceledException or ObjectDisposedException)
            {
                // Ignore during shutdown
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error flushing logs: {ex}");
            }
            finally
            {
                _logLock.Release();
            }
        }

        private async Task WriteToLogFileAsync(LogEntry entry, CancellationToken cancellationToken = default)
        {
            if (_isShuttingDown || cancellationToken.IsCancellationRequested)
                return;

            try
            {
                await _logLock.WaitAsync(cancellationToken);
                
                if (_isShuttingDown || cancellationToken.IsCancellationRequested)
                    return;

                // Check if we need to rotate the log file
                var fileInfo = new FileInfo(_logFilePath);
                if (fileInfo.Exists && fileInfo.Length > _maxFileSizeBytes)
                {
                    await RotateLogFilesAsync(cancellationToken);
                }

                // Write the log entry
                await File.AppendAllTextAsync(_logFilePath, $"{entry}\n", cancellationToken);
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                // Ignore during shutdown
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error writing to log file: {ex}");
                throw;
            }
            finally
            {
                _logLock.Release();
            }
        }

        private async Task SafeWriteToLogFileAsync(LogEntry entry)
        {
            try
            {
                await WriteToLogFileAsync(entry, _shutdownCts.Token).ConfigureAwait(false);
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine($"SafeWriteToLogFileAsync failed: {ex}");
            }
        }

        public void Log(string message, LogLevel level, Exception exception = null)
        {
            if (_isShuttingDown || level < _minimumLogLevel || string.IsNullOrEmpty(message))
                return;

            try
            {
                var entry = new LogEntry
                {
                    Timestamp = DateTime.UtcNow,
                    Message = message,
                    Level = level,
                    Exception = exception,
                    ThreadId = Environment.CurrentManagedThreadId
                };

                _logEntries.Enqueue(entry);
                
                // Log to console if enabled
                bool consoleLoggingEnabled = true;
                if (bool.TryParse(_configuration["Logging:Console:Enabled"], out var configuredConsoleLogging)) consoleLoggingEnabled = configuredConsoleLogging;
                if (consoleLoggingEnabled)
                {
                    Console.WriteLine($"[{entry.Timestamp:HH:mm:ss}] [{entry.Level}] {entry.Message}");
                    if (exception != null)
                    {
                        Console.WriteLine($"Exception: {exception}");
                    }
                }

                // Write to log file if enabled (fire and forget with error handling)
                bool fileLoggingEnabled = true;
                if (bool.TryParse(_configuration["Logging:File:Enabled"], out var configuredFileLogging)) fileLoggingEnabled = configuredFileLogging;
                if (fileLoggingEnabled)
                {
                    _ = SafeWriteToLogFileAsync(entry);
                }

                // Raise event
                LogEntryAdded?.Invoke(this, new LogEntryEventArgs(entry));
            }
            catch (Exception ex)
            {
                // If logging fails, write to debug output as a last resort
                System.Diagnostics.Debug.WriteLine($"Logging failed: {ex}");
            }
        }

        /// <summary>
        /// Synchronous shutdown for scenarios where async isn't possible
        /// </summary>
        public void Shutdown(TimeSpan timeout = default)
        {
            ShutdownAsync(timeout).GetAwaiter().GetResult();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Ensure we're shut down
                    try { Shutdown(TimeSpan.FromSeconds(2)); } catch { /* Ignore */ }
                    
                    _shutdownCts?.Dispose();
                    _logLock?.Dispose();
                    _shutdownLock?.Dispose();
                    _flushTimer?.Dispose();
                }
                _disposed = true;
            }
        }

        // Rest of the existing methods...
        public IReadOnlyList<LogEntry> LogEntries => new List<LogEntry>(_logEntries);
        public LogLevel MinimumLogLevel => _minimumLogLevel;
        
        private string GetCurrentLogFilePath(string baseFileName)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            var fileName = Path.GetFileNameWithoutExtension(baseFileName);
            var extension = Path.GetExtension(baseFileName);
            return Path.Combine(_logDirectory, $"{fileName}_{timestamp}{extension}");
        }

        private async Task RotateLogFilesAsync(CancellationToken cancellationToken = default)
        {
            // Implementation for log rotation
            // ...
            await Task.CompletedTask;
        }

        private async Task CleanupOldLogs()
        {
            // Implementation for cleaning up old logs
            // ...
            await Task.CompletedTask;
        }

        public async Task LogAsync(string message, LogLevel level, Exception exception = null)
        {
            if (_isShuttingDown || level < _minimumLogLevel || string.IsNullOrEmpty(message))
                return;

            var logEntry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = level,
                Message = message,
                Exception = exception,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logEntries.Enqueue(logEntry);
            await SafeWriteToLogFileAsync(logEntry); 
            LogEntryAdded?.Invoke(this, new LogEntryEventArgs(logEntry));
        }

        public void ClearLogs()
        {
            // This method could be implemented to clear the log queue and/or delete log files
            // For now, it will clear the in-memory queue
            while (_logEntries.TryDequeue(out _)) { }
            Log("Log entries cleared.", LogLevel.INFO);
        }

        public string GetLogFilePath()
        {
            return _logFilePath;
        }

        public IEnumerable<LogEntry> GetLogsByLevel(LogLevel level)
        {
            return _logEntries.Where(entry => entry.Level == level).ToList();
        }

        public List<LogEntry> GetLogsByDateRange(DateTime startDate, DateTime endDate)
        {
            return _logEntries.Where(entry => entry.Timestamp >= startDate && entry.Timestamp <= endDate).ToList();
        }

        public List<LogEntry> GetLogsBySearchText(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return new List<LogEntry>();
            return _logEntries.Where(entry => 
                (entry.Message != null && entry.Message.Contains(searchText, StringComparison.OrdinalIgnoreCase)) || 
                (entry.Exception != null && entry.Exception.ToString().Contains(searchText, StringComparison.OrdinalIgnoreCase))).ToList();
        }

        public async Task<bool> ExportLogsAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentNullException(nameof(filePath));

            try
            {
                await _logLock.WaitAsync();
                var logsToExport = _logEntries.ToList(); // Create a copy to avoid issues if new logs come in
                var sb = new StringBuilder();
                foreach (var logEntry in logsToExport)
                {
                    sb.AppendLine(logEntry.ToString()); // Assuming LogEntry has a decent ToString()
                }
                await File.WriteAllTextAsync(filePath, sb.ToString());
                Log($"Logs exported to {filePath}", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                Log($"Failed to export logs to {filePath}", LogLevel.ERROR, ex);
                return false;
            }
            finally
            {
                _logLock.Release();
            }
        }

        public LogLevel LogLevel { get; set; }

        // Helper methods for specific log levels
        public void LogDebug(string message) => Log(message, LogLevel.DEBUG);
        public void LogInfo(string message) => Log(message, LogLevel.INFO);
        public void LogSuccess(string message) => Log(message, LogLevel.SUCCESS);
        public void LogWarning(string message) => Log(message, LogLevel.WARNING);
        public void LogError(string message, Exception ex = null) => Log(message, LogLevel.ERROR, ex);
        public void LogCritical(string message, Exception ex = null) => Log(message, LogLevel.CRITICAL, ex);
    }
}
