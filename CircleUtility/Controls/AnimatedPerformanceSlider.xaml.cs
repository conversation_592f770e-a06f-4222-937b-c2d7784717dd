using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using CircleUtility.Services;
using CircleUtility.Models;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedPerformanceSlider.xaml
    /// </summary>
    public partial class AnimatedPerformanceSlider : UserControl
    {
        private readonly LoggingService _logger;
        private readonly Storyboard _sliderGlowAnimation;
        private readonly Storyboard _thumbGlowAnimation;
        private readonly Storyboard _valueChangeAnimation;
        private readonly Storyboard _profileChangeAnimation;
        private bool _isInitialized;
        private bool _suppressEvents;

        /// <summary>
        /// Event raised when the performance profile changes
        /// </summary>
        public event EventHandler<PerformanceProfileChangedEventArgs> ProfileChanged;

        /// <summary>
        /// Gets or sets the title of the slider
        /// </summary>
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        /// <summary>
        /// Dependency property for Title
        /// </summary>
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata("Performance"));

        /// <summary>
        /// Gets or sets the value of the slider
        /// </summary>
        public double Value
        {
            get { return (double)GetValue(ValueProperty); }
            set { SetValue(ValueProperty, value); }
        }

        /// <summary>
        /// Dependency property for Value
        /// </summary>
        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register("Value", typeof(double), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata(50.0, OnValueChanged));

        private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AnimatedPerformanceSlider slider && !slider._suppressEvents)
            {
                slider.UpdateProfileFromValue((double)e.NewValue);
            }
        }

        /// <summary>
        /// Gets or sets the current performance profile
        /// </summary>
        public string CurrentProfile
        {
            get { return (string)GetValue(CurrentProfileProperty); }
            set { SetValue(CurrentProfileProperty, value); }
        }

        /// <summary>
        /// Dependency property for CurrentProfile
        /// </summary>
        public static readonly DependencyProperty CurrentProfileProperty =
            DependencyProperty.Register("CurrentProfile", typeof(string), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata("Balanced"));

        /// <summary>
        /// Gets or sets the component type this slider controls
        /// </summary>
        public PerformanceComponentType ComponentType
        {
            get { return (PerformanceComponentType)GetValue(ComponentTypeProperty); }
            set { SetValue(ComponentTypeProperty, value); }
        }

        /// <summary>
        /// Dependency property for ComponentType
        /// </summary>
        public static readonly DependencyProperty ComponentTypeProperty =
            DependencyProperty.Register("ComponentType", typeof(PerformanceComponentType), typeof(AnimatedPerformanceSlider),
                new PropertyMetadata(PerformanceComponentType.System));

        /// <summary>
        /// Initializes a new instance of the AnimatedPerformanceSlider class
        /// </summary>
        public AnimatedPerformanceSlider()
        {
            InitializeComponent();

            // Initialize services
            _logger = LoggingService.Instance;

            // Get animations from resources
            _sliderGlowAnimation = (Storyboard)FindResource("SliderGlowAnimation");
            _thumbGlowAnimation = (Storyboard)FindResource("ThumbGlowAnimation");
            _valueChangeAnimation = (Storyboard)FindResource("ValueChangeAnimation");
            _profileChangeAnimation = (Storyboard)FindResource("ProfileChangeAnimation");

            // Set default values
            Title = "Performance";
            Value = 50.0;
            CurrentProfile = "Balanced";
            ComponentType = PerformanceComponentType.System;
        }

        /// <summary>
        /// Handles the Loaded event for the user control
        /// </summary>
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (!_isInitialized)
            {
                // Start animations
                _sliderGlowAnimation.Begin(this, true);
                _thumbGlowAnimation.Begin(this, true);

                // Show initial profile
                _profileChangeAnimation.Begin(this);

                _isInitialized = true;
            }
        }

        /// <summary>
        /// Handles the ValueChanged event for the slider
        /// </summary>
        private void MainSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_isInitialized && !_suppressEvents)
            {
                // Play value change animation
                _valueChangeAnimation.Begin(this);
            }
        }

        /// <summary>
        /// Updates the profile based on the current value
        /// </summary>
        private void UpdateProfileFromValue(double value)
        {
            string newProfile;

            if (value < 25)
            {
                newProfile = "Power Saver";
            }
            else if (value < 75)
            {
                newProfile = "Balanced";
            }
            else
            {
                newProfile = "Performance";
            }

            if (newProfile != CurrentProfile)
            {
                CurrentProfile = newProfile;
                _profileChangeAnimation.Begin(this);

                // Raise profile changed event
                ProfileChanged?.Invoke(this, new PerformanceProfileChangedEventArgs
                {
                    ComponentType = ComponentType,
                    ProfileName = CurrentProfile,
                    Value = Value
                });

                _logger.Log($"{ComponentType} profile changed to {CurrentProfile} ({Value}%)", LogLevel.INFO);
            }
        }

        /// <summary>
        /// Sets the slider to the Power Saver profile
        /// </summary>
        private void PowerSaverButton_Click(object sender, RoutedEventArgs e)
        {
            _suppressEvents = true;
            Value = 0;
            _suppressEvents = false;
            UpdateProfileFromValue(Value);
            _valueChangeAnimation.Begin(this);
        }

        /// <summary>
        /// Sets the slider to the Balanced profile
        /// </summary>
        private void BalancedButton_Click(object sender, RoutedEventArgs e)
        {
            _suppressEvents = true;
            Value = 50;
            _suppressEvents = false;
            UpdateProfileFromValue(Value);
            _valueChangeAnimation.Begin(this);
        }

        /// <summary>
        /// Sets the slider to the Performance profile
        /// </summary>
        private void PerformanceButton_Click(object sender, RoutedEventArgs e)
        {
            _suppressEvents = true;
            Value = 100;
            _suppressEvents = false;
            UpdateProfileFromValue(Value);
            _valueChangeAnimation.Begin(this);
        }
    }

    /// <summary>
    /// Event arguments for the ProfileChanged event
    /// </summary>
    public class PerformanceProfileChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the component type
        /// </summary>
        public PerformanceComponentType ComponentType { get; set; }

        /// <summary>
        /// Gets or sets the profile name
        /// </summary>
        public string ProfileName { get; set; }

        /// <summary>
        /// Gets or sets the value
        /// </summary>
        public double Value { get; set; }
    }
}
