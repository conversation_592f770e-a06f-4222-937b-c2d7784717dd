using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts a string equality check to a style
    /// </summary>
    public class StringEqualityToStyleConverter : IValueConverter
    {
        /// <summary>
        /// Converts a value
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
            {
                return Application.Current.FindResource("AdminTabButtonStyle");
            }

            string stringValue = value.ToString();
            string targetValue = parameter.ToString();

            return stringValue == targetValue
                ? Application.Current.FindResource("AdminTabButtonActiveStyle")
                : Application.Current.FindResource("AdminTabButtonStyle");
        }

        /// <summary>
        /// Converts a value back
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
