using System;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Base interface for full screens/pages
    /// </summary>
    public interface IScreen : IUIComponent
    {
        /// <summary>
        /// Gets the title of the screen
        /// </summary>
        string Title { get; }

        /// <summary>
        /// Gets a value indicating whether navigation back is possible
        /// </summary>
        bool CanNavigateBack { get; }

        /// <summary>
        /// Navigates back to the previous screen
        /// </summary>
        void NavigateBack();
    }
}
