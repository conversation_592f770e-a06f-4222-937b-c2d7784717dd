namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for configuration service
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// Gets a setting value
        /// </summary>
        /// <typeparam name="T">The type of the setting</typeparam>
        /// <param name="section">The section name</param>
        /// <param name="key">The setting key</param>
        /// <returns>The setting value</returns>
        T GetSetting<T>(string section, string key);

        /// <summary>
        /// Sets a setting value
        /// </summary>
        /// <typeparam name="T">The type of the setting</typeparam>
        /// <param name="section">The section name</param>
        /// <param name="key">The setting key</param>
        /// <param name="value">The setting value</param>
        void SetSetting<T>(string section, string key, T value);
    }
}
