using System;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for performance monitoring service
    /// </summary>
    public interface IPerformanceMonitoringService
    {
        /// <summary>
        /// Gets whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Gets whether monitoring is active
        /// </summary>
        bool IsMonitoringActive { get; }

        /// <summary>
        /// Event raised when metrics are updated
        /// </summary>
        event EventHandler<PerformanceMetricsEventArgs> MetricsUpdated;

        /// <summary>
        /// Gets performance metrics
        /// </summary>
        /// <returns>Performance metrics</returns>
        object GetPerformanceMetrics();

        /// <summary>
        /// Gets current metrics
        /// </summary>
        /// <returns>Current performance metrics</returns>
        object GetCurrentMetrics();

        /// <summary>
        /// Starts monitoring performance
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// Stops monitoring performance
        /// </summary>
        void StopMonitoring();
    }
}
