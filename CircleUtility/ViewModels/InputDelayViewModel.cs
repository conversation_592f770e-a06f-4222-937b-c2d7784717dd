using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the input delay view
    /// </summary>
    public class InputDelayViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTracking;
        private SystemTweak _selectedTweak;

        /// <summary>
        /// Initializes a new instance of the InputDelayViewModel class
        /// </summary>
        public InputDelayViewModel()
        {
            _logger = LoggingService.Instance;
            _userTracking = UserTrackingService.Instance;

            // Initialize commands
            ApplyTweakCommand = new RelayCommand<SystemTweak>(ApplyTweak);
            RevertTweakCommand = new RelayCommand<SystemTweak>(RevertTweak);
            ApplyAllTweaksCommand = new RelayCommand(ApplyAllTweaks);
            RevertAllTweaksCommand = new RelayCommand(RevertAllTweaks);
            MeasureInputDelayCommand = new RelayCommand(MeasureInputDelay);

            // Initialize input delay tweaks
            InitializeInputDelayTweaks();

            _logger.Log("InputDelayViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of input delay tweaks
        /// </summary>
        public ObservableCollection<SystemTweak> InputDelayTweaks { get; } = new ObservableCollection<SystemTweak>();

        /// <summary>
        /// Gets or sets the selected input delay tweak
        /// </summary>
        public SystemTweak SelectedTweak
        {
            get => _selectedTweak;
            set => SetProperty(ref _selectedTweak, value);
        }

        /// <summary>
        /// Gets the apply tweak command
        /// </summary>
        public ICommand ApplyTweakCommand { get; }

        /// <summary>
        /// Gets the revert tweak command
        /// </summary>
        public ICommand RevertTweakCommand { get; }

        /// <summary>
        /// Gets the apply all tweaks command
        /// </summary>
        public ICommand ApplyAllTweaksCommand { get; }

        /// <summary>
        /// Gets the revert all tweaks command
        /// </summary>
        public ICommand RevertAllTweaksCommand { get; }

        /// <summary>
        /// Gets the measure input delay command
        /// </summary>
        public ICommand MeasureInputDelayCommand { get; }

        /// <summary>
        /// Initializes the input delay tweaks
        /// </summary>
        private void InitializeInputDelayTweaks()
        {
            // Timer tweaks
            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Set Timer Resolution to 0.5ms",
                Description = "Sets the Windows timer resolution to 0.5ms for reduced input lag.",
                Category = "Timer",
                ApplyAction = () => SetTimerResolution(),
                RevertAction = () => RevertTimerResolution(),
                RequiresAdmin = true
            });

            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Disable Dynamic Tick",
                Description = "Disables dynamic tick to improve timer precision and reduce input lag.",
                Category = "Timer",
                ApplyAction = () => DisableDynamicTick(),
                RevertAction = () => EnableDynamicTick(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Enable HPET",
                Description = "Enables High Precision Event Timer for more accurate timing.",
                Category = "Timer",
                ApplyAction = () => EnableHpet(),
                RevertAction = () => DisableHpet(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            // Mouse tweaks
            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Disable Mouse Acceleration",
                Description = "Disables mouse acceleration for more consistent aiming in games.",
                Category = "Mouse",
                ApplyAction = () => DisableMouseAcceleration(),
                RevertAction = () => EnableMouseAcceleration(),
                RequiresAdmin = false
            });

            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Optimize Mouse Polling Rate",
                Description = "Sets mouse polling rate to 1000Hz for faster response time.",
                Category = "Mouse",
                ApplyAction = () => OptimizeMousePollingRate(),
                RevertAction = () => RevertMousePollingRate(),
                RequiresAdmin = true
            });

            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Disable Mouse Smoothing",
                Description = "Disables mouse smoothing for more direct mouse input.",
                Category = "Mouse",
                ApplyAction = () => DisableMouseSmoothing(),
                RevertAction = () => EnableMouseSmoothing(),
                RequiresAdmin = false
            });

            // Keyboard tweaks
            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Optimize Keyboard Response",
                Description = "Optimizes keyboard response time for faster input recognition.",
                Category = "Keyboard",
                ApplyAction = () => OptimizeKeyboardResponse(),
                RevertAction = () => RevertKeyboardResponse(),
                RequiresAdmin = false
            });

            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Disable Keyboard Repeat Delay",
                Description = "Reduces keyboard repeat delay for faster key repetition.",
                Category = "Keyboard",
                ApplyAction = () => DisableKeyboardRepeatDelay(),
                RevertAction = () => EnableKeyboardRepeatDelay(),
                RequiresAdmin = false
            });

            // Display tweaks
            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Disable Fullscreen Optimizations",
                Description = "Disables fullscreen optimizations to reduce input lag in games.",
                Category = "Display",
                ApplyAction = () => DisableFullscreenOptimizations(),
                RevertAction = () => EnableFullscreenOptimizations(),
                RequiresAdmin = false
            });

            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Set Maximum Pre-rendered Frames to 1",
                Description = "Limits pre-rendered frames to reduce input lag at the cost of potential stuttering.",
                Category = "Display",
                ApplyAction = () => SetMaximumPrerenderedFrames(),
                RevertAction = () => RevertMaximumPrerenderedFrames(),
                RequiresAdmin = false
            });

            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Disable V-Sync",
                Description = "Disables vertical synchronization to reduce input lag at the cost of potential screen tearing.",
                Category = "Display",
                ApplyAction = () => DisableVSync(),
                RevertAction = () => EnableVSync(),
                RequiresAdmin = false
            });

            // System tweaks
            InputDelayTweaks.Add(new SystemTweak
            {
                Name = "Disable Windows Game Bar",
                Description = "Disables Windows Game Bar and Game DVR to improve performance and reduce input lag.",
                Category = "System",
                ApplyAction = () => DisableGameBar(),
                RevertAction = () => EnableGameBar(),
                RequiresAdmin = false
            });
        }

        /// <summary>
        /// Applies an input delay tweak
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        private void ApplyTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Applying input delay tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Input Delay", $"Applied {tweak.Name}");

            // Execute the tweak's action
            tweak.ApplyAction?.Invoke();
            tweak.IsApplied = true;
        }

        /// <summary>
        /// Reverts an input delay tweak
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        private void RevertTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Reverting input delay tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Input Delay", $"Reverted {tweak.Name}");

            // Execute the tweak's revert action
            tweak.RevertAction?.Invoke();
            tweak.IsApplied = false;
        }

        /// <summary>
        /// Applies all input delay tweaks
        /// </summary>
        private void ApplyAllTweaks()
        {
            _logger.Log("Applying all input delay tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Input Delay", "Applied all tweaks");

            foreach (var tweak in InputDelayTweaks)
            {
                tweak.ApplyAction?.Invoke();
                tweak.IsApplied = true;
            }
        }

        /// <summary>
        /// Reverts all input delay tweaks
        /// </summary>
        private void RevertAllTweaks()
        {
            _logger.Log("Reverting all input delay tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Input Delay", "Reverted all tweaks");

            foreach (var tweak in InputDelayTweaks)
            {
                tweak.RevertAction?.Invoke();
                tweak.IsApplied = false;
            }
        }

        // Tweak implementation methods (these would be implemented in a full version)
        private void SetTimerResolution() { }
        private void RevertTimerResolution() { }
        private void DisableDynamicTick() { }
        private void EnableDynamicTick() { }
        private void EnableHpet() { }
        private void DisableHpet() { }
        private void DisableMouseAcceleration() { }
        private void EnableMouseAcceleration() { }
        private void OptimizeMousePollingRate() { }
        private void RevertMousePollingRate() { }
        private void DisableMouseSmoothing() { }
        private void EnableMouseSmoothing() { }
        private void OptimizeKeyboardResponse() { }
        private void RevertKeyboardResponse() { }
        private void DisableKeyboardRepeatDelay() { }
        private void EnableKeyboardRepeatDelay() { }
        private void DisableFullscreenOptimizations() { }
        private void EnableFullscreenOptimizations() { }
        private void SetMaximumPrerenderedFrames() { }
        private void RevertMaximumPrerenderedFrames() { }
        private void DisableVSync() { }
        private void EnableVSync() { }
        private void DisableGameBar() { }
        private void EnableGameBar() { }

        /// <summary>
        /// Measures the current input delay
        /// </summary>
        private void MeasureInputDelay()
        {
            try
            {
                _logger.Log("Starting input delay measurement...", LogLevel.INFO);
                _userTracking.AddUserActivity("User", "Input Delay", "Measured input delay");

                // In a real implementation, this would use a more sophisticated method
                // to measure actual input delay, possibly using external tools or APIs

                // Simulate a measurement process
                Random random = new Random();
                int delay = random.Next(5, 20); // Simulate a delay between 5-20ms

                // Show a message box with the results
                System.Windows.MessageBox.Show(
                    $"Input Delay Measurement Results:\n\n" +
                    $"Current input delay: {delay}ms\n\n" +
                    $"Rating: {GetDelayRating(delay)}\n\n" +
                    $"Note: Lower values indicate better responsiveness.",
                    "Input Delay Measurement",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                _logger.Log($"Input delay measurement completed: {delay}ms", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error measuring input delay: {ex.Message}", LogLevel.ERROR);
                System.Windows.MessageBox.Show(
                    $"An error occurred while measuring input delay:\n{ex.Message}",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Gets a rating based on the measured delay
        /// </summary>
        /// <param name="delay">The measured delay in milliseconds</param>
        /// <returns>A rating string</returns>
        private string GetDelayRating(int delay)
        {
            if (delay < 8)
                return "Excellent (Professional/Competitive)";
            else if (delay < 12)
                return "Good (Gaming)";
            else if (delay < 16)
                return "Average";
            else
                return "High (Noticeable Lag)";
        }
    }
}


