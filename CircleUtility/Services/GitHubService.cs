using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace CircleUtility.Services
{
    public class GitHubService
    {
        private readonly string _token;
        private readonly string _owner;
        private readonly string _repo;
        private readonly HttpClient _httpClient;

        public GitHubService(string token, string owner, string repo)
        {
            _token = token;
            _owner = owner;
            _repo = repo;
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.UserAgent.Add(new ProductInfoHeaderValue("CircleUtility", "1.0"));
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("token", _token);
        }

        public async Task<bool> UpdateFileAsync(string filePath, string newContent, string commitMessage)
        {
            // 1. Get the current file SHA
            var getUrl = $"https://api.github.com/repos/{_owner}/{_repo}/contents/{filePath}";
            var getResp = await _httpClient.GetAsync(getUrl);
            if (!getResp.IsSuccessStatusCode)
                return false;
            var getJson = await getResp.Content.ReadAsStringAsync();
            using var doc = JsonDocument.Parse(getJson);
            var sha = doc.RootElement.GetProperty("sha").GetString();

            // 2. Prepare the update payload
            var payload = new
            {
                message = commitMessage,
                content = Convert.ToBase64String(Encoding.UTF8.GetBytes(newContent)),
                sha = sha
            };
            var payloadJson = JsonSerializer.Serialize(payload);
            var putContent = new StringContent(payloadJson, Encoding.UTF8, "application/json");

            // 3. Send the update request
            var putResp = await _httpClient.PutAsync(getUrl, putContent);
            return putResp.IsSuccessStatusCode;
        }
    }
} 