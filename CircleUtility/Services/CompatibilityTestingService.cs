// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for testing compatibility of optimizations through benchmarking and stress testing
    /// </summary>
    public class CompatibilityTestingService
    {
        private static CompatibilityTestingService _instance;
        private readonly LoggingService _logger;
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IHardwareOptimizationService _optimizationService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;
        private readonly IBenchmarkingService _benchmarkingService;
        private bool _isInitialized;
        private bool _isTestingInProgress;
        private CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// Event raised when a test is started
        /// </summary>
        public event EventHandler<TestEventArgs> TestStarted;

        /// <summary>
        /// Event raised when a test is completed
        /// </summary>
        public event EventHandler<TestResultEventArgs> TestCompleted;

        /// <summary>
        /// Event raised when a test is cancelled
        /// </summary>
        public event EventHandler<TestEventArgs> TestCancelled;

        /// <summary>
        /// Event raised when test progress is updated
        /// </summary>
        public event EventHandler<TestProgressEventArgs> TestProgressUpdated;

        /// <summary>
        /// Initializes a new instance of the CompatibilityTestingService class
        /// </summary>
        private CompatibilityTestingService()
        {
            _logger = LoggingService.Instance;
            _hardwareDetectionService = HardwareDetectionService.Instance;
            _optimizationService = HardwareOptimizationService.Instance;
            _performanceMonitoringService = PerformanceMonitoringService.Instance;
            _benchmarkingService = BenchmarkingService.Instance;
            _isInitialized = false;
            _isTestingInProgress = false;
        }

        /// <summary>
        /// Initializes a new instance of the CompatibilityTestingService class with dependencies (for testing)
        /// </summary>
        /// <param name="hardwareDetectionService">The hardware detection service</param>
        /// <param name="optimizationService">The hardware optimization service</param>
        /// <param name="performanceMonitoringService">The performance monitoring service</param>
        /// <param name="benchmarkingService">The benchmarking service</param>
        public CompatibilityTestingService(
            IHardwareDetectionService hardwareDetectionService,
            IHardwareOptimizationService optimizationService,
            IPerformanceMonitoringService performanceMonitoringService,
            IBenchmarkingService benchmarkingService)
        {
            _logger = LoggingService.Instance;
            _hardwareDetectionService = hardwareDetectionService;
            _optimizationService = optimizationService;
            _performanceMonitoringService = performanceMonitoringService;
            _benchmarkingService = benchmarkingService;
            _isInitialized = false;
            _isTestingInProgress = false;
        }

        /// <summary>
        /// Gets the singleton instance of the compatibility testing service
        /// </summary>
        public static CompatibilityTestingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new CompatibilityTestingService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets a value indicating whether testing is in progress
        /// </summary>
        public bool IsTestingInProgress => _isTestingInProgress;

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task InitializeAsync()
        {
            if (_isInitialized)
            {
                return;
            }

            try
            {
                _logger.Log("Initializing compatibility testing service...", LogLevel.INFO);

                // Wait for other services to initialize
                if (!_benchmarkingService.IsInitialized)
                {
                    await _benchmarkingService.InitializeAsync();
                }

                _isInitialized = true;
                _logger.Log("Compatibility testing service initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing compatibility testing service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tests an optimization for compatibility
        /// </summary>
        /// <param name="optimization">The optimization to test</param>
        /// <param name="testDuration">The duration of the test in seconds</param>
        /// <returns>The test result</returns>
        public async Task<CompatibilityTestResult> TestOptimizationAsync(HardwareSpecificOptimization optimization, int testDuration = 60)
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
            }

            if (_isTestingInProgress)
            {
                throw new InvalidOperationException("A test is already in progress");
            }

            _isTestingInProgress = true;
            _cancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = _cancellationTokenSource.Token;

            CompatibilityTestResult result = new CompatibilityTestResult
            {
                OptimizationName = optimization.Name,
                StartTime = DateTime.Now,
                TestDuration = testDuration,
                IsSuccessful = false
            };

            try
            {
                // Raise test started event
                TestStarted?.Invoke(this, new TestEventArgs
                {
                    OptimizationName = optimization.Name,
                    TestType = "Optimization Compatibility Test",
                    StartTime = result.StartTime
                });

                // Update progress
                TestProgressUpdated?.Invoke(this, new TestProgressEventArgs
                {
                    OptimizationName = optimization.Name,
                    Progress = 0,
                    Status = "Starting baseline benchmark..."
                });

                // Run baseline benchmark
                _logger.Log($"Running baseline benchmark for optimization '{optimization.Name}'...", LogLevel.INFO);
                var baselineResult = await _benchmarkingService.RunBenchmarkAsync(BenchmarkType.Quick);
                result.BaselineBenchmarkScore = baselineResult.Score;

                if (cancellationToken.IsCancellationRequested)
                {
                    throw new OperationCanceledException();
                }

                // Update progress
                TestProgressUpdated?.Invoke(this, new TestProgressEventArgs
                {
                    OptimizationName = optimization.Name,
                    Progress = 20,
                    Status = "Applying optimization..."
                });

                // Apply the optimization
                _logger.Log($"Applying optimization '{optimization.Name}' for testing...", LogLevel.INFO);
                bool applySuccess = await _optimizationService.ApplyOptimizationAsync(optimization, true);

                if (!applySuccess)
                {
                    result.ErrorMessage = "Failed to apply optimization";
                    return result;
                }

                if (cancellationToken.IsCancellationRequested)
                {
                    // Revert the optimization before cancelling
                    await _optimizationService.RevertOptimizationAsync(optimization);
                    throw new OperationCanceledException();
                }

                // Update progress
                TestProgressUpdated?.Invoke(this, new TestProgressEventArgs
                {
                    OptimizationName = optimization.Name,
                    Progress = 30,
                    Status = "Running optimized benchmark..."
                });

                // Run optimized benchmark
                _logger.Log($"Running optimized benchmark for optimization '{optimization.Name}'...", LogLevel.INFO);
                var optimizedResult = await _benchmarkingService.RunBenchmarkAsync(BenchmarkType.Quick);
                result.OptimizedBenchmarkScore = optimizedResult.Score;

                if (cancellationToken.IsCancellationRequested)
                {
                    // Revert the optimization before cancelling
                    await _optimizationService.RevertOptimizationAsync(optimization);
                    throw new OperationCanceledException();
                }

                // Update progress
                TestProgressUpdated?.Invoke(this, new TestProgressEventArgs
                {
                    OptimizationName = optimization.Name,
                    Progress = 50,
                    Status = "Running stress test..."
                });

                // Run stress test
                _logger.Log($"Running stress test for optimization '{optimization.Name}'...", LogLevel.INFO);
                bool stressTestSuccess = await RunStressTestAsync(testDuration, cancellationToken);

                if (!stressTestSuccess)
                {
                    result.ErrorMessage = "Stress test failed";

                    // Revert the optimization
                    await _optimizationService.RevertOptimizationAsync(optimization);

                    return result;
                }

                if (cancellationToken.IsCancellationRequested)
                {
                    // Revert the optimization before cancelling
                    await _optimizationService.RevertOptimizationAsync(optimization);
                    throw new OperationCanceledException();
                }

                // Update progress
                TestProgressUpdated?.Invoke(this, new TestProgressEventArgs
                {
                    OptimizationName = optimization.Name,
                    Progress = 90,
                    Status = "Reverting optimization..."
                });

                // Revert the optimization
                _logger.Log($"Reverting optimization '{optimization.Name}' after testing...", LogLevel.INFO);
                await _optimizationService.RevertOptimizationAsync(optimization);

                // Calculate performance impact
                result.PerformanceImpact = CalculatePerformanceImpact(result.BaselineBenchmarkScore, result.OptimizedBenchmarkScore);

                // Set test as successful
                result.IsSuccessful = true;
                result.EndTime = DateTime.Now;

                // Update progress
                TestProgressUpdated?.Invoke(this, new TestProgressEventArgs
                {
                    OptimizationName = optimization.Name,
                    Progress = 100,
                    Status = "Test completed successfully"
                });

                _logger.Log($"Compatibility test for optimization '{optimization.Name}' completed successfully", LogLevel.SUCCESS);
            }
            catch (OperationCanceledException)
            {
                _logger.Log($"Compatibility test for optimization '{optimization.Name}' was cancelled", LogLevel.WARNING);

                // Raise test cancelled event
                TestCancelled?.Invoke(this, new TestEventArgs
                {
                    OptimizationName = optimization.Name,
                    TestType = "Optimization Compatibility Test",
                    StartTime = result.StartTime
                });
            }
            catch (Exception ex)
            {
                _logger.Log($"Error testing optimization '{optimization.Name}': {ex.Message}", LogLevel.ERROR);
                result.ErrorMessage = ex.Message;

                // Try to revert the optimization if an error occurred
                try
                {
                    await _optimizationService.RevertOptimizationAsync(optimization);
                }
                catch
                {
                    // Ignore errors when reverting
                }
            }
            finally
            {
                _isTestingInProgress = false;
                _cancellationTokenSource = null;

                // Raise test completed event if not cancelled
                if (result.ErrorMessage != "Test was cancelled")
                {
                    TestCompleted?.Invoke(this, new TestResultEventArgs
                    {
                        OptimizationName = optimization.Name,
                        TestType = "Optimization Compatibility Test",
                        StartTime = result.StartTime,
                        EndTime = result.EndTime,
                        IsSuccessful = result.IsSuccessful,
                        ErrorMessage = result.ErrorMessage,
                        PerformanceImpact = result.PerformanceImpact
                    });
                }
            }

            return result;
        }

        /// <summary>
        /// Tests a power profile for compatibility
        /// </summary>
        /// <param name="profile">The power profile to test</param>
        /// <param name="testDuration">The duration of the test in seconds</param>
        /// <returns>The test result</returns>
        public Task<CompatibilityTestResult> TestPowerProfileAsync(PowerManagementProfile profile, int testDuration = 60)
        {
            // Similar implementation to TestOptimizationAsync, but for power profiles
            // For brevity, this is left as a placeholder
            var result = new CompatibilityTestResult
            {
                PowerProfileName = profile.Name,
                IsSuccessful = false,
                ErrorMessage = "Not implemented"
            };

            return Task.FromResult(result);
        }

        /// <summary>
        /// Cancels the current test
        /// </summary>
        public void CancelTest()
        {
            if (_isTestingInProgress && _cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
                _logger.Log("Test cancellation requested", LogLevel.WARNING);
            }
        }

        private async Task<bool> RunStressTestAsync(int durationSeconds, CancellationToken cancellationToken)
        {
            try
            {
                // Start monitoring performance metrics
                List<PerformanceMetrics> metrics = new List<PerformanceMetrics>();

                // Subscribe to performance metrics updates
                void OnMetricsUpdated(object sender, PerformanceMetricsEventArgs e)
                {
                    if (e.Metrics is PerformanceMetrics perfMetrics)
                {
                    metrics.Add(perfMetrics);
                }
                }

                _performanceMonitoringService.MetricsUpdated += OnMetricsUpdated;

                // Run for the specified duration
                for (int i = 0; i < durationSeconds; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return false;
                    }

                    // Update progress
                    TestProgressUpdated?.Invoke(this, new TestProgressEventArgs
                    {
                        Progress = 50 + (i * 40 / durationSeconds),
                        Status = $"Stress testing... {i}/{durationSeconds} seconds"
                    });

                    // Check for stability issues
                    if (metrics.Count > 0)
                    {
                        var latestMetrics = metrics[metrics.Count - 1];

                        // Check for thermal throttling
                        if (latestMetrics.CpuTemperature > 95 || latestMetrics.GpuTemperature > 95)
                        {
                            _logger.Log("Stress test failed: Thermal throttling detected", LogLevel.WARNING);
                            return false;
                        }

                        // Check for crashes or freezes
                        if (latestMetrics.CpuUsage < 5 || latestMetrics.GpuUsage < 5)
                        {
                            _logger.Log("Stress test failed: Possible system freeze detected", LogLevel.WARNING);
                            return false;
                        }
                    }

                    await Task.Delay(1000, cancellationToken);
                }

                // Unsubscribe from performance metrics updates
                _performanceMonitoringService.MetricsUpdated -= OnMetricsUpdated;

                // Analyze metrics for stability issues
                bool isStable = AnalyzeMetricsForStability(metrics);

                return isStable;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running stress test: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        private bool AnalyzeMetricsForStability(List<PerformanceMetrics> metrics)
        {
            if (metrics.Count < 5)
            {
                return false;
            }

            // Check for thermal issues
            bool hasThermalIssues = metrics.Any(m => m.CpuTemperature > 90 || m.GpuTemperature > 90);

            // Check for performance instability
            double avgFps = metrics.Average(m => m.FPS);
            double minFps = metrics.Min(m => m.FPS);
            double maxFps = metrics.Max(m => m.FPS);

            // Calculate FPS stability (lower is better)
            double fpsVariance = (maxFps - minFps) / avgFps;

            // Check for system instability
            bool hasSystemInstability = metrics.Any(m => m.CpuUsage < 5 || m.GpuUsage < 5);

            return !hasThermalIssues && fpsVariance < 0.5 && !hasSystemInstability;
        }

        private double CalculatePerformanceImpact(double baselineScore, double optimizedScore)
        {
            if (baselineScore <= 0)
            {
                return 0;
            }

            return ((optimizedScore - baselineScore) / baselineScore) * 100;
        }
    }

    /// <summary>
    /// Compatibility test result
    /// </summary>
    public class CompatibilityTestResult
    {
        /// <summary>
        /// Gets or sets the optimization name
        /// </summary>
        public string OptimizationName { get; set; }

        /// <summary>
        /// Gets or sets the power profile name
        /// </summary>
        public string PowerProfileName { get; set; }

        /// <summary>
        /// Gets or sets the start time of the test
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the end time of the test
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the duration of the test in seconds
        /// </summary>
        public int TestDuration { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the test was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the baseline benchmark score
        /// </summary>
        public double BaselineBenchmarkScore { get; set; }

        /// <summary>
        /// Gets or sets the optimized benchmark score
        /// </summary>
        public double OptimizedBenchmarkScore { get; set; }

        /// <summary>
        /// Gets or sets the performance impact percentage
        /// </summary>
        public double PerformanceImpact { get; set; }
    }

    /// <summary>
    /// Event arguments for test events
    /// </summary>
    public class TestEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the optimization name
        /// </summary>
        public string OptimizationName { get; set; }

        /// <summary>
        /// Gets or sets the power profile name
        /// </summary>
        public string PowerProfileName { get; set; }

        /// <summary>
        /// Gets or sets the test type
        /// </summary>
        public string TestType { get; set; }

        /// <summary>
        /// Gets or sets the start time of the test
        /// </summary>
        public DateTime StartTime { get; set; }
    }

    /// <summary>
    /// Event arguments for test result events
    /// </summary>
    public class TestResultEventArgs : TestEventArgs
    {
        /// <summary>
        /// Gets or sets the end time of the test
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the test was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the performance impact percentage
        /// </summary>
        public double PerformanceImpact { get; set; }
    }

    /// <summary>
    /// Event arguments for test progress events
    /// </summary>
    public class TestProgressEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the optimization name
        /// </summary>
        public string OptimizationName { get; set; }

        /// <summary>
        /// Gets or sets the power profile name
        /// </summary>
        public string PowerProfileName { get; set; }

        /// <summary>
        /// Gets or sets the progress percentage (0-100)
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string Status { get; set; }
    }
}







