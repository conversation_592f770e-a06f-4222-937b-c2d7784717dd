<Window x:Class="CircleUtility.Windows.LoginRegisterWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CircleUtility.Windows"
        mc:Ignorable="d"
        Title="Circle Utility - Login"
        Height="500"
        Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#FF0A141E"
        BorderBrush="#FF00C8FF"
        BorderThickness="1"
        WindowStyle="None"
        AllowsTransparency="True"
        MouseDown="Window_MouseDown">

    <Window.Resources>
        <!-- Animations -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="SlideInAnimation">
            <ThicknessAnimation Storyboard.TargetProperty="Margin"
                                From="0,0,-800,0" To="0" Duration="0:0:0.3">
                <ThicknessAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </ThicknessAnimation.EasingFunction>
            </ThicknessAnimation>
        </Storyboard>

        <Storyboard x:Key="SlideOutAnimation">
            <ThicknessAnimation Storyboard.TargetProperty="Margin"
                                From="0" To="0,0,-800,0" Duration="0:0:0.3">
                <ThicknessAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </ThicknessAnimation.EasingFunction>
            </ThicknessAnimation>
        </Storyboard>

        <Storyboard x:Key="PulseAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="0.8" To="1.0" Duration="0:0:1.5"
                             AutoReverse="True" RepeatBehavior="Forever"/>
        </Storyboard>

        <Storyboard x:Key="LoadingAnimation">
            <DoubleAnimation Storyboard.TargetName="LoadingSpinner"
                             Storyboard.TargetProperty="RenderTransform.Angle"
                             From="0" To="360" Duration="0:0:1.5"
                             RepeatBehavior="Forever"/>
        </Storyboard>

        <!-- Styles -->
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="SubtitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,30"/>
        </Style>

        <Style x:Key="InputLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="InputBoxStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Height" Value="35"/>
        </Style>

        <Style x:Key="PasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Height" Value="35"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="0,15,0,15"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="Width" Value="200"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF002A57" TargetName="border"/>
                                <Setter Property="BorderBrush" Value="#FF00E1FF" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF003C7A" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#FF0A141E" TargetName="border"/>
                                <Setter Property="BorderBrush" Value="#FF004080" TargetName="border"/>
                                <Setter Property="Opacity" Value="0.7" TargetName="border"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="LinkStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Width" Value="Auto"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Foreground" Value="#FF00E1FF"/>
                    <Setter Property="TextDecorations" Value="Underline"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ErrorMessageStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFFF3232"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,10,0,10"/>
            <Setter Property="Visibility" Value="Collapsed"/>
        </Style>

        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FF808080"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,10,0,0"/>
        </Style>

        <Style x:Key="CheckBoxStyle" TargetType="CheckBox">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF002A57" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF003C7A" TargetName="border"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Grid Grid.Row="0" Background="#FF001428">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="CIRCLE UTILITY"
                       FontFamily="Consolas"
                       FontSize="14"
                       Foreground="#FF00C8FF"
                       VerticalAlignment="Center"
                       Margin="10,0,0,0"/>

            <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        HorizontalAlignment="Right">
                <Button x:Name="MinimizeButton"
                        Content="&#xE921;"
                        Style="{StaticResource WindowControlButtonStyle}"
                        Click="MinimizeButton_Click"/>
                <Button x:Name="CloseButton"
                        Content="&#xE8BB;"
                        Style="{StaticResource WindowControlButtonStyle}"
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Grid>

        <!-- Content Area -->
        <Grid Grid.Row="1" Margin="40">
            <!-- Login Panel -->
            <Grid x:Name="LoginPanel" Visibility="Visible">
                <StackPanel>
                    <TextBlock x:Name="LoginTitle"
                               Text="WELCOME TO CIRCLE UTILITY"
                               Style="{StaticResource TitleStyle}"/>

                    <TextBlock Text="Login to your account to continue"
                               Style="{StaticResource SubtitleStyle}"/>

                    <!-- Username -->
                    <TextBlock Text="USERNAME"
                               Style="{StaticResource InputLabelStyle}"/>
                    <TextBox x:Name="LoginUsernameBox"
                             Style="{StaticResource InputBoxStyle}"
                             KeyDown="InputBox_KeyDown"/>

                    <!-- Password -->
                    <TextBlock Text="PASSWORD"
                               Style="{StaticResource InputLabelStyle}"/>
                    <Grid>
                        <PasswordBox x:Name="LoginPasswordBox"
                                     Style="{StaticResource PasswordBoxStyle}"
                                     KeyDown="InputBox_KeyDown"/>
                        <TextBox x:Name="LoginPasswordBoxVisible"
                                 Style="{StaticResource InputBoxStyle}"
                                 KeyDown="InputBox_KeyDown"
                                 Visibility="Collapsed"
                                 Margin="0,0,0,15"/>
                        <Button x:Name="LoginShowPasswordButton"
                                Content="👁"
                                Width="30"
                                Height="30"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Margin="0,0,5,15"
                                Background="Transparent"
                                BorderThickness="0"
                                Foreground="#FF00C8FF"
                                Cursor="Hand"
                                Click="ShowPasswordButton_Click"
                                Tag="LoginPassword"/>
                    </Grid>

                    <!-- Remember Me -->
                    <CheckBox x:Name="RememberMeCheckBox"
                              Content="Remember me"
                              Style="{StaticResource CheckBoxStyle}"/>

                    <!-- Error Message -->
                    <TextBlock x:Name="LoginErrorMessage"
                               Style="{StaticResource ErrorMessageStyle}"/>

                    <!-- Login Button -->
                    <Button x:Name="LoginButton"
                            Content="LOGIN"
                            Style="{StaticResource ButtonStyle}"
                            Click="LoginButton_Click"/>

                    <!-- Register Link -->
                    <TextBlock Text="Don't have an account? Register now"
                               Style="{StaticResource LinkStyle}"
                               MouseDown="RegisterLink_MouseDown"/>

                    <!-- Status Text -->
                    <TextBlock x:Name="LoginStatusText"
                               Text="Enter your credentials to access Circle Utility"
                               Style="{StaticResource StatusTextStyle}"/>
                </StackPanel>
            </Grid>

            <!-- Register Panel -->
            <Grid x:Name="RegisterPanel" Visibility="Collapsed" Margin="0,0,0,0">
                <StackPanel>
                    <TextBlock x:Name="RegisterTitle"
                               Text="CREATE ACCOUNT"
                               Style="{StaticResource TitleStyle}"/>

                    <TextBlock Text="Register to access Circle Utility"
                               Style="{StaticResource SubtitleStyle}"/>

                    <!-- Username -->
                    <TextBlock Text="USERNAME"
                               Style="{StaticResource InputLabelStyle}"/>
                    <TextBox x:Name="RegisterUsernameBox"
                             Style="{StaticResource InputBoxStyle}"
                             KeyDown="InputBox_KeyDown"/>


                    <!-- Password -->
                    <TextBlock Text="PASSWORD"
                               Style="{StaticResource InputLabelStyle}"/>
                    <PasswordBox x:Name="RegisterPasswordBox"
                                 Style="{StaticResource PasswordBoxStyle}"
                                 KeyDown="InputBox_KeyDown"/>

                    <!-- Confirm Password -->
                    <TextBlock Text="CONFIRM PASSWORD"
                               Style="{StaticResource InputLabelStyle}"/>
                    <PasswordBox x:Name="RegisterConfirmPasswordBox"
                                 Style="{StaticResource PasswordBoxStyle}"
                                 KeyDown="InputBox_KeyDown"/>

                    <!-- Error Message -->
                    <TextBlock x:Name="RegisterErrorMessage"
                               Style="{StaticResource ErrorMessageStyle}"/>

                    <!-- Register Button -->
                    <Button x:Name="RegisterButton"
                            Content="REGISTER"
                            Style="{StaticResource ButtonStyle}"
                            Click="RegisterButton_Click"/>

                    <!-- Login Link -->
                    <TextBlock Text="Already have an account? Login"
                               Style="{StaticResource LinkStyle}"
                               MouseDown="LoginLink_MouseDown"/>

                    <!-- Status Text -->
                    <TextBlock x:Name="RegisterStatusText"
                               Text="Create an account to access Circle Utility"
                               Style="{StaticResource StatusTextStyle}"/>
                </StackPanel>
            </Grid>

            <!-- Loading Overlay -->
            <Grid x:Name="LoadingOverlay"
                  Background="#********"
                  Visibility="Collapsed">
                <Grid Width="60"
                      Height="60"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Center">
                    <Ellipse Width="60"
                             Height="60"
                             Stroke="#FF00C8FF"
                             StrokeThickness="4"
                             StrokeDashArray="0.75,0.25"
                             StrokeDashCap="Round"
                             x:Name="LoadingSpinner">
                        <Ellipse.RenderTransform>
                            <RotateTransform Angle="0" CenterX="30" CenterY="30"/>
                        </Ellipse.RenderTransform>
                    </Ellipse>
                    <TextBlock Text="LOADING"
                               FontFamily="Consolas"
                               FontSize="10"
                               Foreground="White"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</Window>
