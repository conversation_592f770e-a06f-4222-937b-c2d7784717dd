using CircleUtility.ViewModels;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for creating ViewModels with proper dependency injection
    /// Replaces ServiceLocator anti-pattern
    /// </summary>
    public interface IViewModelFactory
    {
        /// <summary>
        /// Creates a DashboardViewModel with injected dependencies
        /// </summary>
        DashboardViewModel CreateDashboardViewModel();

        /// <summary>
        /// Creates a DebloatViewModel with injected dependencies
        /// </summary>
        DebloatViewModel CreateDebloatViewModel();

        /// <summary>
        /// Creates a SettingsViewModel with injected dependencies
        /// </summary>
        SettingsViewModel CreateSettingsViewModel();

        /// <summary>
        /// Creates a DiscordViewModel with injected dependencies
        /// </summary>
        DiscordViewModel CreateDiscordViewModel();

        /// <summary>
        /// Creates a RevertTweaksViewModel with injected dependencies
        /// </summary>
        RevertTweaksViewModel CreateRevertTweaksViewModel();

        /// <summary>
        /// Creates an AdminViewModel with injected dependencies
        /// </summary>
        AdminViewModel CreateAdminViewModel();

        /// <summary>
        /// Creates a PerformanceOptimizerViewModel with injected dependencies
        /// </summary>
        PerformanceOptimizerViewModel CreatePerformanceOptimizerViewModel();

        /// <summary>
        /// Creates a BenchmarkViewModel with injected dependencies
        /// </summary>
        BenchmarkViewModel CreateBenchmarkViewModel();

        /// <summary>
        /// Creates an InputDelayViewModel with injected dependencies
        /// </summary>
        InputDelayViewModel CreateInputDelayViewModel();

        /// <summary>
        /// Creates a SystemTweaksViewModel with injected dependencies
        /// </summary>
        SystemTweaksViewModel CreateSystemTweaksViewModel();

        /// <summary>
        /// Creates a MainViewModel with injected dependencies
        /// </summary>
        MainViewModel CreateMainViewModel(string username);

        /// <summary>
        /// Creates a ViewModel of the specified type with dependency injection
        /// </summary>
        /// <typeparam name="T">The ViewModel type</typeparam>
        /// <returns>The created ViewModel instance</returns>
        T CreateViewModel<T>() where T : class;
    }
}
