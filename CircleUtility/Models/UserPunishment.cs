// Created by Arsenal on 5-17-25 12:15PM
using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for user punishment information
    /// </summary>
    public class UserPunishment
    {
        /// <summary>
        /// Gets or sets the punishment ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Gets or sets the username of the punished user
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Gets or sets the punishment type
        /// </summary>
        public PunishmentType Type { get; set; }

        /// <summary>
        /// Gets or sets the reason for the punishment
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Gets or sets the admin who issued the punishment
        /// </summary>
        public string IssuedBy { get; set; }

        /// <summary>
        /// Gets or sets the date the punishment was issued
        /// </summary>
        public DateTime IssuedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets or sets the expiration date of the punishment
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the punishment is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Gets or sets the date the punishment was revoked
        /// </summary>
        public DateTime? RevokedDate { get; set; }

        /// <summary>
        /// Gets or sets the admin who revoked the punishment
        /// </summary>
        public string RevokedBy { get; set; }

        /// <summary>
        /// Gets or sets the reason for revoking the punishment
        /// </summary>
        public string RevocationReason { get; set; }

        /// <summary>
        /// Gets or sets the reason for revoking the punishment (alias for RevocationReason)
        /// </summary>
        public string RevokeReason { get => RevocationReason; set => RevocationReason = value; }

        /// <summary>
        /// Gets or sets additional notes about the punishment
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets the formatted issued date
        /// </summary>
        public string FormattedIssuedDate => IssuedDate.ToString("MMM dd, yyyy HH:mm:ss");

        /// <summary>
        /// Gets the formatted expiration date
        /// </summary>
        public string FormattedExpirationDate => ExpirationDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Never";

        /// <summary>
        /// Gets the formatted revoked date
        /// </summary>
        public string FormattedRevokedDate => RevokedDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "N/A";

        /// <summary>
        /// Gets the status of the punishment
        /// </summary>
        public string Status
        {
            get
            {
                if (RevokedDate.HasValue)
                {
                    return "Revoked";
                }
                else if (ExpirationDate.HasValue && ExpirationDate.Value < DateTime.Now)
                {
                    return "Expired";
                }
                else
                {
                    return "Active";
                }
            }
        }

        /// <summary>
        /// Gets the status color
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (RevokedDate.HasValue)
                {
                    return "#FFA500"; // Orange
                }
                else if (ExpirationDate.HasValue && ExpirationDate.Value < DateTime.Now)
                {
                    return "#808080"; // Gray
                }
                else
                {
                    return "#FF0000"; // Red
                }
            }
        }

        /// <summary>
        /// Gets the duration of the punishment
        /// </summary>
        public string Duration
        {
            get
            {
                if (!ExpirationDate.HasValue)
                {
                    return "Permanent";
                }

                TimeSpan duration;

                if (RevokedDate.HasValue)
                {
                    duration = RevokedDate.Value - IssuedDate;
                }
                else if (ExpirationDate.Value < DateTime.Now)
                {
                    duration = ExpirationDate.Value - IssuedDate;
                }
                else
                {
                    duration = ExpirationDate.Value - IssuedDate;
                }

                if (duration.TotalDays >= 365)
                {
                    return $"{Math.Floor(duration.TotalDays / 365)} year(s)";
                }
                else if (duration.TotalDays >= 30)
                {
                    return $"{Math.Floor(duration.TotalDays / 30)} month(s)";
                }
                else if (duration.TotalDays >= 1)
                {
                    return $"{Math.Floor(duration.TotalDays)} day(s)";
                }
                else if (duration.TotalHours >= 1)
                {
                    return $"{Math.Floor(duration.TotalHours)} hour(s)";
                }
                else
                {
                    return $"{Math.Floor(duration.TotalMinutes)} minute(s)";
                }
            }
        }
    }

    /// <summary>
    /// Enum for punishment types
    /// </summary>
    public enum PunishmentType
    {
        /// <summary>
        /// Warning
        /// </summary>
        Warning,

        /// <summary>
        /// Mute
        /// </summary>
        Mute,

        /// <summary>
        /// Temporary ban
        /// </summary>
        TemporaryBan,

        /// <summary>
        /// Permanent ban
        /// </summary>
        PermanentBan,

        /// <summary>
        /// IP ban
        /// </summary>
        IpBan,

        /// <summary>
        /// Hardware ban
        /// </summary>
        HardwareBan,

        /// <summary>
        /// Feature restriction
        /// </summary>
        FeatureRestriction
    }
}
