using System;
using System.Windows;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for UI factory
    /// </summary>
    public interface IUIFactory
    {
        /// <summary>
        /// Creates a login screen
        /// </summary>
        /// <returns>The login screen</returns>
        ILoginScreen CreateLoginScreen();

        /// <summary>
        /// Creates a thermal warning dialog
        /// </summary>
        /// <param name="profile">The power profile</param>
        /// <returns>The thermal warning dialog</returns>
        IThermalWarningDialog CreateThermalWarningDialog(PowerManagementProfile profile);

        /// <summary>
        /// Creates a notification panel
        /// </summary>
        /// <returns>The notification panel</returns>
        INotificationPanel CreateNotificationPanel();

        /// <summary>
        /// Creates a performance monitor view
        /// </summary>
        /// <returns>The performance monitor view</returns>
        IPerformanceMonitorView CreatePerformanceMonitorView();

        /// <summary>
        /// Creates a settings screen
        /// </summary>
        /// <returns>The settings screen</returns>
        ISettingsScreen CreateSettingsScreen();

        /// <summary>
        /// Creates a message dialog
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons</param>
        /// <param name="owner">The owner window</param>
        /// <returns>The message dialog</returns>
        IDialog CreateMessageDialog(string title, string message, MessageDialogButtons buttons, Window owner = null);

        /// <summary>
        /// Creates a file dialog
        /// </summary>
        /// <param name="isOpenDialog">Whether this is an open dialog</param>
        /// <param name="filter">The file filter</param>
        /// <param name="initialDirectory">The initial directory</param>
        /// <param name="defaultFileName">The default file name</param>
        /// <param name="owner">The owner window</param>
        /// <returns>The file dialog</returns>
        IDialog CreateFileDialog(bool isOpenDialog, string filter, string initialDirectory = null, string defaultFileName = null, Window owner = null);

        /// <summary>
        /// Creates a progress dialog
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="isIndeterminate">Whether the progress is indeterminate</param>
        /// <param name="owner">The owner window</param>
        /// <returns>The progress dialog</returns>
        IDialog CreateProgressDialog(string title, string message, bool isIndeterminate = false, Window owner = null);
    }
}
