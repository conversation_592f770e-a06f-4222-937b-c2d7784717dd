using System;

namespace CircleUtility.Models
{
    public class UpdateHistory
    {
        public string Version { get; set; }
        public DateTime InstallDate { get; set; }
        public string ReleaseNotes { get; set; }
        public bool IsRollback { get; set; }
        public string PreviousVersion { get; set; }
        public string InstallPath { get; set; }
        public long InstallSize { get; set; }
        public TimeSpan InstallDuration { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }
} 