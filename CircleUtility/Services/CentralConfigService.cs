using System;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Timers;

namespace CircleUtility.Services
{
    public class CentralConfigService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _localConfigPath;
        private readonly Timer _syncTimer;
        private readonly string _owner;
        private readonly string _repo;
        private const string CONFIG_FILE = "central_config.json";
        private const int SYNC_INTERVAL = 30000; // 30 seconds
        private bool _disposed;

        public event EventHandler<CentralConfig> ConfigUpdated;

        public string GitHubToken { get; set; }
        public string GitHubOwner { get; set; }
        public string GitHubRepo { get; set; }

        public CentralConfigService(string githubToken, string owner, string repo)
        {
            _owner = owner;
            _repo = repo;
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "CircleUtility");
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"token {githubToken}");
            
            _localConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, CONFIG_FILE);
            
            // Initialize sync timer
            _syncTimer = new Timer(SYNC_INTERVAL);
            _syncTimer.Elapsed += async (s, e) => await SyncConfigAsync();
            _syncTimer.Start();

            // Initial sync
            _ = SyncConfigAsync();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _syncTimer?.Stop();
                    _syncTimer?.Dispose();
                    _httpClient?.Dispose();
                }
                _disposed = true;
            }
        }

        ~CentralConfigService()
        {
            Dispose(false);
        }

        public async Task<CentralConfig> GetConfigAsync()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(CentralConfigService));
            try
            {
                if (File.Exists(_localConfigPath))
                {
                    var json = await File.ReadAllTextAsync(_localConfigPath);
                    return JsonSerializer.Deserialize<CentralConfig>(json);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading local config: {ex.Message}");
            }

            return new CentralConfig();
        }

        private async Task SyncConfigAsync()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(CentralConfigService));
            try
            {
                var url = $"https://api.github.com/repos/{_owner}/{_repo}/contents/config/{CONFIG_FILE}";
                var response = await _httpClient.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var fileInfo = JsonSerializer.Deserialize<GitHubFileInfo>(content);
                    
                    if (fileInfo != null)
                    {
                        var configContent = Convert.FromBase64String(fileInfo.Content);
                        var configJson = System.Text.Encoding.UTF8.GetString(configContent);
                        var config = JsonSerializer.Deserialize<CentralConfig>(configJson);
                        
                        await File.WriteAllTextAsync(_localConfigPath, configJson);
                        ConfigUpdated?.Invoke(this, config);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error syncing config: {ex.Message}");
            }
        }

        public async Task SaveConfigAsync(CentralConfig config)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(CentralConfigService));
            try
            {
                var json = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
                var content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(json));
                
                var url = $"https://api.github.com/repos/{_owner}/{_repo}/contents/config/{CONFIG_FILE}";
                var response = await _httpClient.GetAsync(url);
                
                string sha = null;
                if (response.IsSuccessStatusCode)
                {
                    var fileInfo = JsonSerializer.Deserialize<GitHubFileInfo>(await response.Content.ReadAsStringAsync());
                    sha = fileInfo?.Sha;
                }

                var updateRequest = new
                {
                    message = "Update central configuration",
                    content = content,
                    sha = sha
                };

                var updateResponse = await _httpClient.PutAsync(
                    url,
                    new StringContent(JsonSerializer.Serialize(updateRequest))
                );

                if (updateResponse.IsSuccessStatusCode)
                {
                    await File.WriteAllTextAsync(_localConfigPath, json);
                    ConfigUpdated?.Invoke(this, config);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving config: {ex.Message}");
                throw;
            }
        }
    }

    public class CentralConfig
    {
        public UserAccess[] Users { get; set; } = Array.Empty<UserAccess>();
        public string Version { get; set; } = "1.0.0";
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
    }

    public class UserAccess
    {
        public string Username { get; set; }
        public string Role { get; set; }
        public bool IsActive { get; set; }
        public DateTime LastLogin { get; set; }
        public string[] Permissions { get; set; } = Array.Empty<string>();
        public bool IsAdmin { get; set; }
    }

    internal class GitHubFileInfo
    {
        public string Content { get; set; }
        public string Sha { get; set; }
    }
} 