using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using CircleUtility.Services;
using CircleUtility.Models;
using Microsoft.Extensions.Configuration;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace CircleUtility
{
    public partial class WelcomeScreen : Window, IConfigAware
    {
        private readonly CentralConfigService _configService;
        private string[] loadingMessages = new string[]
        {
            "INITIALIZING CIRCLE UTILITY...",
            "AUTHENTICATING USER...",
            "LOADING HARDWARE SERVICES...",
            "SCANNING SYSTEM CONFIGURATION...",
            "OPTIMIZING PERFORMANCE SETTINGS...",
            "CONFIGURING SECURITY PROTOCOLS...",
            "PREPARING USER INTERFACE...",
            "FINALIZING SETUP...",
            "SYSTEM READY"
        };

        private int currentMessageIndex = 0;
        private DispatcherTimer messageTimer;
        private Random random = new Random();
        private CentralConfig _currentConfig;

        public string Username { get; private set; }
        public bool IsLoginSuccessful { get; private set; }

        public WelcomeScreen()
        {
            InitializeComponent();
            _configService = new CentralConfigService(
                "****************************************",
                "AquaknowsJava",
                "TheCircleUtility"
            );
            
            // Set up event handlers
            AccessButton.Click += AccessButton_Click;
            PasswordBox.KeyDown += (s, e) =>
            {
                if (e.Key == System.Windows.Input.Key.Enter)
                {
                    AccessButton_Click(s, e);
                }
            };

            // Username and password fields start empty

            // Start the pulse animation for the title
            Storyboard pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            pulseAnimation.Begin();

            // Handle Enter key in password fields
            PasswordBox.KeyDown += (s, e) => 
            {
                if (e.Key == System.Windows.Input.Key.Enter)
                {
                    AccessButton_Click(AccessButton, new RoutedEventArgs());
                }
            };
            
            // Initially show login form
            StatusMessage.Text = "Please enter your credentials to access the system.";
            LoadingStatus.Text = "AWAITING LOGIN...";
        }

        private async void AccessButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AccessButton.IsEnabled = false;
                StatusMessage.Text = "AUTHENTICATING...";

                var username = UsernameBox.Text;
                var password = PasswordBox.Password;

                // Get latest config
                _currentConfig = await App.ConfigService.GetConfigAsync();
                
                // Find user in config
                var user = _currentConfig.Users.FirstOrDefault(u => 
                    u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && 
                    u.IsActive);

                if (user != null)
                {
                    // Update last login
                    user.LastLogin = DateTime.UtcNow;
                    await App.ConfigService.SaveConfigAsync(_currentConfig);

                    // Navigate to main view
                    var mainWindow = Window.GetWindow(this);
                    if (mainWindow != null)
                    {
                        // Get required services from the service provider
                        var hardwareDetectionService = App.ServiceProvider.GetRequiredService<IHardwareDetectionService>();
                        var hardwareOptimizationService = App.ServiceProvider.GetRequiredService<IHardwareOptimizationService>();
                        var hardwareCompatibilityService = App.ServiceProvider.GetRequiredService<IHardwareCompatibilityService>();
                        var performanceMonitoringService = App.ServiceProvider.GetRequiredService<IPerformanceMonitoringService>();
                        var benchmarkingService = App.ServiceProvider.GetRequiredService<IBenchmarkingService>();

                        // Create main window with required services
                        var newMainWindow = new MainWindow(
                            hardwareDetectionService,
                            hardwareOptimizationService,
                            hardwareCompatibilityService,
                            performanceMonitoringService,
                            benchmarkingService);

                        // Set the username
                        newMainWindow.SetUsername(username);

                        // Replace the current window content
                        mainWindow.Content = newMainWindow;
                    }
                }
                else
                {
                    StatusMessage.Text = "INVALID CREDENTIALS";
                    AccessButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                StatusMessage.Text = "AUTHENTICATION ERROR";
                AccessButton.IsEnabled = true;
                MessageBox.Show($"Error during authentication: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task StartLoadingSequence()
        {
            // Start the loading bar animation
            Storyboard loadingAnimation = (Storyboard)FindResource("LoadingAnimation");
            loadingAnimation.Begin();

            // Setup the message timer
            messageTimer = new DispatcherTimer();
            messageTimer.Interval = TimeSpan.FromMilliseconds(600);
            messageTimer.Tick += MessageTimer_Tick;
            messageTimer.Start();

            // Wait for loading to complete
            await Task.Delay(4000);

            // Stop timer
            messageTimer?.Stop();

            // Set final status
            LoadingStatus.Text = loadingMessages[loadingMessages.Length - 1];
            StatusMessage.Text = "System ready. Launching main application...";
        }

        private void MessageTimer_Tick(object sender, EventArgs e)
        {
            // Update the loading message
            if (currentMessageIndex < loadingMessages.Length - 1)
            {
                currentMessageIndex++;
                LoadingStatus.Text = loadingMessages[currentMessageIndex];

                // Update the status message for some messages
                if (currentMessageIndex == 2)
                {
                    StatusMessage.Text = "Analyzing system configuration...";
                }
                else if (currentMessageIndex == 4)
                {
                    StatusMessage.Text = "Preparing optimization engine...";
                }
                else if (currentMessageIndex == 6)
                {
                    StatusMessage.Text = "Finalizing user interface...";
                }
            }
        }

        private void ShowError(string message)
        {
            StatusMessage.Text = message;
            StatusMessage.Foreground = new SolidColorBrush(Colors.Red);

            // Reset color after 3 seconds
            var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(3) };
            timer.Tick += (s, e) =>
            {
                StatusMessage.Foreground = new SolidColorBrush(Colors.White);
                StatusMessage.Text = "Please enter your credentials to access the system.";
                timer.Stop();
            };
            timer.Start();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // Close the dialog with failure
            DialogResult = false;
            Close();
        }

        public void OnConfigUpdated(CentralConfig config)
        {
            _currentConfig = config;
            // Refresh UI if needed
            Dispatcher.Invoke(() =>
            {
                // Update any UI elements that depend on the config
                if (AccessButton.IsEnabled)
                {
                    var username = UsernameBox.Text;
                    var user = config.Users.FirstOrDefault(u => 
                        u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                    
                    AccessButton.IsEnabled = user?.IsActive ?? false;
                }
            });
        }
    }
}
















