using System;
using CircleUtility.Services;

namespace CircleUtility.Extensions
{
    /// <summary>
    /// Extension methods for standardized exception handling
    /// </summary>
    public static class ExceptionHandlingExtensions
    {
        /// <summary>
        /// Executes an action with standardized exception handling
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="context">The context for error reporting</param>
        /// <param name="showErrorsToUser">Whether to show errors to the user</param>
        /// <returns>True if successful, false if an exception occurred</returns>
        public static bool ExecuteSafely(this Action action, string context, bool showErrorsToUser = false)
        {
            return ExceptionHandlingService.Instance.ExecuteWithHandling(action, context, showErrorsToUser);
        }

        /// <summary>
        /// Executes a function with standardized exception handling
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="function">The function to execute</param>
        /// <param name="context">The context for error reporting</param>
        /// <param name="defaultValue">The default value to return on error</param>
        /// <param name="showErrorsToUser">Whether to show errors to the user</param>
        /// <returns>The function result or default value on error</returns>
        public static T ExecuteSafely<T>(this Func<T> function, string context, T defaultValue, bool showErrorsToUser = false)
        {
            return ExceptionHandlingService.Instance.ExecuteWithHandling(function, context, defaultValue, showErrorsToUser);
        }

        /// <summary>
        /// Handles an exception with standardized logging and optional user notification
        /// </summary>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">The context where the exception occurred</param>
        /// <param name="showToUser">Whether to show the error to the user</param>
        /// <param name="userMessage">Custom message to show to the user (optional)</param>
        /// <returns>True if the operation should continue, false if it should be aborted</returns>
        public static bool HandleStandardized(this Exception exception, string context, bool showToUser = false, string userMessage = null)
        {
            return ExceptionHandlingService.Instance.HandleException(exception, context, showToUser, userMessage);
        }

        /// <summary>
        /// Handles an exception and returns a default value
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">The context where the exception occurred</param>
        /// <param name="defaultValue">The default value to return</param>
        /// <param name="showToUser">Whether to show the error to the user</param>
        /// <returns>The default value</returns>
        public static T HandleWithDefault<T>(this Exception exception, string context, T defaultValue, bool showToUser = false)
        {
            return ExceptionHandlingService.Instance.HandleException(exception, context, defaultValue, showToUser);
        }
    }
}
