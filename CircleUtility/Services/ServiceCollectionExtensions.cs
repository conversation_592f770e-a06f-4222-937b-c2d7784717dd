using System;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Interfaces;
using CircleUtility.ViewModels;

namespace CircleUtility.Services
{
    /// <summary>
    /// Extension methods for service collection
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds all CircleUtility services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddCircleUtilityServices(this IServiceCollection services)
        {
            // Register core services
            services.AddSingleton<ILoggerService>(provider => LoggingService.Instance);
            services.AddSingleton<IConfigurationService>(provider => ConfigurationManager.Instance);
            
            // Register hardware services
            services.AddSingleton<IHardwareDetectionService>(provider => HardwareDetectionService.Instance);
            services.AddSingleton<IHardwareCompatibilityService>(provider => HardwareCompatibilityService.Instance);
            services.AddSingleton<IHardwareRecommendationService>(provider => HardwareRecommendationService.Instance);
            services.AddSingleton<IPerformanceMonitoringService>(provider => PerformanceMonitoringService.Instance);
            services.AddSingleton<IHardwareDetectionBadgeService>(provider => HardwareDetectionBadgeService.Instance);
            services.AddSingleton<IHardwareOptimizationService>(provider => HardwareOptimizationService.Instance);
            
            // Register optimization services
            services.AddSingleton<ISystemOptimizationService>(provider => SystemOptimizationService.Instance);
            services.AddSingleton<IRevertTweaksService>(provider => RevertTweaksService.Instance);
            
            // Register tracking services
            services.AddSingleton<IUserTrackingService>(provider => UserTrackingService.Instance);
            services.AddSingleton<ITweakChangeTracker>(provider => TweakChangeTracker.Instance);
            
            // Register communication services
            services.AddSingleton<IDiscordService>(provider => DiscordService.Instance);
            services.AddSingleton<INotificationService>(provider => NotificationService.Instance);
            
            // Register utility services
            services.AddSingleton<ISecurityService>(provider => SecurityService.Instance);
            
            // Register ViewModels
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<SettingsViewModel>();
            services.AddTransient<HelpSupportViewModel>();
            services.AddTransient<PerformanceOptimizerViewModel>();
            services.AddTransient<SmartRecommendationsViewModel>();
            services.AddTransient<RevertTweaksViewModel>();
            
            return services;
        }
    }

    /// <summary>
    /// Extension methods for service provider
    /// </summary>
    public static class ServiceProviderExtensions
    {
        /// <summary>
        /// Initializes all services
        /// </summary>
        /// <param name="serviceProvider">The service provider</param>
        public static void InitializeAllServices(this IServiceProvider serviceProvider)
        {
            // Initialize core services
            var logger = serviceProvider.GetService<ILoggerService>();
            logger?.LogInfo("Initializing all services...");

            // Initialize hardware services
            var hardwareDetection = serviceProvider.GetService<IHardwareDetectionService>();
            var hardwareCompatibility = serviceProvider.GetService<IHardwareCompatibilityService>();
            var performanceMonitoring = serviceProvider.GetService<IPerformanceMonitoringService>();
            var hardwareOptimization = serviceProvider.GetService<IHardwareOptimizationService>();

            // Initialize other services as needed
            logger?.LogInfo("All services initialized successfully.");
        }
    }
}

