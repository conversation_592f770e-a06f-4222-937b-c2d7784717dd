// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for power management profiles
    /// </summary>
    public class PowerManagementProfile
    {
        /// <summary>
        /// Gets or sets the name of the profile
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the profile
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the category of the profile
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the CPU settings
        /// </summary>
        public CpuPowerSettings CpuSettings { get; set; } = new CpuPowerSettings();

        /// <summary>
        /// Gets or sets the GPU settings
        /// </summary>
        public GpuPowerSettings GpuSettings { get; set; } = new GpuPowerSettings();

        /// <summary>
        /// Gets or sets the system settings
        /// </summary>
        public SystemPowerSettings SystemSettings { get; set; } = new SystemPowerSettings();

        /// <summary>
        /// Gets or sets the performance impact rating (0-100)
        /// </summary>
        public int PerformanceImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the power efficiency rating (0-100, higher is more efficient)
        /// </summary>
        public int PowerEfficiency { get; set; } = 50;

        /// <summary>
        /// Gets or sets the thermal impact rating (0-100, higher is hotter)
        /// </summary>
        public int ThermalImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the noise level rating (0-100, higher is louder)
        /// </summary>
        public int NoiseLevel { get; set; } = 50;

        /// <summary>
        /// Gets or sets the input latency impact rating (0-100, higher is better reduction)
        /// </summary>
        public int InputLatencyImpact { get; set; } = 50;

        /// <summary>
        /// Gets or sets the risk level of the profile
        /// </summary>
        public RiskLevel Risk { get; set; } = RiskLevel.Safe;

        /// <summary>
        /// Gets or sets the warning message for risky profiles
        /// </summary>
        public string WarningMessage { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this profile requires a restart
        /// </summary>
        public bool RequiresRestart { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this profile requires admin privileges
        /// </summary>
        public bool RequiresAdmin { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this profile is enabled by default
        /// </summary>
        public bool EnabledByDefault { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether this profile is currently enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether this profile is advanced
        /// </summary>
        public bool IsAdvanced { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this profile is experimental
        /// </summary>
        public bool IsExperimental { get; set; }

        /// <summary>
        /// Gets or sets the author of the profile
        /// </summary>
        public string Author { get; set; } = "Circle Utility";

        /// <summary>
        /// Gets or sets the version of the profile
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// Gets or sets the date the profile was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets or sets the date the profile was last modified
        /// </summary>
        public DateTime LastModifiedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets or sets the date the profile was last applied
        /// </summary>
        public DateTime? LastAppliedDate { get; set; }

        /// <summary>
        /// Gets or sets the user notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets or sets the hardware compatibility
        /// </summary>
        public List<string> HardwareCompatibility { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the game compatibility
        /// </summary>
        public List<string> GameCompatibility { get; set; } = new List<string>();

        /// <summary>
        /// Gets the formatted creation date
        /// </summary>
        [JsonIgnore]
        public string FormattedCreatedDate => CreatedDate.ToString("MMM dd, yyyy");

        /// <summary>
        /// Gets the formatted last modified date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastModifiedDate => LastModifiedDate.ToString("MMM dd, yyyy");

        /// <summary>
        /// Gets the formatted last applied date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastAppliedDate => LastAppliedDate?.ToString("MMM dd, yyyy") ?? "Never";

        /// <summary>
        /// Gets the performance impact description
        /// </summary>
        [JsonIgnore]
        public string PerformanceImpactDescription
        {
            get
            {
                if (PerformanceImpact >= 80) return "Extreme Performance Boost";
                if (PerformanceImpact >= 60) return "High Performance Boost";
                if (PerformanceImpact >= 40) return "Moderate Performance Boost";
                if (PerformanceImpact >= 20) return "Slight Performance Boost";
                return "Minimal Performance Impact";
            }
        }

        /// <summary>
        /// Gets the power efficiency description
        /// </summary>
        [JsonIgnore]
        public string PowerEfficiencyDescription
        {
            get
            {
                if (PowerEfficiency >= 80) return "Extremely Efficient";
                if (PowerEfficiency >= 60) return "Very Efficient";
                if (PowerEfficiency >= 40) return "Moderately Efficient";
                if (PowerEfficiency >= 20) return "Somewhat Inefficient";
                return "Very Inefficient";
            }
        }

        /// <summary>
        /// Gets the thermal impact description
        /// </summary>
        [JsonIgnore]
        public string ThermalImpactDescription
        {
            get
            {
                if (ThermalImpact >= 80) return "Significantly Higher Temperatures";
                if (ThermalImpact >= 60) return "Higher Temperatures";
                if (ThermalImpact >= 40) return "Moderate Temperature Impact";
                if (ThermalImpact >= 20) return "Low Temperature Impact";
                return "Minimal Temperature Impact";
            }
        }

        /// <summary>
        /// Gets the noise level description
        /// </summary>
        [JsonIgnore]
        public string NoiseLevelDescription
        {
            get
            {
                if (NoiseLevel >= 80) return "Very Loud";
                if (NoiseLevel >= 60) return "Loud";
                if (NoiseLevel >= 40) return "Moderate Noise";
                if (NoiseLevel >= 20) return "Quiet";
                return "Very Quiet";
            }
        }

        /// <summary>
        /// Gets the input latency impact description
        /// </summary>
        [JsonIgnore]
        public string InputLatencyImpactDescription
        {
            get
            {
                if (InputLatencyImpact >= 80) return "Extreme Latency Reduction";
                if (InputLatencyImpact >= 60) return "High Latency Reduction";
                if (InputLatencyImpact >= 40) return "Moderate Latency Reduction";
                if (InputLatencyImpact >= 20) return "Slight Latency Reduction";
                return "Minimal Latency Impact";
            }
        }

        /// <summary>
        /// Gets the risk level description
        /// </summary>
        [JsonIgnore]
        public string RiskLevelDescription
        {
            get
            {
                switch (Risk)
                {
                    case RiskLevel.Safe:
                        return "Safe - No risk to hardware or system stability";
                    case RiskLevel.Low:
                        return "Low Risk - Minimal potential for system instability";
                    case RiskLevel.Medium:
                        return "Medium Risk - May cause instability in some systems";
                    case RiskLevel.High:
                        return "High Risk - Can cause system instability or reduced hardware lifespan";
                    case RiskLevel.VeryHigh:
                        return "Very High Risk - May cause hardware damage or system failure";
                    default:
                        return "Unknown Risk";
                }
            }
        }

        /// <summary>
        /// Gets the risk level color
        /// </summary>
        [JsonIgnore]
        public string RiskLevelColor
        {
            get
            {
                switch (Risk)
                {
                    case RiskLevel.Safe:
                        return "#00FF00"; // Green
                    case RiskLevel.Low:
                        return "#AAFF00"; // Light Green
                    case RiskLevel.Medium:
                        return "#FFFF00"; // Yellow
                    case RiskLevel.High:
                        return "#FF8800"; // Orange
                    case RiskLevel.VeryHigh:
                        return "#FF0000"; // Red
                    default:
                        return "#FFFFFF"; // White
                }
            }
        }
    }

    /// <summary>
    /// Model for CPU power settings
    /// </summary>
    public class CpuPowerSettings
    {
        public string PowerPlanName { get; set; }
        public int PrioritySeparation { get; set; }

        /// <summary>
        /// Gets or sets the power plan
        /// </summary>
        public string PowerPlan { get; set; } = "Balanced";

        /// <summary>
        /// Gets or sets the minimum processor state (%)
        /// </summary>
        public int MinProcessorState { get; set; } = 5;

        /// <summary>
        /// Gets or sets the maximum processor state (%)
        /// </summary>
        public int MaxProcessorState { get; set; } = 100;

        /// <summary>
        /// Gets or sets the power limit 1 (PL1) in watts
        /// </summary>
        public int PowerLimit1 { get; set; } = 0; // 0 means default

        /// <summary>
        /// Gets or sets the power limit 2 (PL2) in watts
        /// </summary>
        public int PowerLimit2 { get; set; } = 0; // 0 means default

        /// <summary>
        /// Gets or sets the power limit 4 (PL4) in watts (Intel only)
        /// </summary>
        public int PowerLimit4 { get; set; } = 0; // 0 means default

        /// <summary>
        /// Gets or sets the tau value (turbo duration) in seconds
        /// </summary>
        public int TauValue { get; set; } = 0; // 0 means default

        /// <summary>
        /// Gets or sets a value indicating whether core parking is enabled
        /// </summary>
        public bool CoreParkingEnabled { get; set; } = false;

        /// <summary>
        /// Gets or sets the core parking minimum cores (%)
        /// </summary>
        public int CoreParkingMinCores { get; set; } = 50;

        /// <summary>
        /// Gets or sets a value indicating whether speed shift is enabled (Intel only)
        /// </summary>
        public bool SpeedShiftEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the speed shift EPP value (0-255, lower is more performance)
        /// </summary>
        public int SpeedShiftEppValue { get; set; } = 80;

        /// <summary>
        /// Gets or sets a value indicating whether precision boost overdrive is enabled (AMD only)
        /// </summary>
        public bool PrecisionBoostOverdriveEnabled { get; set; } = false;

        /// <summary>
        /// Gets or sets the precision boost overdrive scalar (AMD only)
        /// </summary>
        public int PrecisionBoostOverdriveScalar { get; set; } = 0; // 0 means default

        /// <summary>
        /// Gets or sets a value indicating whether CPPC preferred cores is enabled (AMD only)
        /// </summary>
        public bool CppcPreferredCoresEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether E-cores are enabled (Intel hybrid only)
        /// </summary>
        public bool ECoresEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether game processes should prefer P-cores (Intel hybrid only)
        /// </summary>
        public bool GameProcessesPreferPCores { get; set; } = true;

        /// <summary>
        /// Gets or sets the C-state limit
        /// </summary>
        public int CStateLimit { get; set; } = 0; // 0 means no limit
    }

    /// <summary>
    /// Model for GPU power settings
    /// </summary>
    public class GpuPowerSettings
    {
        public int GraphicsPreferencePolicy { get; set; }

        /// <summary>
        /// Gets or sets the power management mode
        /// </summary>
        public string PowerManagementMode { get; set; } = "Optimal";

        /// <summary>
        /// Gets or sets the power limit (%)
        /// </summary>
        public int PowerLimit { get; set; } = 100;

        /// <summary>
        /// Gets or sets the temperature target (°C)
        /// </summary>
        public int TemperatureTarget { get; set; } = 83;

        /// <summary>
        /// Gets or sets a value indicating whether the fan curve is custom
        /// </summary>
        public bool CustomFanCurve { get; set; } = false;

        /// <summary>
        /// Gets or sets the fan curve points (temperature, fan speed %)
        /// </summary>
        public List<FanCurvePoint> FanCurvePoints { get; set; } = new List<FanCurvePoint>();

        /// <summary>
        /// Gets or sets a value indicating whether fan hysteresis is enabled
        /// </summary>
        public bool FanHysteresisEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the fan hysteresis value (°C)
        /// </summary>
        public int FanHysteresisValue { get; set; } = 3;

        /// <summary>
        /// Gets or sets a value indicating whether low latency mode is enabled (NVIDIA)
        /// </summary>
        public bool LowLatencyModeEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the low latency mode value (NVIDIA)
        /// </summary>
        public string LowLatencyModeValue { get; set; } = "Ultra";

        /// <summary>
        /// Gets or sets a value indicating whether anti-lag is enabled (AMD)
        /// </summary>
        public bool AntiLagEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether resizable BAR is enabled
        /// </summary>
        public bool ResizableBarEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the texture filtering quality
        /// </summary>
        public string TextureFilteringQuality { get; set; } = "Performance";

        /// <summary>
        /// Gets or sets a value indicating whether threaded optimization is enabled
        /// </summary>
        public bool ThreadedOptimizationEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether shader cache is enabled
        /// </summary>
        public bool ShaderCacheEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the shader cache size (MB)
        /// </summary>
        public int ShaderCacheSize { get; set; } = 0; // 0 means unlimited
    }

    /// <summary>
    /// Model for system power settings
    /// </summary>
    public class SystemPowerSettings
    {
        public int StandbyTimeoutAC { get; set; }
        public int MonitorTimeoutAC { get; set; }

        /// <summary>
        /// Gets or sets the system cooling policy
        /// </summary>
        public string CoolingPolicy { get; set; } = "Active";

        /// <summary>
        /// Gets or sets the hard disk timeout (minutes, 0 means never)
        /// </summary>
        public int HardDiskTimeout { get; set; } = 20;

        /// <summary>
        /// Gets or sets a value indicating whether USB selective suspend is enabled
        /// </summary>
        public bool UsbSelectiveSuspendEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether PCI Express power management is enabled
        /// </summary>
        public bool PciExpressPowerManagementEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether processor idle states are enabled
        /// </summary>
        public bool ProcessorIdleStatesEnabled { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether background services are optimized
        /// </summary>
        public bool OptimizeBackgroundServices { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether network throttling is disabled
        /// </summary>
        public bool DisableNetworkThrottling { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether HPET is disabled
        /// </summary>
        public bool DisableHpet { get; set; } = false;

        /// <summary>
        /// Gets or sets a value indicating whether dynamic tick is disabled
        /// </summary>
        public bool DisableDynamicTick { get; set; } = true;

        /// <summary>
        /// Gets or sets the timer resolution (microseconds)
        /// </summary>
        public double TimerResolution { get; set; } = 0.5;
    }

    /// <summary>
    /// Model for fan curve points
    /// </summary>
    public class FanCurvePoint
    {
        /// <summary>
        /// Gets or sets the temperature (°C)
        /// </summary>
        public int Temperature { get; set; }

        /// <summary>
        /// Gets or sets the fan speed (%)
        /// </summary>
        public int FanSpeed { get; set; }
    }
}
