using System.Windows;
using System.Windows.Controls;
using CircleUtility.ViewModels;

namespace CircleUtility.Views
{
    /// <summary>
    /// Interaction logic for DashboardView.xaml
    /// </summary>
    public partial class DashboardView : UserControl
    {
        private DashboardViewModel _viewModel;

        /// <summary>
        /// Initializes a new instance of the DashboardView class
        /// </summary>
        public DashboardView()
        {
            InitializeComponent();

            // Get the view model from the data context
            Loaded += OnLoaded;
        }

        /// <summary>
        /// Handles the control being loaded
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event arguments</param>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            _viewModel = DataContext as DashboardViewModel;

            if (_viewModel != null)
            {
                // Initialize performance graphs
                _viewModel.InitializePerformanceGraphs(
                    CpuGraph,
                    <PERSON>Graph,
                    GpuGraph,
                    NetworkGraph);

                // Initialize performance gauges
                _viewModel.InitializePerformanceGauges(
                    CpuGauge,
                    RamGauge,
                    GpuGauge,
                    TempGauge);

                // Initialize hardware detail panel
                HardwareDetailPanel.UpdateHardwareInfo(_viewModel.GetHardwareInfo());

                // Connect quick action panel events
                ConnectQuickActionEvents();
            }
        }

        /// <summary>
        /// Connects the quick action panel events to the view model commands
        /// </summary>
        private void ConnectQuickActionEvents()
        {
            // Connect quick action events to view model commands
            QuickActionPanel.OptimizePerformanceClicked += (s, e) => _viewModel.OptimizeSystemCommand.Execute(null);
            QuickActionPanel.OptimizeGpuClicked += (s, e) => _viewModel.OptimizeGpuCommand.Execute(null);
            QuickActionPanel.OptimizeNetworkClicked += (s, e) => _viewModel.OptimizeNetworkCommand.Execute(null);
            QuickActionPanel.CleanSystemClicked += (s, e) => _viewModel.CleanTempFilesCommand.Execute(null);
            QuickActionPanel.OptimizeInputClicked += (s, e) => _viewModel.OptimizeInputDevicesCommand.Execute(null);
            QuickActionPanel.RevertTweaksClicked += (s, e) => _viewModel.RevertAllTweaksCommand.Execute(null);
            QuickActionPanel.TweakSystemClicked += (s, e) => _viewModel.TweakSystemCommand.Execute(null);
            QuickActionPanel.TestNotificationsClicked += (s, e) => _viewModel.ShowTestNotificationsCommand.Execute(null);
        }
    }
}
