// Created by Arsenal on 5-17-25 12:15PM
using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for network latency metrics
    /// </summary>
    public class NetworkLatencyMetrics
    {
        /// <summary>
        /// Gets or sets the host that was pinged
        /// </summary>
        public string Host { get; set; }

        /// <summary>
        /// Gets or sets the number of ping samples
        /// </summary>
        public int SampleCount { get; set; }

        /// <summary>
        /// Gets or sets the minimum latency in milliseconds
        /// </summary>
        public long MinLatency { get; set; }

        /// <summary>
        /// Gets or sets the maximum latency in milliseconds
        /// </summary>
        public long MaxLatency { get; set; }

        /// <summary>
        /// Gets or sets the average latency in milliseconds
        /// </summary>
        public double AverageLatency { get; set; }

        /// <summary>
        /// Gets or sets the jitter (standard deviation of latency) in milliseconds
        /// </summary>
        public double Jitter { get; set; }

        /// <summary>
        /// Gets or sets the packet loss percentage
        /// </summary>
        public double PacketLoss { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when the metrics were captured
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the error message, if any
        /// </summary>
        public string Error { get; set; }

        /// <summary>
        /// Gets a value indicating whether the measurement was successful
        /// </summary>
        public bool IsSuccessful => string.IsNullOrEmpty(Error);

        /// <summary>
        /// Gets the formatted minimum latency
        /// </summary>
        public string FormattedMinLatency => $"{MinLatency} ms";

        /// <summary>
        /// Gets the formatted maximum latency
        /// </summary>
        public string FormattedMaxLatency => $"{MaxLatency} ms";

        /// <summary>
        /// Gets the formatted average latency
        /// </summary>
        public string FormattedAverageLatency => $"{AverageLatency:F2} ms";

        /// <summary>
        /// Gets the formatted jitter
        /// </summary>
        public string FormattedJitter => $"{Jitter:F2} ms";

        /// <summary>
        /// Gets the formatted packet loss
        /// </summary>
        public string FormattedPacketLoss => $"{PacketLoss:F1}%";

        /// <summary>
        /// Gets the formatted timestamp
        /// </summary>
        public string FormattedTimestamp => Timestamp.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// Gets the latency rating (0-100)
        /// </summary>
        public int LatencyRating
        {
            get
            {
                if (!IsSuccessful)
                {
                    return 0;
                }

                // Calculate rating based on average latency
                if (AverageLatency <= 10)
                {
                    return 100;
                }
                else if (AverageLatency <= 20)
                {
                    return 90;
                }
                else if (AverageLatency <= 40)
                {
                    return 80;
                }
                else if (AverageLatency <= 60)
                {
                    return 70;
                }
                else if (AverageLatency <= 80)
                {
                    return 60;
                }
                else if (AverageLatency <= 100)
                {
                    return 50;
                }
                else if (AverageLatency <= 150)
                {
                    return 40;
                }
                else if (AverageLatency <= 200)
                {
                    return 30;
                }
                else if (AverageLatency <= 300)
                {
                    return 20;
                }
                else
                {
                    return 10;
                }
            }
        }

        /// <summary>
        /// Gets the jitter rating (0-100)
        /// </summary>
        public int JitterRating
        {
            get
            {
                if (!IsSuccessful)
                {
                    return 0;
                }

                // Calculate rating based on jitter
                if (Jitter <= 1)
                {
                    return 100;
                }
                else if (Jitter <= 2)
                {
                    return 90;
                }
                else if (Jitter <= 5)
                {
                    return 80;
                }
                else if (Jitter <= 10)
                {
                    return 70;
                }
                else if (Jitter <= 15)
                {
                    return 60;
                }
                else if (Jitter <= 20)
                {
                    return 50;
                }
                else if (Jitter <= 30)
                {
                    return 40;
                }
                else if (Jitter <= 50)
                {
                    return 30;
                }
                else if (Jitter <= 100)
                {
                    return 20;
                }
                else
                {
                    return 10;
                }
            }
        }

        /// <summary>
        /// Gets the packet loss rating (0-100)
        /// </summary>
        public int PacketLossRating
        {
            get
            {
                if (!IsSuccessful)
                {
                    return 0;
                }

                // Calculate rating based on packet loss
                if (PacketLoss == 0)
                {
                    return 100;
                }
                else if (PacketLoss <= 1)
                {
                    return 90;
                }
                else if (PacketLoss <= 2)
                {
                    return 80;
                }
                else if (PacketLoss <= 5)
                {
                    return 70;
                }
                else if (PacketLoss <= 10)
                {
                    return 60;
                }
                else if (PacketLoss <= 15)
                {
                    return 50;
                }
                else if (PacketLoss <= 20)
                {
                    return 40;
                }
                else if (PacketLoss <= 30)
                {
                    return 30;
                }
                else if (PacketLoss <= 50)
                {
                    return 20;
                }
                else
                {
                    return 10;
                }
            }
        }

        /// <summary>
        /// Gets the overall network quality rating (0-100)
        /// </summary>
        public int OverallRating
        {
            get
            {
                if (!IsSuccessful)
                {
                    return 0;
                }

                // Weight the different components
                return (int)((LatencyRating * 0.5) + (JitterRating * 0.3) + (PacketLossRating * 0.2));
            }
        }

        /// <summary>
        /// Gets the network quality description
        /// </summary>
        public string QualityDescription
        {
            get
            {
                if (!IsSuccessful)
                {
                    return "Error";
                }

                if (OverallRating >= 90)
                {
                    return "Excellent";
                }
                else if (OverallRating >= 75)
                {
                    return "Good";
                }
                else if (OverallRating >= 50)
                {
                    return "Average";
                }
                else if (OverallRating >= 25)
                {
                    return "Poor";
                }
                else
                {
                    return "Critical";
                }
            }
        }

        /// <summary>
        /// Gets the quality color code
        /// </summary>
        public string QualityColorCode
        {
            get
            {
                if (!IsSuccessful)
                {
                    return "#FF0000"; // Red
                }

                if (OverallRating >= 90)
                {
                    return "#00FF00"; // Green
                }
                else if (OverallRating >= 75)
                {
                    return "#AAFF00"; // Light Green
                }
                else if (OverallRating >= 50)
                {
                    return "#FFFF00"; // Yellow
                }
                else if (OverallRating >= 25)
                {
                    return "#FF8800"; // Orange
                }
                else
                {
                    return "#FF0000"; // Red
                }
            }
        }

        /// <summary>
        /// Gets the latency color code
        /// </summary>
        public string LatencyColorCode
        {
            get
            {
                if (!IsSuccessful)
                {
                    return "#FF0000"; // Red
                }

                if (AverageLatency <= 20)
                {
                    return "#00FF00"; // Green
                }
                else if (AverageLatency <= 50)
                {
                    return "#AAFF00"; // Light Green
                }
                else if (AverageLatency <= 100)
                {
                    return "#FFFF00"; // Yellow
                }
                else if (AverageLatency <= 200)
                {
                    return "#FF8800"; // Orange
                }
                else
                {
                    return "#FF0000"; // Red
                }
            }
        }

        /// <summary>
        /// Gets the optimization recommendation
        /// </summary>
        public string OptimizationRecommendation
        {
            get
            {
                if (!IsSuccessful)
                {
                    return "Check your internet connection";
                }

                if (PacketLoss > 0)
                {
                    return "Packet loss detected. Consider checking your network connection or router.";
                }

                if (AverageLatency > 100)
                {
                    return "High latency detected. Consider optimizing your network settings or using a wired connection.";
                }

                if (Jitter > 20)
                {
                    return "High jitter detected. Consider using QoS settings to prioritize gaming traffic.";
                }

                if (OverallRating >= 75)
                {
                    return "Your network connection is good for gaming. No specific optimizations needed.";
                }

                return "Consider optimizing your network settings for better gaming performance.";
            }
        }
    }
}
