// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;
using CircleUtility.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace CircleUtility.Tests
{
    [STATestClass]
    public class HardwareCompatibilityTests
    {
        private Mock<IHardwareDetectionService> _mockHardwareDetectionService = null!;
        private Mock<IPerformanceMonitoringService> _mockPerformanceMonitoringService = null!;
        private HardwareCompatibilityService _compatibilityService = null!;
        private HardwareSpecificOptimization _testOptimization = null!;
        private PowerManagementProfile _testProfile = null!;
        private HardwareInfo _testHardwareInfo = null!;

        [TestInitialize]
        public void TestInitialize()
        {
            // Create mock services
            _mockHardwareDetectionService = new Mock<IHardwareDetectionService>();
            _mockPerformanceMonitoringService = new Mock<IPerformanceMonitoringService>();

            // Create test hardware info
            _testHardwareInfo = new HardwareInfo
            {
                CPU = new CPUInfo
                {
                    Name = "Intel Core i9-12900K",
                    Manufacturer = "Intel",
                    Cores = 16,
                    Threads = 24,
                    BaseClockSpeed = 3.2,
                    MaxClockSpeed = 5200
                },
                GPU = new GPUInfo
                {
                    Name = "NVIDIA GeForce RTX 3080",
                    Vendor = GPUVendor.NVIDIA,
                    MemoryGB = 10,
                    VideoMemory = 10240,
                    DriverVersion = "512.95"
                },
                RAM = new RAMInfo
                {
                    TotalCapacity = 32,
                    Modules = new List<RAMModuleInfo>
                    {
                        new RAMModuleInfo
                        {
                            Manufacturer = "Corsair",
                            PartNumber = "CMK32GX4M2E3200C16",
                            Capacity = 16,
                            Speed = 3200
                        },
                        new RAMModuleInfo
                        {
                            Manufacturer = "Corsair",
                            PartNumber = "CMK32GX4M2E3200C16",
                            Capacity = 16,
                            Speed = 3200
                        }
                    }
                }
            };

            // Setup mock hardware detection service
            _mockHardwareDetectionService.Setup(m => m.GetHardwareInfo()).Returns(_testHardwareInfo);
            _mockHardwareDetectionService.Setup(m => m.GetHardwareInfo(It.IsAny<bool>())).Returns(_testHardwareInfo);
            _mockHardwareDetectionService.Setup(m => m.GetHardwareInfoAsync()).Returns(Task.FromResult(_testHardwareInfo));
            _mockHardwareDetectionService.Setup(m => m.GetHardwareInfoAsync(It.IsAny<bool>())).Returns(Task.FromResult(_testHardwareInfo));

            // Create test optimization
            _testOptimization = new HardwareSpecificOptimization
            {
                Name = "Test NVIDIA Optimization",
                Description = "Test optimization for NVIDIA GPUs",
                Category = "GPU Optimization",
                HardwareType = HardwareType.GPU,
                Manufacturer = "NVIDIA",
                ModelPattern = "RTX.*",
                SpecificModels = new List<string> { "RTX 3080", "RTX 3090" },
                Method = OptimizationMethod.Registry,
                RegistryKey = "HKEY_LOCAL_MACHINE\\Test",
                RegistryValueName = "TestValue",
                RegistryValue = 1,
                RegistryValueKind = "DWord",
                PerformanceImpact = 80,
                StabilityImpact = 90,
                PowerConsumptionImpact = 70,
                ThermalImpact = 60,
                InputLatencyImpact = 85,
                Risk = RiskLevel.Low
            };

            // Create test power profile
            _testProfile = new PowerManagementProfile
            {
                Name = "Test Gaming Profile",
                Description = "Test power profile for gaming",
                Category = "Gaming",
                CpuSettings = new CpuPowerSettings
                {
                    PowerPlan = "High Performance",
                    MinProcessorState = 50,
                    MaxProcessorState = 100
                },
                GpuSettings = new GpuPowerSettings
                {
                    PowerManagementMode = "Prefer Maximum Performance",
                    PowerLimit = 100
                },
                PerformanceImpact = 85,
                PowerEfficiency = 40,
                ThermalImpact = 75,
                NoiseLevel = 70,
                InputLatencyImpact = 90,
                Risk = RiskLevel.Low,
                HardwareCompatibility = new List<string> { "RTX 3080", "Core i9" }
            };

            // Create compatibility service with mocked dependencies
            _compatibilityService = new HardwareCompatibilityService(
                _mockHardwareDetectionService.Object,
                _mockPerformanceMonitoringService.Object);
        }

        [TestMethod]
        public async Task ValidateOptimizationAsync_WithCompatibleHardware_ShouldReturnCompatible()
        {
            // Arrange - already done in TestInitialize

            // Act
            var result = await _compatibilityService.ValidateOptimizationAsync(_testOptimization);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsCompatible);
            Assert.AreEqual<CircleUtility.Services.CompatibilityConfidence>(CircleUtility.Services.CompatibilityConfidence.High, result.Confidence);
            Assert.IsNull(result.IncompatibilityReason);
        }

        [TestMethod]
        public async Task ValidateOptimizationAsync_WithIncompatibleManufacturer_ShouldReturnIncompatible()
        {
            // Arrange
            _testOptimization.Manufacturer = "AMD";

            // Act
            var result = await _compatibilityService.ValidateOptimizationAsync(_testOptimization);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsCompatible);
            Assert.AreEqual<CircleUtility.Services.CompatibilityConfidence>(CircleUtility.Services.CompatibilityConfidence.High, result.Confidence);
            Assert.IsNotNull(result.IncompatibilityReason);
            Assert.IsTrue(result.IncompatibilityReason.Contains("AMD"));
        }

        [TestMethod]
        public async Task ValidateOptimizationAsync_WithIncompatibleModelPattern_ShouldReturnIncompatible()
        {
            // Arrange
            _testOptimization.ModelPattern = "GTX.*";

            // Act
            var result = await _compatibilityService.ValidateOptimizationAsync(_testOptimization);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsCompatible);
            Assert.AreEqual<CircleUtility.Services.CompatibilityConfidence>(CircleUtility.Services.CompatibilityConfidence.High, result.Confidence);
            Assert.IsNotNull(result.IncompatibilityReason);
            Assert.IsTrue(result.IncompatibilityReason.Contains("model"));
        }

        [TestMethod]
        public async Task ValidatePowerProfileAsync_WithCompatibleHardware_ShouldReturnCompatible()
        {
            // Arrange - already done in TestInitialize

            // Act
            var result = await _compatibilityService.ValidatePowerProfileAsync(_testProfile);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsCompatible);
            Assert.AreEqual<CircleUtility.Services.CompatibilityConfidence>(CircleUtility.Services.CompatibilityConfidence.High, result.Confidence);
            Assert.IsNull(result.IncompatibilityReason);
        }

        [TestMethod]
        public async Task ValidatePowerProfileAsync_WithIncompatibleHardware_ShouldReturnIncompatible()
        {
            // Arrange
            _testProfile.HardwareCompatibility = new List<string> { "RTX 4090", "Core i7" };

            // Act
            var result = await _compatibilityService.ValidatePowerProfileAsync(_testProfile);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsCompatible);
            Assert.AreEqual<CircleUtility.Services.CompatibilityConfidence>(CircleUtility.Services.CompatibilityConfidence.Medium, result.Confidence);
            Assert.IsNotNull(result.IncompatibilityReason);
            Assert.IsTrue(result.IncompatibilityReason.Contains("not specifically designed"));
        }

        [STATestMethod]
        public void CompatibilityStatusControl_SetStatus_ShouldNotThrowException()
        {
            // Arrange
            var control = new CircleUtility.Controls.CompatibilityStatusControl();

            // Act & Assert - Compatible
            control.SetStatus(CircleUtility.Controls.CompatibilityStatus.Compatible);

            // Act & Assert - Incompatible
            control.SetStatus(CircleUtility.Controls.CompatibilityStatus.Incompatible, "Test reason");

            // Act & Assert - Warning
            control.SetStatus(CircleUtility.Controls.CompatibilityStatus.Warning, "Test warning");

            // Act & Assert - Unknown
            control.SetStatus(CircleUtility.Controls.CompatibilityStatus.Unknown);

            // If we got here without exceptions, the test passes
            Assert.IsTrue(true);
        }

        [STATestMethod]
        public void CompatibilityStatusControl_SetStatusFromResult_ShouldNotThrowException()
        {
            // Arrange
            var control = new CircleUtility.Controls.CompatibilityStatusControl();

            // Act & Assert - Compatible
            var compatibleResult = new CircleUtility.Models.CompatibilityResult
            {
                IsCompatible = true,
                Confidence = CircleUtility.Models.CompatibilityConfidence.High
            };
            control.SetStatus(compatibleResult);

            // Act & Assert - Incompatible with high confidence
            var incompatibleResult = new CircleUtility.Models.CompatibilityResult
            {
                IsCompatible = false,
                Confidence = CircleUtility.Models.CompatibilityConfidence.High,
                IncompatibilityReason = "Test incompatible"
            };
            control.SetStatus(incompatibleResult);

            // Act & Assert - Incompatible with medium confidence (warning)
            var warningResult = new CircleUtility.Models.CompatibilityResult
            {
                IsCompatible = false,
                Confidence = CircleUtility.Models.CompatibilityConfidence.Medium,
                IncompatibilityReason = "Test warning"
            };
            control.SetStatus(warningResult);

            // If we got here without exceptions, the test passes
            Assert.IsTrue(true);
        }
    }
}
