<Window x:Class="CircleUtility.Views.UpdateProgressWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Updating Circle Utility" 
        Height="200" 
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#FF1E1E1E">
    
    <Window.Resources>
        <Style TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="10"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock x:Name="StatusText" 
                   Text="Preparing update..."
                   FontSize="16"
                   FontWeight="SemiBold"
                   HorizontalAlignment="Center"/>

        <ProgressBar x:Name="ProgressBar"
                      Grid.Row="1"
                      Height="20"
                      Margin="10"
                      Background="#FF2D2D2D"
                      BorderBrush="#FF3D3D3D"
                      Foreground="#FF007ACC"/>

        <TextBlock x:Name="ProgressText"
                   Grid.Row="2"
                   Text="0%"
                   HorizontalAlignment="Center"
                   FontSize="14"/>

        <TextBlock x:Name="DetailsText"
                   Grid.Row="3"
                   Text=""
                   TextWrapping="Wrap"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Top"
                   Margin="10,0"/>
    </Grid>
</Window> 