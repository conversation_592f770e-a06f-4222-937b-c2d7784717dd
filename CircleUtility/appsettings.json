{"Application": {"Name": "CircleUtility", "Version": "1.1.2", "Environment": "Development"}, "Discord": {"BotToken": "MTM3NjcyODkyMDQxMzQzNzk1Mg.G1HHqK.kVpdGwt4upPiF2zE3HgbGa4ITnrSWm2R80KUWo", "GuildId": "970106500477493358", "CommandPrefix": "!", "WebhookUrl": "https://discord.com/api/webhooks/1375992418733785169/5AZZVd8T75VsxlqJF082qe4OXfXUI04qVsbLTZM3INkVu46BnPsJEouyUqJ4ityLjEed", "ReportsWebhookUrl": "https://discord.com/api/webhooks/1375992418733785169/5AZZVd8T75VsxlqJF082qe4OXfXUI04qVsbLTZM3INkVu46BnPsJEouyUqJ4ityLjEed", "AdminRoleId": "1376723576190926849", "Enabled": false, "AutoStart": false, "StatusChannelId": "1375992124755148910"}, "FilePaths": {"LogsDirectory": "Logs", "UserDataDirectory": "UserData", "BackupDirectory": "Backups"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "File": {"Path": "logs/app.log", "FileSizeLimitBytes": 10485760, "RetainedFileCountLimit": 10}, "Console": {"Enabled": true}}, "Performance": {"MaxDegreeOfParallelism": 4, "EnableHardwareMonitoring": true, "MonitoringIntervalSeconds": 5}, "Security": {"EncryptionKey": "b7e2f4c1a9d6e3f8b2c5a7e1d4f9b6c3", "AdminPassword": "163059Uuku!", "RequireAdminRights": true, "EnableTelemetry": false, "EnableCrashReporting": true}, "PowerShell": {"ScriptTimeout": 30, "MaxRetries": 3, "RetryDelayMs": 1000}, "Hardware": {"MonitoringIntervalMs": 1000, "PerformanceLogPath": "logs/performance.log", "EnableDetailedLogging": true}, "UI": {"Theme": "Dark", "EnableAnimations": true, "LoadingTimeoutMs": 5000}, "AllowedHosts": "*", "UtilityApiUrl": "http://localhost:5000"}