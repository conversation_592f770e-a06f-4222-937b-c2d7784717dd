using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedCard.xaml
    /// </summary>
    public partial class AnimatedCard : UserControl
    {
        private Storyboard _revealAnimation;
        private Storyboard _hoverAnimation;
        private Storyboard _unhoverAnimation;

        /// <summary>
        /// Title dependency property
        /// </summary>
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(AnimatedCard), new PropertyMetadata(string.Empty));

        /// <summary>
        /// Content dependency property
        /// </summary>
        public static new readonly DependencyProperty ContentProperty =
            DependencyProperty.Register("Content", typeof(object), typeof(AnimatedCard), new PropertyMetadata(null));

        /// <summary>
        /// Animation delay dependency property
        /// </summary>
        public static readonly DependencyProperty AnimationDelayProperty =
            DependencyProperty.Register("AnimationDelay", typeof(double), typeof(AnimatedCard), new PropertyMetadata(0.0));

        /// <summary>
        /// Gets or sets the title
        /// </summary>
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        /// <summary>
        /// Gets or sets the content
        /// </summary>
        public new object Content
        {
            get { return GetValue(ContentProperty); }
            set { SetValue(ContentProperty, value); }
        }

        /// <summary>
        /// Gets or sets the animation delay in seconds
        /// </summary>
        public double AnimationDelay
        {
            get { return (double)GetValue(AnimationDelayProperty); }
            set { SetValue(AnimationDelayProperty, value); }
        }

        /// <summary>
        /// Initializes a new instance of the AnimatedCard class
        /// </summary>
        public AnimatedCard()
        {
            InitializeComponent();

            // Cache animations
            _revealAnimation = (Storyboard)FindResource("RevealAnimation");
            _hoverAnimation = (Storyboard)FindResource("HoverAnimation");
            _unhoverAnimation = (Storyboard)FindResource("UnhoverAnimation");
        }

        /// <summary>
        /// Handles the loaded event
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Clone the storyboard to avoid reuse issues
            Storyboard revealAnimation = _revealAnimation.Clone();

            // Add delay if specified
            if (AnimationDelay > 0)
            {
                foreach (Timeline timeline in revealAnimation.Children)
                {
                    timeline.BeginTime = TimeSpan.FromSeconds(AnimationDelay);
                }
            }

            // Start the reveal animation
            revealAnimation.Begin(this);
        }

        /// <summary>
        /// Handles the mouse enter event
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void MainBorder_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            _hoverAnimation.Begin(this);
        }

        /// <summary>
        /// Handles the mouse leave event
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void MainBorder_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            _unhoverAnimation.Begin(this);
        }
    }
}
