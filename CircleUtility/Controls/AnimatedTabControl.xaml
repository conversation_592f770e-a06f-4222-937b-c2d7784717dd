<UserControl x:Class="CircleUtility.Controls.AnimatedTabControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ea="clr-namespace:System.Windows.Media.Animation;assembly=PresentationFramework"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Animations are created programmatically in code-behind -->
    </UserControl.Resources>

    <Grid>
        <TabControl x:Name="InnerTabControl"
                   Background="Transparent"
                   BorderThickness="0"
                   SelectionChanged="InnerTabControl_SelectionChanged">
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border x:Name="Border"
                                       Background="#FF001428"
                                       BorderBrush="#FF00C8FF"
                                       BorderThickness="1"
                                       CornerRadius="3"
                                       Margin="0,0,5,0"
                                       Padding="10,5">
                                    <ContentPresenter x:Name="ContentSite"
                                                     VerticalAlignment="Center"
                                                     HorizontalAlignment="Center"
                                                     ContentSource="Header"
                                                     Margin="10,2"/>
                                    <Border.Effect>
                                        <DropShadowEffect Color="#FF00C8FF"
                                                         BlurRadius="5"
                                                         ShadowDepth="0"
                                                         Opacity="0.5"/>
                                    </Border.Effect>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#FF002848"/>
                                        <Setter TargetName="Border" Property="Effect">
                                            <Setter.Value>
                                                <DropShadowEffect Color="#FF00C8FF"
                                                                BlurRadius="10"
                                                                ShadowDepth="0"
                                                                Opacity="0.8"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Border" Property="Effect">
                                            <Setter.Value>
                                                <DropShadowEffect Color="#FF00C8FF"
                                                                BlurRadius="10"
                                                                ShadowDepth="0"
                                                                Opacity="0.8"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Resources>

            <TabControl.ContentTemplate>
                <DataTemplate>
                    <ContentPresenter Content="{Binding}" />
                </DataTemplate>
            </TabControl.ContentTemplate>
        </TabControl>
    </Grid>
</UserControl>
