using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using CircleUtility.Services;
using CircleUtility.Models;

namespace CircleUtility
{
    public partial class WelcomeScreen : Window
    {
        private string[] loadingMessages = new string[]
        {
            "INITIALIZING CIRCLE UTILITY...",
            "LOADING HARDWARE SERVICES...",
            "SCANNING SYSTEM CONFIGURATION...",
            "OPTIMIZING PERFORMANCE SETTINGS...",
            "CONFIGURING SECURITY PROTOCOLS...",
            "PREPARING USER INTERFACE...",
            "FINALIZING SETUP...",
            "SYSTEM READY"
        };

        private int currentMessageIndex = 0;
        private DispatcherTimer messageTimer;
        private Random random = new Random();

        public string Username { get; private set; }

        public WelcomeScreen()
        {
            InitializeComponent();

            // Set a default username
            UsernameInput.Text = "User" + random.Next(1000, 9999);

            // Start the pulse animation for the title
            Storyboard pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            pulseAnimation.Begin();

            // Start the loading animation
            StartLoadingSequence();

            // Handle the access button click
            AccessButton.Click += AccessButton_Click;
        }

        /// <summary>
        /// Constructor with username from login
        /// </summary>
        /// <param name="username">Username from login</param>
        public WelcomeScreen(string username) : this()
        {
            if (!string.IsNullOrWhiteSpace(username))
            {
                UsernameInput.Text = username;
                Username = username;
            }
        }

        private void StartLoadingSequence()
        {
            // Start the loading bar animation
            Storyboard loadingAnimation = (Storyboard)FindResource("LoadingAnimation");
            loadingAnimation.Completed += LoadingAnimation_Completed;
            loadingAnimation.Begin();

            // Setup the message timer
            messageTimer = new DispatcherTimer();
            messageTimer.Interval = TimeSpan.FromMilliseconds(800);
            messageTimer.Tick += MessageTimer_Tick;
            messageTimer.Start();

            // Run window loading validation in background
            _ = Task.Run(async () => await RunWindowLoadingValidation());
        }

        private void MessageTimer_Tick(object sender, EventArgs e)
        {
            // Update the loading message
            if (currentMessageIndex < loadingMessages.Length - 1)
            {
                currentMessageIndex++;
                LoadingStatus.Text = loadingMessages[currentMessageIndex];

                // Update the status message for some messages
                if (currentMessageIndex == 1)
                {
                    StatusMessage.Text = "Analyzing system configuration...";
                }
                else if (currentMessageIndex == 3)
                {
                    StatusMessage.Text = "Preparing optimization engine...";
                }
            }
            else
            {
                // Stop the timer when we've shown all messages
                messageTimer.Stop();
            }
        }

        private void LoadingAnimation_Completed(object sender, EventArgs e)
        {
            // Enable the access button
            AccessButton.IsEnabled = true;

            // Set the final status
            LoadingStatus.Text = loadingMessages[loadingMessages.Length - 1];
            StatusMessage.Text = "System ready. Please access when ready.";

            // Add a subtle pulse effect to the button
            DoubleAnimation pulseAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 1.05,
                Duration = TimeSpan.FromSeconds(0.5),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever
            };

            AccessButton.RenderTransform = new ScaleTransform(1, 1);
            AccessButton.RenderTransformOrigin = new Point(0.5, 0.5);
            AccessButton.RenderTransform.BeginAnimation(ScaleTransform.ScaleXProperty, pulseAnimation);
            AccessButton.RenderTransform.BeginAnimation(ScaleTransform.ScaleYProperty, pulseAnimation);
        }

        private void AccessButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Get the username
                Username = string.IsNullOrWhiteSpace(UsernameInput.Text)
                    ? "User" + random.Next(1000, 9999)
                    : UsernameInput.Text.Trim();

                Console.WriteLine($"Access button clicked, username: {Username}");

                // Close the dialog with success
                DialogResult = true;

                // Hide the window first to ensure it doesn't block the main window
                this.Visibility = Visibility.Hidden;

                // Close the window
                Close();

                Console.WriteLine("Welcome screen closed with DialogResult = true");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in AccessButton_Click: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // Close the dialog with failure
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// Runs window loading validation in the background
        /// </summary>
        private async Task RunWindowLoadingValidation()
        {
            try
            {
                var windowLoadingManager = WindowLoadingManager.Instance;

                // Subscribe to events to update UI
                windowLoadingManager.StepCompleted += (sender, args) =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        // Update loading message based on step
                        string stepMessage = GetMessageForStep(args.Step.StepName);
                        if (!string.IsNullOrEmpty(stepMessage))
                        {
                            StatusMessage.Text = stepMessage;
                        }
                    });
                };

                // Start validation
                bool result = await windowLoadingManager.StartLoadingSequence();

                // Log result
                var logger = LoggingService.Instance;
                logger.Log($"Window loading validation completed: {result}", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                var logger = LoggingService.Instance;
                logger.Log($"Error in window loading validation: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets appropriate loading message for validation step
        /// </summary>
        private string GetMessageForStep(string stepName)
        {
            return stepName switch
            {
                "LoginWindow" => "Validating login system...",
                "WelcomeScreen" => "Preparing welcome interface...",
                "MainWindow" => "Loading main application...",
                "Services" => "Initializing core services...",
                "UI_Components" => "Finalizing user interface...",
                _ => ""
            };
        }
    }
}
