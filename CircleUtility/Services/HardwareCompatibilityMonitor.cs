// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for continuously monitoring hardware compatibility with active optimizations
    /// </summary>
    public class HardwareCompatibilityMonitor
    {
        private static HardwareCompatibilityMonitor _instance;
        private readonly LoggingService _logger;
        private HardwareCompatibilityService _compatibilityService;
        private HardwareOptimizationService _optimizationService;
        private readonly PerformanceMonitoringService _performanceMonitoringService;
        private readonly Dictionary<string, CompatibilityResult> _activeOptimizationsCompatibility;
        private readonly Dictionary<string, CompatibilityResult> _activePowerProfilesCompatibility;
        private readonly Timer _monitoringTimer;
        private readonly TimeSpan _monitoringInterval = TimeSpan.FromMinutes(5); // Check every 5 minutes
        private readonly TimeSpan _performanceMonitoringInterval = TimeSpan.FromSeconds(30); // Check performance metrics every 30 seconds
        private bool _isMonitoring;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isInitialized = false;

        /// <summary>
        /// Event raised when an incompatibility is detected
        /// </summary>
        public event EventHandler<IncompatibilityDetectedEventArgs> IncompatibilityDetected;

        /// <summary>
        /// Initializes a new instance of the HardwareCompatibilityMonitor class
        /// </summary>
        private HardwareCompatibilityMonitor()
        {
            _logger = LoggingService.Instance;
            _performanceMonitoringService = PerformanceMonitoringService.Instance;
            _activeOptimizationsCompatibility = new Dictionary<string, CompatibilityResult>();
            _activePowerProfilesCompatibility = new Dictionary<string, CompatibilityResult>();
            _monitoringTimer = new Timer(OnMonitoringTimerElapsed);
            _isMonitoring = false;
        }

        /// <summary>
        /// Initializes the monitor with required services
        /// </summary>
        public void Initialize(HardwareCompatibilityService compatibilityService, HardwareOptimizationService optimizationService)
        {
            if (_isInitialized)
                return;

            _compatibilityService = compatibilityService;
            _optimizationService = optimizationService;

            // Subscribe to events
            _compatibilityService.CompatibilityChanged += OnCompatibilityChanged;
            _optimizationService.OptimizationApplied += OnOptimizationApplied;
            _optimizationService.OptimizationReverted += OnOptimizationReverted;
            _optimizationService.PowerProfileApplied += OnPowerProfileApplied;
            _optimizationService.PowerProfileReverted += OnPowerProfileReverted;
            _performanceMonitoringService.MetricsUpdated += OnPerformanceMetricsUpdated;

            _isInitialized = true;
            _logger.Log("Hardware compatibility monitor initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the singleton instance of the hardware compatibility monitor
        /// </summary>
        public static HardwareCompatibilityMonitor Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new HardwareCompatibilityMonitor();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the monitor is running
        /// </summary>
        public bool IsMonitoring => _isMonitoring;

        /// <summary>
        /// Gets the active optimizations compatibility status
        /// </summary>
        public IReadOnlyDictionary<string, CompatibilityResult> ActiveOptimizationsCompatibility => _activeOptimizationsCompatibility;

        /// <summary>
        /// Gets the active power profiles compatibility status
        /// </summary>
        public IReadOnlyDictionary<string, CompatibilityResult> ActivePowerProfilesCompatibility => _activePowerProfilesCompatibility;

        /// <summary>
        /// Starts monitoring hardware compatibility
        /// </summary>
        public async Task StartMonitoringAsync()
        {
            if (_isMonitoring)
            {
                return;
            }

            try
            {
                _logger.Log("Starting hardware compatibility monitoring...", LogLevel.INFO);

                // Check if the monitor is initialized
                if (!_isInitialized)
                {
                    _logger.Log("Hardware compatibility monitor not initialized. Call Initialize() first.", LogLevel.ERROR);
                    return;
                }

                // Initialize compatibility service if needed
                if (!_compatibilityService.IsInitialized)
                {
                    await _compatibilityService.InitializeAsync();
                }

                // Initialize monitoring
                _cancellationTokenSource = new CancellationTokenSource();
                _monitoringTimer.Change(0, Timeout.Infinite); // Start immediately, then managed by the timer callback
                _isMonitoring = true;

                _logger.Log("Hardware compatibility monitoring started", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error starting hardware compatibility monitoring: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Stops monitoring hardware compatibility
        /// </summary>
        public void StopMonitoring()
        {
            if (!_isMonitoring)
            {
                return;
            }

            try
            {
                _logger.Log("Stopping hardware compatibility monitoring...", LogLevel.INFO);

                // Stop monitoring
                _monitoringTimer.Change(Timeout.Infinite, Timeout.Infinite);
                _cancellationTokenSource?.Cancel();
                _isMonitoring = false;

                _logger.Log("Hardware compatibility monitoring stopped", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error stopping hardware compatibility monitoring: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Performs a full compatibility check of all active optimizations and power profiles
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the hardware info</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task PerformFullCompatibilityCheckAsync(bool forceRefresh = false)
        {
            try
            {
                _logger.Log("Performing full compatibility check...", LogLevel.INFO);

                // Check if the monitor is initialized
                if (!_isInitialized)
                {
                    _logger.Log("Hardware compatibility monitor not initialized. Call Initialize() first.", LogLevel.ERROR);
                    return;
                }

                // Get all active optimizations
                var activeOptimizations = _optimizationService.Optimizations
                    .Where(o => o.IsEnabled)
                    .ToList();

                // Get all active power profiles
                var activePowerProfiles = _optimizationService.PowerProfiles
                    .Where(p => p.IsEnabled)
                    .ToList();

                // Check compatibility of all active optimizations
                foreach (var optimization in activeOptimizations)
                {
                    var result = await _compatibilityService.ValidateOptimizationAsync(optimization, forceRefresh);
                    _activeOptimizationsCompatibility[optimization.Name] = result;

                    // Only force refresh for the first one
                    forceRefresh = false;

                    // Check for incompatibilities
                    if (!result.IsCompatible)
                    {
                        _logger.Log($"Incompatibility detected for optimization '{optimization.Name}': {result.IncompatibilityReason}", LogLevel.WARNING);
                        IncompatibilityDetected?.Invoke(this, new IncompatibilityDetectedEventArgs
                        {
                            OptimizationName = optimization.Name,
                            Reason = result.IncompatibilityReason,
                            Confidence = result.Confidence,
                            DetectionTime = DateTime.Now
                        });
                    }
                }

                // Check compatibility of all active power profiles
                foreach (var profile in activePowerProfiles)
                {
                    var result = await _compatibilityService.ValidatePowerProfileAsync(profile, forceRefresh);
                    _activePowerProfilesCompatibility[profile.Name] = result;

                    // Check for incompatibilities
                    if (!result.IsCompatible)
                    {
                        _logger.Log($"Incompatibility detected for power profile '{profile.Name}': {result.IncompatibilityReason}", LogLevel.WARNING);
                        IncompatibilityDetected?.Invoke(this, new IncompatibilityDetectedEventArgs
                        {
                            PowerProfileName = profile.Name,
                            Reason = result.IncompatibilityReason,
                            Confidence = result.Confidence,
                            DetectionTime = DateTime.Now
                        });
                    }
                }

                _logger.Log("Full compatibility check completed", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error performing full compatibility check: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets the compatibility status for an optimization
        /// </summary>
        /// <param name="optimizationName">The name of the optimization</param>
        /// <returns>The compatibility result, or null if not found</returns>
        public CompatibilityResult GetOptimizationCompatibility(string optimizationName)
        {
            if (_activeOptimizationsCompatibility.TryGetValue(optimizationName, out CompatibilityResult result))
            {
                return result;
            }
            return null;
        }

        /// <summary>
        /// Gets the compatibility status for a power profile
        /// </summary>
        /// <param name="profileName">The name of the power profile</param>
        /// <returns>The compatibility result, or null if not found</returns>
        public CompatibilityResult GetPowerProfileCompatibility(string profileName)
        {
            if (_activePowerProfilesCompatibility.TryGetValue(profileName, out CompatibilityResult result))
            {
                return result;
            }
            return null;
        }

        private async void OnMonitoringTimerElapsed(object state)
        {
            if (!_isMonitoring || _cancellationTokenSource.IsCancellationRequested)
            {
                return;
            }

            try
            {
                // Perform a full compatibility check
                await PerformFullCompatibilityCheckAsync();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error in monitoring timer: {ex.Message}", LogLevel.ERROR);
            }
            finally
            {
                // Schedule the next check
                if (_isMonitoring && !_cancellationTokenSource.IsCancellationRequested)
                {
                    _monitoringTimer.Change((int)_monitoringInterval.TotalMilliseconds, Timeout.Infinite);
                }
            }
        }

        private void OnCompatibilityChanged(object sender, CompatibilityChangedEventArgs e)
        {
            // Hardware compatibility has changed, perform a full check
            Task.Run(async () => await PerformFullCompatibilityCheckAsync(true));
        }

        private async void OnOptimizationApplied(object sender, OptimizationEventArgs e)
        {
            // An optimization was applied, check its compatibility
            var result = await _compatibilityService.ValidateOptimizationAsync(e.Optimization);
            _activeOptimizationsCompatibility[e.Optimization.Name] = result;

            // Check for incompatibilities
            if (!result.IsCompatible)
            {
                _logger.Log($"Incompatibility detected for newly applied optimization '{e.Optimization.Name}': {result.IncompatibilityReason}", LogLevel.WARNING);
                IncompatibilityDetected?.Invoke(this, new IncompatibilityDetectedEventArgs
                {
                    OptimizationName = e.Optimization.Name,
                    Reason = result.IncompatibilityReason,
                    Confidence = result.Confidence,
                    DetectionTime = DateTime.Now
                });
            }
        }

        private void OnOptimizationReverted(object sender, OptimizationEventArgs e)
        {
            // An optimization was reverted, remove it from active optimizations
            _activeOptimizationsCompatibility.Remove(e.Optimization.Name);
        }

        private async void OnPowerProfileApplied(object sender, PowerProfileEventArgs e)
        {
            // A power profile was applied, check its compatibility
            var result = await _compatibilityService.ValidatePowerProfileAsync(e.Profile);
            _activePowerProfilesCompatibility[e.Profile.Name] = result;

            // Check for incompatibilities
            if (!result.IsCompatible)
            {
                _logger.Log($"Incompatibility detected for newly applied power profile '{e.Profile.Name}': {result.IncompatibilityReason}", LogLevel.WARNING);
                IncompatibilityDetected?.Invoke(this, new IncompatibilityDetectedEventArgs
                {
                    PowerProfileName = e.Profile.Name,
                    Reason = result.IncompatibilityReason,
                    Confidence = result.Confidence,
                    DetectionTime = DateTime.Now
                });
            }
        }

        private void OnPowerProfileReverted(object sender, PowerProfileEventArgs e)
        {
            // A power profile was reverted, remove it from active profiles
            _activePowerProfilesCompatibility.Remove(e.Profile.Name);
        }

        private void OnPerformanceMetricsUpdated(object sender, PerformanceMetricsEventArgs e)
        {
            // Check for performance anomalies that might indicate incompatibility
            // This is a simplified implementation
            if (DateTime.Now.Second % 30 == 0) // Only check every 30 seconds
            {
                Task.Run(async () =>
                {
                    try
                    {
                        // Check for performance anomalies
                        if (e.Metrics is PerformanceMetrics perfMetrics && HasPerformanceAnomalies(perfMetrics))
                        {
                            _logger.Log("Performance anomalies detected, performing compatibility check", LogLevel.WARNING);
                            await PerformFullCompatibilityCheckAsync(true);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Log($"Error checking for performance anomalies: {ex.Message}", LogLevel.ERROR);
                    }
                });
            }
        }

        private bool HasPerformanceAnomalies(PerformanceMetrics metrics)
        {
            // This is a simplified implementation
            // In a real implementation, we would look for unusual patterns in performance metrics
            // that might indicate incompatibility, such as:
            // - Unusually high CPU or GPU usage
            // - Thermal throttling
            // - Crashes or stability issues
            // - Unusual power consumption

            // For now, just check for extreme values
            if (metrics.CpuUsage > 95 || metrics.GpuUsage > 95 || metrics.CpuTemperature > 90 || metrics.GpuTemperature > 90)
            {
                return true;
            }

            return false;
        }
    }

    /// <summary>
    /// Event arguments for incompatibility detected events
    /// </summary>
    public class IncompatibilityDetectedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the name of the optimization
        /// </summary>
        public string OptimizationName { get; set; }

        /// <summary>
        /// Gets or sets the name of the power profile
        /// </summary>
        public string PowerProfileName { get; set; }

        /// <summary>
        /// Gets or sets the reason for the incompatibility
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Gets or sets the confidence level of the incompatibility detection
        /// </summary>
        public CompatibilityConfidence Confidence { get; set; }

        /// <summary>
        /// Gets or sets the time the incompatibility was detected
        /// </summary>
        public DateTime DetectionTime { get; set; }
    }
}








