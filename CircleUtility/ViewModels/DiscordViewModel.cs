using System;
using CircleUtility.Models;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using CircleUtility.Commands;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the Discord integration view
    /// </summary>
    public class DiscordViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly UserTrackingService _userTracking;
        private readonly DiscordService _discord;
        private string _discordWebhookUrl;
        private string _discordUserId;
        private bool _enableDiscordIntegration;
        private bool _enableDiscordCommands;
        private string _testMessage;
        private string _statusMessage;
        private bool _isStatusSuccess;
        private bool _isStatusVisible;
        private string _commandOutput;
        private bool _isCommandOutputVisible;

        /// <summary>
        /// Initializes a new instance of the DiscordViewModel class
        /// </summary>
        public DiscordViewModel()
        {
            _logger = LoggingService.Instance;
            _userTracking = UserTrackingService.Instance;
            _discord = DiscordService.Instance;

            // Initialize commands
            SaveSettingsCommand = new RelayCommand(SaveSettings);
            TestDiscordCommand = new RelayCommand(TestDiscord, () => EnableDiscordIntegration && !string.IsNullOrWhiteSpace(DiscordWebhookUrl));
            TestAliveCommand = new RelayCommand(async () => await TestCommand("?alive"), () => EnableDiscordIntegration && EnableDiscordCommands);
            TestEmergencyCommand = new RelayCommand(async () => await TestCommand("?emergency Test emergency notification"),
                () => EnableDiscordIntegration && EnableDiscordCommands && !string.IsNullOrWhiteSpace(DiscordUserId));
            TestAboutCommand = new RelayCommand(async () => await TestCommand("?about"), () => EnableDiscordIntegration && EnableDiscordCommands);
            SendMessageCommand = new RelayCommand(SendMessage, () => EnableDiscordIntegration && !string.IsNullOrWhiteSpace(TestMessage));

            // Initialize properties
            // Set default webhook URL from the PowerShell script
            DiscordWebhookUrl = "https://discord.com/api/webhooks/1373372270596329584/iu4SrIROsxYYZNB66gpmuRiwovhX1uNf7lS-s21szYkmAamf2ppJl9wdghVkzc50CIGa";
            // Set default user ID for ogarsenal
            DiscordUserId = "200121271449681920";
            EnableDiscordIntegration = _discord.IsEnabled;
            EnableDiscordCommands = _discord.IsListening;

            // Initialize Discord service with the default values
            _discord.Initialize(DiscordWebhookUrl, DiscordUserId);
            TestMessage = "";
            StatusMessage = "";
            IsStatusVisible = false;
            CommandOutput = "";
            IsCommandOutputVisible = false;

            _logger.Log("DiscordViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets or sets the Discord webhook URL
        /// </summary>
        public string DiscordWebhookUrl
        {
            get => _discordWebhookUrl;
            set
            {
                if (SetProperty(ref _discordWebhookUrl, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the Discord user ID
        /// </summary>
        public string DiscordUserId
        {
            get => _discordUserId;
            set
            {
                if (SetProperty(ref _discordUserId, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether Discord integration is enabled
        /// </summary>
        public bool EnableDiscordIntegration
        {
            get => _enableDiscordIntegration;
            set
            {
                if (SetProperty(ref _enableDiscordIntegration, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether Discord commands are enabled
        /// </summary>
        public bool EnableDiscordCommands
        {
            get => _enableDiscordCommands;
            set
            {
                if (SetProperty(ref _enableDiscordCommands, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the test message
        /// </summary>
        public string TestMessage
        {
            get => _testMessage;
            set
            {
                if (SetProperty(ref _testMessage, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is a success
        /// </summary>
        public bool IsStatusSuccess
        {
            get => _isStatusSuccess;
            set => SetProperty(ref _isStatusSuccess, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the status is visible
        /// </summary>
        public bool IsStatusVisible
        {
            get => _isStatusVisible;
            set => SetProperty(ref _isStatusVisible, value);
        }

        /// <summary>
        /// Gets or sets the command output
        /// </summary>
        public string CommandOutput
        {
            get => _commandOutput;
            set => SetProperty(ref _commandOutput, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the command output is visible
        /// </summary>
        public bool IsCommandOutputVisible
        {
            get => _isCommandOutputVisible;
            set => SetProperty(ref _isCommandOutputVisible, value);
        }

        /// <summary>
        /// Gets the save settings command
        /// </summary>
        public ICommand SaveSettingsCommand { get; }

        /// <summary>
        /// Gets the test Discord command
        /// </summary>
        public ICommand TestDiscordCommand { get; }

        /// <summary>
        /// Gets the test alive command
        /// </summary>
        public ICommand TestAliveCommand { get; }

        /// <summary>
        /// Gets the test emergency command
        /// </summary>
        public ICommand TestEmergencyCommand { get; }

        /// <summary>
        /// Gets the test about command
        /// </summary>
        public ICommand TestAboutCommand { get; }

        /// <summary>
        /// Gets the send message command
        /// </summary>
        public ICommand SendMessageCommand { get; }

        /// <summary>
        /// Saves the Discord settings
        /// </summary>
        private void SaveSettings()
        {
            _logger.Log("Saving Discord settings", LogLevel.INFO);

            try
            {
                // Save Discord settings
                if (EnableDiscordIntegration)
                {
                    _discord.Initialize(DiscordWebhookUrl, DiscordUserId);

                    // Start or stop Discord command listening based on settings
                    if (EnableDiscordCommands)
                    {
                        _discord.StartListening();
                    }
                    else
                    {
                        _discord.StopListening();
                    }
                }
                else
                {
                    _discord.StopListening();
                }

                // Show success message
                ShowStatus("Discord settings saved successfully", true);

                _logger.Log("Discord settings saved successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                // Show error message
                ShowStatus($"Error saving Discord settings: {ex.Message}", false);

                _logger.Log($"Error saving Discord settings: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tests the Discord integration
        /// </summary>
        private async void TestDiscord()
        {
            _logger.Log("Testing Discord integration", LogLevel.INFO);

            try
            {
                // Initialize Discord service with the webhook URL and user ID
                _discord.Initialize(DiscordWebhookUrl, DiscordUserId);

                // Send a test message
                bool result = await _discord.SendMessageAsync("**Test Message** from The Circle Utility");

                if (result)
                {
                    // Show success message
                    ShowStatus("Discord test message sent successfully", true);

                    _logger.Log("Discord test message sent successfully", LogLevel.SUCCESS);
                }
                else
                {
                    // Show error message
                    ShowStatus("Failed to send Discord test message", false);

                    _logger.Log("Failed to send Discord test message", LogLevel.ERROR);
                }
            }
            catch (Exception ex)
            {
                // Show error message
                ShowStatus($"Error testing Discord integration: {ex.Message}", false);

                _logger.Log($"Error testing Discord integration: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Tests a specific Discord command
        /// </summary>
        private async Task TestCommand(string command)
        {
            try
            {
                _logger.Log($"Testing Discord command: {command}", LogLevel.INFO);

                // Process the command
                bool result = await _discord.ProcessMessageAsync(command);

                if (result)
                {
                    // Show success message
                    ShowStatus($"Discord command {command} executed successfully", true);

                    _logger.Log($"Discord command {command} executed successfully", LogLevel.SUCCESS);
                }
                else
                {
                    // Show error message
                    ShowStatus($"Failed to execute Discord command: {command}", false);

                    _logger.Log($"Failed to execute Discord command: {command}", LogLevel.ERROR);
                }
            }
            catch (Exception ex)
            {
                // Show error message
                ShowStatus($"Error testing Discord command {command}: {ex.Message}", false);

                _logger.Log($"Error testing Discord command {command}: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Sends a custom message to Discord
        /// </summary>
        private async void SendMessage()
        {
            try
            {
                _logger.Log("Sending custom Discord message", LogLevel.INFO);

                // Send the message
                bool result = await _discord.SendMessageAsync(TestMessage);

                if (result)
                {
                    // Show success message
                    ShowStatus("Discord message sent successfully", true);

                    _logger.Log("Discord message sent successfully", LogLevel.SUCCESS);
                }
                else
                {
                    // Show error message
                    ShowStatus("Failed to send Discord message", false);

                    _logger.Log("Failed to send Discord message", LogLevel.ERROR);
                }
            }
            catch (Exception ex)
            {
                // Show error message
                ShowStatus($"Error sending Discord message: {ex.Message}", false);

                _logger.Log($"Error sending Discord message: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Shows a status message
        /// </summary>
        private void ShowStatus(string message, bool isSuccess)
        {
            StatusMessage = message;
            IsStatusSuccess = isSuccess;
            IsStatusVisible = true;

            // Hide the status after 5 seconds
            var timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(5);
            timer.Tick += (sender, e) =>
            {
                IsStatusVisible = false;
                timer.Stop();
            };
            timer.Start();
        }
    }
}


