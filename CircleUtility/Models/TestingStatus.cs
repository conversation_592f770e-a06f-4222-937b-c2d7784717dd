// Created by Arsenal on 5-17-25 12:15PM
namespace CircleUtility.Models
{
    /// <summary>
    /// Enum for testing status
    /// </summary>
    public enum TestingStatus
    {
        /// <summary>
        /// No testing has been performed
        /// </summary>
        None,

        /// <summary>
        /// Minimal testing has been performed
        /// </summary>
        Minimal,

        /// <summary>
        /// Limited testing has been performed
        /// </summary>
        Limited,

        /// <summary>
        /// Extensive testing has been performed
        /// </summary>
        Extensive
    }
}
