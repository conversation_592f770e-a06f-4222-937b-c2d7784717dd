using System;
using System.Windows;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Centralized exception handling service for consistent error management
    /// </summary>
    public class ExceptionHandlingService
    {
        private static ExceptionHandlingService _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of the exception handling service
        /// </summary>
        public static ExceptionHandlingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ExceptionHandlingService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private ExceptionHandlingService()
        {
            // Initialize service
        }
        private readonly LoggingService _logger;

        /// <summary>
        /// Initializes a new instance of the ExceptionHandlingService
        /// </summary>
        public ExceptionHandlingService(LoggingService logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Handles an exception with standardized logging and optional user notification
        /// </summary>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">The context where the exception occurred</param>
        /// <param name="showToUser">Whether to show the error to the user</param>
        /// <param name="userMessage">Custom message to show to the user (optional)</param>
        /// <returns>True if the operation should continue, false if it should be aborted</returns>
        public bool HandleException(Exception exception, string context, bool showToUser = false, string userMessage = null)
        {
            try
            {
                // Log the exception with full details
                _logger.Log($"Exception in {context}: {exception.Message}", LogLevel.ERROR, exception);

                // Determine severity and appropriate action
                var severity = DetermineExceptionSeverity(exception);

                // Show to user if requested or if it's a critical error
                if (showToUser || severity == ExceptionSeverity.Critical)
                {
                    ShowErrorToUser(exception, context, userMessage, severity);
                }

                // Return whether the operation should continue
                return severity != ExceptionSeverity.Critical;
            }
            catch (Exception handlingException)
            {
                // Fallback error handling
                try
                {
                    _logger?.Log($"Error in exception handling: {handlingException.Message}", LogLevel.ERROR, handlingException);
                }
                catch
                {
                    // Last resort - write to console
                    Console.WriteLine($"Critical error in exception handling: {handlingException.Message}");
                }

                return false;
            }
        }


        /// <summary>
        /// Handles an exception and returns a default value
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">The context where the exception occurred</param>
        /// <param name="defaultValue">The default value to return</param>
        /// <param name="showToUser">Whether to show the error to the user</param>
        /// <returns>The default value</returns>
        public T HandleException<T>(Exception exception, string context, T defaultValue, bool showToUser = false)
        {
            HandleException(exception, context, showToUser);
            return defaultValue;
        }

        /// <summary>
        /// Executes an action with standardized exception handling
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="context">The context for error reporting</param>
        /// <param name="showErrorsToUser">Whether to show errors to the user</param>
        /// <returns>True if successful, false if an exception occurred</returns>
        public bool ExecuteWithHandling(Action action, string context, bool showErrorsToUser = false)
        {
            try
            {
                action();
                return true;
            }
            catch (Exception ex)
            {
                return HandleException(ex, context, showErrorsToUser);
            }
        }

        /// <summary>
        /// Executes a function with standardized exception handling
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="function">The function to execute</param>
        /// <param name="context">The context for error reporting</param>
        /// <param name="defaultValue">The default value to return on error</param>
        /// <param name="showErrorsToUser">Whether to show errors to the user</param>
        /// <returns>The function result or default value on error</returns>
        public T ExecuteWithHandling<T>(Func<T> function, string context, T defaultValue, bool showErrorsToUser = false)
        {
            try
            {
                return function();
            }
            catch (Exception ex)
            {
                HandleException(ex, context, showErrorsToUser);
                return defaultValue;
            }
        }

        /// <summary>
        /// Determines the severity of an exception
        /// </summary>
        /// <param name="exception">The exception to analyze</param>
        /// <returns>The exception severity</returns>
        private ExceptionSeverity DetermineExceptionSeverity(Exception exception)
        {
            if (exception is OutOfMemoryException || 
                exception is StackOverflowException || 
                exception is AccessViolationException || 
                exception is AppDomainUnloadedException)
            {
                return ExceptionSeverity.Critical;
            }
            
            if (exception is UnauthorizedAccessException || 
                exception is System.Security.SecurityException)
            {
                return ExceptionSeverity.High;
            }
            
            if (exception is InvalidOperationException invalidOp && invalidOp.Message.Contains("disposed"))
            {
                return ExceptionSeverity.High;
            }
            
            if (exception is ArgumentException || 
                exception is ArgumentNullException || 
                exception is InvalidOperationException || 
                exception is NotSupportedException)
            {
                return ExceptionSeverity.Medium;
            }
            
            if (exception is System.IO.FileNotFoundException || 
                exception is System.IO.DirectoryNotFoundException || 
                exception is System.Net.NetworkInformation.NetworkInformationException)
            {
                return ExceptionSeverity.Low;
            }
            
            return ExceptionSeverity.Medium;
        }

        /// <summary>
        /// Shows an error message to the user
        /// </summary>
        /// <param name="exception">The exception</param>
        /// <param name="context">The context</param>
        /// <param name="userMessage">Custom user message</param>
        /// <param name="severity">The exception severity</param>
        private void ShowErrorToUser(Exception exception, string context, string userMessage, ExceptionSeverity severity)
        {
            try
            {
                string title = GetErrorTitle(severity);
                string message = userMessage ?? GetUserFriendlyMessage(exception, context);
                MessageBoxImage icon = GetErrorIcon(severity);

                // Show on UI thread
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    MessageBox.Show(message, title, MessageBoxButton.OK, icon);
                });
            }
            catch
            {
                // Fallback - don't let error display cause more errors
            }
        }

        /// <summary>
        /// Gets a user-friendly error message
        /// </summary>
        /// <param name="exception">The exception</param>
        /// <param name="context">The context</param>
        /// <returns>A user-friendly error message</returns>
        private string GetUserFriendlyMessage(Exception exception, string context)
        {
            return exception switch
            {
                UnauthorizedAccessException => "Access denied. Please run the application as administrator or check file permissions.",
                System.IO.FileNotFoundException => "A required file was not found. Please reinstall the application.",
                System.IO.DirectoryNotFoundException => "A required directory was not found. Please check the installation.",
                OutOfMemoryException => "The system is running low on memory. Please close other applications and try again.",
                System.Net.NetworkInformation.NetworkInformationException => "Network connection error. Please check your internet connection.",
                _ => $"An error occurred in {context}. Please try again or contact support if the problem persists."
            };
        }

        /// <summary>
        /// Gets the error title based on severity
        /// </summary>
        /// <param name="severity">The exception severity</param>
        /// <returns>The error title</returns>
        private string GetErrorTitle(ExceptionSeverity severity)
        {
            return severity switch
            {
                ExceptionSeverity.Critical => "Critical Error",
                ExceptionSeverity.High => "Error",
                ExceptionSeverity.Medium => "Warning",
                ExceptionSeverity.Low => "Information",
                _ => "Error"
            };
        }

        /// <summary>
        /// Gets the error icon based on severity
        /// </summary>
        /// <param name="severity">The exception severity</param>
        /// <returns>The message box icon</returns>
        private MessageBoxImage GetErrorIcon(ExceptionSeverity severity)
        {
            return severity switch
            {
                ExceptionSeverity.Critical => MessageBoxImage.Stop,
                ExceptionSeverity.High => MessageBoxImage.Error,
                ExceptionSeverity.Medium => MessageBoxImage.Warning,
                ExceptionSeverity.Low => MessageBoxImage.Information,
                _ => MessageBoxImage.Error
            };
        }
    }

    /// <summary>
    /// Exception severity levels
    /// </summary>
    public enum ExceptionSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }
}


