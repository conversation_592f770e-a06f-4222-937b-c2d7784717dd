[{"Name": "NVIDIA Maximum Performance Power Mode", "Description": "Sets the NVIDIA GPU power management mode to 'Prefer Maximum Performance' for optimal gaming performance and reduced input latency.", "Category": "GPU Power Management", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "PerfLevelSrc", "RegistryValue": 2222, "RegistryValueKind": "DWord", "PerformanceImpact": 75, "StabilityImpact": 95, "PowerConsumptionImpact": 80, "ThermalImpact": 75, "InputLatencyImpact": 85, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization ensures your GPU always runs at its highest performance state, which reduces input latency and provides more consistent frame rates. The downside is slightly higher power consumption and heat output.", "RevertRegistryValue": 1}, {"Name": "NVIDIA Ultra Low Latency Mode", "Description": "Enables Ultra Low Latency mode in the NVIDIA Control Panel, which minimizes the number of frames queued by the CPU, reducing input lag.", "Category": "GPU Latency Optimization", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "RMLatencyBoostOptions", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 60, "StabilityImpact": 90, "PowerConsumptionImpact": 60, "ThermalImpact": 60, "InputLatencyImpact": 90, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Ultra Low Latency mode is particularly effective in GPU-bound scenarios. It's the equivalent of setting 'Maximum Pre-rendered Frames' to 1 in older driver versions, but with additional optimizations.", "RevertRegistryValue": 0}, {"Name": "NVIDIA Threaded Optimization", "Description": "Enables threaded optimization for NVIDIA GPUs, which allows the driver to use multiple CPU cores for better performance in CPU-limited scenarios.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "ThreadedOptimization", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 70, "StabilityImpact": 95, "PowerConsumptionImpact": 60, "ThermalImpact": 60, "InputLatencyImpact": 50, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Threaded optimization can significantly improve performance in CPU-limited scenarios by distributing driver workload across multiple CPU cores. This is particularly beneficial for systems with many cores.", "RevertRegistryValue": 0}, {"Name": "NVIDIA Performance Texture Filtering", "Description": "Sets texture filtering quality to 'High Performance' for better FPS at the cost of slightly reduced texture quality.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "TextureFilteringQuality", "RegistryValue": 2, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 100, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 60, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization reduces the quality of texture filtering to improve performance. The visual difference is minimal in most games, but the performance gain can be significant, especially at higher resolutions.", "RevertRegistryValue": 0}, {"Name": "NVIDIA Shader Cache Optimization", "Description": "Enables and optimizes the shader cache size for NVIDIA GPUs, reducing stuttering and improving loading times.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "ShaderCacheSize", "RegistryValue": 1024, "RegistryValueKind": "DWord", "PerformanceImpact": 60, "StabilityImpact": 100, "PowerConsumptionImpact": 40, "ThermalImpact": 40, "InputLatencyImpact": 50, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "A larger shader cache helps reduce stuttering in games by storing compiled shaders on disk. This is particularly beneficial when revisiting areas in games or replaying games you've played before.", "RevertRegistryValue": 128}, {"Name": "NVIDIA RTX 30-Series Power Limit Increase", "Description": "Increases the power limit for RTX 30-series GPUs to allow for higher sustained boost clocks and better performance.", "Category": "GPU Power Management", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": "RTX 30.*", "SpecificModels": ["RTX 3060", "RTX 3060 Ti", "RTX 3070", "RTX 3070 Ti", "RTX 3080", "RTX 3080 Ti", "RTX 3090"], "Method": 2, "Command": "nvidia-smi.exe", "Arguments": "-pl 105", "PerformanceImpact": 70, "StabilityImpact": 80, "PowerConsumptionImpact": 85, "ThermalImpact": 85, "InputLatencyImpact": 60, "Risk": 1, "WarningMessage": "This optimization increases power consumption and heat output. Ensure your cooling system is adequate and monitor temperatures. This is a safe increase that stays within NVIDIA's specifications.", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This sets the power limit to 105% of the default value, which allows the GPU to maintain higher boost clocks for longer periods. The increase is modest and safe, but will increase power consumption and heat output.", "RevertCommand": "nvidia-smi.exe", "RevertArguments": "-pl 100"}, {"Name": "NVIDIA RTX 40-Series Optimized Power Management", "Description": "Optimizes power management for RTX 40-series GPUs to balance performance and efficiency using Ada Lovelace architecture-specific settings.", "Category": "GPU Power Management", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": "RTX 40.*", "SpecificModels": ["RTX 4060", "RTX 4060 Ti", "RTX 4070", "RTX 4070 Ti", "RTX 4080", "RTX 4090"], "Method": 4, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "AdaPowerMode", "RegistryValue": 2, "RegistryValueKind": "DWord", "Command": "nvidia-smi.exe", "Arguments": "--power-limit=102", "PerformanceImpact": 75, "StabilityImpact": 90, "PowerConsumptionImpact": 75, "ThermalImpact": 75, "InputLatencyImpact": 70, "Risk": 1, "WarningMessage": "This optimization slightly increases power consumption and heat output. It's designed specifically for RTX 40-series GPUs and uses Ada Lovelace architecture-specific optimizations.", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization is specifically designed for the Ada Lovelace architecture in RTX 40-series GPUs. It balances performance and efficiency better than the generic 'Maximum Performance' setting.", "RevertRegistryValue": 0, "RevertCommand": "nvidia-smi.exe", "RevertArguments": "--power-limit=100"}, {"Name": "NVIDIA Resizable BAR Optimization", "Description": "Optimizes Resizable BAR settings for compatible NVIDIA GPUs and motherboards to improve performance in supported games.", "Category": "GPU Performance Optimization", "HardwareType": 1, "Manufacturer": "NVIDIA", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RegistryValueName": "EnableGR", "RegistryValue": 1, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 95, "PowerConsumptionImpact": 50, "ThermalImpact": 50, "InputLatencyImpact": 55, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Resizable BAR allows the CPU to access the full GPU memory at once, rather than in 256MB chunks. This can improve performance in supported games. This optimization only works if your motherboard and GPU both support Resizable BAR and it's enabled in BIOS.", "RevertRegistryValue": 0}]