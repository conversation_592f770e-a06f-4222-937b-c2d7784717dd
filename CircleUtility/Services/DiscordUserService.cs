using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Timers;
using System.Text;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing user accounts from Discord file
    /// </summary>
    public class DiscordUserService
    {
        private readonly HttpClient _httpClient;
        private readonly string _localUsersFile;
        private List<DiscordUser> _cachedUsers;
        private DateTime _lastFileCheck;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

        public DiscordUserService()
        {
            _httpClient = new HttpClient();
            _localUsersFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                         "CircleUtility", "discord_users.json");
            _cachedUsers = new List<DiscordUser>();
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(_localUsersFile));
            
            // Create default file if it doesn't exist
            CreateDefaultUsersFile();
        }

        /// <summary>
        /// Loads users from Discord file URL or local cache
        /// </summary>
        public async Task<List<DiscordUser>> LoadUsersAsync(string discordFileUrl = null)
        {
            try
            {
                // Check if we need to refresh cache
                if (_cachedUsers.Any() && DateTime.Now - _lastFileCheck < _cacheExpiry)
                {
                    return _cachedUsers;
                }

                List<DiscordUser> users = new List<DiscordUser>();

                // Try to load from Discord URL first
                if (!string.IsNullOrEmpty(discordFileUrl))
                {
                    users = await LoadFromDiscordUrl(discordFileUrl);
                    if (users.Any())
                    {
                        // Save to local cache
                        await SaveUsersToLocalFile(users);
                    }
                }

                // If Discord load failed, try local file
                if (!users.Any())
                {
                    users = await LoadFromLocalFile();
                }

                // If still no users, use defaults
                if (!users.Any())
                {
                    users = GetDefaultUsers();
                    await SaveUsersToLocalFile(users);
                }

                _cachedUsers = users;
                _lastFileCheck = DateTime.Now;
                return users;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading users: {ex.Message}");
                // Return default users on error
                return GetDefaultUsers();
            }
        }

        /// <summary>
        /// Authenticates user against loaded user list
        /// </summary>
        public async Task<bool> AuthenticateUserAsync(string username, string password, string discordFileUrl = null)
        {
            try
            {
                var users = await LoadUsersAsync(discordFileUrl);
                
                return users.Any(u => 
                    string.Equals(u.Username, username, StringComparison.OrdinalIgnoreCase) && 
                    u.Password == password && 
                    u.IsActive);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Authentication error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets user information
        /// </summary>
        public async Task<DiscordUser> GetUserAsync(string username, string discordFileUrl = null)
        {
            try
            {
                var users = await LoadUsersAsync(discordFileUrl);
                return users.FirstOrDefault(u => 
                    string.Equals(u.Username, username, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting user: {ex.Message}");
                return null;
            }
        }

        private async Task<List<DiscordUser>> LoadFromDiscordUrl(string url)
        {
            try
            {
                Console.WriteLine($"Loading users from Discord URL: {url}");
                
                var response = await _httpClient.GetStringAsync(url);
                var users = JsonSerializer.Deserialize<List<DiscordUser>>(response, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                Console.WriteLine($"Loaded {users?.Count ?? 0} users from Discord");
                return users ?? new List<DiscordUser>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to load from Discord URL: {ex.Message}");
                return new List<DiscordUser>();
            }
        }

        private async Task<List<DiscordUser>> LoadFromLocalFile()
        {
            try
            {
                if (!File.Exists(_localUsersFile))
                {
                    return new List<DiscordUser>();
                }

                var json = await File.ReadAllTextAsync(_localUsersFile);
                var users = JsonSerializer.Deserialize<List<DiscordUser>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                Console.WriteLine($"Loaded {users?.Count ?? 0} users from local file");
                return users ?? new List<DiscordUser>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to load from local file: {ex.Message}");
                return new List<DiscordUser>();
            }
        }

        private async Task SaveUsersToLocalFile(List<DiscordUser> users)
        {
            try
            {
                var json = JsonSerializer.Serialize(users, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                await File.WriteAllTextAsync(_localUsersFile, json);
                Console.WriteLine($"Saved {users.Count} users to local file");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to save users to local file: {ex.Message}");
            }
        }

        private void CreateDefaultUsersFile()
        {
            if (!File.Exists(_localUsersFile))
            {
                var defaultUsers = GetDefaultUsers();
                Task.Run(() => SaveUsersToLocalFile(defaultUsers));
            }
        }

        private List<DiscordUser> GetDefaultUsers()
        {
            return new List<DiscordUser>
            {
                new DiscordUser
                {
                    Username = "admin",
                    Password = "admin",
                    DisplayName = "Administrator",
                    Role = "Admin",
                    IsActive = true,
                    DiscordId = "admin001",
                    JoinDate = DateTime.Now.AddDays(-30)
                },
                new DiscordUser
                {
                    Username = "user",
                    Password = "user",
                    DisplayName = "Standard User",
                    Role = "User",
                    IsActive = true,
                    DiscordId = "user001",
                    JoinDate = DateTime.Now.AddDays(-15)
                },
                new DiscordUser
                {
                    Username = "arsenal",
                    Password = "arsenal",
                    DisplayName = "Arsenal User",
                    Role = "User",
                    IsActive = true,
                    DiscordId = "arsenal001",
                    JoinDate = DateTime.Now.AddDays(-10)
                },
                new DiscordUser
                {
                    Username = "admincp123",
                    Password = "163059Uuku!",
                    DisplayName = "Admin CP",
                    Role = "Admin",
                    IsActive = true,
                    DiscordId = "admincp001",
                    JoinDate = DateTime.Now.AddDays(-5)
                }
            };
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}


