namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a system metric
    /// </summary>
    public class SystemMetric
    {
        /// <summary>
        /// Gets or sets the name
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Gets or sets the value
        /// </summary>
        public string Value { get; set; }
        
        /// <summary>
        /// Gets or sets the status
        /// </summary>
        public string Status { get; set; }
    }
}
