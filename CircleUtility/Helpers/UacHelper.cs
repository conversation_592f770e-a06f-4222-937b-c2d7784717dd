// Created by Arsenal on 5-17-25 12:15PM
using System;
using CircleUtility.Models;
using System.Diagnostics;
using System.IO;
using System.Security.Principal;
using System.Windows;
using CircleUtility.Services;

namespace CircleUtility.Helpers
{
    /// <summary>
    /// Helper class for UAC-related operations
    /// </summary>
    public static class UacHelper
    {
        private static readonly LoggingService _logger = LoggingService.Instance;

        /// <summary>
        /// Checks if the current process is running with administrator privileges
        /// </summary>
        /// <returns>True if running as administrator, false otherwise</returns>
        public static bool IsRunningAsAdmin()
        {
            try
            {
                WindowsIdentity identity = WindowsIdentity.GetCurrent();
                WindowsPrincipal principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking admin privileges: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restarts the application with administrator privileges
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RestartAsAdmin()
        {
            try
            {
                // Check if already running as admin
                if (IsRunningAsAdmin())
                {
                    return true;
                }
                
                // Get the executable path
                string exePath = Process.GetCurrentProcess().MainModule.FileName;
                
                // Create process start info
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    UseShellExecute = true,
                    Verb = "runas" // Run as administrator
                };
                
                // Start the process
                Process.Start(startInfo);
                
                // Exit the current process
                Environment.Exit(0);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restarting as admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Runs a command with administrator privileges
        /// </summary>
        /// <param name="command">The command to run</param>
        /// <param name="arguments">The command arguments</param>
        /// <param name="waitForExit">Whether to wait for the process to exit</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RunAsAdmin(string command, string arguments, bool waitForExit = true)
        {
            try
            {
                _logger.Log($"Running command as admin: {command} {arguments}", LogLevel.INFO);
                
                // Create process start info
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = command,
                    Arguments = arguments,
                    UseShellExecute = true,
                    Verb = "runas", // Run as administrator
                    CreateNoWindow = false,
                    WindowStyle = ProcessWindowStyle.Normal
                };
                
                // Start the process
                Process process = Process.Start(startInfo);
                
                // Wait for the process to exit if requested
                if (waitForExit && process != null)
                {
                    process.WaitForExit();
                    _logger.Log($"Command completed with exit code: {process.ExitCode}", LogLevel.INFO);
                    return process.ExitCode == 0;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running command as admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Runs a PowerShell script with administrator privileges
        /// </summary>
        /// <param name="scriptPath">The path to the PowerShell script</param>
        /// <param name="arguments">The script arguments</param>
        /// <param name="waitForExit">Whether to wait for the process to exit</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RunPowerShellAsAdmin(string scriptPath, string arguments = "", bool waitForExit = true)
        {
            try
            {
                _logger.Log($"Running PowerShell script as admin: {scriptPath} {arguments}", LogLevel.INFO);
                
                // Ensure the script exists
                if (!File.Exists(scriptPath))
                {
                    _logger.Log($"PowerShell script not found: {scriptPath}", LogLevel.ERROR);
                    return false;
                }
                
                // Build PowerShell arguments
                string psArguments = $"-ExecutionPolicy Bypass -File \"{scriptPath}\" {arguments}";
                
                // Run PowerShell as admin
                return RunAsAdmin("powershell.exe", psArguments, waitForExit);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error running PowerShell script as admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Prompts the user for elevation if needed
        /// </summary>
        /// <param name="operation">The operation requiring elevation</param>
        /// <returns>True if the user accepted elevation, false otherwise</returns>
        public static bool PromptForElevation(string operation)
        {
            try
            {
                // Check if already running as admin
                if (IsRunningAsAdmin())
                {
                    return true;
                }
                
                // Prompt the user
                MessageBoxResult result = MessageBox.Show(
                    $"The operation '{operation}' requires administrator privileges. Do you want to continue?",
                    "Administrator Privileges Required",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.Yes)
                {
                    return RestartAsAdmin();
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error prompting for elevation: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Creates a scheduled task to run the application with administrator privileges
        /// </summary>
        /// <param name="taskName">The name of the scheduled task</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool CreateAdminTask(string taskName)
        {
            try
            {
                _logger.Log($"Creating admin task: {taskName}", LogLevel.INFO);
                
                // Get the executable path
                string exePath = Process.GetCurrentProcess().MainModule.FileName;
                
                // Build the task creation command
                string arguments = $"/create /tn \"{taskName}\" /tr \"\\\"{exePath}\\\"\" /sc onlogon /rl highest /f";
                
                // Run schtasks as admin
                return RunAsAdmin("schtasks.exe", arguments);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating admin task: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Deletes a scheduled task
        /// </summary>
        /// <param name="taskName">The name of the scheduled task</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool DeleteTask(string taskName)
        {
            try
            {
                _logger.Log($"Deleting task: {taskName}", LogLevel.INFO);
                
                // Build the task deletion command
                string arguments = $"/delete /tn \"{taskName}\" /f";
                
                // Run schtasks as admin
                return RunAsAdmin("schtasks.exe", arguments);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error deleting task: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }
    }
}

