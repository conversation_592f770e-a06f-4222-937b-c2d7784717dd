<Window x:Class="CircleUtility.Windows.LoadingScreenPreview"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Loading Screen Preview - Circle Utility" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#FF0A141E"
        BorderBrush="#FF00C8FF"
        BorderThickness="2"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Loading Spinner Animation -->
        <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="LoadingSpinner"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                           From="0" To="360" Duration="0:0:1.5"/>
        </Storyboard>

        <!-- Title Pulse Animation -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="LoadingTitle"
                                         Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1.0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="0.8"/>
                <EasingDoubleKeyFrame KeyTime="0:0:3" Value="1.0"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </Window.Resources>

    <!-- Loading Screen Content (Exact copy from LoginRegisterWindow) -->
    <Grid Background="#FF0A141E">
        
        <!-- Close Button -->
        <Button x:Name="CloseButton"
                Content="✕"
                Width="35"
                Height="35"
                HorizontalAlignment="Right"
                VerticalAlignment="Top"
                Margin="15"
                Background="#FF333333"
                BorderThickness="1"
                BorderBrush="#FF555555"
                Foreground="#FF888888"
                FontSize="18"
                Cursor="Hand"
                Click="CloseButton_Click"/>

        <!-- Loading Screen Content -->
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            
            <!-- Loading Title -->
            <TextBlock x:Name="LoadingTitle"
                       Text="CIRCLE UTILITY"
                       FontFamily="Consolas"
                       FontSize="32"
                       FontWeight="Bold"
                       Foreground="#FF00C8FF"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,40"/>

            <!-- Loading Spinner -->
            <Grid Width="80" Height="80" Margin="0,0,0,30">
                <Ellipse Width="80"
                         Height="80"
                         Stroke="#FF00C8FF"
                         StrokeThickness="4"
                         StrokeDashArray="0.75,0.25"
                         StrokeDashCap="Round"
                         x:Name="LoadingSpinner">
                    <Ellipse.RenderTransform>
                        <RotateTransform Angle="0" CenterX="40" CenterY="40"/>
                    </Ellipse.RenderTransform>
                </Ellipse>
            </Grid>

            <!-- Loading Status -->
            <TextBlock x:Name="LoadingStatusText"
                       Text="LOADING HARDWARE SERVICES..."
                       FontFamily="Consolas"
                       FontSize="16"
                       FontWeight="Bold"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,20"/>

            <!-- Clean Progress Bar -->
            <Grid Width="320" Height="25" Margin="0,0,0,20">
                <!-- Progress Bar Container -->
                <Border Width="300" Height="8"
                        Background="#FF333333"
                        BorderBrush="#FF555555"
                        BorderThickness="1"
                        CornerRadius="4">
                    
                    <!-- Progress Bar -->
                    <ProgressBar x:Name="LoadingProgressBar"
                                 Width="298"
                                 Height="6"
                                 Background="Transparent"
                                 Foreground="#FF00C8FF"
                                 BorderThickness="0"
                                 Value="65"/>
                </Border>
                
                <!-- Progress Percentage -->
                <TextBlock x:Name="ProgressPercentage"
                           Text="65%"
                           FontFamily="Consolas"
                           FontSize="10"
                           FontWeight="Bold"
                           Foreground="#FF00C8FF"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Bottom"
                           Margin="0,0,0,-10"/>
            </Grid>

            <!-- Loading Details -->
            <TextBlock x:Name="LoadingDetailsText"
                       Text="Loading hardware detection modules..."
                       FontFamily="Consolas"
                       FontSize="12"
                       Foreground="#FF888888"
                       HorizontalAlignment="Center"
                       TextAlignment="Center"
                       TextWrapping="Wrap"
                       Width="400"
                       Margin="0,0,0,40"/>

            <!-- Access Button (shown when loading completes) -->
            <Button x:Name="AccessButton"
                    Content="ACCESS SYSTEM"
                    Width="200"
                    Height="45"
                    FontFamily="Consolas"
                    FontSize="14"
                    FontWeight="Bold"
                    Background="#FF00C8FF"
                    Foreground="White"
                    BorderThickness="0"
                    Cursor="Hand"
                    Margin="0,20,0,0"
                    Click="AccessButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="3">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#FF0099CC"/>
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter Property="Background" Value="#FF006699"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Button.Style>
            </Button>

            <!-- Preview Info -->
            <TextBlock Text="⚡ PREVIEW MODE - This is how the loading screen looks"
                       FontFamily="Consolas"
                       FontSize="10"
                       Foreground="#FF666666"
                       HorizontalAlignment="Center"
                       Margin="0,30,0,0"/>

        </StackPanel>

        <!-- Corner Info -->
        <TextBlock Text="Loading Screen Preview"
                   FontFamily="Consolas"
                   FontSize="10"
                   Foreground="#FF444444"
                   HorizontalAlignment="Left"
                   VerticalAlignment="Bottom"
                   Margin="10"/>

    </Grid>
</Window>
