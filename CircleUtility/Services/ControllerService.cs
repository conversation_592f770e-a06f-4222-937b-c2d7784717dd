// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing and optimizing game controllers
    /// </summary>
    public class ControllerService
    {
        private static ControllerService _instance;
        private readonly LoggingService _logger;
        private readonly List<ControllerInfo> _controllers;
        private bool _isOverclockEnabled;
        private int _pollingRate = 1000; // Default 1000Hz
        private bool _isDeadZoneAdjusted;
        private bool _isTriggerStopEnabled;

        /// <summary>
        /// Initializes a new instance of the ControllerService class
        /// </summary>
        private ControllerService()
        {
            _logger = LoggingService.Instance;
            _controllers = new List<ControllerInfo>();
            DetectControllers();
        }

        /// <summary>
        /// Gets the singleton instance of the controller service
        /// </summary>
        public static ControllerService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ControllerService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets all detected controllers
        /// </summary>
        public IReadOnlyList<ControllerInfo> Controllers => _controllers;

        /// <summary>
        /// Gets a value indicating whether controller overclocking is enabled
        /// </summary>
        public bool IsOverclockEnabled => _isOverclockEnabled;

        /// <summary>
        /// Gets the current polling rate in Hz
        /// </summary>
        public int PollingRate => _pollingRate;

        /// <summary>
        /// Gets a value indicating whether dead zone adjustment is enabled
        /// </summary>
        public bool IsDeadZoneAdjusted => _isDeadZoneAdjusted;

        /// <summary>
        /// Gets a value indicating whether trigger stop is enabled
        /// </summary>
        public bool IsTriggerStopEnabled => _isTriggerStopEnabled;

        /// <summary>
        /// Detects all connected controllers
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DetectControllers()
        {
            try
            {
                _logger.Log("Detecting controllers...", LogLevel.INFO);
                _controllers.Clear();

                // In a real implementation, this would use Windows APIs to detect controllers
                // For now, we'll add some sample controllers
                _controllers.Add(new ControllerInfo
                {
                    ControllerId = "Controller1",
                    Name = "Xbox Elite Wireless Controller Series 2",
                    Type = ControllerType.Xbox,
                    ConnectionType = "Wireless",
                    BatteryLevel = 85,
                    IsConnected = true,
                    FirmwareVersion = "5.13.3143.0",
                    SerialNumber = "0123456789",
                    LastConnected = DateTime.Now
                });

                _controllers.Add(new ControllerInfo
                {
                    ControllerId = "Controller2",
                    Name = "DualSense Wireless Controller",
                    Type = ControllerType.PlayStation,
                    ConnectionType = "Bluetooth",
                    BatteryLevel = 62,
                    IsConnected = true,
                    FirmwareVersion = "0241",
                    SerialNumber = "9876543210",
                    LastConnected = DateTime.Now
                });

                _logger.Log($"Detected {_controllers.Count} controllers", LogLevel.INFO);
                foreach (var controller in _controllers)
                {
                    _logger.Log($"Controller: {controller.Name}, {controller.Type}, {controller.ConnectionType}", LogLevel.INFO);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting controllers: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a controller by ID
        /// </summary>
        /// <param name="controllerId">The controller ID</param>
        /// <returns>The controller info, or null if not found</returns>
        public ControllerInfo GetController(string controllerId)
        {
            return _controllers.FirstOrDefault(c => c.ControllerId == controllerId);
        }

        /// <summary>
        /// Enables controller overclocking for reduced input delay
        /// </summary>
        /// <param name="pollingRate">The polling rate in Hz (125, 250, 500, 1000, 2000, 4000, or 8000)</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool EnableOverclock(int pollingRate)
        {
            try
            {
                // Validate polling rate
                if (pollingRate != 125 && pollingRate != 250 && pollingRate != 500 &&
                    pollingRate != 1000 && pollingRate != 2000 && pollingRate != 4000 &&
                    pollingRate != 8000)
                {
                    _logger.Log($"Invalid polling rate: {pollingRate}. Must be 125, 250, 500, 1000, 2000, 4000, or 8000.", LogLevel.ERROR);
                    return false;
                }

                _logger.Log($"Enabling controller overclock with {pollingRate}Hz polling rate...", LogLevel.INFO);

                // In a real implementation, this would use a driver or hardware-specific API
                // For now, we'll just simulate success
                _pollingRate = pollingRate;
                _isOverclockEnabled = true;

                // Update controller info
                foreach (var controller in _controllers)
                {
                    controller.PollingRate = pollingRate;
                    controller.IsOverclocked = true;
                    controller.InputLatency = Math.Max(1, 1000.0 / pollingRate);
                }

                _logger.Log($"Controller overclock enabled with {pollingRate}Hz polling rate", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error enabling controller overclock: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables controller overclocking
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableOverclock()
        {
            try
            {
                _logger.Log("Disabling controller overclock...", LogLevel.INFO);

                // In a real implementation, this would use a driver or hardware-specific API
                // For now, we'll just simulate success
                _pollingRate = 125; // Default polling rate
                _isOverclockEnabled = false;

                // Update controller info
                foreach (var controller in _controllers)
                {
                    controller.PollingRate = 125;
                    controller.IsOverclocked = false;
                    controller.InputLatency = 8; // 1000 / 125 = 8ms
                }

                _logger.Log("Controller overclock disabled", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling controller overclock: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Adjusts controller dead zones for better precision
        /// </summary>
        /// <param name="leftStickDeadZone">The left stick dead zone (0-100)</param>
        /// <param name="rightStickDeadZone">The right stick dead zone (0-100)</param>
        /// <param name="controllerId">The controller ID, or null for all controllers</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool AdjustDeadZones(int leftStickDeadZone, int rightStickDeadZone, string controllerId = null)
        {
            try
            {
                // Validate dead zones
                if (leftStickDeadZone < 0 || leftStickDeadZone > 100 || rightStickDeadZone < 0 || rightStickDeadZone > 100)
                {
                    _logger.Log("Invalid dead zone values. Must be between 0 and 100.", LogLevel.ERROR);
                    return false;
                }

                _logger.Log($"Adjusting controller dead zones: Left={leftStickDeadZone}, Right={rightStickDeadZone}...", LogLevel.INFO);

                // In a real implementation, this would use a driver or hardware-specific API
                // For now, we'll just simulate success
                _isDeadZoneAdjusted = true;

                // Update controller info
                if (controllerId != null)
                {
                    var controller = GetController(controllerId);
                    if (controller != null)
                    {
                        controller.LeftStickDeadZone = leftStickDeadZone;
                        controller.RightStickDeadZone = rightStickDeadZone;
                    }
                }
                else
                {
                    foreach (var controller in _controllers)
                    {
                        controller.LeftStickDeadZone = leftStickDeadZone;
                        controller.RightStickDeadZone = rightStickDeadZone;
                    }
                }

                _logger.Log("Controller dead zones adjusted successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error adjusting controller dead zones: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Enables trigger stops for faster actuation
        /// </summary>
        /// <param name="leftTriggerStop">The left trigger stop point (0-100)</param>
        /// <param name="rightTriggerStop">The right trigger stop point (0-100)</param>
        /// <param name="controllerId">The controller ID, or null for all controllers</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool EnableTriggerStops(int leftTriggerStop, int rightTriggerStop, string controllerId = null)
        {
            try
            {
                // Validate trigger stops
                if (leftTriggerStop < 0 || leftTriggerStop > 100 || rightTriggerStop < 0 || rightTriggerStop > 100)
                {
                    _logger.Log("Invalid trigger stop values. Must be between 0 and 100.", LogLevel.ERROR);
                    return false;
                }

                _logger.Log($"Enabling trigger stops: Left={leftTriggerStop}, Right={rightTriggerStop}...", LogLevel.INFO);

                // In a real implementation, this would use a driver or hardware-specific API
                // For now, we'll just simulate success
                _isTriggerStopEnabled = true;

                // Update controller info
                if (controllerId != null)
                {
                    var controller = GetController(controllerId);
                    if (controller != null)
                    {
                        controller.LeftTriggerStop = leftTriggerStop;
                        controller.RightTriggerStop = rightTriggerStop;
                        controller.HasTriggerStops = true;
                    }
                }
                else
                {
                    foreach (var controller in _controllers)
                    {
                        controller.LeftTriggerStop = leftTriggerStop;
                        controller.RightTriggerStop = rightTriggerStop;
                        controller.HasTriggerStops = true;
                    }
                }

                _logger.Log("Trigger stops enabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error enabling trigger stops: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Disables trigger stops
        /// </summary>
        /// <param name="controllerId">The controller ID, or null for all controllers</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool DisableTriggerStops(string controllerId = null)
        {
            try
            {
                _logger.Log("Disabling trigger stops...", LogLevel.INFO);

                // In a real implementation, this would use a driver or hardware-specific API
                // For now, we'll just simulate success
                _isTriggerStopEnabled = false;

                // Update controller info
                if (controllerId != null)
                {
                    var controller = GetController(controllerId);
                    if (controller != null)
                    {
                        controller.LeftTriggerStop = 100;
                        controller.RightTriggerStop = 100;
                        controller.HasTriggerStops = false;
                    }
                }
                else
                {
                    foreach (var controller in _controllers)
                    {
                        controller.LeftTriggerStop = 100;
                        controller.RightTriggerStop = 100;
                        controller.HasTriggerStops = false;
                    }
                }

                _logger.Log("Trigger stops disabled successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error disabling trigger stops: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Creates a controller profile
        /// </summary>
        /// <param name="profileName">The profile name</param>
        /// <param name="controllerId">The controller ID</param>
        /// <returns>The created profile, or null if creation failed</returns>
        public ControllerProfile CreateProfile(string profileName, string controllerId)
        {
            try
            {
                var controller = GetController(controllerId);
                if (controller == null)
                {
                    _logger.Log($"Controller not found: {controllerId}", LogLevel.ERROR);
                    return null;
                }

                _logger.Log($"Creating controller profile: {profileName}...", LogLevel.INFO);

                ControllerProfile profile = new ControllerProfile
                {
                    ProfileName = profileName,
                    ControllerId = controllerId,
                    PollingRate = controller.PollingRate,
                    LeftStickDeadZone = controller.LeftStickDeadZone,
                    RightStickDeadZone = controller.RightStickDeadZone,
                    LeftTriggerStop = controller.LeftTriggerStop,
                    RightTriggerStop = controller.RightTriggerStop,
                    IsOverclockEnabled = controller.IsOverclocked,
                    HasTriggerStops = controller.HasTriggerStops,
                    CreatedDate = DateTime.Now
                };

                // In a real implementation, this would save the profile to disk
                _logger.Log($"Controller profile created: {profileName}", LogLevel.SUCCESS);
                return profile;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating controller profile: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Applies a controller profile
        /// </summary>
        /// <param name="profile">The controller profile</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ApplyProfile(ControllerProfile profile)
        {
            try
            {
                if (profile == null)
                {
                    _logger.Log("Profile is null", LogLevel.ERROR);
                    return false;
                }

                _logger.Log($"Applying controller profile: {profile.ProfileName}...", LogLevel.INFO);

                // Apply profile settings
                if (profile.IsOverclockEnabled)
                {
                    EnableOverclock(profile.PollingRate);
                }
                else
                {
                    DisableOverclock();
                }

                AdjustDeadZones(profile.LeftStickDeadZone, profile.RightStickDeadZone, profile.ControllerId);

                if (profile.HasTriggerStops)
                {
                    EnableTriggerStops(profile.LeftTriggerStop, profile.RightTriggerStop, profile.ControllerId);
                }
                else
                {
                    DisableTriggerStops(profile.ControllerId);
                }

                _logger.Log($"Controller profile applied: {profile.ProfileName}", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying controller profile: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Measures controller input latency
        /// </summary>
        /// <param name="controllerId">The controller ID</param>
        /// <returns>The measured input latency in milliseconds</returns>
        public async Task<double> MeasureInputLatency(string controllerId)
        {
            try
            {
                var controller = GetController(controllerId);
                if (controller == null)
                {
                    _logger.Log($"Controller not found: {controllerId}", LogLevel.ERROR);
                    return -1;
                }

                _logger.Log($"Measuring input latency for controller: {controller.Name}...", LogLevel.INFO);

                // In a real implementation, this would use hardware-specific methods
                // For now, we'll simulate a measurement based on current settings
                await Task.Delay(1000); // Simulate measurement time

                // Base latency based on connection type
                double baseLatency;
                switch (controller.ConnectionType.ToLower())
                {
                    case "wired":
                        baseLatency = 2.0;
                        break;
                    case "wireless":
                        baseLatency = 4.0;
                        break;
                    case "bluetooth":
                        baseLatency = 6.0;
                        break;
                    default:
                        baseLatency = 5.0;
                        break;
                }

                // Apply modifiers based on settings
                if (controller.IsOverclocked)
                {
                    baseLatency *= (125.0 / controller.PollingRate) * 0.8;
                }

                if (controller.HasTriggerStops)
                {
                    baseLatency *= 0.9;
                }

                // Add some randomness
                Random random = new Random();
                double finalLatency = baseLatency + (random.NextDouble() * 1.0 - 0.5);
                finalLatency = Math.Max(1, Math.Round(finalLatency, 2));

                // Update controller info
                controller.InputLatency = finalLatency;

                _logger.Log($"Input latency measured: {finalLatency}ms", LogLevel.SUCCESS);
                return finalLatency;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error measuring input latency: {ex.Message}", LogLevel.ERROR);
                return -1;
            }
        }

        /// <summary>
        /// Applies all controller optimizations
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool ApplyAllOptimizations()
        {
            try
            {
                _logger.Log("Applying all controller optimizations...", LogLevel.INFO);

                bool overclockResult = EnableOverclock(1000);
                bool deadZoneResult = AdjustDeadZones(5, 5);
                bool triggerStopResult = EnableTriggerStops(50, 50);

                bool allSuccessful = overclockResult && deadZoneResult && triggerStopResult;

                if (allSuccessful)
                {
                    _logger.Log("All controller optimizations applied successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some controller optimizations failed to apply", LogLevel.WARNING);
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying controller optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Reverts all controller optimizations to default settings
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RevertAllOptimizations()
        {
            try
            {
                _logger.Log("Reverting all controller optimizations...", LogLevel.INFO);

                bool overclockResult = DisableOverclock();
                bool deadZoneResult = AdjustDeadZones(10, 10);
                bool triggerStopResult = DisableTriggerStops();

                bool allSuccessful = overclockResult && deadZoneResult && triggerStopResult;

                if (allSuccessful)
                {
                    _logger.Log("All controller optimizations reverted successfully", LogLevel.SUCCESS);
                }
                else
                {
                    _logger.Log("Some controller optimizations failed to revert", LogLevel.WARNING);
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting controller optimizations: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }
    }
}

