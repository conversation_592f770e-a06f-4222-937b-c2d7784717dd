using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for CircularGaugeControl.xaml
    /// </summary>
    public partial class CircularGaugeControl : UserControl
    {
        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register(nameof(Value), typeof(double), typeof(CircularGaugeControl),
                new PropertyMetadata(0.0, OnValueChanged));

        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register(nameof(Title), typeof(string), typeof(CircularGaugeControl),
                new PropertyMetadata("Gauge", OnTitleChanged));

        public double Value
        {
            get => (double)GetValue(ValueProperty);
            set => SetValue(ValueProperty, value);
        }

        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }

        public CircularGaugeControl()
        {
            InitializeComponent();
            Loaded += CircularGaugeControl_Loaded;
        }

        private void CircularGaugeControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Start the glow pulse animation
            Storyboard pulse = (Storyboard)FindResource("PulseAnimation");
            pulse.Begin(this, true);
        }

        private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CircularGaugeControl gauge)
            {
                double value = (double)e.NewValue;
                double normalized = Math.Max(0, Math.Min(value, 100)); // Clamp to 0–100
                double angle = -225 + (normalized / 100.0) * 270;      // -225 to +45

                // Animate the EndAngle property
                Storyboard storyboard = (Storyboard)gauge.FindResource("ValueChangeAnimation");
                DoubleAnimation arcAnimation = (DoubleAnimation)storyboard.Children[0];
                arcAnimation.To = angle;
                storyboard.Begin(gauge);

                // Update value text
                gauge.PART_ValueText.Text = $"{(int)normalized}%";
            }
        }

        private static void OnTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CircularGaugeControl gauge && e.NewValue is string title)
            {
                gauge.PART_TitleText.Text = title;
            }
        }
    }
}
