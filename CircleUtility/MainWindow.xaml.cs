using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using System.Management;
using CircleUtility.Services;
using CircleUtility.ViewModels;

namespace CircleUtility
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private MainViewModel _viewModel;
        private DispatcherTimer _loadingTimer;
        private int _loadingProgress = 0;
        private string _username;
        private bool _isPasswordVisible = false;
        private bool _isRegisterPasswordVisible = false;

        // Service dependencies
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IHardwareOptimizationService _hardwareOptimizationService;
        private readonly IHardwareCompatibilityService _hardwareCompatibilityService;
        private readonly IHardwareRecommendationService _hardwareRecommendationService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;
        private readonly IBenchmarkingService _benchmarkingService;
        private readonly IUserTrackingService _userTrackingService;
        private readonly HardwareFingerprintService _hardwareFingerprintService = HardwareFingerprintService.Instance;

        private readonly string[] _loadingMessages = new string[]
        {
            "Initializing system...",
            "Loading core components...",
            "Configuring system tweaks...",
            "Loading game profiles...",
            "Initializing performance monitors...",
            "Preparing optimization tools...",
            "Establishing secure connection...",
            "Finalizing initialization..."
        };

        /// <summary>
        /// Initializes a new instance of the MainWindow class
        /// </summary>
        public MainWindow(
            IHardwareDetectionService hardwareDetectionService,
            IHardwareOptimizationService hardwareOptimizationService,
            IHardwareCompatibilityService hardwareCompatibilityService,
            IPerformanceMonitoringService performanceMonitoringService,
            IBenchmarkingService benchmarkingService,
            IUserTrackingService userTrackingService,
            IHardwareRecommendationService hardwareRecommendationService = null)
        {
            try
            {
                Console.WriteLine("Initializing MainWindow with dependency injection");

                // Store the injected services
                _hardwareDetectionService = hardwareDetectionService ?? throw new ArgumentNullException(nameof(hardwareDetectionService));
                _hardwareOptimizationService = hardwareOptimizationService ?? throw new ArgumentNullException(nameof(hardwareOptimizationService));
                _hardwareCompatibilityService = hardwareCompatibilityService ?? throw new ArgumentNullException(nameof(hardwareCompatibilityService));
                _hardwareRecommendationService = hardwareRecommendationService ?? HardwareRecommendationService.Instance;
                _performanceMonitoringService = performanceMonitoringService ?? throw new ArgumentNullException(nameof(performanceMonitoringService));
                _benchmarkingService = benchmarkingService ?? throw new ArgumentNullException(nameof(benchmarkingService));
                _userTrackingService = userTrackingService ?? throw new ArgumentNullException(nameof(userTrackingService));

                // Initialize the component
                InitializeComponent();

                // Start the rainbow animation for the welcome title
                Storyboard rainbowAnimation = (Storyboard)FindResource("RainbowAnimation");
                rainbowAnimation.Begin();

                // Set the window title
                this.Title = "The Circle's Utility v1.0";

                // Set window properties
                this.WindowStartupLocation = WindowStartupLocation.CenterScreen;

                // Update system information (commented out - not needed for loading screen)
                // UpdateSystemInformation();

                // Set up the loading animation
                SetupLoadingAnimation();

                // Set up the login functionality
                SetupLogin();

                // Set up the access button
                SetupAccessButton();

                Console.WriteLine("MainWindow initialization completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in MainWindow constructor: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error initializing main window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Updates the system information displayed in the UI
        /// </summary>
        private void UpdateSystemInformation()
        {
            try
            {
                Console.WriteLine("Updating system information");

                // Get OS info
                string osVersion = "Windows";
                try
                {
                    var osInfo = Environment.OSVersion;
                    if (osInfo.Platform == PlatformID.Win32NT)
                    {
                        // Get a more user-friendly Windows version
                        if (osInfo.Version.Major == 10)
                        {
                            osVersion = osInfo.Version.Build >= 22000 ? "Windows 11" : "Windows 10";
                        }
                        else if (osInfo.Version.Major == 6)
                        {
                            switch (osInfo.Version.Minor)
                            {
                                case 3:
                                    osVersion = "Windows 8.1";
                                    break;
                                case 2:
                                    osVersion = "Windows 8";
                                    break;
                                case 1:
                                    osVersion = "Windows 7";
                                    break;
                                case 0:
                                    osVersion = "Windows Vista";
                                    break;
                            }
                        }

                        // Add build number for more detail
                        osVersion += $" ({osInfo.Version.Build})";
                    }
                    else
                    {
                        osVersion = osInfo.ToString();
                    }
                }
                catch
                {
                    // Fallback to basic info if there's an error
                    osVersion = "Windows";
                }

                TextBlock osText = FindName("SystemInfoOS") as TextBlock;
                if (osText != null)
                {
                    osText.Text = osVersion;
                }

                // Get architecture info
                string architecture = Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit";
                TextBlock archText = FindName("SystemInfoArch") as TextBlock;
                if (archText != null)
                {
                    archText.Text = architecture;
                }

                // Get CPU info
                string cpuName = "Unknown CPU";
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        string fullName = obj["Name"].ToString().Trim();

                        // Clean up common CPU name patterns
                        // Remove excessive spaces
                        fullName = System.Text.RegularExpressions.Regex.Replace(fullName, @"\s+", " ");

                        // Remove CPU frequency if present at the end (e.g., "@ 3.60GHz")
                        fullName = System.Text.RegularExpressions.Regex.Replace(fullName, @"\s*@\s*[\d\.]+GHz$", "");

                        // Remove "CPU" text if redundant
                        fullName = fullName.Replace(" CPU ", " ");

                        cpuName = fullName;
                        break; // Just get the first CPU
                    }
                }
                TextBlock cpuText = FindName("SystemInfoCPU") as TextBlock;
                if (cpuText != null)
                {
                    cpuText.Text = cpuName;
                }

                // Get RAM info
                string ramSize = "Unknown RAM";
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        long totalMemory = Convert.ToInt64(obj["TotalPhysicalMemory"]);
                        ramSize = $"{totalMemory / (1024 * 1024 * 1024)} GB";
                        break;
                    }
                }
                TextBlock ramText = FindName("SystemInfoRAM") as TextBlock;
                if (ramText != null)
                {
                    ramText.Text = ramSize;
                }

                // Get GPU info
                string gpuName = "Unknown GPU";
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                {
                    var gpus = new List<string>();
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        string name = obj["Name"].ToString();
                        // Skip duplicates
                        if (!gpus.Contains(name))
                        {
                            gpus.Add(name);
                        }
                    }

                    // Prioritize dedicated GPUs (NVIDIA, AMD) over integrated ones
                    var dedicatedGpu = gpus.FirstOrDefault(g =>
                        g.Contains("NVIDIA", StringComparison.OrdinalIgnoreCase) ||
                        g.Contains("AMD", StringComparison.OrdinalIgnoreCase) ||
                        g.Contains("Radeon", StringComparison.OrdinalIgnoreCase) ||
                        g.Contains("GeForce", StringComparison.OrdinalIgnoreCase));

                    if (!string.IsNullOrEmpty(dedicatedGpu))
                    {
                        gpuName = dedicatedGpu;
                    }
                    else if (gpus.Count > 0)
                    {
                        gpuName = gpus[0];
                    }
                }

                TextBlock gpuText = FindName("SystemInfoGPU") as TextBlock;
                if (gpuText != null)
                {
                    gpuText.Text = gpuName;
                }

                Console.WriteLine("System information updated");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating system information: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Sets up the loading animation
        /// </summary>
        private void SetupLoadingAnimation()
        {
            try
            {
                Console.WriteLine("Setting up loading animation");

                // Initialize the loading timer
                _loadingTimer = new DispatcherTimer();
                _loadingTimer.Interval = TimeSpan.FromMilliseconds(100);
                _loadingTimer.Tick += LoadingTimer_Tick;

                // Start the loading animation
                _loadingTimer.Start();

                Console.WriteLine("Loading animation started");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up loading animation: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handles the loading timer tick event
        /// </summary>
        private void LoadingTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                // Update the loading progress
                _loadingProgress++;

                // Update the loading bar
                LoadingBar.Value = _loadingProgress;

                // Update the progress percentage
                if (ProgressPercentage != null)
                {
                    ProgressPercentage.Text = $"{_loadingProgress}%";
                }

                // Update the loading message
                if (_loadingProgress % 12 == 0 && _loadingProgress < 96)
                {
                    int messageIndex = _loadingProgress / 12 - 1;
                    if (messageIndex >= 0 && messageIndex < _loadingMessages.Length)
                    {
                        StatusMessage.Text = _loadingMessages[messageIndex];
                    }
                }

                // Check if loading is complete
                if (_loadingProgress >= 100)
                {
                    // Stop the loading timer
                    _loadingTimer.Stop();

                    // Update the loading status
                    LoadingStatus.Text = "SYSTEM READY";
                    StatusMessage.Text = "Click ACCESS SYSTEM to continue...";

                    // Enable the access button with animation
                    if (AccessButton != null)
                    {
                        // Create a storyboard for the button enable animation
                        var storyboard = new Storyboard();
                        
                        // Fade in animation
                        var fadeIn = new DoubleAnimation
                        {
                            From = 0.5,
                            To = 1.0,
                            Duration = TimeSpan.FromSeconds(0.5)
                        };
                        Storyboard.SetTarget(fadeIn, AccessButton);
                        Storyboard.SetTargetProperty(fadeIn, new PropertyPath(UIElement.OpacityProperty));
                        storyboard.Children.Add(fadeIn);

                        // Glow animation
                        var glowAnimation = new DoubleAnimation
                        {
                            From = 0,
                            To = 1,
                            Duration = TimeSpan.FromSeconds(0.5),
                            AutoReverse = true,
                            RepeatBehavior = new RepeatBehavior(2)
                        };
                        Storyboard.SetTarget(glowAnimation, AccessButton);
                        Storyboard.SetTargetProperty(glowAnimation, new PropertyPath("(Button.Effect).(DropShadowEffect.Opacity)"));
                        storyboard.Children.Add(glowAnimation);

                        // Start the animation
                        storyboard.Begin();

                        // Enable the button
                        AccessButton.IsEnabled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in loading timer tick: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Sets up the login functionality
        /// </summary>
        private void SetupLogin()
        {
            try
            {
                Console.WriteLine("Setting up login functionality");

                // Handle Enter key press in password field
                LoginPasswordInput.KeyDown += (sender, e) =>
                {
                    if (e.Key == Key.Enter)
                    {
                        LoginButton_Click(LoginButton, new RoutedEventArgs());
                    }
                };

                // Handle Enter key press in password text box (when visible)
                LoginPasswordTextBox.KeyDown += (sender, e) =>
                {
                    if (e.Key == Key.Enter)
                    {
                        LoginButton_Click(LoginButton, new RoutedEventArgs());
                    }
                };

                // Handle Enter key press in username field
                LoginUsernameInput.KeyDown += (sender, e) =>
                {
                    if (e.Key == Key.Enter)
                    {
                        if (_isPasswordVisible)
                            LoginPasswordTextBox.Focus();
                        else
                            LoginPasswordInput.Focus();
                    }
                };

                // Sync password fields
                LoginPasswordInput.PasswordChanged += (sender, e) =>
                {
                    if (!_isPasswordVisible)
                    {
                        LoginPasswordTextBox.Text = LoginPasswordInput.Password;
                    }
                };

                LoginPasswordTextBox.TextChanged += (sender, e) =>
                {
                    if (_isPasswordVisible)
                    {
                        LoginPasswordInput.Password = LoginPasswordTextBox.Text;
                    }
                };

                Console.WriteLine("Login functionality setup completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up login: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Toggles password visibility
        /// </summary>
        private void TogglePasswordVisibility_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isPasswordVisible = !_isPasswordVisible;

                if (_isPasswordVisible)
                {
                    // Show password as text
                    LoginPasswordTextBox.Text = LoginPasswordInput.Password;
                    LoginPasswordInput.Visibility = Visibility.Collapsed;
                    LoginPasswordTextBox.Visibility = Visibility.Visible;
                    LoginPasswordTextBox.Focus();
                    LoginPasswordTextBox.CaretIndex = LoginPasswordTextBox.Text.Length;

                    // Update icon to "hide" (crossed eye)
                    PasswordToggleIcon.Text = "🙈";
                }
                else
                {
                    // Hide password
                    LoginPasswordInput.Password = LoginPasswordTextBox.Text;
                    LoginPasswordTextBox.Visibility = Visibility.Collapsed;
                    LoginPasswordInput.Visibility = Visibility.Visible;
                    LoginPasswordInput.Focus();

                    // Update icon to "show" (eye)
                    PasswordToggleIcon.Text = "👁";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error toggling password visibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the login button click
        /// </summary>
        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Console.WriteLine("Login button clicked");

                string username = LoginUsernameInput.Text.Trim();
                string password = _isPasswordVisible ? LoginPasswordTextBox.Text : LoginPasswordInput.Password;

                // Hide any previous error messages
                LoginStatusText.Visibility = Visibility.Collapsed;

                // Validate input
                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowLoginError("Please enter a username.");
                    return;
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    ShowLoginError("Please enter a password.");
                    return;
                }

                // TODO: Replace this with your real authentication logic
                bool isAuthenticated = username.Equals("Arsenal", StringComparison.OrdinalIgnoreCase) && password == "Arsenal";
                // For demo, allow any username/password pair that matches a stored user
                var logins = _userTrackingService.GetUserLogins();
                var userLogin = logins.FirstOrDefault(l => l.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                if (userLogin == null && password == "Arsenal") // First login for this user
                {
                    // Register the hardware/IP for this user
                    _userTrackingService.RegisterUserLogin(username);
                    _username = username;
                    StartLoadingSequence();
                }
                else if (userLogin != null && password == "Arsenal") // Subsequent logins
                {
                    string currentHardware = _hardwareFingerprintService.GetHardwareFingerprint();
                    string currentIp = _hardwareFingerprintService.GetIpAddresses().FirstOrDefault() ?? "127.0.0.1";
                    if (userLogin.HardwareFingerprint != currentHardware || !userLogin.IpAddresses.Contains(currentIp))
                    {
                        ShowLoginError("Access denied. Accounts are hardware locked and routed to IP's.");
                        return;
                    }
                    // Register the login (update last login, IP, etc.)
                    _userTrackingService.RegisterUserLogin(username);
                    _username = username;
                    StartLoadingSequence();
                }
                else
                {
                    ShowLoginError("Invalid username or password.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in login: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                ShowLoginError("An error occurred during login.");
            }
        }

        /// <summary>
        /// Shows a login error message
        /// </summary>
        private void ShowLoginError(string message)
        {
            LoginStatusText.Text = message;
            LoginStatusText.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// Starts the loading sequence after successful login
        /// </summary>
        private void StartLoadingSequence()
        {
            try
            {
                Console.WriteLine("Starting loading sequence");

                // Hide login panel and show loading panel
                LoginPanel.Visibility = Visibility.Collapsed;
                LoadingPanel.Visibility = Visibility.Visible;

                // Reset loading progress
                _loadingProgress = 0;
                LoadingBar.Value = 0;
                LoadingStatus.Text = "INITIALIZING...";
                StatusMessage.Text = "Initializing system...";
                ProgressPercentage.Text = "0%";

                // Start the loading timer
                if (_loadingTimer != null)
                {
                    _loadingTimer.Start();
                }

                Console.WriteLine("Loading sequence started");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting loading sequence: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Sets up the access button
        /// </summary>
        private void SetupAccessButton()
        {
            try
            {
                Console.WriteLine("Setting up access button");

                // Handle the access button click event
                AccessButton.Click += AccessButton_Click;

                // Enable the access button since user has already logged in
                AccessButton.IsEnabled = true;

                Console.WriteLine("Access button setup completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up access button: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handles the access button click event
        /// </summary>
        private async void AccessButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Console.WriteLine("Access button clicked");

                // Disable the button to prevent multiple clicks
                AccessButton.IsEnabled = false;

                // Show loading state
                AccessButton.Content = "ACCESSING...";
                StatusMessage.Text = "Preparing system interface...";

                // Get the username (use the stored username or default)
                string username = !string.IsNullOrWhiteSpace(_username)
                    ? _username
                    : "User" + new Random().Next(1000, 9999);

                Console.WriteLine($"Username: {username}");

                // Check if MainContent exists
                if (MainContent == null)
                {
                    Console.WriteLine("ERROR: MainContent is null!");
                    MessageBox.Show("MainContent panel not found. The main utility interface cannot be loaded.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                Console.WriteLine("MainContent found, initializing...");

                // Initialize the main content
                await Task.Run(() => InitializeMainContent(username));

                // Hide the loading panel and show the main content with a fade transition
                var fadeOut = new DoubleAnimation
                {
                    From = 1.0,
                    To = 0.0,
                    Duration = TimeSpan.FromSeconds(0.3)
                };
                fadeOut.Completed += (s, args) =>
                {
                    LoadingPanel.Visibility = Visibility.Collapsed;
                    MainContent.Visibility = Visibility.Visible;
                    MainContent.Opacity = 0;

                    var fadeIn = new DoubleAnimation
                    {
                        From = 0.0,
                        To = 1.0,
                        Duration = TimeSpan.FromSeconds(0.3)
                    };
                    MainContent.BeginAnimation(UIElement.OpacityProperty, fadeIn);
                };
                LoadingPanel.BeginAnimation(UIElement.OpacityProperty, fadeOut);

                Console.WriteLine("Transition to main content completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in access button click: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error accessing system: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                
                // Reset button state
                AccessButton.IsEnabled = true;
                AccessButton.Content = "ACCESS SYSTEM";
            }
        }

        /// <summary>
        /// Initializes the main content
        /// </summary>
        private void InitializeMainContent(string username)
        {
            try
            {
                Console.WriteLine("Initializing main content");

                Console.WriteLine("Creating MainViewModel...");
                // Create and set the view model with injected services
                try
                {
                    // Get ViewModelFactory from service provider
                    var viewModelFactory = App.ServiceProvider.GetRequiredService<IViewModelFactory>();
                    
                    _viewModel = viewModelFactory.CreateMainViewModel(username);
                    Console.WriteLine("MainViewModel created successfully");
                }
                catch (Exception vmEx)
                {
                    Console.WriteLine($"Error creating MainViewModel: {vmEx.Message}");
                    Console.WriteLine($"Stack trace: {vmEx.StackTrace}");
                    throw new Exception($"Failed to create MainViewModel: {vmEx.Message}", vmEx);
                }

                Console.WriteLine("Setting DataContext...");
                DataContext = _viewModel;

                Console.WriteLine("Starting rainbow animation...");
                // Start the rainbow animation for the title
                try
                {
                    Storyboard rainbowAnimation = (Storyboard)FindResource("RainbowAnimation");
                    rainbowAnimation.Begin();
                }
                catch (Exception animEx)
                {
                    Console.WriteLine($"Warning: Could not start rainbow animation: {animEx.Message}");
                }

                Console.WriteLine("Setting window title...");
                // Set the window title
                this.Title = $"The Circle's Utility v1.0 - Logged in as: {username}";

                Console.WriteLine("Initializing notification service...");
                // Initialize notification service with the notification panel
                try
                {
                    if (NotificationPanel != null)
                    {
                        NotificationService.Instance.Initialize(NotificationPanel);
                    }
                    else
                    {
                        Console.WriteLine("Warning: NotificationPanel is null");
                    }
                }
                catch (Exception notifEx)
                {
                    Console.WriteLine($"Warning: Could not initialize notification service: {notifEx.Message}");
                }

                // Navigation is now handled by the view model

                Console.WriteLine("Main content initialization completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing main content: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error initializing main content: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Sets the username for the application
        /// </summary>
        /// <param name="username">The username to set</param>
        public void SetUsername(string username)
        {
            try
            {
                Console.WriteLine($"Setting username: {username}");
                _username = username;

                // Enable the access button if it exists
                if (AccessButton != null)
                {
                    AccessButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting username: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Initializes the main window and ensures the welcome panel is visible
        /// </summary>
        public void InitializeMainWindow()
        {
            try
            {
                Console.WriteLine("Initializing main window");

                // Make sure the login panel is visible and other panels are hidden
                if (LoginPanel != null)
                {
                    LoginPanel.Visibility = Visibility.Visible;
                }

                if (LoadingPanel != null)
                {
                    LoadingPanel.Visibility = Visibility.Collapsed;
                }

                if (MainContent != null)
                {
                    MainContent.Visibility = Visibility.Collapsed;
                }

                // Reset loading progress
                _loadingProgress = 0;

                // Make sure the loading bar is visible and set to 0
                if (LoadingBar != null)
                {
                    LoadingBar.Value = 0;
                }

                // Set initial loading status
                if (LoadingStatus != null)
                {
                    LoadingStatus.Text = "INITIALIZING...";
                }

                // Set initial status message
                if (StatusMessage != null)
                {
                    StatusMessage.Text = "Initializing system...";
                }

                // Start the loading animation if it's not already running
                if (_loadingTimer != null && !_loadingTimer.IsEnabled)
                {
                    _loadingTimer.Start();
                }

                // If we have a username, automatically trigger the access button click after a short delay
                if (!string.IsNullOrWhiteSpace(_username))
                {
                    Console.WriteLine($"Username is set to {_username}, will auto-trigger access button");

                    // Use a dispatcher timer to delay the click slightly to ensure UI is fully loaded
                    DispatcherTimer autoClickTimer = new DispatcherTimer();
                    autoClickTimer.Interval = TimeSpan.FromSeconds(2); // 2 second delay
                    autoClickTimer.Tick += (sender, e) =>
                    {
                        // Stop the timer
                        autoClickTimer.Stop();

                        // Trigger the access button click
                        Console.WriteLine("Auto-triggering access button click");
                        if (AccessButton != null)
                        {
                            AccessButton_Click(AccessButton, new RoutedEventArgs());
                        }
                    };
                    autoClickTimer.Start();
                }

                Console.WriteLine("Main window initialization completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing main window: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error initializing main window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Handles the register link click
        /// </summary>
        private void RegisterLink_MouseDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                Console.WriteLine("Register link clicked");

                // Hide login panel and show register panel
                LoginPanel.Visibility = Visibility.Collapsed;
                RegisterPanel.Visibility = Visibility.Visible;

                // Clear register form
                RegisterUsernameInput.Text = "";
                RegisterPasswordInput.Password = "";
                RegisterConfirmPasswordInput.Password = "";
                RegisterStatusText.Visibility = Visibility.Collapsed;

                // Reset password visibility
                _isRegisterPasswordVisible = false;
                RegisterPasswordInput.Visibility = Visibility.Visible;
                RegisterPasswordTextBox.Visibility = Visibility.Collapsed;
                RegisterPasswordToggleIcon.Text = "👁";

                // Focus on username field
                RegisterUsernameInput.Focus();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in register link click: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the login link click
        /// </summary>
        private void LoginLink_MouseDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                Console.WriteLine("Login link clicked");

                // Hide register panel and show login panel
                RegisterPanel.Visibility = Visibility.Collapsed;
                LoginPanel.Visibility = Visibility.Visible;

                // Clear login form errors
                LoginStatusText.Visibility = Visibility.Collapsed;
                RegisterStatusText.Visibility = Visibility.Collapsed;

                // Focus on username field
                LoginUsernameInput.Focus();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in login link click: {ex.Message}");
            }
        }

        /// <summary>
        /// Toggles register password visibility
        /// </summary>
        private void RegisterTogglePasswordVisibility_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isRegisterPasswordVisible = !_isRegisterPasswordVisible;

                if (_isRegisterPasswordVisible)
                {
                    // Show password as text
                    RegisterPasswordTextBox.Text = RegisterPasswordInput.Password;
                    RegisterPasswordInput.Visibility = Visibility.Collapsed;
                    RegisterPasswordTextBox.Visibility = Visibility.Visible;
                    RegisterPasswordTextBox.Focus();
                    RegisterPasswordTextBox.CaretIndex = RegisterPasswordTextBox.Text.Length;

                    // Update icon to "hide" (crossed eye)
                    RegisterPasswordToggleIcon.Text = "🙈";
                }
                else
                {
                    // Hide password
                    RegisterPasswordInput.Password = RegisterPasswordTextBox.Text;
                    RegisterPasswordTextBox.Visibility = Visibility.Collapsed;
                    RegisterPasswordInput.Visibility = Visibility.Visible;
                    RegisterPasswordInput.Focus();

                    // Update icon to "show" (eye)
                    RegisterPasswordToggleIcon.Text = "👁";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error toggling register password visibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the register button click
        /// </summary>
        private void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Console.WriteLine("Register button clicked");

                string username = RegisterUsernameInput.Text.Trim();
                string password = _isRegisterPasswordVisible ? RegisterPasswordTextBox.Text : RegisterPasswordInput.Password;
                string confirmPassword = RegisterConfirmPasswordInput.Password;

                // Hide any previous error messages
                RegisterStatusText.Visibility = Visibility.Collapsed;

                // Validate input
                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowRegisterError("Please enter a username.");
                    return;
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    ShowRegisterError("Please enter a password.");
                    return;
                }

                if (string.IsNullOrWhiteSpace(confirmPassword))
                {
                    ShowRegisterError("Please confirm your password.");
                    return;
                }

                if (password != confirmPassword)
                {
                    ShowRegisterError("Passwords do not match.");
                    return;
                }

                if (password.Length < 6)
                {
                    ShowRegisterError("Password must be at least 6 characters long.");
                    return;
                }

                // For demo purposes, just accept any registration and proceed
                Console.WriteLine($"Registration successful for user: {username}");

                // Store the username
                _username = username;

                // Start the loading sequence
                StartLoadingSequence();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in register: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                ShowRegisterError("An error occurred during registration.");
            }
        }

        /// <summary>
        /// Shows a register error message
        /// </summary>
        private void ShowRegisterError(string message)
        {
            RegisterStatusText.Text = message;
            RegisterStatusText.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// Finds all visual children of a specified type
        /// </summary>
        /// <typeparam name="T">The type of visual children to find</typeparam>
        /// <param name="parent">The parent object</param>
        /// <returns>An enumerable of visual children</returns>
        private static System.Collections.Generic.IEnumerable<T> FindVisualChildren<T>(DependencyObject parent) where T : DependencyObject
        {
            int childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);

                if (child is T childOfType)
                {
                    yield return childOfType;
                }

                foreach (T childOfChild in FindVisualChildren<T>(child))
                {
                    yield return childOfChild;
                }
            }
        }
    }
}




