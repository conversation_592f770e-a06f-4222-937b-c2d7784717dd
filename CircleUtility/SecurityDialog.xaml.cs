// Created by Arsenal on 5-17-25 12:15PM
using System.Windows;
using CircleUtility.Services;
using CircleUtility.Models;

namespace CircleUtility
{
    /// <summary>
    /// Interaction logic for SecurityDialog.xaml
    /// </summary>
    public partial class SecurityDialog : Window
    {
        private readonly LoggingService _logger;

        /// <summary>
        /// Initializes a new instance of the SecurityDialog class
        /// </summary>
        public SecurityDialog()
        {
            InitializeComponent();

            _logger = LoggingService.Instance;
            _logger.Log("Security dialog opened", LogLevel.INFO);
        }

        /// <summary>
        /// Handles the close button click event
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The event args</param>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            _logger.Log("Security dialog closed", LogLevel.INFO);
            DialogResult = true;
            Close();
        }
    }
}


