using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Event arguments for power profile events
    /// </summary>
    public class PowerProfileEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the power profile name
        /// </summary>
        public string ProfileName { get; set; }

        /// <summary>
        /// Gets or sets the power profile type
        /// </summary>
        public string ProfileType { get; set; }

        /// <summary>
        /// Gets or sets whether the profile change was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Gets or sets the power profile message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the expected power savings
        /// </summary>
        public double ExpectedPowerSavings { get; set; }

        /// <summary>
        /// Initializes a new instance of the PowerProfileEventArgs class
        /// </summary>
        public PowerProfileEventArgs()
        {
        }

        /// <summary>
        /// Initializes a new instance of the PowerProfileEventArgs class
        /// </summary>
        /// <param name="profileName">The power profile name</param>
        /// <param name="profileType">The power profile type</param>
        /// <param name="isSuccessful">Whether the profile change was successful</param>
        /// <param name="message">The power profile message</param>
        /// <param name="expectedPowerSavings">The expected power savings</param>
        public PowerProfileEventArgs(string profileName, string profileType, bool isSuccessful, string message, double expectedPowerSavings)
        {
            ProfileName = profileName;
            ProfileType = profileType;
            IsSuccessful = isSuccessful;
            Message = message;
            ExpectedPowerSavings = expectedPowerSavings;
        }
    }
}
