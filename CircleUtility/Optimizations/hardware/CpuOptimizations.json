[{"Name": "CPU Core Parking Optimization", "Description": "Disables CPU core parking to ensure all cores are always available for gaming, reducing latency spikes caused by cores being parked and unparked.", "Category": "CPU Performance Optimization", "HardwareType": 0, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583", "RegistryValueName": "Attributes", "RegistryValue": 0, "RegistryValueKind": "DWord", "PerformanceImpact": 70, "StabilityImpact": 95, "PowerConsumptionImpact": 70, "ThermalImpact": 65, "InputLatencyImpact": 75, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Core parking is a power-saving feature that puts unused CPU cores into a low-power state. While this saves power, it can cause latency spikes when cores need to be unparked to handle sudden workloads. Disabling core parking ensures all cores are always ready to process game data.", "RevertRegistryValue": 1}, {"Name": "Intel Speed Shift Optimization", "Description": "Optimizes Intel Speed Shift technology for gaming by setting an appropriate Energy Performance Preference (EPP) value that balances performance and power efficiency.", "Category": "CPU Performance Optimization", "HardwareType": 0, "Manufacturer": "Intel", "ModelPattern": ".*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\be337238-0d82-4146-a960-4f3749d470c7", "RegistryValueName": "Attributes", "RegistryValue": 2, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 100, "PowerConsumptionImpact": 60, "ThermalImpact": 60, "InputLatencyImpact": 70, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Speed Shift (also known as HWP - Hardware-controlled Performance states) allows the CPU to more quickly and efficiently change its frequency and voltage. This optimization makes the EPP setting visible in power options, allowing you to set it to a value that prioritizes performance for gaming.", "RevertRegistryValue": 1}, {"Name": "Intel Hybrid Core Optimization", "Description": "Optimizes thread scheduling for Intel hybrid architecture CPUs (12th gen and newer) to ensure game threads run on Performance cores rather than Efficiency cores.", "Category": "CPU Performance Optimization", "HardwareType": 0, "Manufacturer": "Intel", "ModelPattern": ".*Core.*1[2-9].*|.*Core.*2[0-9].*", "SpecificModels": [], "Method": 2, "Command": "powercfg.exe", "Arguments": "/setacvalueindex scheme_current sub_processor SCHEDPOLICY 2", "PerformanceImpact": 75, "StabilityImpact": 95, "PowerConsumptionImpact": 70, "ThermalImpact": 65, "InputLatencyImpact": 80, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Intel's hybrid architecture combines Performance (P) cores and Efficiency (E) cores. Games perform best when their threads run on P-cores. This optimization sets the thread scheduling policy to prioritize P-cores for foreground applications like games.", "RevertCommand": "powercfg.exe", "RevertArguments": "/setacvalueindex scheme_current sub_processor SCHEDPOLICY 0"}, {"Name": "AMD Precision Boost Overdrive Optimization", "Description": "Optimizes AMD Precision Boost Overdrive settings for gaming by enabling it with a conservative scalar that balances performance and stability.", "Category": "CPU Performance Optimization", "HardwareType": 0, "Manufacturer": "AMD", "ModelPattern": ".*<PERSON><PERSON><PERSON>.*", "SpecificModels": [], "Method": 2, "Command": "ryzenadj.exe", "Arguments": "--pbo-scalar=2", "PerformanceImpact": 70, "StabilityImpact": 85, "PowerConsumptionImpact": 75, "ThermalImpact": 75, "InputLatencyImpact": 65, "Risk": 1, "WarningMessage": "This optimization increases power consumption and heat output. It uses a conservative PBO scalar of 2x, which is safe for most systems with adequate cooling.", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Precision Boost Overdrive (PBO) allows AMD Ryzen CPUs to boost higher and longer than stock settings, within safe limits based on your motherboard's power delivery and your cooling solution. This optimization enables PBO with a conservative 2x scalar, which is safe for most systems while still providing a noticeable performance improvement.", "RevertCommand": "ryzenadj.exe", "RevertArguments": "--pbo-scalar=1"}, {"Name": "AMD CPPC Preferred Cores Optimization", "Description": "Enables and optimizes AMD's CPPC (Collaborative Processor Performance Control) Preferred Cores feature to ensure games use the best-performing cores.", "Category": "CPU Performance Optimization", "HardwareType": 0, "Manufacturer": "AMD", "ModelPattern": ".*<PERSON><PERSON><PERSON>.*", "SpecificModels": [], "Method": 0, "RegistryKey": "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\a55612aa-f624-42c6-a443-7397d064c04f", "RegistryValueName": "Attributes", "RegistryValue": 2, "RegistryValueKind": "DWord", "PerformanceImpact": 65, "StabilityImpact": 100, "PowerConsumptionImpact": 55, "ThermalImpact": 55, "InputLatencyImpact": 70, "Risk": 0, "WarningMessage": "", "RequiresRestart": true, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "CPPC Preferred Cores is a feature that identifies the best-performing cores in your CPU and ensures that single-threaded workloads (which are common in games) are assigned to these cores. This optimization makes the CPPC setting visible in power options, allowing you to enable it for better gaming performance.", "RevertRegistryValue": 1}, {"Name": "CPU Power Plan Optimization", "Description": "Creates and applies a custom high-performance power plan optimized for gaming with reduced latency and maximum performance.", "Category": "CPU Performance Optimization", "HardwareType": 0, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 2, "Command": "powercfg.exe", "Arguments": "-duplicatescheme 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "PerformanceImpact": 75, "StabilityImpact": 95, "PowerConsumptionImpact": 80, "ThermalImpact": 70, "InputLatencyImpact": 80, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "This optimization creates a custom power plan based on the High Performance plan, then applies additional optimizations to it. The resulting power plan provides maximum CPU performance with minimal latency, which is ideal for gaming.", "RevertCommand": "powercfg.exe", "RevertArguments": "-setactive 381b4222-f694-41f0-9685-ff5bb260df2e"}, {"Name": "CPU C-State Optimization", "Description": "Optimizes CPU C-States for gaming by limiting deep sleep states that can cause latency when waking up.", "Category": "CPU Latency Optimization", "HardwareType": 0, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 2, "Command": "powercfg.exe", "Arguments": "/setacvalueindex scheme_current sub_processor IDLEPROMOTE 50", "PerformanceImpact": 60, "StabilityImpact": 100, "PowerConsumptionImpact": 75, "ThermalImpact": 65, "InputLatencyImpact": 85, "Risk": 0, "WarningMessage": "", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": true, "IsEnabled": true, "IsAdvanced": false, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "C-States are power-saving states that the CPU enters when idle. While deeper C-States save more power, they also take longer to wake up from, which can cause latency spikes in games. This optimization limits the deepest C-States to reduce wake-up latency while still allowing some power saving.", "RevertCommand": "powercfg.exe", "RevertArguments": "/setacvalueindex scheme_current sub_processor IDLEPROMOTE 90"}, {"Name": "CPU Safe Power Limit Increase", "Description": "Increases CPU power limits (PL1/PL2) within safe ranges to allow for higher sustained performance in CPU-limited games.", "Category": "CPU Performance Optimization", "HardwareType": 0, "Manufacturer": "", "ModelPattern": ".*", "SpecificModels": [], "Method": 2, "Command": "powercfg.exe", "Arguments": "/setacvalueindex scheme_current sub_processor PROCTHROTTLEMAX 100", "PerformanceImpact": 70, "StabilityImpact": 90, "PowerConsumptionImpact": 80, "ThermalImpact": 80, "InputLatencyImpact": 65, "Risk": 1, "WarningMessage": "This optimization increases power consumption and heat output. It uses safe power limits that stay within the CPU's design specifications, but requires adequate cooling.", "RequiresRestart": false, "RequiresAdmin": true, "EnabledByDefault": false, "IsEnabled": false, "IsAdvanced": true, "IsExperimental": false, "Author": "Circle Utility", "Version": "1.0", "CreatedDate": "2025-05-17T12:15:00", "LastModifiedDate": "2025-05-17T12:15:00", "Notes": "Modern CPUs are often power-limited rather than temperature-limited. This optimization ensures your CPU can use its full power budget, which can improve performance in CPU-limited games. The exact power limits are determined based on your specific CPU model and cooling solution.", "RevertCommand": "powercfg.exe", "RevertArguments": "/setacvalueindex scheme_current sub_processor PROCTHROTTLEMAX 100"}]