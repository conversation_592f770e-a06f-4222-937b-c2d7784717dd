// Created by Arsenal on 5-17-25 12:15PM
namespace CircleUtility.Models
{
    /// <summary>
    /// Enum for log levels
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// Error log level
        /// </summary>
        ERROR,

        /// <summary>
        /// Warning log level
        /// </summary>
        WARNING,

        /// <summary>
        /// Success log level
        /// </summary>
        SUCCESS,

        /// <summary>
        /// Info log level
        /// </summary>
        INFO,

        /// <summary>
        /// Debug log level
        /// </summary>
        DEBUG,

        /// <summary>
        /// Verbose log level
        /// </summary>
        VERBOSE
    }
}

