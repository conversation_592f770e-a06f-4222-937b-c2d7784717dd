using System;
using System.Collections.Generic;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    public interface IUserTrackingService
    {
        void AddUserActivity(string activity, string details, string category);
        void AddUserActivity(string activity);
        void RegisterUserLogin(string username);
        void TrackLogin(string username);
        void TrackLogout(string username);
        List<UserLogin> GetUserLogins();
        void ResetUserHardwareAndIp(string username);
        List<PendingRegistration> GetPendingRegistrations();
        void ApprovePendingRegistration(string username);
        void DenyPendingRegistration(string username);
    }
} 