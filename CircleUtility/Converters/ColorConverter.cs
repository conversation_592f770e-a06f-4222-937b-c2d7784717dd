using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts a brush to a color
    /// </summary>
    public class ColorConverter : IValueConverter
    {
        /// <summary>
        /// Converts a brush to a color
        /// </summary>
        /// <param name="value">The brush to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>The color of the brush</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SolidColorBrush brush)
            {
                return brush.Color;
            }
            
            return Colors.White;
        }

        /// <summary>
        /// Converts a color back to a brush
        /// </summary>
        /// <param name="value">The color to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>A solid color brush with the specified color</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Color color)
            {
                return new SolidColorBrush(color);
            }
            
            return new SolidColorBrush(Colors.White);
        }
    }
}
