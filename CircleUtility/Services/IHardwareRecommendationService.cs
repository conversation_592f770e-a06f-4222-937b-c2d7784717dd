// Created by Arsenal on 5-17-25 12:15PM
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Interface for hardware recommendation service
    /// </summary>
    public interface IHardwareRecommendationService
    {
        /// <summary>
        /// Gets recommended optimizations based on hardware detection
        /// </summary>
        /// <returns>A list of optimization recommendations</returns>
        Task<List<OptimizationRecommendation>> GetRecommendedOptimizationsAsync();

        /// <summary>
        /// Gets recommended power profiles based on hardware detection
        /// </summary>
        /// <returns>A list of power profile recommendations</returns>
        Task<List<PowerProfileRecommendation>> GetRecommendedPowerProfilesAsync();

        /// <summary>
        /// Gets recommended tweaks for a specific hardware component
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>A list of optimization recommendations</returns>
        Task<List<OptimizationRecommendation>> GetRecommendedTweaksForHardwareAsync(HardwareType hardwareType);

        /// <summary>
        /// Gets the recommendation score for a specific optimization
        /// </summary>
        /// <param name="optimizationName">The optimization name</param>
        /// <returns>The recommendation score (0-100)</returns>
        Task<int> GetRecommendationScoreAsync(string optimizationName);
    }
}
