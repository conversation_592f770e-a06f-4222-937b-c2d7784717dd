using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a hardware-specific optimization
    /// </summary>
    public class HardwareSpecificOptimization
    {
        /// <summary>
        /// Gets or sets the optimization name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the optimization description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the hardware type this optimization applies to
        /// </summary>
        public HardwareType HardwareType { get; set; }

        /// <summary>
        /// Gets or sets the manufacturer this optimization applies to
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the optimization category
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the optimization priority
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// Gets or sets whether the optimization is applied
        /// </summary>
        public bool IsApplied { get; set; }

        /// <summary>
        /// Gets or sets when the optimization was applied
        /// </summary>
        public DateTime AppliedDate { get; set; }

        /// <summary>
        /// Gets or sets the expected performance improvement
        /// </summary>
        public string ExpectedImprovement { get; set; }

        /// <summary>
        /// Gets or sets any risks associated with this optimization
        /// </summary>
        public string Risks { get; set; }

        /// <summary>
        /// Initializes a new instance of the HardwareSpecificOptimization class
        /// </summary>
        public HardwareSpecificOptimization()
        {
            IsApplied = false;
            AppliedDate = DateTime.MinValue;
            Priority = 1;
        }

        /// <summary>
        /// Initializes a new instance of the HardwareSpecificOptimization class
        /// </summary>
        /// <param name="name">The optimization name</param>
        /// <param name="description">The optimization description</param>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        public HardwareSpecificOptimization(string name, string description, HardwareType hardwareType, string manufacturer)
        {
            Name = name;
            Description = description;
            HardwareType = hardwareType;
            Manufacturer = manufacturer;
            IsApplied = false;
            AppliedDate = DateTime.MinValue;
            Priority = 1;
        }
    }
}
