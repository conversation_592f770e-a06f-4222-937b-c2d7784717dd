using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using System.Threading.Tasks;
using CircleUtility.Services;
using CircleUtility.Models;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for LoginScreen.xaml
    /// </summary>
    public partial class LoginScreen : Window
    {
        private readonly SecurityService _securityService;
        private readonly UserTrackingService _userTrackingService;
        private readonly LoggingService _logger;
        private readonly ConfigurationManager _configManager;
        private readonly Storyboard _pulseAnimation;
        private readonly Storyboard _shakeAnimation;
        private readonly Storyboard _successAnimation;
        private readonly Storyboard _showResetPasswordAnimation;
        private readonly Storyboard _returnToLoginAnimation;
        private readonly Storyboard _loadingAnimation;
        private readonly string _credentialsFilePath;
        private bool _isLoading;

        /// <summary>
        /// Gets a value indicating whether the login was successful
        /// </summary>
        public bool LoginSuccessful { get; private set; }

        /// <summary>
        /// Gets the username that was used to log in
        /// </summary>
        public string Username { get; private set; }

        /// <summary>
        /// Gets or sets a value indicating whether the login is in progress
        /// </summary>
        private bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                LoadingOverlay.Visibility = value ? Visibility.Visible : Visibility.Collapsed;

                if (value)
                {
                    _loadingAnimation.Begin(this);
                }
                else
                {
                    _loadingAnimation.Stop(this);
                }
            }
        }

        /// <summary>
        /// Initializes a new instance of the LoginScreen class
        /// </summary>
        public LoginScreen()
        {
            InitializeComponent();

            // Initialize services
            _securityService = SecurityService.Instance;
            _userTrackingService = UserTrackingService.Instance;
            _logger = LoggingService.Instance;
            _configManager = ConfigurationManager.Instance;

            // Get animations from resources
            _pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            _shakeAnimation = (Storyboard)FindResource("ShakeAnimation");
            _successAnimation = (Storyboard)FindResource("SuccessAnimation");
            _showResetPasswordAnimation = (Storyboard)FindResource("ShowResetPasswordAnimation");
            _returnToLoginAnimation = (Storyboard)FindResource("ReturnToLoginAnimation");
            _loadingAnimation = (Storyboard)FindResource("LoadingAnimation");

            // Set up credentials file path
            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "CircleUtility");
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }
            _credentialsFilePath = Path.Combine(appDataPath, "credentials.dat");

            // Start pulse animation
            _pulseAnimation.Begin(this);

            // Load saved credentials if available
            LoadSavedCredentials();

            // Set focus to password box
            Loaded += (s, e) => PasswordBox.Focus();
        }

        /// <summary>
        /// Loads saved credentials if available
        /// </summary>
        private void LoadSavedCredentials()
        {
            try
            {
                if (File.Exists(_credentialsFilePath))
                {
                    // Read encrypted data
                    byte[] encryptedData = File.ReadAllBytes(_credentialsFilePath);

                    // Decrypt data using the security service
                    string decryptedData = _securityService.DecryptData(encryptedData);

                    // Split username and remember me flag
                    string[] parts = decryptedData.Split('|');
                    if (parts.Length >= 2)
                    {
                        string username = parts[0];
                        bool rememberMe = bool.Parse(parts[1]);

                        if (rememberMe)
                        {
                            UsernameTextBox.Text = username;
                            RememberMeCheckBox.IsChecked = true;
                            PasswordBox.Focus();

                            _logger.Log($"Loaded saved credentials for user: {username}", LogLevel.INFO);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading saved credentials: {ex.Message}", LogLevel.ERROR);
                // Continue without saved credentials
            }
        }

        /// <summary>
        /// Saves credentials if remember me is checked
        /// </summary>
        /// <param name="username">The username to save</param>
        private void SaveCredentials(string username)
        {
            try
            {
                if (RememberMeCheckBox.IsChecked == true)
                {
                    // Create data to save (username and remember me flag)
                    string dataToSave = $"{username}|{true}";

                    // Encrypt data using the security service
                    byte[] encryptedData = _securityService.EncryptData(dataToSave);

                    // Save encrypted data
                    File.WriteAllBytes(_credentialsFilePath, encryptedData);

                    _logger.Log($"Saved credentials for user: {username}", LogLevel.INFO);
                }
                else
                {
                    // Remove saved credentials if remember me is unchecked
                    if (File.Exists(_credentialsFilePath))
                    {
                        File.Delete(_credentialsFilePath);
                        _logger.Log("Removed saved credentials", LogLevel.INFO);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving credentials: {ex.Message}", LogLevel.ERROR);
                // Continue without saving credentials
            }
        }

        /// <summary>
        /// Handles the click event for the close button
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            LoginSuccessful = false;
            Close();
        }

        /// <summary>
        /// Handles the click event for the login button
        /// </summary>
        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            AttemptLogin();
        }

        /// <summary>
        /// Handles the key down event for the password box
        /// </summary>
        private void PasswordBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                AttemptLogin();
            }
        }

        /// <summary>
        /// Attempts to log in with the provided credentials
        /// </summary>
        private async void AttemptLogin()
        {
            try
            {
                // Get username and password
                string username = UsernameTextBox.Text.Trim();
                string password = PasswordBox.Password;

                // Validate input
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ShowError("Please enter both username and password.");
                    return;
                }

                // Show loading spinner
                IsLoading = true;
                StatusText.Text = "Authenticating...";

                // Disable login button while authenticating
                LoginButton.IsEnabled = false;

                // Simulate network delay for better UX (remove in production)
                await Task.Delay(1000);

                // Attempt to authenticate
                bool isAuthenticated = _securityService.AuthenticateUser(username, password);

                if (isAuthenticated)
                {
                    // Check if user is admin
                    bool isAdmin = _securityService.IsAdmin(username);

                    if (isAdmin)
                    {
                        // Save credentials if remember me is checked
                        SaveCredentials(username);

                        // Login successful
                        _logger.Log($"Admin login successful: {username}", LogLevel.SUCCESS);
                        StatusText.Text = "Login successful. Redirecting...";

                        // Register login
                        _userTrackingService.RegisterUserLogin(username);

                        // Set properties
                        LoginSuccessful = true;
                        Username = username;

                        // Hide loading spinner
                        IsLoading = false;

                        // Show success animation
                        _successAnimation.Completed += (s, e) =>
                        {
                            // Close the dialog after animation completes
                            Close();
                        };
                        _successAnimation.Begin(this);
                    }
                    else
                    {
                        // User is not an admin
                        _logger.Log($"Admin login failed: User is not an admin: {username}", LogLevel.WARNING);
                        ShowError("You do not have administrator privileges.");

                        // Hide loading spinner
                        IsLoading = false;

                        // Re-enable login button
                        LoginButton.IsEnabled = true;
                    }
                }
                else
                {
                    // Authentication failed
                    _logger.Log($"Admin login failed: Invalid credentials for user: {username}", LogLevel.WARNING);
                    ShowError("Invalid username or password.");

                    // Hide loading spinner
                    IsLoading = false;

                    // Re-enable login button
                    LoginButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error during login: {ex.Message}", LogLevel.ERROR);
                ShowError("An error occurred during login. Please try again.");

                // Hide loading spinner
                IsLoading = false;

                // Re-enable login button
                LoginButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Shows an error message and plays the shake animation
        /// </summary>
        /// <param name="message">The error message to show</param>
        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
            _shakeAnimation.Begin(this);
        }

        /// <summary>
        /// Shows an error message in the reset password panel
        /// </summary>
        /// <param name="message">The error message to show</param>
        private void ShowResetError(string message)
        {
            ResetErrorMessage.Text = message;
            ResetErrorMessage.Visibility = Visibility.Visible;
            _shakeAnimation.Begin(this);
        }

        /// <summary>
        /// Handles the mouse down event for the forgot password link
        /// </summary>
        private void ForgotPasswordLink_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // Show reset password panel
            _showResetPasswordAnimation.Begin(this);

            // Clear any error messages
            ResetErrorMessage.Visibility = Visibility.Collapsed;

            // Set focus to reset username text box
            ResetUsernameTextBox.Text = UsernameTextBox.Text;
            ResetUsernameTextBox.Focus();

            // Update status text
            StatusText.Text = "Enter your username to reset your password";
        }

        /// <summary>
        /// Handles the mouse down event for the back to login link
        /// </summary>
        private void BackToLoginLink_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // Show login panel
            _returnToLoginAnimation.Begin(this);

            // Clear any error messages
            ErrorMessage.Visibility = Visibility.Collapsed;

            // Set focus to password box
            PasswordBox.Focus();

            // Update status text
            StatusText.Text = "Enter your credentials to access admin features";
        }

        /// <summary>
        /// Handles the click event for the reset password button
        /// </summary>
        private async void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Get username
                string username = ResetUsernameTextBox.Text.Trim();

                // Validate input
                if (string.IsNullOrEmpty(username))
                {
                    ShowResetError("Please enter your username.");
                    return;
                }

                // Show loading spinner
                IsLoading = true;
                StatusText.Text = "Processing password reset...";

                // Disable reset button while processing
                ResetPasswordButton.IsEnabled = false;

                // Simulate network delay for better UX (remove in production)
                await Task.Delay(1500);

                // Check if user exists
                if (_securityService.UserExists(username))
                {
                    // Generate a temporary password
                    string tempPassword = GenerateTemporaryPassword();

                    // Reset the password
                    bool resetSuccess = _securityService.AdminResetPassword(username, tempPassword);

                    if (resetSuccess)
                    {
                        // Log the password reset
                        _logger.Log($"Password reset for user: {username}", LogLevel.INFO);

                        // Show success message
                        StatusText.Text = "Password reset successful. Check your email for instructions.";

                        // Hide loading spinner
                        IsLoading = false;

                        // Show temporary password (in a real app, this would be sent via email)
                        MessageBox.Show($"Your temporary password is: {tempPassword}\n\nPlease change it after logging in.",
                            "Password Reset", MessageBoxButton.OK, MessageBoxImage.Information);

                        // Return to login panel
                        BackToLoginLink_MouseDown(null, null);

                        // Pre-fill the username
                        UsernameTextBox.Text = username;
                        PasswordBox.Focus();
                    }
                    else
                    {
                        // Reset failed
                        ShowResetError("Failed to reset password. Please try again later.");

                        // Hide loading spinner
                        IsLoading = false;

                        // Re-enable reset button
                        ResetPasswordButton.IsEnabled = true;
                    }
                }
                else
                {
                    // User not found
                    ShowResetError("Username not found.");

                    // Hide loading spinner
                    IsLoading = false;

                    // Re-enable reset button
                    ResetPasswordButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error during password reset: {ex.Message}", LogLevel.ERROR);
                ShowResetError("An error occurred during password reset. Please try again.");

                // Hide loading spinner
                IsLoading = false;

                // Re-enable reset button
                ResetPasswordButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Generates a temporary password
        /// </summary>
        /// <returns>A temporary password</returns>
        private string GenerateTemporaryPassword()
        {
            // Generate a random password with 8 characters
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
