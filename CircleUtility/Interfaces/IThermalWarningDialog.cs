using System;
using CircleUtility.Models;
using System.Windows;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for thermal warning dialog
    /// </summary>
    public interface IThermalWarningDialog : IDialog
    {
        /// <summary>
        /// Gets or sets the power management profile
        /// </summary>
        PowerManagementProfile Profile { get; set; }

        /// <summary>
        /// Gets a value indicating whether the user chose to proceed
        /// </summary>
        bool UserChoseToProceed { get; }

        /// <summary>
        /// Shows the warning dialog with a power profile
        /// </summary>
        /// <param name="owner">The owner window</param>
        /// <param name="profile">The power profile</param>
        /// <returns>True if the user chose to proceed, false otherwise</returns>
        bool ShowWarning(Window owner, PowerManagementProfile profile);
    }
}
