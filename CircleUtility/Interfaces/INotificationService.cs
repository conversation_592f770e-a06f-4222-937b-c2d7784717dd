using System;
using System.Collections.Generic;
using System.Windows.Input;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for notification services
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// Adds a notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="severity">The severity</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        SystemNotification AddNotification(string title, string message, NotificationSeverity severity, string category = "System", ICommand action = null, string actionText = null);

        /// <summary>
        /// Adds an info notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        SystemNotification AddInfoNotification(string title, string message, string category = "System", ICommand action = null, string actionText = null);

        /// <summary>
        /// Adds a success notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        SystemNotification AddSuccessNotification(string title, string message, string category = "System", ICommand action = null, string actionText = null);

        /// <summary>
        /// Adds a warning notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        SystemNotification AddWarningNotification(string title, string message, string category = "System", ICommand action = null, string actionText = null);

        /// <summary>
        /// Adds an error notification
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="category">The category</param>
        /// <param name="action">The action</param>
        /// <param name="actionText">The action text</param>
        /// <returns>The notification</returns>
        SystemNotification AddErrorNotification(string title, string message, string category = "System", ICommand action = null, string actionText = null);

        /// <summary>
        /// Gets the notification history
        /// </summary>
        IEnumerable<SystemNotification> NotificationHistory { get; }

        /// <summary>
        /// Marks all notifications as read
        /// </summary>
        void MarkAllAsRead();

        /// <summary>
        /// Clears all notifications
        /// </summary>
        void ClearAll();

        /// <summary>
        /// Dismisses a notification
        /// </summary>
        /// <param name="notification">The notification</param>
        void DismissNotification(SystemNotification notification);

        /// <summary>
        /// Marks a notification as read
        /// </summary>
        /// <param name="notification">The notification</param>
        void MarkAsRead(SystemNotification notification);

        /// <summary>
        /// Marks a notification as unread
        /// </summary>
        /// <param name="notification">The notification</param>
        void MarkAsUnread(SystemNotification notification);

        /// <summary>
        /// Event that is raised when a notification is added
        /// </summary>
        event EventHandler<SystemNotification> NotificationAdded;

        /// <summary>
        /// Event that is raised when a notification is read
        /// </summary>
        event EventHandler<SystemNotification> NotificationRead;

        /// <summary>
        /// Event that is raised when the unread count changes
        /// </summary>
        event EventHandler<int> UnreadCountChanged;
    }
}
