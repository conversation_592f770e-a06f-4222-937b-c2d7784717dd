//Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace CircleUtility.Converters
{
    /// <summary>
    /// Converts a boolean value to a success or error color
    /// </summary>
    public class BoolToSuccessColorConverter : IValueConverter
    {
        /// <summary>
        /// Converts a boolean value to a success or error color
        /// </summary>
        /// <param name="value">The boolean value to convert</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>A success color if the value is true, an error color if the value is false</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSuccess)
            {
                return isSuccess ? new SolidColorBrush(Color.FromRgb(0x00, 0xFF, 0x00)) : new SolidColorBrush(Color.FromRgb(0xFF, 0x32, 0x32));
            }

            return new SolidColorBrush(Color.FromRgb(0x00, 0xC8, 0xFF));
        }

        /// <summary>
        /// Converts a color back to a boolean value
        /// </summary>
        /// <param name="value">The color to convert back</param>
        /// <param name="targetType">The type of the binding target property</param>
        /// <param name="parameter">The converter parameter to use</param>
        /// <param name="culture">The culture to use in the converter</param>
        /// <returns>True if the color is a success color, false otherwise</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
