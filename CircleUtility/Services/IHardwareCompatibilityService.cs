// Created by Arsenal on 5-17-25 12:15PM
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Interface for hardware compatibility service
    /// </summary>
    public interface IHardwareCompatibilityService
    {
        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task InitializeAsync();

        /// <summary>
        /// Validates an optimization for compatibility with the current hardware
        /// </summary>
        /// <param name="optimization">The optimization to validate</param>
        /// <returns>A compatibility result</returns>
        Task<CompatibilityResult> ValidateOptimizationAsync(HardwareSpecificOptimization optimization);

        /// <summary>
        /// Validates an optimization for compatibility with the current hardware
        /// </summary>
        /// <param name="optimization">The optimization to validate</param>
        /// <param name="forceRefresh">Whether to force a refresh of hardware information</param>
        /// <returns>A compatibility result</returns>
        Task<CompatibilityResult> ValidateOptimizationAsync(HardwareSpecificOptimization optimization, bool forceRefresh);

        /// <summary>
        /// Validates a power profile for compatibility with the current hardware
        /// </summary>
        /// <param name="profile">The power profile to validate</param>
        /// <returns>A compatibility result</returns>
        Task<CompatibilityResult> ValidatePowerProfileAsync(PowerManagementProfile profile);

        /// <summary>
        /// Validates a power profile for compatibility with the current hardware
        /// </summary>
        /// <param name="profile">The power profile to validate</param>
        /// <param name="forceRefresh">Whether to force a refresh of hardware information</param>
        /// <returns>A compatibility result</returns>
        Task<CompatibilityResult> ValidatePowerProfileAsync(PowerManagementProfile profile, bool forceRefresh);
    }
}
