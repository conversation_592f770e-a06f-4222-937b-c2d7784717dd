using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for AnimatedLoadingSpinner.xaml
    /// </summary>
    public partial class AnimatedLoadingSpinner : UserControl
    {
        private Storyboard _rotationAnimation;
        private Storyboard _pulseAnimation;

        public static readonly DependencyProperty SpinnerColorProperty =
            DependencyProperty.Register(nameof(SpinnerColor), typeof(Brush), typeof(AnimatedLoadingSpinner),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(0, 200, 255))));

        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register(nameof(Text), typeof(string), typeof(AnimatedLoadingSpinner),
                new PropertyMetadata(string.Empty));

        public static readonly DependencyProperty IsActiveProperty =
            DependencyProperty.Register(nameof(IsActive), typeof(bool), typeof(AnimatedLoadingSpinner),
                new PropertyMetadata(true, OnIsActiveChanged));

        public Brush SpinnerColor
        {
            get => (Brush)GetValue(SpinnerColorProperty);
            set => SetValue(SpinnerColorProperty, value);
        }

        public string Text
        {
            get => (string)GetValue(TextProperty);
            set => SetValue(TextProperty, value);
        }

        public bool IsActive
        {
            get => (bool)GetValue(IsActiveProperty);
            set => SetValue(IsActiveProperty, value);
        }

        public AnimatedLoadingSpinner()
        {
            InitializeComponent();

            _rotationAnimation = TryFindResource("RotationAnimation") as Storyboard;
            _pulseAnimation = TryFindResource("PulseAnimation") as Storyboard;
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (IsActive)
                StartAnimations();
        }

        public void StartAnimations()
        {
            _rotationAnimation?.Begin(this, true);
            _pulseAnimation?.Begin(this, true);
        }

        public void StopAnimations()
        {
            _rotationAnimation?.Stop(this);
            _pulseAnimation?.Stop(this);
        }

        private static void OnIsActiveChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AnimatedLoadingSpinner spinner)
            {
                if ((bool)e.NewValue)
                    spinner.StartAnimations();
                else
                    spinner.StopAnimations();
            }
        }
    }
}
