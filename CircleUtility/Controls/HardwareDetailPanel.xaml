<UserControl x:Class="CircleUtility.Controls.HardwareDetailPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CircleUtility.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- Animation for expanding/collapsing sections -->
        <Storyboard x:Key="ExpandAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Height"
                             Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
        
        <!-- Style for section headers -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <!-- Style for property names -->
        <Style x:Key="PropertyNameStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFAAAAAA"/>
            <Setter Property="Margin" Value="10,2,0,2"/>
        </Style>
        
        <!-- Style for property values -->
        <Style x:Key="PropertyValueStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,2,0,2"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        
        <!-- Style for expand/collapse buttons -->
        <Style x:Key="ExpandButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Foreground" Value="#FF00C8FF"/>
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Width" Value="20"/>
            <Setter Property="Height" Value="20"/>
            <Setter Property="Margin" Value="0,0,5,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid Background="Transparent">
                            <TextBlock Text="{TemplateBinding Content}" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"
                                       Foreground="{TemplateBinding Foreground}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="10">
            <!-- CPU Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button x:Name="CpuExpandButton" 
                        Content="&#xE70E;" 
                        Style="{StaticResource ExpandButtonStyle}"
                        Click="CpuExpandButton_Click"/>
                
                <TextBlock Grid.Column="1" 
                           Text="CPU" 
                           Style="{StaticResource SectionHeaderStyle}"/>
            </Grid>
            
            <Grid x:Name="CpuDetailsGrid" Height="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- CPU Properties -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CpuNameValue" Text="Intel Core i7-10700K" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="Cores:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="CpuCoresValue" Text="8 Cores, 16 Threads" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="Clock Speed:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="CpuClockSpeedValue" Text="3.80 GHz (Turbo: 5.10 GHz)" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="3" Grid.Column="0" Text="Architecture:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="CpuArchitectureValue" Text="x64" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="4" Grid.Column="0" Text="L2 Cache:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="4" Grid.Column="1" x:Name="CpuL2CacheValue" Text="2.0 MB" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="5" Grid.Column="0" Text="L3 Cache:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="5" Grid.Column="1" x:Name="CpuL3CacheValue" Text="16.0 MB" Style="{StaticResource PropertyValueStyle}"/>
            </Grid>
            
            <!-- GPU Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button x:Name="GpuExpandButton" 
                        Content="&#xE70E;" 
                        Style="{StaticResource ExpandButtonStyle}"
                        Click="GpuExpandButton_Click"/>
                
                <TextBlock Grid.Column="1" 
                           Text="GPU" 
                           Style="{StaticResource SectionHeaderStyle}"/>
            </Grid>
            
            <Grid x:Name="GpuDetailsGrid" Height="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- GPU Properties -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="GpuNameValue" Text="NVIDIA GeForce RTX 3080" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="Memory:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="GpuMemoryValue" Text="10 GB GDDR6X" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="Vendor:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="GpuVendorValue" Text="NVIDIA" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="3" Grid.Column="0" Text="Driver Version:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="GpuDriverValue" Text="512.15" Style="{StaticResource PropertyValueStyle}"/>
            </Grid>
            
            <!-- RAM Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button x:Name="RamExpandButton" 
                        Content="&#xE70E;" 
                        Style="{StaticResource ExpandButtonStyle}"
                        Click="RamExpandButton_Click"/>
                
                <TextBlock Grid.Column="1" 
                           Text="RAM" 
                           Style="{StaticResource SectionHeaderStyle}"/>
            </Grid>
            
            <Grid x:Name="RamDetailsGrid" Height="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- RAM Properties -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="Total Capacity:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="RamCapacityValue" Text="32 GB" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="Type:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="RamTypeValue" Text="DDR4" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="Speed:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="RamSpeedValue" Text="3200 MHz" Style="{StaticResource PropertyValueStyle}"/>
            </Grid>
            
            <!-- Storage Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button x:Name="StorageExpandButton" 
                        Content="&#xE70E;" 
                        Style="{StaticResource ExpandButtonStyle}"
                        Click="StorageExpandButton_Click"/>
                
                <TextBlock Grid.Column="1" 
                           Text="Storage" 
                           Style="{StaticResource SectionHeaderStyle}"/>
            </Grid>
            
            <StackPanel x:Name="StorageDetailsPanel" Height="0">
                <!-- Storage items will be added dynamically -->
            </StackPanel>
            
            <!-- Network Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button x:Name="NetworkExpandButton" 
                        Content="&#xE70E;" 
                        Style="{StaticResource ExpandButtonStyle}"
                        Click="NetworkExpandButton_Click"/>
                
                <TextBlock Grid.Column="1" 
                           Text="Network" 
                           Style="{StaticResource SectionHeaderStyle}"/>
            </Grid>
            
            <StackPanel x:Name="NetworkDetailsPanel" Height="0">
                <!-- Network items will be added dynamically -->
            </StackPanel>
            
            <!-- OS Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button x:Name="OsExpandButton" 
                        Content="&#xE70E;" 
                        Style="{StaticResource ExpandButtonStyle}"
                        Click="OsExpandButton_Click"/>
                
                <TextBlock Grid.Column="1" 
                           Text="Operating System" 
                           Style="{StaticResource SectionHeaderStyle}"/>
            </Grid>
            
            <Grid x:Name="OsDetailsGrid" Height="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- OS Properties -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="OsNameValue" Text="Windows 11 Pro" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="Version:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="OsVersionValue" Text="22H2" Style="{StaticResource PropertyValueStyle}"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="Build:" Style="{StaticResource PropertyNameStyle}"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="OsBuildValue" Text="22621.1702" Style="{StaticResource PropertyValueStyle}"/>
            </Grid>
        </StackPanel>
    </ScrollViewer>
</UserControl>
