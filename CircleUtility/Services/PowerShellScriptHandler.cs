using CircleUtility.Interfaces;
using System;
using System.Collections.Generic;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    public class PowerShellScriptHandler : IPowerShellScriptHandler, IDisposable
    {
        private readonly LoggingService _logger;
        private readonly InitialSessionState _sessionState;
        private readonly Runspace _runspace;
        private bool _disposed;

        public PowerShellScriptHandler()
        {
            _logger = LoggingService.Instance;
            
            // Initialize session state with required modules and settings
            _sessionState = InitialSessionState.CreateDefault();
            _sessionState.ExecutionPolicy = Microsoft.PowerShell.ExecutionPolicy.Bypass;
            _sessionState.ThreadOptions = PSThreadOptions.UseCurrentThread;

            // Remove problematic module/snap-in loading
            // _sessionState.ImportPSModule(new[] 
            // { 
            //     "Microsoft.PowerShell.Management",
            //     "Microsoft.PowerShell.Utility",
            //     "Microsoft.PowerShell.Security"
            // });

            // Create and open runspace
            _runspace = RunspaceFactory.CreateRunspace(_sessionState);
            _runspace.Open();

            _logger.LogInfo("PowerShell script handler initialized");
        }

        // Public constructor for DI compatibility
        public PowerShellScriptHandler(bool forDI = false) : this() { }

        public async Task<PowerShellResult> ExecuteScriptAsync(string script)
        {
            if (string.IsNullOrEmpty(script))
            {
                throw new ArgumentNullException(nameof(script));
            }

            try
            {
                _logger.LogInfo($"Executing PowerShell script: {script.Substring(0, Math.Min(100, script.Length))}...");

                using var powerShell = PowerShell.Create(_runspace);
                powerShell.AddScript(script);

                var output = new List<string>();
                var errors = new List<string>();

                var result = await Task.Run(() =>
                {
                    try
                    {
                        var pipelineObjects = powerShell.Invoke();

                        foreach (var item in pipelineObjects)
                        {
                            if (item != null)
                            {
                                output.Add(item.ToString());
                            }
                        }

                        foreach (var error in powerShell.Streams.Error)
                        {
                            if (error != null)
                            {
                                errors.Add(error.ToString());
                            }
                        }

                        return new PowerShellResult
                        {
                            Output = output,
                            Errors = errors,
                            HadErrors = errors.Count > 0
                        };
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error executing PowerShell script", ex);
                        errors.Add(ex.Message);
                        return new PowerShellResult
                        {
                            Output = output,
                            Errors = errors,
                            HadErrors = true
                        };
                    }
                });

                if (result.HadErrors)
                {
                    _logger.LogError($"PowerShell script execution failed: {string.Join(Environment.NewLine, result.Errors)}");
                }
                else
                {
                    _logger.LogSuccess("PowerShell script executed successfully");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to execute PowerShell script", ex);
                return new PowerShellResult
                {
                    Output = new List<string>(),
                    Errors = new List<string> { ex.Message },
                    HadErrors = true
                };
            }
        }

        public async Task<PowerShellResult> ExecuteCommandAsync(string command, Dictionary<string, object> parameters = null)
        {
            if (string.IsNullOrEmpty(command))
            {
                throw new ArgumentNullException(nameof(command));
            }

            try
            {
                _logger.LogInfo($"Executing PowerShell command: {command}");

                using var powerShell = PowerShell.Create(_runspace);
                powerShell.AddCommand(command);

                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        powerShell.AddParameter(param.Key, param.Value);
                    }
                }

                var output = new List<string>();
                var errors = new List<string>();

                var result = await Task.Run(() =>
                {
                    try
                    {
                        var pipelineObjects = powerShell.Invoke();

                        foreach (var item in pipelineObjects)
                        {
                            if (item != null)
                            {
                                output.Add(item.ToString());
                            }
                        }

                        foreach (var error in powerShell.Streams.Error)
                        {
                            if (error != null)
                            {
                                errors.Add(error.ToString());
                            }
                        }

                        return new PowerShellResult
                        {
                            Output = output,
                            Errors = errors,
                            HadErrors = errors.Count > 0
                        };
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error executing PowerShell command", ex);
                        errors.Add(ex.Message);
                        return new PowerShellResult
                        {
                            Output = output,
                            Errors = errors,
                            HadErrors = true
                        };
                    }
                });

                if (result.HadErrors)
                {
                    _logger.LogError($"PowerShell command execution failed: {string.Join(Environment.NewLine, result.Errors)}");
                }
                else
                {
                    _logger.LogSuccess("PowerShell command executed successfully");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to execute PowerShell command", ex);
                return new PowerShellResult
                {
                    Output = new List<string>(),
                    Errors = new List<string> { ex.Message },
                    HadErrors = true
                };
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        if (_runspace != null)
                        {
                            _runspace.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error disposing PowerShell script handler", ex);
                    }
                }
                _disposed = true;
            }
        }

        ~PowerShellScriptHandler()
        {
            Dispose(false);
        }
    }

    public class PowerShellResult
    {
        public List<string> Output { get; set; }
        public List<string> Errors { get; set; }
        public bool HadErrors { get; set; }
    }
} 



