using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for QuickActionPanel.xaml
    /// </summary>
    public partial class QuickActionPanel : UserControl
    {
        private readonly Storyboard _buttonHoverAnimation;
        private readonly Storyboard _buttonLeaveAnimation;

        /// <summary>
        /// Event raised when the optimize performance button is clicked
        /// </summary>
        public event EventHandler OptimizePerformanceClicked;

        /// <summary>
        /// Event raised when the optimize GPU button is clicked
        /// </summary>
        public event EventHandler OptimizeGpuClicked;

        /// <summary>
        /// Event raised when the optimize network button is clicked
        /// </summary>
        public event EventHandler OptimizeNetworkClicked;

        /// <summary>
        /// Event raised when the clean system button is clicked
        /// </summary>
        public event EventHandler CleanSystemClicked;

        /// <summary>
        /// Event raised when the optimize input button is clicked
        /// </summary>
        public event EventHandler OptimizeInputClicked;

        /// <summary>
        /// Event raised when the revert tweaks button is clicked
        /// </summary>
        public event EventHandler RevertTweaksClicked;

        /// <summary>
        /// Event raised when the tweak system button is clicked
        /// </summary>
        public event EventHandler TweakSystemClicked;

        /// <summary>
        /// Event raised when the test notifications button is clicked
        /// </summary>
        public event EventHandler TestNotificationsClicked;

        /// <summary>
        /// Initializes a new instance of the QuickActionPanel class
        /// </summary>
        public QuickActionPanel()
        {
            InitializeComponent();

            // Get animations from resources
            _buttonHoverAnimation = (Storyboard)FindResource("ButtonHoverAnimation");
            _buttonLeaveAnimation = (Storyboard)FindResource("ButtonLeaveAnimation");
        }

        private void OptimizePerformanceButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizePerformanceButton, "OptimizePerformanceClickAnimation");
            OptimizePerformanceClicked?.Invoke(this, EventArgs.Empty);
        }

        private void OptimizeGpuButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizeGpuButton, "OptimizeGpuClickAnimation");
            OptimizeGpuClicked?.Invoke(this, EventArgs.Empty);
        }

        private void OptimizeNetworkButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizeNetworkButton, "OptimizeNetworkClickAnimation");
            OptimizeNetworkClicked?.Invoke(this, EventArgs.Empty);
        }

        private void CleanSystemButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(CleanSystemButton, "CleanSystemClickAnimation");
            CleanSystemClicked?.Invoke(this, EventArgs.Empty);
        }

        private void OptimizeInputButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(OptimizeInputButton, "OptimizeInputClickAnimation");
            OptimizeInputClicked?.Invoke(this, EventArgs.Empty);
        }

        private void RevertTweaksButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(RevertTweaksButton, "RevertTweaksClickAnimation");
            RevertTweaksClicked?.Invoke(this, EventArgs.Empty);
        }

        private void TweakSystemButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(TweakSystemButton, "TweakSystemClickAnimation");
            TweakSystemClicked?.Invoke(this, EventArgs.Empty);
        }

        private void TestNotificationsButton_Click(object sender, RoutedEventArgs e)
        {
            PlayClickAnimation(TestNotificationsButton, "TestNotificationsClickAnimation");
            TestNotificationsClicked?.Invoke(this, EventArgs.Empty);
        }

        private void Button_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (sender is Button button)
            {
                // Clone the animation to avoid conflicts
                Storyboard animation = _buttonHoverAnimation.Clone();
                animation.Begin(button);
            }
        }

        private void Button_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (sender is Button button)
            {
                // Clone the animation to avoid conflicts
                Storyboard animation = _buttonLeaveAnimation.Clone();
                animation.Begin(button);
            }
        }

        private void PlayClickAnimation(Button button, string animationKey)
        {
            // Set initial scale
            button.RenderTransform = new System.Windows.Media.ScaleTransform(0.95, 0.95);

            // Get the button-specific animation
            if (button.Resources[animationKey] is Storyboard animation)
            {
                animation = animation.Clone();
                animation.Begin(button);
            }
        }
    }
}
