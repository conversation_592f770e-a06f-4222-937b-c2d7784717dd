<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Merge all resource dictionaries -->
    <ResourceDictionary.MergedDictionaries>
        
        <!-- Color Resources -->
        <ResourceDictionary Source="Colors/ColorResources.xaml"/>
        
        <!-- Converter Resources -->
        <ResourceDictionary Source="Converters/ConverterResources.xaml"/>
        
        <!-- Style Resources -->
        <ResourceDictionary Source="Styles/ButtonStyles.xaml"/>
        
    </ResourceDictionary.MergedDictionaries>

    <!-- Additional global resources can be defined here -->
    
    <!-- Font Families -->
    <FontFamily x:Key="ConsoleFont">Consolas</FontFamily>
    <FontFamily x:Key="UIFont">Segoe UI</FontFamily>
    
    <!-- Common Font Sizes -->
    <sys:Double x:Key="HeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="SubHeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">18</sys:Double>
    <sys:Double x:Key="NormalFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="SmallFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    
    <!-- Common Margins and Paddings -->
    <Thickness x:Key="StandardMargin">10</Thickness>
    <Thickness x:Key="LargeMargin">20</Thickness>
    <Thickness x:Key="SmallMargin">5</Thickness>
    
    <Thickness x:Key="StandardPadding">10,5</Thickness>
    <Thickness x:Key="LargePadding">15,8</Thickness>
    <Thickness x:Key="SmallPadding">5,3</Thickness>
    
    <!-- Common Border Thickness -->
    <Thickness x:Key="StandardBorderThickness">1</Thickness>
    <Thickness x:Key="ThickBorderThickness">2</Thickness>
    
    <!-- Common Corner Radius -->
    <CornerRadius x:Key="StandardCornerRadius">3</CornerRadius>
    <CornerRadius x:Key="RoundedCornerRadius">5</CornerRadius>
    
    <!-- Animation Durations -->
    <Duration x:Key="FastAnimation">0:0:0.2</Duration>
    <Duration x:Key="NormalAnimation">0:0:0.3</Duration>
    <Duration x:Key="SlowAnimation">0:0:0.5</Duration>

</ResourceDictionary>
