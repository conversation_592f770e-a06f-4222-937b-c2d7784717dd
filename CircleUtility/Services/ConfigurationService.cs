using System;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for managing application configuration
    /// </summary>
    public class ConfigurationService
    {
        private static ConfigurationService _instance;
        private readonly string _configFilePath;
        private readonly Dictionary<string, object> _settings;
        private readonly object _lockObject = new object();
        
        /// <summary>
        /// Gets the instance of the configuration service
        /// </summary>
        public static ConfigurationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ConfigurationService();
                }
                
                return _instance;
            }
        }
        
        /// <summary>
        /// Initializes a new instance of the ConfigurationService class
        /// </summary>
        private ConfigurationService()
        {
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CircleUtility");
            
            // Create the directory if it doesn't exist
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }
            
            _configFilePath = Path.Combine(appDataPath, "config.json");
            _settings = new Dictionary<string, object>();
            
            // Load settings from file
            LoadSettings();
            
            // Set default settings if not already set
            SetDefaultSettings();
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether to auto-start with Windows
        /// </summary>
        public bool AutoStartWithWindows
        {
            get => GetSetting<bool>("AutoStartWithWindows");
            set => SetSetting("AutoStartWithWindows", value);
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether to automatically update the application
        /// </summary>
        public bool AutoUpdate
        {
            get => GetSetting<bool>("AutoUpdate");
            set => SetSetting("AutoUpdate", value);
        }
        
        /// <summary>
        /// Gets or sets the update check interval in days
        /// </summary>
        public int UpdateCheckInterval
        {
            get => GetSetting<int>("UpdateCheckInterval");
            set => SetSetting("UpdateCheckInterval", value);
        }
        
        /// <summary>
        /// Gets or sets the update server URL
        /// </summary>
        public string UpdateServerUrl
        {
            get => GetSetting<string>("UpdateServerUrl");
            set => SetSetting("UpdateServerUrl", value);
        }
        
        /// <summary>
        /// Gets or sets a value indicating whether to require login
        /// </summary>
        public bool RequireLogin
        {
            get => GetSetting<bool>("RequireLogin");
            set => SetSetting("RequireLogin", value);
        }
        
        /// <summary>
        /// Gets or sets the session timeout in minutes
        /// </summary>
        public int SessionTimeout
        {
            get => GetSetting<int>("SessionTimeout");
            set => SetSetting("SessionTimeout", value);
        }
        
        /// <summary>
        /// Gets a setting value
        /// </summary>
        /// <typeparam name="T">The setting type</typeparam>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">The default value</param>
        /// <returns>The setting value</returns>
        public T GetSetting<T>(string key, T defaultValue = default)
        {
            lock (_lockObject)
            {
                if (_settings.TryGetValue(key, out object value))
                {
                    if (value is T typedValue)
                    {
                        return typedValue;
                    }
                    
                    try
                    {
                        return (T)Convert.ChangeType(value, typeof(T));
                    }
                    catch
                    {
                        return defaultValue;
                    }
                }
                
                return defaultValue;
            }
        }
        
        /// <summary>
        /// Sets a setting value
        /// </summary>
        /// <typeparam name="T">The setting type</typeparam>
        /// <param name="key">The setting key</param>
        /// <param name="value">The setting value</param>
        public void SetSetting<T>(string key, T value)
        {
            lock (_lockObject)
            {
                _settings[key] = value;
                SaveSettings();
            }
        }
        
        /// <summary>
        /// Loads settings from file
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    string json = File.ReadAllText(_configFilePath);
                    var settings = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
                    
                    if (settings != null)
                    {
                        lock (_lockObject)
                        {
                            _settings.Clear();
                            
                            foreach (var kvp in settings)
                            {
                                _settings[kvp.Key] = kvp.Value;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Saves settings to file
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                string json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Sets default settings if not already set
        /// </summary>
        private void SetDefaultSettings()
        {
            if (!_settings.ContainsKey("AutoStartWithWindows"))
            {
                _settings["AutoStartWithWindows"] = false;
            }
            
            if (!_settings.ContainsKey("AutoUpdate"))
            {
                _settings["AutoUpdate"] = true;
            }
            
            if (!_settings.ContainsKey("UpdateCheckInterval"))
            {
                _settings["UpdateCheckInterval"] = 1; // Daily
            }
            
            if (!_settings.ContainsKey("UpdateServerUrl"))
            {
                _settings["UpdateServerUrl"] = "https://updates.circleutility.com";
            }
            
            if (!_settings.ContainsKey("RequireLogin"))
            {
                _settings["RequireLogin"] = true;
            }
            
            if (!_settings.ContainsKey("SessionTimeout"))
            {
                _settings["SessionTimeout"] = 30; // 30 minutes
            }
            
            SaveSettings();
        }
    }
}
