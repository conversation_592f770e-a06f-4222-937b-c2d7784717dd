// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.AccessControl;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Security.Principal;
using System.Diagnostics;
using Microsoft.Win32;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for handling security-related operations
    /// </summary>
    public class SecurityService
    {
        private static SecurityService _instance;
        private static readonly object _instanceLock = new object();
        private readonly LoggingService _logger;
        private readonly string _securityDirectory;
        private readonly string _usersFilePath;
        private readonly string _encryptionKeyPath;
        private readonly Dictionary<string, UserSecurityInfo> _users;
        private readonly HardwareFingerprintService _hardwareFingerprintService;
        private byte[] _encryptionKey;
        private bool _isInitialized;

        /// <summary>
        /// Initializes a new instance of the SecurityService class
        /// </summary>
        private SecurityService()
        {
            _logger = LoggingService.Instance;
            _hardwareFingerprintService = HardwareFingerprintService.Instance;
            _securityDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Security");
            _usersFilePath = Path.Combine(_securityDirectory, "users.dat");
            _encryptionKeyPath = Path.Combine(_securityDirectory, "key.dat");
            _users = new Dictionary<string, UserSecurityInfo>(StringComparer.OrdinalIgnoreCase);

            // Ensure security directory exists
            if (!Directory.Exists(_securityDirectory))
            {
                Directory.CreateDirectory(_securityDirectory);
            }

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Initializes a new instance of the SecurityService class with custom paths (for testing)
        /// </summary>
        /// <param name="securityDirectory">The security directory path</param>
        /// <param name="usersFilePath">The users file path</param>
        /// <param name="encryptionKeyPath">The encryption key path</param>
        public SecurityService(string securityDirectory, string usersFilePath, string encryptionKeyPath)
        {
            _logger = LoggingService.Instance;
            _hardwareFingerprintService = HardwareFingerprintService.Instance;
            _securityDirectory = securityDirectory;
            _usersFilePath = usersFilePath;
            _encryptionKeyPath = encryptionKeyPath;
            _users = new Dictionary<string, UserSecurityInfo>(StringComparer.OrdinalIgnoreCase);

            // Ensure security directory exists
            if (!Directory.Exists(_securityDirectory))
            {
                Directory.CreateDirectory(_securityDirectory);
            }

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Initializes a new instance of the SecurityService class with predefined users (for testing)
        /// </summary>
        /// <param name="users">The predefined users</param>
        public SecurityService(Dictionary<string, UserSecurityInfo> users)
        {
            _logger = LoggingService.Instance;
            _hardwareFingerprintService = HardwareFingerprintService.Instance;
            _securityDirectory = Path.Combine(Path.GetTempPath(), "CircleUtilityTests");
            _usersFilePath = Path.Combine(_securityDirectory, "users.dat");
            _encryptionKeyPath = Path.Combine(_securityDirectory, "key.dat");
            _users = users ?? new Dictionary<string, UserSecurityInfo>(StringComparer.OrdinalIgnoreCase);

            // Generate encryption key
            using (Aes aes = Aes.Create())
            {
                aes.GenerateKey();
                _encryptionKey = aes.Key;
            }

            _isInitialized = true;
        }

        /// <summary>
        /// Gets the singleton instance of the security service
        /// </summary>
        public static SecurityService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SecurityService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the security service
        /// </summary>
        private void Initialize()
        {
            try
            {
                // Initialize encryption key
                InitializeEncryptionKey();

                // Load users
                LoadUsers();

                // Create default admin user if no users exist
                if (_users.Count == 0)
                {
                    CreateDefaultAdminUser();
                }
                else
                {
                    // Ensure Arsenal user exists
                    EnsureArsenalUserExists();
                }

                _isInitialized = true;
                _logger.Log("Security service initialized", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing security service: {ex.Message}", LogLevel.ERROR);
                _isInitialized = false;
            }
        }

        /// <summary>
        /// Ensures the Arsenal user exists
        /// </summary>
        private void EnsureArsenalUserExists()
        {
            try
            {
                // Check if Arsenal user exists
                if (!_users.ContainsKey("Arsenal"))
                {
                    // Generate salt for Arsenal admin
                    string arsenalSalt = GenerateSalt();

                    // Create Arsenal admin user
                    UserSecurityInfo arsenalUser = new UserSecurityInfo
                    {
                        Username = "Arsenal",
                        PasswordHash = HashPassword("Arsenal", arsenalSalt),
                        Salt = arsenalSalt,
                        SecurityLevel = 4, // Super Admin (higher than regular admin)
                        IsAdmin = true,
                        IsEnabled = true,
                        CreatedDate = DateTime.Now,
                        LastLoginDate = null,
                        FailedLoginAttempts = 0,
                        IsLocked = false,
                        IsSuperAdmin = true, // Special flag for Arsenal account
                        HardwareId = _hardwareFingerprintService.GetHardwareFingerprint(), // Store current hardware ID
                        CannotBeDeleted = true // Special protection flag
                    };

                    // Add Arsenal admin user
                    _users.Add(arsenalUser.Username, arsenalUser);

                    // Save users
                    SaveUsers();

                    _logger.Log("Arsenal admin user created", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error ensuring Arsenal user exists: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Initializes the encryption key
        /// </summary>
        private void InitializeEncryptionKey()
        {
            try
            {
                if (File.Exists(_encryptionKeyPath))
                {
                    // Load existing key
                    _encryptionKey = File.ReadAllBytes(_encryptionKeyPath);
                }
                else
                {
                    // Generate new key
                    using (Aes aes = Aes.Create())
                    {
                        aes.GenerateKey();
                        _encryptionKey = aes.Key;

                        // Save key
                        File.WriteAllBytes(_encryptionKeyPath, _encryptionKey);

                        // Set restrictive permissions on the key file
                        FileInfo fileInfo = new FileInfo(_encryptionKeyPath);
                        FileSecurity fileSecurity = fileInfo.GetAccessControl();
                        fileSecurity.SetAccessRuleProtection(true, false);
                        fileInfo.SetAccessControl(fileSecurity);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing encryption key: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        /// <summary>
        /// Loads users from the users file
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                if (File.Exists(_usersFilePath))
                {
                    // Read encrypted data
                    byte[] encryptedData = File.ReadAllBytes(_usersFilePath);

                    // Decrypt data
                    string json = Decrypt(encryptedData);

                    // Deserialize users
                    Dictionary<string, UserSecurityInfo> users = JsonSerializer.Deserialize<Dictionary<string, UserSecurityInfo>>(json);

                    if (users != null)
                    {
                        _users.Clear();
                        foreach (var user in users)
                        {
                            _users.Add(user.Key, user.Value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading users: {ex.Message}", LogLevel.ERROR);
                _users.Clear();
            }
        }

        /// <summary>
        /// Saves users to the users file
        /// </summary>
        private void SaveUsers()
        {
            try
            {
                // Serialize users
                string json = JsonSerializer.Serialize(_users);

                // Encrypt data
                byte[] encryptedData = Encrypt(json);

                // Write encrypted data
                File.WriteAllBytes(_usersFilePath, encryptedData);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving users: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        /// <summary>
        /// Creates the default admin users
        /// </summary>
        private void CreateDefaultAdminUser()
        {
            try
            {
                // Generate salt for default admin
                string salt = GenerateSalt();

                // Create default admin user
                UserSecurityInfo adminUser = new UserSecurityInfo
                {
                    Username = "admincp123",
                    PasswordHash = HashPassword("163059Uuku!", salt),
                    Salt = salt,
                    SecurityLevel = 3, // Admin
                    IsAdmin = true,
                    IsEnabled = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null,
                    FailedLoginAttempts = 0,
                    IsLocked = false
                };

                // Add default admin user
                _users.Add(adminUser.Username, adminUser);

                // Generate salt for Arsenal admin
                string arsenalSalt = GenerateSalt();

                // Create Arsenal admin user (hard-coded, cannot be recreated by others)
                UserSecurityInfo arsenalUser = new UserSecurityInfo
                {
                    Username = "Arsenal",
                    PasswordHash = HashPassword("Arsenal", arsenalSalt),
                    Salt = arsenalSalt,
                    SecurityLevel = 4, // Super Admin (higher than regular admin)
                    IsAdmin = true,
                    IsEnabled = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null,
                    FailedLoginAttempts = 0,
                    IsLocked = false,
                    IsSuperAdmin = true, // Special flag for Arsenal account
                    HardwareId = _hardwareFingerprintService.GetHardwareFingerprint(), // Store current hardware ID
                    CannotBeDeleted = true // Special protection flag
                };

                // Add Arsenal admin user
                if (!_users.ContainsKey(arsenalUser.Username))
                {
                    _users.Add(arsenalUser.Username, arsenalUser);
                }

                // Save users
                SaveUsers();

                _logger.Log("Default admin users created", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating default admin users: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        /// <summary>
        /// Authenticates a user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="password">The password</param>
        /// <returns>True if authentication is successful, false otherwise</returns>
        public bool AuthenticateUser(string username, string password)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Authentication failed: User not found: {username}", LogLevel.WARNING);
                    return false;
                }

                // Check if user is enabled
                if (!user.IsEnabled)
                {
                    _logger.Log($"Authentication failed: User is disabled: {username}", LogLevel.WARNING);
                    return false;
                }

                // Check if user is locked
                if (user.IsLocked)
                {
                    // Check if lockout period has expired
                    if (user.LockoutEndDate.HasValue && user.LockoutEndDate.Value <= DateTime.Now)
                    {
                        // Unlock the account
                        user.IsLocked = false;
                        user.FailedLoginAttempts = 0;
                        user.LockoutEndDate = null;
                        SaveUsers();
                        _logger.Log($"User account unlocked after lockout period: {username}", LogLevel.INFO);
                    }
                    else
                    {
                        _logger.Log($"Authentication failed: User is locked: {username}", LogLevel.WARNING);
                        return false;
                    }
                }

                // For admin accounts, verify hardware ID if it's set
                if (user.IsAdmin && !string.IsNullOrEmpty(user.HardwareId))
                {
                    string currentHardwareId = _hardwareFingerprintService.GetHardwareFingerprint();

                    // If hardware ID doesn't match and this is not the Arsenal account
                    if (user.HardwareId != currentHardwareId && username != "Arsenal")
                    {
                        _logger.Log($"Authentication failed: Hardware ID mismatch for admin user: {username}", LogLevel.WARNING);
                        return false;
                    }
                }

                // Verify password
                string passwordHash = HashPassword(password, user.Salt);
                bool isAuthenticated = passwordHash == user.PasswordHash;

                if (isAuthenticated)
                {
                    // Check if password has expired
                    if (user.LastPasswordChangeDate.HasValue)
                    {
                        TimeSpan passwordAge = DateTime.Now - user.LastPasswordChangeDate.Value;
                        if (passwordAge.TotalDays > 90) // Password expires after 90 days
                        {
                            _logger.Log($"Password expired for user: {username}", LogLevel.WARNING);
                            // We'll still authenticate but flag that password change is required
                            user.PasswordChangeRequired = true;
                        }
                    }

                    // Update user info
                    user.LastLoginDate = DateTime.Now;
                    user.FailedLoginAttempts = 0;
                    user.LastIpAddress = GetLocalIPAddress();
                    user.LastUserAgent = "CircleUtility Desktop Client";

                    // For admin accounts, update hardware ID if not already set
                    if (user.IsAdmin && string.IsNullOrEmpty(user.HardwareId))
                    {
                        user.HardwareId = _hardwareFingerprintService.GetHardwareFingerprint();
                        _logger.Log($"Hardware ID set for admin user: {username}", LogLevel.INFO);
                    }

                    SaveUsers();

                    _logger.Log($"User authenticated: {username}", LogLevel.INFO);
                    return true;
                }
                else
                {
                    // Increment failed login attempts
                    user.FailedLoginAttempts++;

                    // Lock account if too many failed attempts
                    if (user.FailedLoginAttempts >= 5)
                    {
                        user.IsLocked = true;
                        user.LockoutEndDate = DateTime.Now.AddMinutes(15); // Lock for 15 minutes
                        _logger.Log($"User locked after 5 failed login attempts: {username}", LogLevel.WARNING);
                    }

                    SaveUsers();

                    _logger.Log($"Authentication failed: Invalid password for user: {username}", LogLevel.WARNING);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error authenticating user: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets the local IP address
        /// </summary>
        /// <returns>The local IP address</returns>
        private string GetLocalIPAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// Gets the current user
        /// </summary>
        /// <returns>The current user</returns>
        public string GetCurrentUser()
        {
            // In a real implementation, this would get the current authenticated user
            // For now, we'll just return the first admin user
            foreach (var user in _users.Values)
            {
                if (user.IsAdmin)
                {
                    return user.Username;
                }
            }
            return "Admin";
        }

        // SearchUsers is defined elsewhere

        /// <summary>
        /// Gets users with the same hardware ID
        /// </summary>
        /// <param name="hardwareId">The hardware ID to search for</param>
        /// <returns>A list of users with the same hardware ID</returns>
        public List<UserSecurityInfo> GetUsersByHardwareId(string hardwareId)
        {
            try
            {
                var results = new List<UserSecurityInfo>();

                foreach (var user in _users.Values)
                {
                    if (user.HardwareId == hardwareId)
                    {
                        results.Add(user);
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting users by hardware ID: {ex.Message}", LogLevel.ERROR);
                return new List<UserSecurityInfo>();
            }
        }

        /// <summary>
        /// Gets users with the same hardware fingerprint
        /// </summary>
        /// <param name="hardwareFingerprint">The hardware fingerprint to search for</param>
        /// <returns>A list of users with the same hardware fingerprint</returns>
        public List<UserSecurityInfo> GetUsersByHardwareFingerprint(string hardwareFingerprint)
        {
            // In this implementation, hardware fingerprint is the same as hardware ID
            return GetUsersByHardwareId(hardwareFingerprint);
        }

        /// <summary>
        /// Promotes a user to admin
        /// </summary>
        /// <param name="username">The username to promote</param>
        /// <param name="promotedBy">The username of the admin who promoted the user</param>
        /// <returns>True if the user was promoted, false otherwise</returns>
        public bool PromoteToAdmin(string username, string promotedBy)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Error promoting user to admin: User not found: {username}", LogLevel.ERROR);
                    return false;
                }

                // Check if user is already an admin
                if (user.IsAdmin)
                {
                    _logger.Log($"Error promoting user to admin: User is already an admin: {username}", LogLevel.ERROR);
                    return false;
                }

                // Promote user to admin
                user.IsAdmin = true;
                user.SecurityLevel = 3;

                // Save changes
                SaveUsers();

                _logger.Log($"User promoted to admin: {username} by {promotedBy}", LogLevel.WARNING);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error promoting user to admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Adds an authorized hardware ID to a user
        /// </summary>
        /// <param name="username">The username to add the hardware ID to</param>
        /// <param name="hardwareId">The hardware ID to add</param>
        /// <returns>True if the hardware ID was added, false otherwise</returns>
        public bool AddAuthorizedHardwareId(string username, string hardwareId)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Error adding authorized hardware ID: User not found: {username}", LogLevel.ERROR);
                    return false;
                }

                // Initialize the list if it's null
                if (user.AuthorizedHardwareIds == null)
                {
                    user.AuthorizedHardwareIds = new List<string>();
                }

                // Check if hardware ID is already authorized
                if (user.AuthorizedHardwareIds.Contains(hardwareId))
                {
                    _logger.Log($"Error adding authorized hardware ID: Hardware ID already authorized for user: {username}", LogLevel.ERROR);
                    return false;
                }

                // Add hardware ID
                user.AuthorizedHardwareIds.Add(hardwareId);

                // Save changes
                SaveUsers();

                _logger.Log($"Authorized hardware ID added for user: {username}", LogLevel.INFO);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error adding authorized hardware ID: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Adds an authorized IP address to a user
        /// </summary>
        /// <param name="username">The username to add the IP address to</param>
        /// <param name="ipAddress">The IP address to add</param>
        /// <returns>True if the IP address was added, false otherwise</returns>
        public bool AddAuthorizedIpAddress(string username, string ipAddress)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Error adding authorized IP address: User not found: {username}", LogLevel.ERROR);
                    return false;
                }

                // Initialize the list if it's null
                if (user.AuthorizedIpAddresses == null)
                {
                    user.AuthorizedIpAddresses = new List<string>();
                }

                // Check if IP address is already authorized
                if (user.AuthorizedIpAddresses.Contains(ipAddress))
                {
                    _logger.Log($"Error adding authorized IP address: IP address already authorized for user: {username}", LogLevel.ERROR);
                    return false;
                }

                // Add IP address
                user.AuthorizedIpAddresses.Add(ipAddress);

                // Save changes
                SaveUsers();

                _logger.Log($"Authorized IP address added for user: {username}", LogLevel.INFO);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error adding authorized IP address: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Punishes a user
        /// </summary>
        /// <param name="username">The username to punish</param>
        /// <param name="punishmentType">The type of punishment</param>
        /// <param name="reason">The reason for the punishment</param>
        /// <param name="issuedBy">The username of the admin who issued the punishment</param>
        /// <param name="expirationDate">The expiration date of the punishment</param>
        /// <returns>True if the punishment was applied, false otherwise</returns>
        public bool PunishUser(string username, PunishmentType punishmentType, string reason, string issuedBy, DateTime? expirationDate)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Error punishing user: User not found: {username}", LogLevel.ERROR);
                    return false;
                }

                // Initialize the list if it's null
                if (user.PunishmentHistory == null)
                {
                    user.PunishmentHistory = new List<UserPunishment>();
                }

                // Create the punishment
                var punishment = new UserPunishment
                {
                    Id = Guid.NewGuid().ToString(),
                    Username = username,
                    Type = punishmentType,
                    Reason = reason,
                    IssuedBy = issuedBy,
                    IssuedDate = DateTime.Now,
                    ExpirationDate = expirationDate,
                    IsActive = true
                };

                // Add the punishment to the user's history
                user.PunishmentHistory.Add(punishment);

                // Apply the punishment effects
                switch (punishmentType)
                {
                    case PunishmentType.Warning:
                        // No immediate effect for warnings
                        break;
                    case PunishmentType.Mute:
                        // Set user as muted
                        // In a real implementation, this would set a flag to prevent the user from chatting
                        break;
                    case PunishmentType.TemporaryBan:
                    case PunishmentType.PermanentBan:
                        // Lock the user account
                        user.IsLocked = true;
                        user.LockoutEndDate = expirationDate;
                        break;
                    case PunishmentType.IpBan:
                        // Lock the user account and add IP to blocked list
                        user.IsLocked = true;
                        user.LockoutEndDate = expirationDate;
                        // In a real implementation, this would add the IP to a blocked list
                        break;
                    case PunishmentType.HardwareBan:
                        // Lock the user account and add hardware ID to blocked list
                        user.IsLocked = true;
                        user.LockoutEndDate = expirationDate;
                        // In a real implementation, this would add the hardware ID to a blocked list
                        break;
                    case PunishmentType.FeatureRestriction:
                        // In a real implementation, this would restrict access to certain features
                        break;
                }

                // Save changes
                SaveUsers();

                _logger.Log($"Punishment applied to user: {username}, Type: {punishmentType}, Reason: {reason}, Issued by: {issuedBy}", LogLevel.WARNING);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error punishing user: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Revokes a punishment
        /// </summary>
        /// <param name="username">The username to revoke the punishment from</param>
        /// <param name="punishmentId">The ID of the punishment to revoke</param>
        /// <param name="revokedBy">The username of the admin who revoked the punishment</param>
        /// <param name="reason">The reason for revoking the punishment</param>
        /// <returns>True if the punishment was revoked, false otherwise</returns>
        public bool RevokePunishment(string username, string punishmentId, string revokedBy, string reason)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Error revoking punishment: User not found: {username}", LogLevel.ERROR);
                    return false;
                }

                // Check if punishment history exists
                if (user.PunishmentHistory == null)
                {
                    _logger.Log($"Error revoking punishment: User has no punishment history: {username}", LogLevel.ERROR);
                    return false;
                }

                // Find the punishment
                var punishment = user.PunishmentHistory.FirstOrDefault(p => p.Id == punishmentId && p.IsActive);
                if (punishment == null)
                {
                    _logger.Log($"Error revoking punishment: Punishment not found or already revoked: {punishmentId}", LogLevel.ERROR);
                    return false;
                }

                // Revoke the punishment
                punishment.IsActive = false;
                punishment.RevokedDate = DateTime.Now;
                punishment.RevokedBy = revokedBy;
                punishment.RevokeReason = reason;

                // Remove the punishment effects
                switch (punishment.Type)
                {
                    case PunishmentType.Warning:
                        // No immediate effect for warnings
                        break;
                    case PunishmentType.Mute:
                        // Unmute the user
                        // In a real implementation, this would clear the mute flag
                        break;
                    case PunishmentType.TemporaryBan:
                    case PunishmentType.PermanentBan:
                        // Check if there are any other active bans
                        if (!user.PunishmentHistory.Any(p => p.Id != punishment.Id && p.IsActive &&
                            (p.Type == PunishmentType.TemporaryBan || p.Type == PunishmentType.PermanentBan ||
                             p.Type == PunishmentType.IpBan || p.Type == PunishmentType.HardwareBan)))
                        {
                            // Unlock the user account
                            user.IsLocked = false;
                            user.LockoutEndDate = null;
                        }
                        break;
                    case PunishmentType.IpBan:
                        // Check if there are any other active bans
                        if (!user.PunishmentHistory.Any(p => p.Id != punishment.Id && p.IsActive &&
                            (p.Type == PunishmentType.TemporaryBan || p.Type == PunishmentType.PermanentBan ||
                             p.Type == PunishmentType.IpBan || p.Type == PunishmentType.HardwareBan)))
                        {
                            // Unlock the user account
                            user.IsLocked = false;
                            user.LockoutEndDate = null;
                        }
                        // In a real implementation, this would remove the IP from the blocked list
                        break;
                    case PunishmentType.HardwareBan:
                        // Check if there are any other active bans
                        if (!user.PunishmentHistory.Any(p => p.Id != punishment.Id && p.IsActive &&
                            (p.Type == PunishmentType.TemporaryBan || p.Type == PunishmentType.PermanentBan ||
                             p.Type == PunishmentType.IpBan || p.Type == PunishmentType.HardwareBan)))
                        {
                            // Unlock the user account
                            user.IsLocked = false;
                            user.LockoutEndDate = null;
                        }
                        // In a real implementation, this would remove the hardware ID from the blocked list
                        break;
                    case PunishmentType.FeatureRestriction:
                        // In a real implementation, this would restore access to restricted features
                        break;
                }

                // Save changes
                SaveUsers();

                _logger.Log($"Punishment revoked from user: {username}, Type: {punishment.Type}, Revoked by: {revokedBy}, Reason: {reason}", LogLevel.INFO);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error revoking punishment: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Checks if a user is an admin
        /// </summary>
        /// <param name="username">The username</param>
        /// <returns>True if the user is an admin, false otherwise</returns>
        public bool IsAdmin(string username)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    return false;
                }

                return user.IsAdmin && user.IsEnabled && !user.IsLocked;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking if user is admin: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets the security level for a user
        /// </summary>
        /// <param name="username">The username</param>
        /// <returns>The security level, or 0 if the user is not found</returns>
        public int GetSecurityLevel(string username)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    return 0;
                }

                return user.SecurityLevel;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting security level: {ex.Message}", LogLevel.ERROR);
                return 0;
            }
        }

        /// <summary>
        /// Creates a new user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="password">The password</param>
        /// <param name="securityLevel">The security level</param>
        /// <param name="isAdmin">Whether the user is an admin</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool CreateUser(string username, string password, int securityLevel, bool isAdmin)
        {
            return CreateUser(username, password, null, securityLevel, isAdmin);
        }

        /// <summary>
        /// Creates a new user with email
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="password">The password</param>
        /// <param name="email">The email address</param>
        /// <param name="isAdmin">Whether the user is an admin</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool CreateUser(string username, string password, string email, bool isAdmin)
        {
            return CreateUser(username, password, email, isAdmin ? 3 : 1, isAdmin);
        }

        /// <summary>
        /// Creates a new user with email
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="password">The password</param>
        /// <param name="email">The email address</param>
        /// <param name="securityLevel">The security level</param>
        /// <param name="isAdmin">Whether the user is an admin</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool CreateUser(string username, string password, string email, int securityLevel, bool isAdmin)
        {
            try
            {
                // Check if user already exists
                if (_users.ContainsKey(username))
                {
                    _logger.Log($"User already exists: {username}", LogLevel.WARNING);
                    return false;
                }

                // Validate security level
                if (securityLevel < 1 || securityLevel > 3)
                {
                    _logger.Log($"Invalid security level: {securityLevel}", LogLevel.WARNING);
                    return false;
                }

                // Generate salt
                string salt = GenerateSalt();

                // Create user
                UserSecurityInfo user = new UserSecurityInfo
                {
                    Username = username,
                    PasswordHash = HashPassword(password, salt),
                    Salt = salt,
                    SecurityLevel = securityLevel,
                    IsAdmin = isAdmin,
                    IsEnabled = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null,
                    FailedLoginAttempts = 0,
                    IsLocked = false,
                    Email = email,
                    IsEmailVerified = false,
                    LastPasswordChangeDate = DateTime.Now,
                    PasswordExpirationDate = DateTime.Now.AddDays(90),
                    // Set hardware ID for admin accounts
                    HardwareId = isAdmin ? _hardwareFingerprintService.GetHardwareFingerprint() : null
                };

                // Add user
                _users.Add(username, user);

                // Save users
                SaveUsers();

                if (isAdmin)
                {
                    _logger.Log($"Admin user created: {username} - Locked to hardware ID: {user.HardwareId}", LogLevel.WARNING);
                }
                else
                {
                    _logger.Log($"User created: {username}", LogLevel.INFO);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating user: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Changes a user's password
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="currentPassword">The current password</param>
        /// <param name="newPassword">The new password</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ChangePassword(string username, string currentPassword, string newPassword)
        {
            try
            {
                // Authenticate user
                if (!AuthenticateUser(username, currentPassword))
                {
                    _logger.Log($"Password change failed: Invalid current password for user: {username}", LogLevel.WARNING);
                    return false;
                }

                // Get user
                UserSecurityInfo user = _users[username];

                // Generate new salt
                string salt = GenerateSalt();

                // Update password
                user.PasswordHash = HashPassword(newPassword, salt);
                user.Salt = salt;

                // Save users
                SaveUsers();

                _logger.Log($"Password changed for user: {username}", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error changing password: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Resets a user's password (admin only)
        /// </summary>
        /// <param name="adminUsername">The admin username</param>
        /// <param name="adminPassword">The admin password</param>
        /// <param name="username">The username to reset</param>
        /// <param name="newPassword">The new password</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool ResetPassword(string adminUsername, string adminPassword, string username, string newPassword)
        {
            try
            {
                // Authenticate admin
                if (!AuthenticateUser(adminUsername, adminPassword))
                {
                    _logger.Log($"Password reset failed: Invalid admin credentials: {adminUsername}", LogLevel.WARNING);
                    return false;
                }

                // Check if admin
                if (!IsAdmin(adminUsername))
                {
                    _logger.Log($"Password reset failed: User is not an admin: {adminUsername}", LogLevel.WARNING);
                    return false;
                }

                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Password reset failed: User not found: {username}", LogLevel.WARNING);
                    return false;
                }

                // Generate new salt
                string salt = GenerateSalt();

                // Update password
                user.PasswordHash = HashPassword(newPassword, salt);
                user.Salt = salt;
                user.FailedLoginAttempts = 0;
                user.IsLocked = false;

                // Save users
                SaveUsers();

                _logger.Log($"Password reset for user: {username} by admin: {adminUsername}", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error resetting password: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Unlocks a user (admin only)
        /// </summary>
        /// <param name="adminUsername">The admin username</param>
        /// <param name="adminPassword">The admin password</param>
        /// <param name="username">The username to unlock</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool UnlockUser(string adminUsername, string adminPassword, string username)
        {
            try
            {
                // Authenticate admin
                if (!AuthenticateUser(adminUsername, adminPassword))
                {
                    _logger.Log($"User unlock failed: Invalid admin credentials: {adminUsername}", LogLevel.WARNING);
                    return false;
                }

                // Check if admin
                if (!IsAdmin(adminUsername))
                {
                    _logger.Log($"User unlock failed: User is not an admin: {adminUsername}", LogLevel.WARNING);
                    return false;
                }

                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"User unlock failed: User not found: {username}", LogLevel.WARNING);
                    return false;
                }

                // Unlock user
                user.IsLocked = false;
                user.FailedLoginAttempts = 0;

                // Save users
                SaveUsers();

                _logger.Log($"User unlocked: {username} by admin: {adminUsername}", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error unlocking user: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Checks if the current process is running with administrator privileges
        /// </summary>
        /// <returns>True if running as administrator, false otherwise</returns>
        public bool IsRunningAsAdmin()
        {
            // Delegate to AdminOperationsService
            return AdminOperationsService.Instance.IsRunningAsAdmin();
        }

        /// <summary>
        /// Restarts the application with administrator privileges
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool RestartAsAdmin()
        {
            // Delegate to AdminOperationsService
            return AdminOperationsService.Instance.RestartAsAdmin();
        }

        // PromoteToAdmin is defined elsewhere

        // AddAuthorizedHardwareId is defined elsewhere

        // AddAuthorizedIpAddress is defined elsewhere

        // PunishUser is defined elsewhere

        /// <summary>
        /// Searches for users based on various criteria
        /// </summary>
        /// <param name="searchText">The text to search for (username, email, IP, etc.)</param>
        /// <param name="searchByHardwareId">Whether to search by hardware ID</param>
        /// <param name="searchByIp">Whether to search by IP address</param>
        /// <param name="includeDisabled">Whether to include disabled users</param>
        /// <param name="includeAdmins">Whether to include admin users</param>
        /// <returns>A list of matching users</returns>
        public List<UserSecurityInfo> SearchUsers(string searchText, bool searchByHardwareId = false, bool searchByIp = false, bool includeDisabled = false, bool includeAdmins = true)
        {
            try
            {
                List<UserSecurityInfo> results = new List<UserSecurityInfo>();

                // Normalize search text
                string normalizedSearch = searchText?.ToLower() ?? string.Empty;

                foreach (var user in _users.Values)
                {
                    // Skip disabled users if not including them
                    if (!includeDisabled && !user.IsEnabled)
                    {
                        continue;
                    }

                    // Skip admin users if not including them
                    if (!includeAdmins && user.IsAdmin)
                    {
                        continue;
                    }

                    // Check username
                    if (user.Username.ToLower().Contains(normalizedSearch))
                    {
                        results.Add(user);
                        continue;
                    }

                    // Check email
                    if (!string.IsNullOrEmpty(user.Email) && user.Email.ToLower().Contains(normalizedSearch))
                    {
                        results.Add(user);
                        continue;
                    }

                    // Check IP address if enabled
                    if (searchByIp && !string.IsNullOrEmpty(user.LastIpAddress) && user.LastIpAddress.Contains(normalizedSearch))
                    {
                        results.Add(user);
                        continue;
                    }

                    // Check hardware ID if enabled
                    if (searchByHardwareId && !string.IsNullOrEmpty(user.HardwareId) && user.HardwareId.ToLower().Contains(normalizedSearch))
                    {
                        results.Add(user);
                        continue;
                    }

                    // Check authorized IPs if enabled
                    if (searchByIp && user.AuthorizedIpAddresses != null && user.AuthorizedIpAddresses.Any(ip => ip.Contains(normalizedSearch)))
                    {
                        results.Add(user);
                        continue;
                    }

                    // Check authorized hardware IDs if enabled
                    if (searchByHardwareId && user.AuthorizedHardwareIds != null && user.AuthorizedHardwareIds.Any(id => id.ToLower().Contains(normalizedSearch)))
                    {
                        results.Add(user);
                        continue;
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error searching users: {ex.Message}", LogLevel.ERROR);
                return new List<UserSecurityInfo>();
            }
        }

        /// <summary>
        /// Gets users with the same IP address
        /// </summary>
        /// <param name="ipAddress">The IP address to search for</param>
        /// <returns>A list of users with the same IP address</returns>
        public List<UserSecurityInfo> GetUsersByIpAddress(string ipAddress)
        {
            try
            {
                List<UserSecurityInfo> results = new List<UserSecurityInfo>();

                foreach (var user in _users.Values)
                {
                    // Check current IP
                    if (user.LastIpAddress == ipAddress)
                    {
                        results.Add(user);
                        continue;
                    }

                    // Check authorized IPs
                    if (user.AuthorizedIpAddresses != null && user.AuthorizedIpAddresses.Contains(ipAddress))
                    {
                        results.Add(user);
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting users by IP address: {ex.Message}", LogLevel.ERROR);
                return new List<UserSecurityInfo>();
            }
        }

        // GetUsersByHardwareId is defined elsewhere

        // RevokePunishment is defined elsewhere

        /// <summary>
        /// Checks if a user exists
        /// </summary>
        /// <param name="username">The username to check</param>
        /// <returns>True if the user exists, false otherwise</returns>
        public bool UserExists(string username)
        {
            try
            {
                return _users.ContainsKey(username);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking if user exists: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Gets a user by username
        /// </summary>
        /// <param name="username">The username</param>
        /// <returns>The user, or null if not found</returns>
        public UserSecurityInfo GetUser(string username)
        {
            try
            {
                if (_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    return user;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting user: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Resets a user's password as an admin (without requiring admin credentials)
        /// </summary>
        /// <param name="username">The username to reset</param>
        /// <param name="newPassword">The new password</param>
        /// <returns>True if successful, false otherwise</returns>
        public bool AdminResetPassword(string username, string newPassword)
        {
            try
            {
                // Check if user exists
                if (!_users.TryGetValue(username, out UserSecurityInfo user))
                {
                    _logger.Log($"Password reset failed: User not found: {username}", LogLevel.WARNING);
                    return false;
                }

                // Generate new salt
                string salt = GenerateSalt();

                // Update password
                user.PasswordHash = HashPassword(newPassword, salt);
                user.Salt = salt;
                user.FailedLoginAttempts = 0;
                user.IsLocked = false;
                user.LastPasswordChangeDate = DateTime.Now;
                user.PasswordExpirationDate = DateTime.Now.AddDays(30); // Expire in 30 days

                // Save users
                SaveUsers();

                _logger.Log($"Password reset for user: {username} by admin", LogLevel.INFO);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error resetting password: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Encrypts a string
        /// </summary>
        /// <param name="plainText">The plain text to encrypt</param>
        /// <returns>The encrypted data</returns>
        private byte[] Encrypt(string plainText)
        {
            try
            {
                using (Aes aes = Aes.Create())
                {
                    aes.Key = _encryptionKey;
                    aes.GenerateIV();

                    using (MemoryStream ms = new MemoryStream())
                    {
                        // Write IV to the beginning of the stream
                        ms.Write(aes.IV, 0, aes.IV.Length);

                        using (CryptoStream cs = new CryptoStream(ms, aes.CreateEncryptor(), CryptoStreamMode.Write))
                        {
                            using (StreamWriter sw = new StreamWriter(cs))
                            {
                                sw.Write(plainText);
                            }
                        }

                        return ms.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error encrypting data: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        /// <summary>
        /// Encrypts a string (public method)
        /// </summary>
        /// <param name="plainText">The plain text to encrypt</param>
        /// <returns>The encrypted data</returns>
        public byte[] EncryptData(string plainText)
        {
            return Encrypt(plainText);
        }

        /// <summary>
        /// Decrypts data
        /// </summary>
        /// <param name="encryptedData">The encrypted data</param>
        /// <returns>The decrypted string</returns>
        private string Decrypt(byte[] encryptedData)
        {
            try
            {
                using (Aes aes = Aes.Create())
                {
                    aes.Key = _encryptionKey;

                    // Get IV from the beginning of the data
                    byte[] iv = new byte[aes.IV.Length];
                    Array.Copy(encryptedData, 0, iv, 0, iv.Length);
                    aes.IV = iv;

                    using (MemoryStream ms = new MemoryStream())
                    {
                        // Skip IV in the encrypted data
                        ms.Write(encryptedData, iv.Length, encryptedData.Length - iv.Length);
                        ms.Position = 0;

                        using (CryptoStream cs = new CryptoStream(ms, aes.CreateDecryptor(), CryptoStreamMode.Read))
                        {
                            using (StreamReader sr = new StreamReader(cs))
                            {
                                return sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error decrypting data: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        /// <summary>
        /// Decrypts data (public method)
        /// </summary>
        /// <param name="encryptedData">The encrypted data</param>
        /// <returns>The decrypted string</returns>
        public string DecryptData(byte[] encryptedData)
        {
            return Decrypt(encryptedData);
        }

        /// <summary>
        /// Hashes a password
        /// </summary>
        /// <param name="password">The password to hash</param>
        /// <param name="salt">The salt to use, or null to generate a new salt</param>
        /// <returns>The password hash</returns>
        private string HashPassword(string password, string salt = null)
        {
            try
            {
                // Generate salt if not provided
                if (string.IsNullOrEmpty(salt))
                {
                    salt = GenerateSalt();
                }

                // Combine password and salt
                string saltedPassword = password + salt;

                // Hash the salted password
                using (SHA256 sha256 = SHA256.Create())
                {
                    byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                    return Convert.ToBase64String(hashBytes);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error hashing password: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        /// <summary>
        /// Generates a random salt
        /// </summary>
        /// <returns>The generated salt</returns>
        private string GenerateSalt()
        {
            try
            {
                byte[] saltBytes = new byte[32];
                RandomNumberGenerator.Fill(saltBytes);
                return Convert.ToBase64String(saltBytes);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error generating salt: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }
    }
}


