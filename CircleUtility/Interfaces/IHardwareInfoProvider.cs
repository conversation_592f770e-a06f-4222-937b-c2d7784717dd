using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for providing hardware information
    /// Used to break circular dependencies between hardware services
    /// </summary>
    public interface IHardwareInfoProvider
    {
        /// <summary>
        /// Gets the current hardware information
        /// </summary>
        /// <returns>The hardware information</returns>
        HardwareInfo GetHardwareInfo();

        /// <summary>
        /// Gets the current hardware information asynchronously
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the hardware information</param>
        /// <returns>The hardware information</returns>
        Task<HardwareInfo> GetHardwareInfoAsync(bool forceRefresh = false);

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }
    }
}
