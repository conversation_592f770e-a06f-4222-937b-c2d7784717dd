using CircleUtility.Interfaces;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for input validation
    /// </summary>
    public class InputValidationService : IInputValidationService
    {
        private static InputValidationService _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of the input validation service
        /// </summary>
        public static InputValidationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new InputValidationService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private InputValidationService()
        {
            // Initialize input validation service
        }

        /// <summary>
        /// Validates input data
        /// </summary>
        /// <param name="input">The input to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public bool ValidateInput(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;

            // Basic validation - can be extended
            return input.Length <= 1000 && !input.Contains("<script>");
        }

        /// <summary>
        /// Sanitizes input data
        /// </summary>
        /// <param name="input">The input to sanitize</param>
        /// <returns>The sanitized input</returns>
        public string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Basic sanitization - remove potentially dangerous content
            return input.Replace("<script>", "").Replace("</script>", "").Trim();
        }
    }
}
