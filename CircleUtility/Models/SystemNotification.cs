using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace CircleUtility.Models
{
    public enum NotificationSeverity
    {
        Info,
        Success,
        Warning,
        Error,
        Critical
    }

    public class SystemNotification : INotifyPropertyChanged
    {
        private string _title;
        private string _category;
        private string _message;
        private DateTime _timestamp;
        private bool _isRead;

        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Category
        {
            get => _category;
            set
            {
                if (_category != value)
                {
                    _category = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                if (_message != value)
                {
                    _message = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                if (_timestamp != value)
                {
                    _timestamp = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsRead
        {
            get => _isRead;
            set
            {
                if (_isRead != value)
                {
                    _isRead = value;
                    OnPropertyChanged();
                }
            }
        }

        // Read-only properties that are calculated or set once
        public string CategoryIcon { get; }
        public string RelativeTime { get; }

        // Properties that can be set internally but not publicly
        public NotificationSeverity Severity { get; internal set; }
        public ICommand Action { get; internal set; }
        public string ActionText { get; internal set; }
        public object ExpirationDate { get; internal set; }

        public void MarkAsRead()
        {
            IsRead = true;
        }

        public void MarkAsUnread()
        {
            IsRead = false;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}