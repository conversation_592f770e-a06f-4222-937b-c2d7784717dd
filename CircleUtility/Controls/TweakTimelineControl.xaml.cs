using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for TweakTimelineControl.xaml
    /// </summary>
    public partial class TweakTimelineControl : UserControl
    {
        private List<TweakChange> _allChanges;
        private TweakChangeTracker _changeTracker;

        /// <summary>
        /// Event raised when a revert button is clicked
        /// </summary>
        public event EventHandler<TweakChangeEventArgs> RevertRequested;

        /// <summary>
        /// Initializes a new instance of the TweakTimelineControl class
        /// </summary>
        public TweakTimelineControl()
        {
            InitializeComponent();
            _changeTracker = TweakChangeTracker.Instance;
            Loaded += TweakTimelineControl_Loaded;
        }

        /// <summary>
        /// Handles the control loaded event
        /// </summary>
        private void TweakTimelineControl_Loaded(object sender, RoutedEventArgs e)
        {
            RefreshTimeline();
        }

        /// <summary>
        /// Refreshes the timeline with the latest changes
        /// </summary>
        public void RefreshTimeline()
        {
            // Get all changes
            _allChanges = _changeTracker.GetActiveChanges();
            _allChanges.AddRange(_changeTracker.GetChangesByTimePeriod(DateTime.Now.AddDays(-30), DateTime.Now).Where(c => !c.IsActive));

            // Sort by applied date (newest first)
            _allChanges = _allChanges.OrderByDescending(c => c.AppliedDate).ToList();

            // Display all changes
            DisplayChanges(_allChanges);
        }

        /// <summary>
        /// Displays the specified changes in the timeline
        /// </summary>
        /// <param name="changes">The changes to display</param>
        private void DisplayChanges(List<TweakChange> changes)
        {
            // Clear existing items
            TimelinePanel.Children.Clear();

            // Add each change to the timeline
            for (int i = 0; i < changes.Count; i++)
            {
                AddTimelineItem(changes[i], i);
            }
        }

        /// <summary>
        /// Adds a timeline item for the specified change
        /// </summary>
        /// <param name="change">The change to add</param>
        /// <param name="index">The index of the change</param>
        private void AddTimelineItem(TweakChange change, int index)
        {
            // Create border
            Border border = new Border
            {
                Style = (Style)FindResource("TimelineItemStyle"),
                Opacity = 0
            };

            // Set border color based on status
            if (!change.IsActive)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(128, 128, 128));
            }
            else if (change.TweakType == OptimizationType.SystemTweak)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(0, 200, 83));
            }
            else if (change.TweakType == OptimizationType.NetworkTweak)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(255, 152, 0));
            }
            else if (change.TweakType == OptimizationType.RegistryTweak)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(156, 39, 176));
            }

            // Create grid
            Grid grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Create title text block
            TextBlock titleBlock = new TextBlock
            {
                Text = change.TweakName,
                Style = (Style)FindResource("TimelineItemTitleStyle")
            };
            Grid.SetRow(titleBlock, 0);
            grid.Children.Add(titleBlock);

            // Create description text block
            TextBlock descriptionBlock = new TextBlock
            {
                Text = change.Description,
                Style = (Style)FindResource("TimelineItemDescriptionStyle")
            };
            Grid.SetRow(descriptionBlock, 1);
            grid.Children.Add(descriptionBlock);

            // Create date text block
            TextBlock dateBlock = new TextBlock
            {
                Text = $"Applied: {change.FormattedAppliedDate}" + (change.RevertedDate.HasValue ? $" | Reverted: {change.FormattedRevertedDate}" : ""),
                Style = (Style)FindResource("TimelineItemDateStyle")
            };
            Grid.SetRow(dateBlock, 2);
            grid.Children.Add(dateBlock);

            // Create button panel
            StackPanel buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 10, 0, 0)
            };
            Grid.SetRow(buttonPanel, 3);
            grid.Children.Add(buttonPanel);

            // Add revert button if the change is active
            if (change.IsActive)
            {
                Button revertButton = new Button
                {
                    Content = "REVERT",
                    Style = (Style)FindResource("TimelineItemButtonStyle"),
                    Tag = change
                };
                revertButton.Click += RevertButton_Click;
                buttonPanel.Children.Add(revertButton);
            }

            // Add details button
            Button detailsButton = new Button
            {
                Content = "DETAILS",
                Style = (Style)FindResource("TimelineItemButtonStyle"),
                Tag = change
            };
            detailsButton.Click += DetailsButton_Click;
            buttonPanel.Children.Add(detailsButton);

            // Add grid to border
            border.Child = grid;

            // Add border to panel
            TimelinePanel.Children.Add(border);

            // Animate the border
            DoubleAnimation fadeIn = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(300),
                BeginTime = TimeSpan.FromMilliseconds(index * 50)
            };
            border.BeginAnimation(UIElement.OpacityProperty, fadeIn);
        }

        /// <summary>
        /// Handles the revert button click
        /// </summary>
        private void RevertButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is TweakChange change)
            {
                // Raise the event
                RevertRequested?.Invoke(this, new TweakChangeEventArgs { Change = change });
            }
        }

        /// <summary>
        /// Handles the details button click
        /// </summary>
        private void DetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is TweakChange change)
            {
                // Show details dialog
                MessageBox.Show(
                    $"Tweak: {change.TweakName}\n" +
                    $"Type: {change.TweakType}\n" +
                    $"Applied: {change.FormattedAppliedDate}\n" +
                    $"Reverted: {change.FormattedRevertedDate}\n" +
                    $"Status: {(change.IsActive ? "Active" : "Reverted")}\n\n" +
                    $"Description: {change.Description}",
                    "Tweak Details",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// Handles the all filter button click
        /// </summary>
        private void AllFilter_Click(object sender, RoutedEventArgs e)
        {
            DisplayChanges(_allChanges);
        }

        /// <summary>
        /// Handles the today filter button click
        /// </summary>
        private void TodayFilter_Click(object sender, RoutedEventArgs e)
        {
            var today = DateTime.Today;
            var todayChanges = _allChanges.Where(c => c.AppliedDate.Date == today).ToList();
            DisplayChanges(todayChanges);
        }

        /// <summary>
        /// Handles the this week filter button click
        /// </summary>
        private void ThisWeekFilter_Click(object sender, RoutedEventArgs e)
        {
            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            var thisWeekChanges = _allChanges.Where(c => c.AppliedDate >= startOfWeek).ToList();
            DisplayChanges(thisWeekChanges);
        }

        /// <summary>
        /// Handles the active filter button click
        /// </summary>
        private void ActiveFilter_Click(object sender, RoutedEventArgs e)
        {
            var activeChanges = _allChanges.Where(c => c.IsActive).ToList();
            DisplayChanges(activeChanges);
        }

        /// <summary>
        /// Handles the reverted filter button click
        /// </summary>
        private void RevertedFilter_Click(object sender, RoutedEventArgs e)
        {
            var revertedChanges = _allChanges.Where(c => !c.IsActive).ToList();
            DisplayChanges(revertedChanges);
        }
    }
}
