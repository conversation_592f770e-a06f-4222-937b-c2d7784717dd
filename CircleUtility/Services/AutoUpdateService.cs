// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Windows;
using System.Reflection;
using System.ComponentModel;
using System.IO.Compression;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for handling automatic updates
    /// </summary>
    public class AutoUpdateService
    {
        private static AutoUpdateService _instance;
        private readonly LoggingService _logger;
        private readonly ConfigurationService _configService;
        private readonly string _updateServerUrl;
        private readonly string _applicationPath;
        private readonly string _applicationDirectory;
        private readonly string _updateDirectory;
        private readonly string _backupDirectory;
        private readonly string _versionFile;
        private readonly string _updateFile;
        private bool _isCheckingForUpdates;
        private bool _isUpdating;

        /// <summary>
        /// Gets the singleton instance of the AutoUpdateService
        /// </summary>
        public static AutoUpdateService Instance => _instance ?? (_instance = new AutoUpdateService());

        /// <summary>
        /// Initializes a new instance of the AutoUpdateService class
        /// </summary>
        private AutoUpdateService()
        {
            _logger = LoggingService.Instance;
            _configService = ConfigurationService.Instance;

            // Get the update server URL from configuration
            _updateServerUrl = _configService.GetSetting("UpdateServerUrl", "https://updates.circleutility.com");

            // Get application paths
            _applicationPath = Assembly.GetExecutingAssembly().Location;
            _applicationDirectory = Path.GetDirectoryName(_applicationPath);
            _updateDirectory = Path.Combine(_applicationDirectory, "Updates");
            _backupDirectory = Path.Combine(_applicationDirectory, "Backup");
            _versionFile = Path.Combine(_updateDirectory, "version.json");
            _updateFile = Path.Combine(_updateDirectory, "update.zip");

            // Create directories if they don't exist
            Directory.CreateDirectory(_updateDirectory);
            Directory.CreateDirectory(_backupDirectory);

            _isCheckingForUpdates = false;
            _isUpdating = false;

            _logger.Log("AutoUpdateService initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Checks for updates
        /// </summary>
        /// <returns>True if an update is available, false otherwise</returns>
        public async Task<bool> CheckForUpdatesAsync()
        {
            if (_isCheckingForUpdates)
            {
                _logger.Log("Already checking for updates", LogLevel.WARNING);
                return false;
            }

            _isCheckingForUpdates = true;

            try
            {
                _logger.Log("Checking for updates...", LogLevel.INFO);

                // Get current version
                Version currentVersion = Assembly.GetExecutingAssembly().GetName().Version;

                // Get latest version from server
                using (HttpClient client = new HttpClient())
                {
                    string versionUrl = $"{_updateServerUrl}/version.json";
                    byte[] versionData = await client.GetByteArrayAsync(versionUrl);
                    await File.WriteAllBytesAsync(_versionFile, versionData);
                }

                // Parse version file
                string versionJson = File.ReadAllText(_versionFile);
                dynamic versionInfo = Newtonsoft.Json.JsonConvert.DeserializeObject(versionJson);
                Version latestVersion = new Version(versionInfo.version.ToString());

                // Compare versions
                if (latestVersion > currentVersion)
                {
                    _logger.Log($"Update available: {latestVersion} (current: {currentVersion})", LogLevel.INFO);
                    _isCheckingForUpdates = false;
                    return true;
                }
                else
                {
                    _logger.Log($"No updates available. Current version: {currentVersion}", LogLevel.INFO);
                    _isCheckingForUpdates = false;
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking for updates: {ex.Message}", LogLevel.ERROR);
                _isCheckingForUpdates = false;
                return false;
            }
        }

        /// <summary>
        /// Downloads and installs an update
        /// </summary>
        /// <returns>True if the update was successful, false otherwise</returns>
        public async Task<bool> UpdateAsync()
        {
            if (_isUpdating)
            {
                _logger.Log("Already updating", LogLevel.WARNING);
                return false;
            }

            _isUpdating = true;

            try
            {
                _logger.Log("Downloading update...", LogLevel.INFO);

                // Download update
                using (HttpClient client = new HttpClient())
                {
                    string updateUrl = $"{_updateServerUrl}/update.zip";
                    byte[] updateData = await client.GetByteArrayAsync(updateUrl);
                    await File.WriteAllBytesAsync(_updateFile, updateData);
                }

                _logger.Log("Update downloaded. Installing...", LogLevel.INFO);

                // Backup current application
                BackupApplication();

                // Extract update
                ZipFile.ExtractToDirectory(_updateFile, _applicationDirectory);

                _logger.Log("Update installed. Restarting application...", LogLevel.SUCCESS);

                // Restart application
                RestartApplication();

                _isUpdating = false;
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating: {ex.Message}", LogLevel.ERROR);
                _isUpdating = false;
                return false;
            }
        }

        /// <summary>
        /// Backs up the current application
        /// </summary>
        private void BackupApplication()
        {
            try
            {
                _logger.Log("Backing up application...", LogLevel.INFO);

                // Clear backup directory
                if (Directory.Exists(_backupDirectory))
                {
                    Directory.Delete(_backupDirectory, true);
                }

                Directory.CreateDirectory(_backupDirectory);

                // Copy all files to backup directory
                foreach (string file in Directory.GetFiles(_applicationDirectory, "*.*", SearchOption.AllDirectories))
                {
                    string relativePath = file.Substring(_applicationDirectory.Length + 1);
                    string backupPath = Path.Combine(_backupDirectory, relativePath);
                    string backupDir = Path.GetDirectoryName(backupPath);

                    if (!Directory.Exists(backupDir))
                    {
                        Directory.CreateDirectory(backupDir);
                    }

                    File.Copy(file, backupPath, true);
                }

                _logger.Log("Application backed up successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error backing up application: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }

        /// <summary>
        /// Restarts the application
        /// </summary>
        private void RestartApplication()
        {
            try
            {
                // Start a new instance of the application
                Process.Start(_applicationPath);

                // Exit the current instance
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restarting application: {ex.Message}", LogLevel.ERROR);
                throw;
            }
        }
    }
}
