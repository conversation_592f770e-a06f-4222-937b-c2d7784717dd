// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Windows;
using System.Windows.Controls;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for TweakView.xaml
    /// </summary>
    public partial class TweakView : UserControl
    {
        private readonly LoggingService _logger;
        private readonly IHardwareOptimizationService _optimizationService;
        private readonly IHardwareRecommendationService _recommendationService;

        /// <summary>
        /// Gets or sets the title
        /// </summary>
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        /// <summary>
        /// Dependency property for Title
        /// </summary>
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(TweakView), new PropertyMetadata(string.Empty));

        /// <summary>
        /// Gets or sets the description
        /// </summary>
        public string Description
        {
            get { return (string)GetValue(DescriptionProperty); }
            set { SetValue(DescriptionProperty, value); }
        }

        /// <summary>
        /// Dependency property for Description
        /// </summary>
        public static readonly DependencyProperty DescriptionProperty =
            DependencyProperty.Register("Description", typeof(string), typeof(TweakView), new PropertyMetadata(string.Empty));

        /// <summary>
        /// Gets or sets the impact text
        /// </summary>
        public string ImpactText
        {
            get { return (string)GetValue(ImpactTextProperty); }
            set { SetValue(ImpactTextProperty, value); }
        }

        /// <summary>
        /// Dependency property for ImpactText
        /// </summary>
        public static readonly DependencyProperty ImpactTextProperty =
            DependencyProperty.Register("ImpactText", typeof(string), typeof(TweakView), new PropertyMetadata(string.Empty));

        /// <summary>
        /// Gets or sets the optimization name
        /// </summary>
        public string OptimizationName
        {
            get { return (string)GetValue(OptimizationNameProperty); }
            set { SetValue(OptimizationNameProperty, value); }
        }

        /// <summary>
        /// Dependency property for OptimizationName
        /// </summary>
        public static readonly DependencyProperty OptimizationNameProperty =
            DependencyProperty.Register("OptimizationName", typeof(string), typeof(TweakView), 
                new PropertyMetadata(string.Empty, OnOptimizationNameChanged));

        private static void OnOptimizationNameChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TweakView tweakView && e.NewValue is string optimizationName)
            {
                tweakView.UpdateRecommendationBadge();
            }
        }

        /// <summary>
        /// Gets or sets the recommendation reason
        /// </summary>
        public string RecommendationReason
        {
            get { return (string)GetValue(RecommendationReasonProperty); }
            set { SetValue(RecommendationReasonProperty, value); }
        }

        /// <summary>
        /// Dependency property for RecommendationReason
        /// </summary>
        public static readonly DependencyProperty RecommendationReasonProperty =
            DependencyProperty.Register("RecommendationReason", typeof(string), typeof(TweakView), 
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// Gets or sets the recommendation score
        /// </summary>
        public int RecommendationScore
        {
            get { return (int)GetValue(RecommendationScoreProperty); }
            set { SetValue(RecommendationScoreProperty, value); }
        }

        /// <summary>
        /// Dependency property for RecommendationScore
        /// </summary>
        public static readonly DependencyProperty RecommendationScoreProperty =
            DependencyProperty.Register("RecommendationScore", typeof(int), typeof(TweakView), 
                new PropertyMetadata(0));

        /// <summary>
        /// Event for when the apply button is clicked
        /// </summary>
        public event EventHandler<OptimizationEventArgs> ApplyClicked;

        /// <summary>
        /// Event for when the revert button is clicked
        /// </summary>
        public event EventHandler<OptimizationEventArgs> RevertClicked;

        /// <summary>
        /// Initializes a new instance of the TweakView class
        /// </summary>
        public TweakView()
        {
            InitializeComponent();
            
            _logger = LoggingService.Instance;
            _optimizationService = HardwareOptimizationService.Instance;
            _recommendationService = HardwareRecommendationService.Instance;
            
            DataContext = this;
        }

        /// <summary>
        /// Updates the recommendation badge
        /// </summary>
        private async void UpdateRecommendationBadge()
        {
            try
            {
                if (string.IsNullOrEmpty(OptimizationName))
                {
                    RecommendationBadge.Visibility = Visibility.Collapsed;
                    return;
                }

                // Get the recommendation score
                int score = await _recommendationService.GetRecommendationScoreAsync(OptimizationName);
                
                // Only show the badge if the score is high enough
                if (score >= 80)
                {
                    RecommendationScore = score;
                    
                    // Get all recommendations
                    var recommendations = await _recommendationService.GetRecommendedOptimizationsAsync();
                    
                    // Find the recommendation for this optimization
                    var recommendation = recommendations.Find(r => r.Optimization.Name == OptimizationName);
                    
                    if (recommendation != null)
                    {
                        RecommendationReason = recommendation.RecommendationReason;
                    }
                    else
                    {
                        RecommendationReason = "Recommended for your hardware";
                    }
                    
                    RecommendationBadge.Visibility = Visibility.Visible;
                }
                else
                {
                    RecommendationBadge.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error updating recommendation badge: {ex.Message}", LogLevel.ERROR);
                RecommendationBadge.Visibility = Visibility.Collapsed;
            }
        }

        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyClicked?.Invoke(this, new OptimizationEventArgs { OptimizationName = OptimizationName });
        }

        private void RevertButton_Click(object sender, RoutedEventArgs e)
        {
            RevertClicked?.Invoke(this, new OptimizationEventArgs { OptimizationName = OptimizationName });
        }
    }

    /// <summary>
    /// Event arguments for optimization events
    /// </summary>
    public class OptimizationEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the optimization name
        /// </summary>
        public string OptimizationName { get; set; }
    }
}
