using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CircleUtility.Interfaces;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for hardware optimization
    /// </summary>
    public class HardwareOptimizationService : IHardwareOptimizationService
    {
        private static HardwareOptimizationService _instance;
        private static readonly object _lock = new object();
        private readonly List<HardwareSpecificOptimization> _optimizations;
        private readonly List<PowerManagementProfile> _powerProfiles;
        private readonly List<SmartTweak> _smartTweaks;

        /// <summary>
        /// Gets the singleton instance of the hardware optimization service
        /// </summary>
        public static HardwareOptimizationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new HardwareOptimizationService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private HardwareOptimizationService()
        {
            _optimizations = new List<HardwareSpecificOptimization>();
            _powerProfiles = new List<PowerManagementProfile>();
            _smartTweaks = new List<SmartTweak>();
            InitializeDefaultOptimizations();
        }

        /// <summary>
        /// Gets whether the service is initialized
        /// </summary>
        public bool IsInitialized => true;

        /// <summary>
        /// Gets the list of available optimizations
        /// </summary>
        public List<HardwareSpecificOptimization> Optimizations => _optimizations;

        /// <summary>
        /// Gets the list of available power profiles
        /// </summary>
        public List<PowerManagementProfile> PowerProfiles => _powerProfiles;

        /// <summary>
        /// Initializes the service asynchronously
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task InitializeAsync()
        {
            await Task.Delay(100);
        }

        /// <summary>
        /// Gets recommended smart tweaks asynchronously
        /// </summary>
        /// <returns>Task with recommended tweaks</returns>
        public async Task<IEnumerable<SmartTweak>> GetRecommendedSmartTweaksAsync()
        {
            await Task.Delay(100);
            return _smartTweaks.Where(t => t.Category == "Recommended");
        }

        /// <summary>
        /// Applies smart tweak asynchronously
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        /// <returns>Task representing the async operation</returns>
        public async Task<bool> ApplySmartTweakAsync(object tweak)
        {
            await Task.Delay(100);
            return true;
        }

        /// <summary>
        /// Reverts smart tweak asynchronously
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        /// <returns>Task representing the async operation</returns>
        public async Task<bool> RevertSmartTweakAsync(object tweak)
        {
            await Task.Delay(100);
            return true;
        }

        /// <summary>
        /// Gets optimizations for a specific category
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <param name="manufacturer">The manufacturer</param>
        /// <param name="filter">The filter (e.g., "Recommended", "All")</param>
        /// <returns>List of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForCategory(HardwareType hardwareType, string manufacturer, string filter)
        {
            return _optimizations
                .Where(o => o.HardwareType == hardwareType)
                .Where(o => string.IsNullOrEmpty(manufacturer) || o.Manufacturer == manufacturer)
                .ToList();
        }

        /// <summary>
        /// Applies an optimization by name
        /// </summary>
        /// <param name="optimizationName">Name of optimization to apply</param>
        /// <returns>True if successful</returns>
        public bool ApplyOptimization(string optimizationName)
        {
            var optimization = _optimizations.FirstOrDefault(o => o.Name == optimizationName);
            if (optimization != null)
            {
                optimization.IsApplied = true;
                optimization.AppliedDate = DateTime.Now;
                OptimizationApplied?.Invoke(this, new OptimizationEventArgs(optimizationName, "Hardware", true, "Applied successfully") { Optimization = optimization });
                return true;
            }
            return false;
        }

        /// <summary>
        /// Applies optimization asynchronously
        /// </summary>
        /// <param name="optimization">The optimization to apply</param>
        /// <returns>Task representing the async operation</returns>
        public async Task<bool> ApplyOptimizationAsync(HardwareSpecificOptimization optimization)
        {
            await Task.Delay(100);
            return ApplyOptimization(optimization.Name);
        }

        /// <summary>
        /// Reverts optimization
        /// </summary>
        /// <param name="optimizationName">Name of optimization to revert</param>
        /// <returns>True if successful</returns>
        public bool RevertOptimization(string optimizationName)
        {
            var optimization = _optimizations.FirstOrDefault(o => o.Name == optimizationName);
            if (optimization != null)
            {
                optimization.IsApplied = false;
                OptimizationReverted?.Invoke(this, new OptimizationEventArgs(optimizationName, "Hardware", true, "Reverted successfully") { Optimization = optimization });
                return true;
            }
            return false;
        }

        /// <summary>
        /// Reverts power profile asynchronously
        /// </summary>
        /// <param name="profileName">Name of power profile to revert</param>
        /// <returns>Task representing the async operation</returns>
        public async Task<bool> RevertPowerProfileAsync(string profileName)
        {
            await Task.Delay(100);
            PowerProfileReverted?.Invoke(this, new PowerProfileEventArgs(profileName, "Power", true, "Reverted successfully", 0) { Profile = profileName });
            return true;
        }

        /// <summary>
        /// Reverts optimization asynchronously
        /// </summary>
        /// <param name="optimizationName">Name of optimization to revert</param>
        /// <returns>Task representing the async operation</returns>
        public async Task<bool> RevertOptimizationAsync(string optimizationName)
        {
            await Task.Delay(100);
            return RevertOptimization(optimizationName);
        }

        /// <summary>
        /// Applies power profile asynchronously
        /// </summary>
        /// <param name="profileName">Name of power profile to apply</param>
        /// <returns>Task representing the async operation</returns>
        public async Task<bool> ApplyPowerProfileAsync(string profileName)
        {
            await Task.Delay(100);
            PowerProfileApplied?.Invoke(this, new PowerProfileEventArgs(profileName, "Power", true, "Applied successfully", 15) { Profile = profileName });
            return true;
        }

        /// <summary>
        /// Gets optimizations for specific hardware
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>List of optimizations</returns>
        public List<HardwareSpecificOptimization> GetOptimizationsForHardware(HardwareType hardwareType)
        {
            return GetOptimizationsForCategory(hardwareType, null, "All");
        }

        /// <summary>
        /// Gets applied optimizations
        /// </summary>
        /// <returns>List of applied optimizations</returns>
        public List<HardwareSpecificOptimization> GetAppliedOptimizations()
        {
            return _optimizations.Where(o => o.IsApplied).ToList();
        }

        /// <summary>
        /// Event raised when optimization is applied
        /// </summary>
        public event EventHandler<OptimizationEventArgs> OptimizationApplied;

        /// <summary>
        /// Event raised when optimization is reverted
        /// </summary>
        public event EventHandler<OptimizationEventArgs> OptimizationReverted;

        /// <summary>
        /// Event raised when power profile is applied
        /// </summary>
        public event EventHandler<PowerProfileEventArgs> PowerProfileApplied;

        /// <summary>
        /// Event raised when power profile is reverted
        /// </summary>
        public event EventHandler<PowerProfileEventArgs> PowerProfileReverted;

        /// <summary>
        /// Initializes default optimizations
        /// </summary>
        private void InitializeDefaultOptimizations()
        {
            // Add sample optimizations
            _optimizations.Add(new HardwareSpecificOptimization
            {
                Name = "CPU Performance Boost",
                Description = "Optimizes CPU performance settings",
                HardwareType = HardwareType.CPU,
                Manufacturer = "AMD",
                IsApplied = false
            });

            _optimizations.Add(new HardwareSpecificOptimization
            {
                Name = "NVIDIA GPU Optimization",
                Description = "Optimizes NVIDIA GPU settings",
                HardwareType = HardwareType.GPU,
                Manufacturer = "NVIDIA",
                IsApplied = false
            });

            // Add sample smart tweaks
            _smartTweaks.Add(new SmartTweak
            {
                SettingName = "CPU Priority",
                Description = "Optimize CPU priority for gaming",
                Category = "Recommended"
            });

            _smartTweaks.Add(new SmartTweak
            {
                SettingName = "Memory Management",
                Description = "Optimize memory management",
                Category = "Performance"
            });
        }
    }
}
