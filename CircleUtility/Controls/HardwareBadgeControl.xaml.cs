using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using CircleUtility.Models;

namespace CircleUtility.Controls
{
    /// <summary>
    /// Interaction logic for HardwareBadgeControl.xaml
    /// </summary>
    public partial class HardwareBadgeControl : UserControl
    {
        private static readonly Color CpuColor = Color.FromRgb(0, 200, 255);  // Blue
        private static readonly Color GpuColor = Color.FromRgb(0, 255, 128);  // Green
        private static readonly Color RamColor = Color.FromRgb(255, 128, 0);  // Orange
        private static readonly Color StorageColor = Color.FromRgb(255, 0, 128);  // Pink
        private static readonly Color NetworkColor = Color.FromRgb(128, 0, 255);  // Purple
        private static readonly Color DefaultColor = Color.FromRgb(200, 200, 200);  // Gray

        private Storyboard _glowAnimation;
        private Storyboard _rotateAnimation;

        /// <summary>
        /// Gets or sets the hardware type
        /// </summary>
        public HardwareType HardwareType
        {
            get { return (HardwareType)GetValue(HardwareTypeProperty); }
            set { SetValue(HardwareTypeProperty, value); }
        }

        /// <summary>
        /// Dependency property for HardwareType
        /// </summary>
        public static readonly DependencyProperty HardwareTypeProperty =
            DependencyProperty.Register("HardwareType", typeof(HardwareType), typeof(HardwareBadgeControl),
                new PropertyMetadata(HardwareType.CPU, OnHardwareTypeChanged));

        private static void OnHardwareTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HardwareBadgeControl control && e.NewValue is HardwareType type)
            {
                control.UpdateBadgeAppearance(type);
            }
        }

        /// <summary>
        /// Gets or sets the compatibility score (0-100)
        /// </summary>
        public int CompatibilityScore
        {
            get { return (int)GetValue(CompatibilityScoreProperty); }
            set { SetValue(CompatibilityScoreProperty, value); }
        }

        /// <summary>
        /// Dependency property for CompatibilityScore
        /// </summary>
        public static readonly DependencyProperty CompatibilityScoreProperty =
            DependencyProperty.Register("CompatibilityScore", typeof(int), typeof(HardwareBadgeControl),
                new PropertyMetadata(100, OnCompatibilityScoreChanged));

        private static void OnCompatibilityScoreChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HardwareBadgeControl control && e.NewValue is int score)
            {
                control.UpdateGlowIntensity(score);
            }
        }

        /// <summary>
        /// Gets or sets the tooltip text
        /// </summary>
        public string BadgeTooltip
        {
            get { return (string)GetValue(BadgeTooltipProperty); }
            set { SetValue(BadgeTooltipProperty, value); }
        }

        /// <summary>
        /// Dependency property for BadgeTooltip
        /// </summary>
        public static readonly DependencyProperty BadgeTooltipProperty =
            DependencyProperty.Register("BadgeTooltip", typeof(string), typeof(HardwareBadgeControl),
                new PropertyMetadata("Detected Hardware Component", OnBadgeTooltipChanged));

        private static void OnBadgeTooltipChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HardwareBadgeControl control && e.NewValue is string tooltip)
            {
                control.MainGrid.ToolTip = tooltip;
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the badge should rotate
        /// </summary>
        public bool EnableRotation
        {
            get { return (bool)GetValue(EnableRotationProperty); }
            set { SetValue(EnableRotationProperty, value); }
        }

        /// <summary>
        /// Dependency property for EnableRotation
        /// </summary>
        public static readonly DependencyProperty EnableRotationProperty =
            DependencyProperty.Register("EnableRotation", typeof(bool), typeof(HardwareBadgeControl),
                new PropertyMetadata(false, OnEnableRotationChanged));

        private static void OnEnableRotationChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HardwareBadgeControl control && e.NewValue is bool enable)
            {
                if (enable)
                {
                    control._rotateAnimation.Begin(control);
                }
                else
                {
                    control._rotateAnimation.Stop(control);
                }
            }
        }

        /// <summary>
        /// Initializes a new instance of the HardwareBadgeControl class
        /// </summary>
        public HardwareBadgeControl()
        {
            InitializeComponent();

            // Get the animations from resources
            _glowAnimation = (Storyboard)FindResource("GlowAnimation");
            _rotateAnimation = (Storyboard)FindResource("RotateAnimation");

            // Start the glow animation
            _glowAnimation.Begin(this);

            // Set initial tooltip
            MainGrid.ToolTip = BadgeTooltip;

            // Update appearance based on initial values
            UpdateBadgeAppearance(HardwareType);
            UpdateGlowIntensity(CompatibilityScore);

            // Start rotation if enabled
            if (EnableRotation)
            {
                _rotateAnimation.Begin(this);
            }
        }

        /// <summary>
        /// Updates the badge appearance based on the hardware type
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        private void UpdateBadgeAppearance(HardwareType hardwareType)
        {
            Color badgeColor;
            string iconPath;

            switch (hardwareType)
            {
                case HardwareType.CPU:
                    badgeColor = CpuColor;
                    iconPath = "M4,11H20V13H4V11M4,5H20V7H4V5M4,17H14V19H4V17Z"; // CPU icon
                    break;
                case HardwareType.GPU:
                    badgeColor = GpuColor;
                    iconPath = "M2,2V4H4V2H2M6,2V4H8V2H6M10,2V4H12V2H10M14,2V4H16V2H14M18,2V4H20V2H18M22,2V4H24V2H22M2,6V8H4V6H2M6,6V8H8V6H6M10,6V8H12V6H10M14,6V8H16V6H14M18,6V8H20V6H18M22,6V8H24V6H22M2,10V12H4V10H2M6,10V12H8V10H6M10,10V12H12V10H10M14,10V12H16V10H14M18,10V12H20V10H18M22,10V12H24V10H22M2,14V16H4V14H2M6,14V16H8V14H6M10,14V16H12V14H10M14,14V16H16V14H14M18,14V16H20V14H18M22,14V16H24V14H22M2,18V20H4V18H2M6,18V20H8V18H6M10,18V20H12V18H10M14,18V20H16V18H14M18,18V20H20V18H18M22,18V20H24V18H22M2,22V24H4V22H2M6,22V24H8V22H6M10,22V24H12V22H10M14,22V24H16V22H14M18,22V24H20V22H18M22,22V24H24V22H22Z"; // GPU icon
                    break;
                case HardwareType.RAM:
                    badgeColor = RamColor;
                    iconPath = "M17,17H7V7H17M21,11V9H19V7C19,5.89 18.1,5 17,5H15V3H13V5H11V3H9V5H7C5.89,5 5,5.89 5,7V9H3V11H5V13H3V15H5V17A2,2 0 0,0 7,19H9V21H11V19H13V21H15V19H17A2,2 0 0,0 19,17V15H21V13H19V11M13,13H11V11H13Z"; // RAM icon
                    break;
                case HardwareType.Storage:
                    badgeColor = StorageColor;
                    iconPath = "M6,2H18A2,2 0 0,1 20,4V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V4A2,2 0 0,1 6,2M12,4A6,6 0 0,0 6,10C6,13.31 8.69,16 12.1,16L11.22,13.77C10.95,13.29 11.11,12.68 11.59,12.4L12.45,11.9C12.93,11.63 13.54,11.79 13.82,12.27L15.74,14.69C17.12,13.59 18,11.9 18,10A6,6 0 0,0 12,4M12,9A1,1 0 0,1 13,10A1,1 0 0,1 12,11A1,1 0 0,1 11,10A1,1 0 0,1 12,9M7,18A1,1 0 0,0 6,19A1,1 0 0,0 7,20A1,1 0 0,0 8,19A1,1 0 0,0 7,18M12.09,13.27L14.58,19.58L17.17,18.08L12.95,12.77L12.09,13.27Z"; // Storage icon
                    break;
                case HardwareType.Network:
                    badgeColor = NetworkColor;
                    iconPath = "M15,20A1,1 0 0,0 14,19H13V17H17A2,2 0 0,0 19,15V5A2,2 0 0,0 17,3H7A2,2 0 0,0 5,5V15A2,2 0 0,0 7,17H11V19H10A1,1 0 0,0 9,20H2V22H9A1,1 0 0,0 10,23H14A1,1 0 0,0 15,22H22V20H15M7,15V5H17V15H7Z"; // Network icon
                    break;
                default:
                    badgeColor = DefaultColor;
                    iconPath = "M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"; // Default checkmark
                    break;
            }

            // Update badge color
            BadgeBackground.Fill = new SolidColorBrush(badgeColor);
            ((DropShadowEffect)BadgeBackground.Effect).Color = badgeColor;

            // Update icon
            HardwareIcon.Data = Geometry.Parse(iconPath);
        }

        /// <summary>
        /// Updates the glow intensity based on the compatibility score
        /// </summary>
        /// <param name="score">The compatibility score (0-100)</param>
        private void UpdateGlowIntensity(int score)
        {
            // Adjust glow intensity based on score
            if (score >= 90)
            {
                // Highly compatible - brightest glow
                ((DropShadowEffect)BadgeBackground.Effect).BlurRadius = 15;
                ((DropShadowEffect)BadgeBackground.Effect).Opacity = 0.8;
            }
            else if (score >= 70)
            {
                // Compatible - medium glow
                ((DropShadowEffect)BadgeBackground.Effect).BlurRadius = 10;
                ((DropShadowEffect)BadgeBackground.Effect).Opacity = 0.6;
            }
            else if (score >= 50)
            {
                // Somewhat compatible - subtle glow
                ((DropShadowEffect)BadgeBackground.Effect).BlurRadius = 8;
                ((DropShadowEffect)BadgeBackground.Effect).Opacity = 0.4;
            }
            else
            {
                // Not very compatible - minimal glow
                ((DropShadowEffect)BadgeBackground.Effect).BlurRadius = 5;
                ((DropShadowEffect)BadgeBackground.Effect).Opacity = 0.2;
            }
        }
    }
}
