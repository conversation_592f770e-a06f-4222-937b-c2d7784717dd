using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CircleUtility.Controls
{
    public partial class AnimatedProgressBar : UserControl
    {
        private Storyboard _progressAnimation;
        private Storyboard _pulseAnimation;
        private Storyboard _indeterminateAnimation;

        public static readonly DependencyProperty ProgressColorProperty =
            DependencyProperty.Register(nameof(ProgressColor), typeof(Brush), typeof(AnimatedProgressBar),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(0, 200, 255))));

        public static readonly DependencyProperty ProgressTextProperty =
            DependencyProperty.Register(nameof(ProgressText), typeof(string), typeof(AnimatedProgressBar),
                new PropertyMetadata(string.Empty));

        public static readonly DependencyProperty ProgressValueProperty =
            DependencyProperty.Register(nameof(ProgressValue), typeof(double), typeof(AnimatedProgressBar),
                new PropertyMetadata(0.0, OnProgressValueChanged));

        public static readonly DependencyProperty MaximumValueProperty =
            DependencyProperty.Register(nameof(MaximumValue), typeof(double), typeof(AnimatedProgressBar),
                new PropertyMetadata(100.0));

        public static readonly DependencyProperty IsIndeterminateProperty =
            DependencyProperty.Register(nameof(IsIndeterminate), typeof(bool), typeof(AnimatedProgressBar),
                new PropertyMetadata(false, OnIsIndeterminateChanged));

        public Brush ProgressColor
        {
            get => (Brush)GetValue(ProgressColorProperty);
            set => SetValue(ProgressColorProperty, value);
        }

        public string ProgressText
        {
            get => (string)GetValue(ProgressTextProperty);
            set => SetValue(ProgressTextProperty, value);
        }

        public double ProgressValue
        {
            get => (double)GetValue(ProgressValueProperty);
            set => SetValue(ProgressValueProperty, value);
        }

        public double MaximumValue
        {
            get => (double)GetValue(MaximumValueProperty);
            set => SetValue(MaximumValueProperty, value);
        }

        public bool IsIndeterminate
        {
            get => (bool)GetValue(IsIndeterminateProperty);
            set => SetValue(IsIndeterminateProperty, value);
        }

        public AnimatedProgressBar()
        {
            InitializeComponent();

            _progressAnimation = (Storyboard)FindResource("ProgressAnimation");
            _pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            _indeterminateAnimation = (Storyboard)FindResource("IndeterminateAnimation");

            _pulseAnimation?.Begin(this, true);

            Loaded += (_, _) => UpdateProgress(ProgressValue, MaximumValue);
            SizeChanged += (_, _) => UpdateProgress(ProgressValue, MaximumValue);
        }

        private static void OnProgressValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AnimatedProgressBar bar && !bar.IsIndeterminate)
            {
                bar.UpdateProgress((double)e.NewValue, bar.MaximumValue);
            }
        }

        private static void OnIsIndeterminateChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AnimatedProgressBar bar)
            {
                bar.UpdateIndeterminateState((bool)e.NewValue);
            }
        }

        private void UpdateIndeterminateState(bool isIndeterminate)
        {
            IndeterminateIndicator.Visibility = isIndeterminate ? Visibility.Visible : Visibility.Collapsed;
            ProgressIndicator.Visibility = isIndeterminate ? Visibility.Collapsed : Visibility.Visible;

            if (isIndeterminate)
            {
                _indeterminateAnimation?.Begin(this, true);
            }
            else
            {
                _indeterminateAnimation?.Stop(this);
                UpdateProgress(ProgressValue, MaximumValue);
            }
        }

        private void UpdateProgress(double value, double max)
        {
            if (IsIndeterminate || ActualWidth <= 0) return;

            double percent = max > 0 ? Math.Clamp(value / max, 0, 1) : 0;
            double targetWidth = ActualWidth * percent;

            if (string.IsNullOrWhiteSpace(ProgressText))
            {
                ProgressText = $"{Math.Round(percent * 100)}%";
            }

            // Clone animation to avoid reuse conflicts
            var clonedStoryboard = _progressAnimation.Clone();

            if (clonedStoryboard.Children[0] is DoubleAnimation widthAnimation)
            {
                widthAnimation.To = targetWidth;
            }

            clonedStoryboard.Begin(this);
        }
    }
}
