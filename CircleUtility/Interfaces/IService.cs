using System;
using System.Threading.Tasks;

namespace CircleUtility.Interfaces
{
    public interface IInitializableService
    {
        void Initialize();
    }

    public interface IStoppableService
    {
        void Stop();
    }

    public interface IAsyncStoppableService
    {
        Task StopAsync();
    }

    public interface IHardwareService : IInitializableService, IStoppableService
    {
        bool IsInitialized { get; }
        string GetSystemInfo();
        Task<bool> OptimizeHardware();
        Task<bool> RunCompatibilityTests();
    }

    public interface IDiscordService : IInitializableService, IAsyncStoppableService
    {
        bool IsConnected { get; }
        Task<bool> Connect();
        Task<bool> Disconnect();
        Task SendMessage(string message);
    }

    public interface IConfigurationService : IInitializableService
    {
        T GetSetting<T>(string section, string key);
        bool UpdateSetting<T>(string section, string key, T value);
        bool SaveConfiguration();
        bool LoadConfiguration();
    }

    public interface ISecurityService : IInitializableService
    {
        bool ValidateCredentials(string username, string password);
        string HashPassword(string password);
        string GenerateToken(string username);
        bool ValidateToken(string token);
    }

    public interface IWindowService : IInitializableService
    {
        void ShowWindow<T>() where T : System.Windows.Window;
        void CloseWindow<T>() where T : System.Windows.Window;
        bool IsWindowOpen<T>() where T : System.Windows.Window;
    }
} 