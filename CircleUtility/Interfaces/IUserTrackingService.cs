namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for user tracking service
    /// </summary>
    public interface IUserTrackingService
    {
        /// <summary>
        /// Tracks user action
        /// </summary>
        /// <param name="action">The action to track</param>
        void TrackAction(string action);

        /// <summary>
        /// Gets user statistics
        /// </summary>
        /// <returns>User statistics</returns>
        object GetUserStatistics();
    }

        /// <summary>
        /// Adds user activity
        /// </summary>
        /// <param name="activity">The activity to add</param>
        void AddUserActivity(string activity);

        /// <summary>
        /// Gets user logins
        /// </summary>
        /// <returns>User login information</returns>
        object GetUserLogins();

        /// <summary>
        /// Registers user login
        /// </summary>
        /// <param name="loginInfo">Login information</param>
        void RegisterUserLogin(object loginInfo);

        /// <summary>
        /// Resets user hardware and IP
        /// </summary>
        void ResetUserHardwareAndIp();

        /// <summary>
        /// Gets pending registrations
        /// </summary>
        /// <returns>Pending registrations</returns>
        object GetPendingRegistrations();

        /// <summary>
        /// Approves pending registration
        /// </summary>
        /// <param name="registration">Registration to approve</param>
        void ApprovePendingRegistration(object registration);

        /// <summary>
        /// Denies pending registration
        /// </summary>
        /// <param name="registration">Registration to deny</param>
        void DenyPendingRegistration(object registration);
    }

