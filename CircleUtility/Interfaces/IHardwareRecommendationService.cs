using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for hardware recommendation service
    /// </summary>
    public interface IHardwareRecommendationService
    {
        /// <summary>
        /// Gets hardware recommendations
        /// </summary>
        /// <returns>Hardware recommendations</returns>
        object GetRecommendations();

        /// <summary>
        /// Analyzes hardware compatibility
        /// </summary>
        /// <param name="hardware">Hardware to analyze</param>
        /// <returns>Compatibility analysis</returns>
        object AnalyzeCompatibility(object hardware);

        /// <summary>
        /// Gets recommended optimizations asynchronously
        /// </summary>
        /// <returns>Task with recommended optimizations</returns>
        Task<List<OptimizationRecommendation>> GetRecommendedOptimizationsAsync();

        /// <summary>
        /// Gets recommended power profiles asynchronously
        /// </summary>
        /// <returns>Task with recommended power profiles</returns>
        Task<List<PowerProfileRecommendation>> GetRecommendedPowerProfilesAsync();

        /// <summary>
        /// Gets recommended tweaks for hardware asynchronously
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>Task with recommended tweaks</returns>
        Task<List<OptimizationRecommendation>> GetRecommendedTweaksForHardwareAsync(HardwareType hardwareType);

        /// <summary>
        /// Gets recommendation score asynchronously
        /// </summary>
        /// <param name="recommendation">The recommendation</param>
        /// <returns>Task with recommendation score</returns>
        Task<int> GetRecommendationScoreAsync(string recommendation);
    }
}
