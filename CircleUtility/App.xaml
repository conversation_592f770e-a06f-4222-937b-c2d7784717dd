<Application x:Class="CircleUtility.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:helpers="clr-namespace:CircleUtility.Helpers"
             xmlns:converters="clr-namespace:CircleUtility.Converters"
             xmlns:views="clr-namespace:CircleUtility.Views"
             Startup="Application_Startup" Exit="Application_Exit">
    <Application.Resources>
        <!-- Centralized Resource Dictionary -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/ResourceDictionary.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
