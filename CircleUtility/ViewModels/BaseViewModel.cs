using CircleUtility.Commands;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// Base class for all ViewModels with proper disposal patterns
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged, IDisposable
    {
        protected readonly LoggingService _logger;
        private bool _disposed = false;

        /// <summary>
        /// Initializes a new instance of the BaseViewModel class
        /// </summary>
        protected BaseViewModel()
        {
            _logger = LoggingService.Instance;
        }

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets a property value and raises PropertyChanged if the value changed
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">The backing field</param>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">The name of the property</param>
        /// <returns>True if the value changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value))
            {
                return false;
            }

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Throws an ObjectDisposedException if the object has been disposed
        /// </summary>
        protected void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }

        /// <summary>
        /// Disposes the ViewModel
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the ViewModel
        /// </summary>
        /// <param name="disposing">Whether to dispose managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    OnDisposing();

                    // Clear event handlers to prevent memory leaks
                    PropertyChanged = null;

                    _logger?.Log($"{GetType().Name} disposed", LogLevel.DEBUG);
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// Called when the ViewModel is being disposed
        /// Override this method to dispose of ViewModel-specific resources
        /// </summary>
        protected virtual void OnDisposing()
        {
            // Override in derived classes to dispose of specific resources
        }

        /// <summary>
        /// Finalizer
        /// </summary>
        ~BaseViewModel()
        {
            Dispose(false);
        }
    }
}

