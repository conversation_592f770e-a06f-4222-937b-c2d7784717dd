<UserControl x:Class="CircleUtility.Controls.AnimatedLoadingSpinner"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="100" d:DesignWidth="100"
             Loaded="UserControl_Loaded">
    
    <UserControl.Resources>
        <!-- Rotation animation -->
        <Storyboard x:Key="RotationAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="SpinnerRotation"
                            Storyboard.TargetProperty="Angle"
                            From="0" To="360" Duration="0:0:1.5"/>
        </Storyboard>
        
        <!-- Pulse animation -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="GlowEffect"
                            Storyboard.TargetProperty="Opacity"
                            From="0.5" To="1.0" Duration="0:0:0.75"
                            AutoReverse="True"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Grid>
        <Ellipse x:Name="OuterCircle"
                Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                StrokeThickness="3"
                Fill="Transparent">
            <Ellipse.Effect>
                <DropShadowEffect x:Name="GlowEffect"
                                 Color="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource ColorConverter}}"
                                 BlurRadius="10"
                                 ShadowDepth="0"
                                 Opacity="0.5"/>
            </Ellipse.Effect>
        </Ellipse>
        
        <Grid RenderTransformOrigin="0.5,0.5">
            <Grid.RenderTransform>
                <RotateTransform x:Name="SpinnerRotation" Angle="0"/>
            </Grid.RenderTransform>
            
            <!-- Spinner segments -->
            <Path x:Name="Segment1"
                 Data="M 50,0 A 50,50 0 0,1 75,6.7"
                 Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="5"
                 StrokeStartLineCap="Round"
                 StrokeEndLineCap="Round">
                <Path.RenderTransform>
                    <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                       Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"/>
                </Path.RenderTransform>
            </Path>
            
            <Path x:Name="Segment2"
                 Data="M 93.3,25 A 50,50 0 0,1 100,50"
                 Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="5"
                 StrokeStartLineCap="Round"
                 StrokeEndLineCap="Round"
                 Opacity="0.9">
                <Path.RenderTransform>
                    <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                       Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"/>
                </Path.RenderTransform>
            </Path>
            
            <Path x:Name="Segment3"
                 Data="M 93.3,75 A 50,50 0 0,1 75,93.3"
                 Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="5"
                 StrokeStartLineCap="Round"
                 StrokeEndLineCap="Round"
                 Opacity="0.8">
                <Path.RenderTransform>
                    <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                       Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"/>
                </Path.RenderTransform>
            </Path>
            
            <Path x:Name="Segment4"
                 Data="M 50,100 A 50,50 0 0,1 25,93.3"
                 Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="5"
                 StrokeStartLineCap="Round"
                 StrokeEndLineCap="Round"
                 Opacity="0.7">
                <Path.RenderTransform>
                    <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                       Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"/>
                </Path.RenderTransform>
            </Path>
            
            <Path x:Name="Segment5"
                 Data="M 6.7,75 A 50,50 0 0,1 0,50"
                 Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="5"
                 StrokeStartLineCap="Round"
                 StrokeEndLineCap="Round"
                 Opacity="0.6">
                <Path.RenderTransform>
                    <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                       Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"/>
                </Path.RenderTransform>
            </Path>
            
            <Path x:Name="Segment6"
                 Data="M 6.7,25 A 50,50 0 0,1 25,6.7"
                 Stroke="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="5"
                 StrokeStartLineCap="Round"
                 StrokeEndLineCap="Round"
                 Opacity="0.5">
                <Path.RenderTransform>
                    <TranslateTransform X="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"
                                       Y="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource HalfSizeConverter}}"/>
                </Path.RenderTransform>
            </Path>
        </Grid>
        
        <!-- Center text -->
        <TextBlock x:Name="CenterText"
                  Text="{Binding Text, RelativeSource={RelativeSource AncestorType=UserControl}}"
                  FontFamily="Consolas"
                  FontSize="12"
                  Foreground="{Binding SpinnerColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  Visibility="{Binding Text, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource StringToVisibilityConverter}}"/>
    </Grid>
</UserControl>
