<UserControl x:Class="CircleUtility.Views.DebloatView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="Black">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="Debloat Windows"
                   FontFamily="Consolas"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20"/>

        <!-- Description -->
        <TextBlock Grid.Row="1"
                   Text="Remove unnecessary Windows components, services, and apps to improve system performance and reduce resource usage."
                   FontFamily="Segoe UI"
                   FontSize="12"
                   Foreground="White"
                   TextWrapping="Wrap"
                   Margin="0,0,0,10"/>

        <!-- Quick Actions Panel -->
        <Border Grid.Row="1"
                Background="#FF050A0F"
                BorderThickness="1"
                BorderBrush="#FF00C8FF"
                Margin="0,40,0,0">
            <Grid Margin="15">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <TextBlock Text="Quick Actions"
                           FontFamily="Consolas"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#FF00C8FF"
                           Margin="0,0,0,10"/>

                <!-- Description -->
                <TextBlock Grid.Row="1"
                           Text="Choose a preset debloat profile or apply individual tweaks below. Presets are combinations of tweaks optimized for different use cases."
                           FontFamily="Segoe UI"
                           FontSize="12"
                           Foreground="White"
                           TextWrapping="Wrap"
                           Margin="0,0,0,15"/>

                <!-- Buttons -->
                <StackPanel Grid.Row="2" Orientation="Horizontal">
                    <Button Content="Gaming Preset"
                            Command="{Binding ApplyGamingPresetCommand}"
                            Width="150"
                            Height="35"
                            Margin="0,0,15,0"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="#FF00C8FF"
                            BorderThickness="1"/>

                    <Button Content="Minimal Preset"
                            Command="{Binding ApplyMinimalPresetCommand}"
                            Width="150"
                            Height="35"
                            Margin="0,0,15,0"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="#FF00C8FF"
                            BorderThickness="1"/>

                    <Button Content="Balanced Preset"
                            Command="{Binding ApplyBalancedPresetCommand}"
                            Width="150"
                            Height="35"
                            Margin="0,0,15,0"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="#FF00C8FF"
                            BorderThickness="1"/>

                    <Button Content="Revert All"
                            Command="{Binding RevertAllTweaksCommand}"
                            Width="150"
                            Height="35"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="#FFFF3232"
                            BorderThickness="1"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Tweaks list -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="0,20,0,0">
            <ItemsControl ItemsSource="{Binding DebloatTweaks}">
                <ItemsControl.GroupStyle>
                    <GroupStyle>
                        <GroupStyle.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}"
                                           FontFamily="Consolas"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#FF00C8FF"
                                           Margin="0,10,0,5"/>
                            </DataTemplate>
                        </GroupStyle.HeaderTemplate>
                    </GroupStyle>
                </ItemsControl.GroupStyle>

                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Background="#FF050A0F"
                                BorderThickness="0,0,0,1"
                                BorderBrush="#FF004080"
                                Padding="10"
                                Margin="0,0,0,5">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Tweak info -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding Name}"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="White"/>

                                    <TextBlock Text="{Binding Description}"
                                               FontFamily="Segoe UI"
                                               FontSize="12"
                                               Foreground="#FFC0C0C0"
                                               TextWrapping="Wrap"
                                               Margin="0,5,0,0"/>

                                    <!-- Requirements -->
                                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                        <TextBlock Text="Admin: "
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF808080"/>
                                        <TextBlock Text="{Binding RequiresAdmin, Converter={StaticResource BoolToYesNoConverter}}"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="{Binding RequiresAdmin, Converter={StaticResource BoolToColorConverter}}"/>

                                        <TextBlock Text=" | Restart: "
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF808080"
                                                   Margin="10,0,0,0"/>
                                        <TextBlock Text="{Binding RequiresRestart, Converter={StaticResource BoolToYesNoConverter}}"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="{Binding RequiresRestart, Converter={StaticResource BoolToColorConverter}}"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Action buttons with status indicator -->
                                <Grid Grid.Column="1" VerticalAlignment="Center">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Status indicator -->
                                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,5">
                                        <TextBlock Text="Status: "
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF808080"/>

                                        <TextBlock Text="Not Applied"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FFFF6060"
                                                   Visibility="{Binding IsApplied, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>

                                        <TextBlock Text="Applied"
                                                   FontFamily="Consolas"
                                                   FontSize="11"
                                                   Foreground="#FF60FF60"
                                                   Visibility="{Binding IsApplied, Converter={StaticResource BoolToVisibilityConverter}}"/>

                                        <Ellipse Width="8" Height="8" Margin="5,0,0,0"
                                                 Fill="#FFFF6060"
                                                 Visibility="{Binding IsApplied, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                            <Ellipse.Effect>
                                                <BlurEffect Radius="1"/>
                                            </Ellipse.Effect>
                                        </Ellipse>

                                        <Ellipse Width="8" Height="8" Margin="5,0,0,0"
                                                 Fill="#FF60FF60"
                                                 Visibility="{Binding IsApplied, Converter={StaticResource BoolToVisibilityConverter}}">
                                            <Ellipse.Effect>
                                                <BlurEffect Radius="1"/>
                                            </Ellipse.Effect>
                                        </Ellipse>
                                    </StackPanel>

                                    <!-- Buttons -->
                                    <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                                        <Button Content="Apply"
                                                Command="{Binding DataContext.ApplyTweakCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                CommandParameter="{Binding}"
                                                Width="80"
                                                Height="30"
                                                Margin="0,0,10,0"
                                                Background="#FF001428"
                                                Foreground="White"
                                                BorderBrush="#FF00C8FF"
                                                BorderThickness="1"
                                                Visibility="{Binding IsApplied, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>

                                        <Button Content="Revert"
                                                Command="{Binding DataContext.RevertTweakCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                CommandParameter="{Binding}"
                                                Width="80"
                                                Height="30"
                                                Background="#FF001428"
                                                Foreground="White"
                                                BorderBrush="#FFFF3232"
                                                BorderThickness="1"
                                                Visibility="{Binding IsApplied, Converter={StaticResource BoolToVisibilityConverter}}"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
