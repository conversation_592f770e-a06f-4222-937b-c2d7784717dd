namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for system optimization service
    /// </summary>
    public interface ISystemOptimizationService
    {
        /// <summary>
        /// Optimizes system performance
        /// </summary>
        /// <returns>True if successful</returns>
        bool OptimizeSystem();

        /// <summary>
        /// Reverts system optimizations
        /// </summary>
        /// <returns>True if successful</returns>
        bool RevertOptimizations();
    }
}
