// Created by Arsenal on 5-17-25 12:15PM
namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a power profile recommendation
    /// </summary>
    public class PowerProfileRecommendation
    {
        /// <summary>
        /// Gets or sets the power profile
        /// </summary>
        public PowerProfile PowerProfile { get; set; }

        /// <summary>
        /// Gets or sets the recommendation score (0-100)
        /// </summary>
        public int RecommendationScore { get; set; }

        /// <summary>
        /// Gets or sets the recommendation reason
        /// </summary>
        public string RecommendationReason { get; set; }

        /// <summary>
        /// Gets or sets the hardware compatibility
        /// </summary>
        public HardwareCompatibility HardwareCompatibility { get; set; }

        /// <summary>
        /// Gets or sets the confidence level (0-100)
        /// </summary>
        public int ConfidenceLevel { get; set; }
    }
}
