// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using CircleUtility.Models;
using System.Windows;
using CircleUtility.Views;
using System.Windows.Threading;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for checking and applying updates
    /// </summary>
    public class UpdateService
    {
        private static UpdateService _instance;
        private readonly LoggingService _logger;
        private readonly string _updateDirectory;
        private readonly string _backupDirectory;
        private readonly string _historyFile;
        private readonly HttpClient _httpClient;
        private UpdateInfo _latestUpdate;
        private bool _isUpdateAvailable;
        private bool _isUpdateInProgress;
        private readonly string _githubToken;
        private readonly string _owner;
        private readonly string _repo;
        private readonly string _currentVersion;
        private readonly string _updatePath;
        private UpdateProgressWindow _progressWindow;
        private List<UpdateHistory> _updateHistory;
        private DispatcherTimer _updateCheckTimer;
        private DateTime _lastUpdateCheck;
        private TimeSpan _updateCheckInterval;

        /// <summary>
        /// Event raised when an update is available
        /// </summary>
        public event EventHandler<UpdateInfo> UpdateAvailable;

        /// <summary>
        /// Event raised when an update is downloaded
        /// </summary>
        public event EventHandler<string> UpdateProgress;

        /// <summary>
        /// Event raised when an update is installed
        /// </summary>
        public event EventHandler<Exception> UpdateError;

        /// <summary>
        /// Event raised when an update is installed
        /// </summary>
        public event EventHandler<UpdateHistory> UpdateInstalled;

        /// <summary>
        /// Event raised when an update is rolled back
        /// </summary>
        #pragma warning disable CS0067
        public event EventHandler<UpdateHistory> UpdateRolledBack;
        #pragma warning restore CS0067

        /// <summary>
        /// Initializes a new instance of the UpdateService class
        /// </summary>
        private UpdateService(string githubToken, string owner, string repo)
        {
            _logger = LoggingService.Instance;
            _updateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Updates");
            _backupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
            _historyFile = Path.Combine(_updateDirectory, "update_history.json");
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "CircleUtility");
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"token {githubToken}");
            _isUpdateAvailable = false;
            _isUpdateInProgress = false;
            _githubToken = githubToken;
            _owner = owner;
            _repo = repo;
            _currentVersion = GetCurrentVersion();
            _updatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Updates");
            _updateHistory = LoadUpdateHistory();
            _lastUpdateCheck = DateTime.MinValue;
            _updateCheckInterval = TimeSpan.FromHours(24); // Default to daily checks
            
            // Ensure directories exist
            if (!Directory.Exists(_updateDirectory))
            {
                Directory.CreateDirectory(_updateDirectory);
            }
            
            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }

            // Initialize update check timer
            _updateCheckTimer = new DispatcherTimer();
            _updateCheckTimer.Tick += async (s, e) => await CheckForUpdatesAsync();
            _updateCheckTimer.Interval = _updateCheckInterval;
            _updateCheckTimer.Start();
        }

        /// <summary>
        /// Gets the singleton instance of the update service
        /// </summary>
        public static UpdateService Instance
        {
            get
            {
                if (_instance == null)
                {
                    throw new InvalidOperationException("UpdateService has not been initialized. Call Initialize first.");
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether an update is available
        /// </summary>
        public bool IsUpdateAvailable => _isUpdateAvailable;

        /// <summary>
        /// Gets a value indicating whether an update is in progress
        /// </summary>
        public bool IsUpdateInProgress => _isUpdateInProgress;

        /// <summary>
        /// Gets the latest update information
        /// </summary>
        public UpdateInfo LatestUpdate => _latestUpdate;

        /// <summary>
        /// Gets the current version of the application
        /// </summary>
        public Version CurrentVersion
        {
            get
            {
                try
                {
                    return Assembly.GetExecutingAssembly().GetName().Version;
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error getting current version: {ex.Message}", LogLevel.ERROR);
                    return new Version(1, 0, 0, 0);
                }
            }
        }

        /// <summary>
        /// Gets the update history
        /// </summary>
        public IReadOnlyList<UpdateHistory> UpdateHistory => _updateHistory.AsReadOnly();

        /// <summary>
        /// Gets the update check interval
        /// </summary>
        public TimeSpan UpdateCheckInterval
        {
            get => _updateCheckInterval;
            set
            {
                _updateCheckInterval = value;
                _updateCheckTimer.Interval = value;
            }
        }

        /// <summary>
        /// Checks for updates
        /// </summary>
        /// <returns>True if an update is available, false otherwise</returns>
        public async Task CheckForUpdatesAsync()
        {
            try
            {
                if (DateTime.Now - _lastUpdateCheck < _updateCheckInterval)
                    return;

                _lastUpdateCheck = DateTime.Now;
                _logger.Log("Checking for updates...", LogLevel.INFO);
                
                var url = $"https://api.github.com/repos/{_owner}/{_repo}/releases/latest";
                var response = await _httpClient.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var release = JsonSerializer.Deserialize<GitHubRelease>(content);
                    
                    if (IsNewVersionAvailable(release.tag_name))
                    {
                        var updateInfo = new UpdateInfo
                        {
                            Version = release.tag_name.TrimStart('v'),
                            ReleaseNotes = release.body,
                            DownloadUrl = release.assets[0].browser_download_url
                        };
                        
                        _latestUpdate = updateInfo;
                        _isUpdateAvailable = true;
                        _logger.Log($"Update available: {_latestUpdate.Version}", LogLevel.INFO);
                        
                        UpdateAvailable?.Invoke(this, updateInfo);
                    }
                    else
                    {
                        _logger.Log("No updates available", LogLevel.INFO);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error checking for updates: {ex.Message}", LogLevel.ERROR);
                UpdateError?.Invoke(this, ex);
            }
        }

        /// <summary>
        /// Downloads and installs an update
        /// </summary>
        /// <param name="updateInfo">The update information</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task DownloadAndInstallUpdateAsync(string downloadUrl)
        {
            var startTime = DateTime.Now;
            var history = new UpdateHistory
            {
                Version = _latestUpdate.Version,
                InstallDate = DateTime.Now,
                ReleaseNotes = _latestUpdate.ReleaseNotes,
                PreviousVersion = _currentVersion,
                IsRollback = false
            };

            try
            {
                _logger.Log($"Downloading and installing update {_latestUpdate.Version}...", LogLevel.INFO);
                _isUpdateInProgress = true;
                
                // Create backup before update
                var backupPath = await CreateBackupAsync();
                if (string.IsNullOrEmpty(backupPath))
                {
                    throw new Exception("Failed to create backup before update");
                }

                // Show progress window
                _progressWindow = new UpdateProgressWindow();
                _progressWindow.Show();

                // Download update
                _progressWindow.UpdateProgress("Downloading update...", 0);
                using var client = new HttpClient();
                var response = await client.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead);
                response.EnsureSuccessStatusCode();

                var totalBytes = response.Content.Headers.ContentLength ?? -1L;
                var downloadedBytes = 0L;
                var updateFile = Path.Combine(_updatePath, "update.exe");

                using (var stream = await response.Content.ReadAsStreamAsync())
                using (var fileStream = File.Create(updateFile))
                {
                    var buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                    {
                        await fileStream.WriteAsync(buffer, 0, bytesRead);
                        downloadedBytes += bytesRead;

                        if (totalBytes > 0)
                        {
                            var progress = (int)((downloadedBytes * 100) / totalBytes);
                            _progressWindow.UpdateProgress($"Downloading update... {downloadedBytes / 1024 / 1024}MB / {totalBytes / 1024 / 1024}MB", progress);
                        }
                    }
                }

                // Update configuration
                var config = await App.ConfigService.GetConfigAsync();
                config.Version = _latestUpdate.Version;
                config.LastUpdate = DateTime.UtcNow;
                await App.ConfigService.SaveConfigAsync(config);

                // Record update history
                history.InstallPath = updateFile;
                history.InstallSize = downloadedBytes;
                history.InstallDuration = DateTime.Now - startTime;
                history.Success = true;
                _updateHistory.Add(history);
                SaveUpdateHistory();

                // Launch installer
                _progressWindow.UpdateProgress("Installing update...", 100);
                var startInfo = new ProcessStartInfo
                {
                    FileName = updateFile,
                    UseShellExecute = true,
                    Verb = "runas" // Run as administrator
                };

                UpdateInstalled?.Invoke(this, history);
                Process.Start(startInfo);
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                _progressWindow?.Close();
                _logger.Log($"Error downloading and installing update: {ex.Message}", LogLevel.ERROR);
                _isUpdateInProgress = false;

                history.Success = false;
                history.ErrorMessage = ex.Message;
                _updateHistory.Add(history);
                SaveUpdateHistory();

                UpdateError?.Invoke(this, ex);
            }
        }

        /// <summary>
        /// Creates a backup before updating
        /// </summary>
        /// <returns>The backup file path, or null if backup failed</returns>
        public async Task<string> CreateBackupAsync()
        {
            try
            {
                _logger.Log("Creating backup before update...", LogLevel.INFO);
                
                // In a real implementation, this would create a backup of the application files
                // For now, we'll simulate a backup
                
                string backupFilePath = Path.Combine(_backupDirectory, $"CircleUtility_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip");
                
                // Simulate backup progress
                for (int i = 0; i <= 100; i += 25)
                {
                    // Raise progress event
                    UpdateProgress?.Invoke(this, $"{i}%");
                    
                    // Simulate backup delay
                    await Task.Delay(200);
                }
                
                // Create empty file to simulate backup
                using (FileStream fs = File.Create(backupFilePath))
                {
                    // Just create the file
                }
                
                _logger.Log($"Backup created successfully: {backupFilePath}", LogLevel.SUCCESS);
                return backupFilePath;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating backup: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Restores a backup
        /// </summary>
        /// <param name="backupFilePath">The path to the backup file</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RestoreBackupAsync(string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    _logger.Log($"Backup file not found: {backupFilePath}", LogLevel.ERROR);
                    return false;
                }
                
                _logger.Log($"Restoring backup from {backupFilePath}...", LogLevel.INFO);
                
                // In a real implementation, this would restore the application files from the backup
                // For now, we'll simulate a restoration
                
                // Simulate restoration progress
                for (int i = 0; i <= 100; i += 20)
                {
                    // Raise progress event
                    UpdateProgress?.Invoke(this, $"{i}%");
                    
                    // Simulate restoration delay
                    await Task.Delay(300);
                }
                
                _logger.Log("Backup restored successfully", LogLevel.SUCCESS);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restoring backup: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Restarts the application to apply updates
        /// </summary>
        public void RestartApplication()
        {
            try
            {
                _logger.Log("Restarting application to apply updates...", LogLevel.INFO);
                
                // Get the executable path
                string exePath = Process.GetCurrentProcess().MainModule.FileName;
                
                // Start the application
                Process.Start(exePath);
                
                // Exit the current process
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error restarting application: {ex.Message}", LogLevel.ERROR);
            }
        }

        private bool IsNewVersionAvailable(string newVersion)
        {
            var current = Version.Parse(_currentVersion);
            var latest = Version.Parse(newVersion.TrimStart('v'));
            return latest > current;
        }

        private string GetCurrentVersion()
        {
            var version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version;
            return $"{version.Major}.{version.Minor}.{version.Build}";
        }

        private List<UpdateHistory> LoadUpdateHistory()
        {
            try
            {
                if (File.Exists(_historyFile))
                {
                    var json = File.ReadAllText(_historyFile);
                    return JsonSerializer.Deserialize<List<UpdateHistory>>(json) ?? new List<UpdateHistory>();
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading update history: {ex.Message}", LogLevel.ERROR);
            }
            return new List<UpdateHistory>();
        }

        private void SaveUpdateHistory()
        {
            try
            {
                var json = JsonSerializer.Serialize(_updateHistory, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_historyFile, json);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving update history: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Initializes the singleton instance of the UpdateService
        /// </summary>
        public static void Initialize(string githubToken, string owner, string repo)
        {
            if (_instance == null)
            {
                _instance = new UpdateService(githubToken, owner, repo);
            }
        }
    }

    /// <summary>
    /// Event arguments for update available events
    /// </summary>
    public class UpdateInfo
    {
        /// <summary>
        /// Gets the update version
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets the release notes
        /// </summary>
        public string ReleaseNotes { get; set; }

        /// <summary>
        /// Gets the download URL
        /// </summary>
        public string DownloadUrl { get; set; }
    }

    internal class GitHubRelease
    {
        public string tag_name { get; set; }
        public string body { get; set; }
        public GitHubAsset[] assets { get; set; }
    }

    internal class GitHubAsset
    {
        public string browser_download_url { get; set; }
    }
}

