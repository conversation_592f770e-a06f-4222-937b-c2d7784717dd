<UserControl x:Class="CircleUtility.Views.ToolsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="Black">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Text="Tools" 
                   FontFamily="Consolas" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20"/>
        
        <!-- Description -->
        <TextBlock Grid.Row="1" 
                   Text="Access various system tools to help maintain and optimize your system."
                   FontFamily="Segoe UI" 
                   FontSize="12" 
                   Foreground="White"
                   TextWrapping="Wrap"
                   Margin="0,0,0,20"/>
        
        <!-- Tools content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Tools list -->
            <Border Grid.Column="0" 
                    Background="#FF050A0F" 
                    BorderThickness="1" 
                    BorderBrush="#FF00C8FF" 
                    Padding="15" 
                    Margin="0,0,20,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Header -->
                    <TextBlock Text="Available Tools" 
                               FontFamily="Consolas" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="#FF00C8FF"
                               Margin="0,0,0,15"/>
                    
                    <!-- Tools list -->
                    <ListBox Grid.Row="1" 
                             ItemsSource="{Binding Tools}"
                             SelectedItem="{Binding SelectedTool}"
                             Background="Transparent"
                             BorderThickness="0"
                             Margin="0,0,0,15">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" 
                                           FontFamily="Consolas" 
                                           FontSize="12" 
                                           Foreground="White"
                                           Margin="0,5"/>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                    
                    <!-- Run button -->
                    <Button Grid.Row="2" 
                            Content="Run Tool" 
                            Command="{Binding RunToolCommand}"
                            Height="40" 
                            Background="#FF001428" 
                            Foreground="White" 
                            BorderBrush="#FF00C8FF" 
                            BorderThickness="1"/>
                </Grid>
            </Border>
            
            <!-- Tool description -->
            <Border Grid.Column="1" 
                    Background="#FF050A0F" 
                    BorderThickness="1" 
                    BorderBrush="#FF00C8FF" 
                    Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Header -->
                    <TextBlock Text="Tool Description" 
                               FontFamily="Consolas" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="#FF00C8FF"
                               Margin="0,0,0,15"/>
                    
                    <!-- Description content -->
                    <StackPanel Grid.Row="1">
                        <TextBlock Text="{Binding SelectedTool}" 
                                   FontFamily="Consolas" 
                                   FontSize="14" 
                                   FontWeight="Bold" 
                                   Foreground="White"
                                   Margin="0,0,0,10"/>
                        
                        <TextBlock Text="{Binding SelectedTool, Converter={StaticResource ToolToDescriptionConverter}}" 
                                   FontFamily="Segoe UI" 
                                   FontSize="12" 
                                   Foreground="#FFC0C0C0"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
