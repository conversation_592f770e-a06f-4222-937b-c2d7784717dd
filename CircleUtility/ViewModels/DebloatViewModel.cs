using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CircleUtility.Commands;
using CircleUtility.Models;
using CircleUtility.Services;
using CircleUtility.Interfaces;

namespace CircleUtility.ViewModels
{
    /// <summary>
    /// View model for the debloat view
    /// </summary>
    public class DebloatViewModel : ViewModelBase
    {
        private readonly LoggingService _logger;
        private readonly IUserTrackingService _userTracking;
        private SystemTweak _selectedTweak;
        private bool _isOperationRunning;

        /// <summary>
        /// Initializes a new instance of the DebloatViewModel class
        /// </summary>
        public DebloatViewModel(IUserTrackingService userTracking = null)
        {
            _logger = LoggingService.Instance;
            _userTracking = userTracking;
            
            // Initialize commands
            ApplyTweakCommand = new RelayCommand<SystemTweak>(ApplyTweak, CanApplyTweak);
            RevertTweakCommand = new RelayCommand<SystemTweak>(RevertTweak, CanRevertTweak);
            ApplyAllTweaksCommand = new RelayCommand(ApplyAllTweaks, () => !IsOperationRunning);
            RevertAllTweaksCommand = new RelayCommand(RevertAllTweaks, () => !IsOperationRunning);
            
            // Initialize debloat tweaks
            InitializeDebloatTweaks();
            
            _logger.Log("DebloatViewModel initialized", LogLevel.INFO);
        }

        /// <summary>
        /// Gets the collection of debloat tweaks
        /// </summary>
        public ObservableCollection<SystemTweak> DebloatTweaks { get; } = new ObservableCollection<SystemTweak>();

        /// <summary>
        /// Gets or sets the selected debloat tweak
        /// </summary>
        public SystemTweak SelectedTweak
        {
            get => _selectedTweak;
            set => SetProperty(ref _selectedTweak, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether an operation is running
        /// </summary>
        public bool IsOperationRunning
        {
            get => _isOperationRunning;
            set
            {
                if (SetProperty(ref _isOperationRunning, value))
                {
                    // Refresh command can execute status
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// Gets the apply tweak command
        /// </summary>
        public ICommand ApplyTweakCommand { get; }

        /// <summary>
        /// Gets the revert tweak command
        /// </summary>
        public ICommand RevertTweakCommand { get; }

        /// <summary>
        /// Gets the apply all tweaks command
        /// </summary>
        public ICommand ApplyAllTweaksCommand { get; }

        /// <summary>
        /// Gets the revert all tweaks command
        /// </summary>
        public ICommand RevertAllTweaksCommand { get; }

        /// <summary>
        /// Initializes the debloat tweaks
        /// </summary>
        private void InitializeDebloatTweaks()
        {
            // Windows apps tweaks
            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Remove Bloatware Apps",
                Description = "Removes pre-installed bloatware apps from Windows.",
                Category = "Windows Apps",
                ApplyAction = () => RemoveBloatwareApps(),
                RevertAction = () => RestoreBloatwareApps(),
                RequiresAdmin = true
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Windows Store Apps",
                Description = "Disables Windows Store apps from running in the background.",
                Category = "Windows Apps",
                ApplyAction = () => DisableWindowsStoreApps(),
                RevertAction = () => EnableWindowsStoreApps(),
                RequiresAdmin = true
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Remove Xbox Apps",
                Description = "Removes Xbox-related apps and services.",
                Category = "Windows Apps",
                ApplyAction = () => RemoveXboxApps(),
                RevertAction = () => RestoreXboxApps(),
                RequiresAdmin = true
            });

            // Windows services tweaks
            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Telemetry Services",
                Description = "Disables Windows telemetry and data collection services.",
                Category = "Windows Services",
                ApplyAction = () => DisableTelemetryServices(),
                RevertAction = () => EnableTelemetryServices(),
                RequiresAdmin = true
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Unnecessary Services",
                Description = "Disables unnecessary Windows services that consume system resources.",
                Category = "Windows Services",
                ApplyAction = () => DisableUnnecessaryServices(),
                RevertAction = () => EnableUnnecessaryServices(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Optimize Service Startup",
                Description = "Optimizes service startup types for better performance.",
                Category = "Windows Services",
                ApplyAction = () => OptimizeServiceStartup(),
                RevertAction = () => RevertServiceStartup(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            // Windows features tweaks
            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Windows Search Indexing",
                Description = "Disables Windows Search indexing to reduce disk and CPU usage.",
                Category = "Windows Features",
                ApplyAction = () => DisableWindowsSearchIndexing(),
                RevertAction = () => EnableWindowsSearchIndexing(),
                RequiresAdmin = true
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Superfetch/Prefetch",
                Description = "Disables Superfetch/Prefetch to reduce disk usage and improve SSD performance.",
                Category = "Windows Features",
                ApplyAction = () => DisableSuperfetch(),
                RevertAction = () => EnableSuperfetch(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Windows Defender",
                Description = "Disables Windows Defender for better performance (not recommended unless you have alternative protection).",
                Category = "Windows Features",
                ApplyAction = () => DisableWindowsDefender(),
                RevertAction = () => EnableWindowsDefender(),
                RequiresAdmin = true,
                RequiresRestart = true
            });

            // Privacy tweaks
            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Activity History",
                Description = "Disables Windows activity history tracking.",
                Category = "Privacy",
                ApplyAction = () => DisableActivityHistory(),
                RevertAction = () => EnableActivityHistory(),
                RequiresAdmin = false
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Location Tracking",
                Description = "Disables location tracking and services.",
                Category = "Privacy",
                ApplyAction = () => DisableLocationTracking(),
                RevertAction = () => EnableLocationTracking(),
                RequiresAdmin = false
            });

            DebloatTweaks.Add(new SystemTweak
            {
                Name = "Disable Advertising ID",
                Description = "Disables advertising ID tracking.",
                Category = "Privacy",
                ApplyAction = () => DisableAdvertisingId(),
                RevertAction = () => EnableAdvertisingId(),
                RequiresAdmin = false
            });
        }

        /// <summary>
        /// Determines whether a tweak can be applied
        /// </summary>
        /// <param name="tweak">The tweak to check</param>
        /// <returns>True if the tweak can be applied, false otherwise</returns>
        private bool CanApplyTweak(SystemTweak tweak)
        {
            return tweak != null && !IsOperationRunning && !tweak.IsApplied;
        }

        /// <summary>
        /// Determines whether a tweak can be reverted
        /// </summary>
        /// <param name="tweak">The tweak to check</param>
        /// <returns>True if the tweak can be reverted, false otherwise</returns>
        private bool CanRevertTweak(SystemTweak tweak)
        {
            return tweak != null && !IsOperationRunning && tweak.IsApplied;
        }

        /// <summary>
        /// Applies a debloat tweak
        /// </summary>
        /// <param name="tweak">The tweak to apply</param>
        private async void ApplyTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Applying debloat tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Debloat", $"Applied {tweak.Name}");
            
            IsOperationRunning = true;
            
            try
            {
                // Execute the tweak's action
                tweak.ApplyAction?.Invoke();
                
                // Simulate operation time
                await System.Threading.Tasks.Task.Delay(1000);
                
                tweak.IsApplied = true;
                
                _logger.Log($"Debloat tweak applied successfully: {tweak.Name}", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying debloat tweak: {ex.Message}", LogLevel.ERROR);
            }
            finally
            {
                IsOperationRunning = false;
            }
        }

        /// <summary>
        /// Reverts a debloat tweak
        /// </summary>
        /// <param name="tweak">The tweak to revert</param>
        private async void RevertTweak(SystemTweak tweak)
        {
            if (tweak == null)
                return;

            _logger.Log($"Reverting debloat tweak: {tweak.Name}", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Debloat", $"Reverted {tweak.Name}");
            
            IsOperationRunning = true;
            
            try
            {
                // Execute the tweak's revert action
                tweak.RevertAction?.Invoke();
                
                // Simulate operation time
                await System.Threading.Tasks.Task.Delay(1000);
                
                tweak.IsApplied = false;
                
                _logger.Log($"Debloat tweak reverted successfully: {tweak.Name}", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting debloat tweak: {ex.Message}", LogLevel.ERROR);
            }
            finally
            {
                IsOperationRunning = false;
            }
        }

        /// <summary>
        /// Applies all debloat tweaks
        /// </summary>
        private async void ApplyAllTweaks()
        {
            _logger.Log("Applying all debloat tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Debloat", "Applied all tweaks");
            
            IsOperationRunning = true;
            
            try
            {
                foreach (var tweak in DebloatTweaks)
                {
                    if (!tweak.IsApplied)
                    {
                        _logger.Log($"Applying debloat tweak: {tweak.Name}", LogLevel.INFO);
                        
                        // Execute the tweak's action
                        tweak.ApplyAction?.Invoke();
                        
                        // Simulate operation time
                        await System.Threading.Tasks.Task.Delay(500);
                        
                        tweak.IsApplied = true;
                    }
                }
                
                _logger.Log("All debloat tweaks applied successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error applying all debloat tweaks: {ex.Message}", LogLevel.ERROR);
            }
            finally
            {
                IsOperationRunning = false;
            }
        }

        /// <summary>
        /// Reverts all debloat tweaks
        /// </summary>
        private async void RevertAllTweaks()
        {
            _logger.Log("Reverting all debloat tweaks", LogLevel.INFO);
            _userTracking.AddUserActivity("User", "Debloat", "Reverted all tweaks");
            
            IsOperationRunning = true;
            
            try
            {
                foreach (var tweak in DebloatTweaks)
                {
                    if (tweak.IsApplied)
                    {
                        _logger.Log($"Reverting debloat tweak: {tweak.Name}", LogLevel.INFO);
                        
                        // Execute the tweak's revert action
                        tweak.RevertAction?.Invoke();
                        
                        // Simulate operation time
                        await System.Threading.Tasks.Task.Delay(500);
                        
                        tweak.IsApplied = false;
                    }
                }
                
                _logger.Log("All debloat tweaks reverted successfully", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error reverting all debloat tweaks: {ex.Message}", LogLevel.ERROR);
            }
            finally
            {
                IsOperationRunning = false;
            }
        }

        // Tweak implementation methods (these would be implemented in a full version)
        private void RemoveBloatwareApps() { }
        private void RestoreBloatwareApps() { }
        private void DisableWindowsStoreApps() { }
        private void EnableWindowsStoreApps() { }
        private void RemoveXboxApps() { }
        private void RestoreXboxApps() { }
        private void DisableTelemetryServices() { }
        private void EnableTelemetryServices() { }
        private void DisableUnnecessaryServices() { }
        private void EnableUnnecessaryServices() { }
        private void OptimizeServiceStartup() { }
        private void RevertServiceStartup() { }
        private void DisableWindowsSearchIndexing() { }
        private void EnableWindowsSearchIndexing() { }
        private void DisableSuperfetch() { }
        private void EnableSuperfetch() { }
        private void DisableWindowsDefender() { }
        private void EnableWindowsDefender() { }
        private void DisableActivityHistory() { }
        private void EnableActivityHistory() { }
        private void DisableLocationTracking() { }
        private void EnableLocationTracking() { }
        private void DisableAdvertisingId() { }
        private void EnableAdvertisingId() { }
    }
}



