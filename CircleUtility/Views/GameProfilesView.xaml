<UserControl x:Class="CircleUtility.Views.GameProfilesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="Black">

    <UserControl.Resources>
        <!-- Game profile panel style -->
        <Style x:Key="GameProfileStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF050A0F"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="CornerRadius" Value="0"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect ShadowDepth="0" BlurRadius="15" Color="#FF00C8FF" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Apply button style -->
        <Style x:Key="ApplyButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- Outer glow effect -->
                            <Border x:Name="GlowBorder"
                                    Background="#3300C8FF"
                                    CornerRadius="0"
                                    Margin="-2"
                                    Opacity="0">
                                <Border.Effect>
                                    <BlurEffect Radius="10"/>
                                </Border.Effect>
                            </Border>

                            <!-- Button background -->
                            <Border x:Name="ButtonBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Margin="{TemplateBinding Padding}"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="GlowBorder" Property="Opacity" Value="1"/>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#FF00FFFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF002850"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with glow effect -->
        <TextBlock Text="GAME PROFILES - 2025 CONFIGURATIONS"
                   FontFamily="Consolas"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,10">
            <TextBlock.Effect>
                <DropShadowEffect Color="#FF00C8FF" BlurRadius="15" ShadowDepth="0" Opacity="0.7"/>
            </TextBlock.Effect>
        </TextBlock>

        <!-- Description with futuristic styling -->
        <Border Grid.Row="1"
                Background="#FF050A0F"
                BorderBrush="#FF00C8FF"
                BorderThickness="1"
                Padding="15"
                Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            <TextBlock Text="Select a game profile to apply quantum-optimized settings for maximum competitive advantage. Each profile is calibrated with neural network analysis for optimal performance in 2025. Profiles include advanced rendering pipeline optimizations, neural-haptic feedback calibration, and AI-enhanced visibility settings."
                       FontFamily="Consolas"
                       FontSize="12"
                       Foreground="White"
                       TextWrapping="Wrap"/>
        </Border>

        <!-- Game profiles list -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Fortnite -->
                <Border Style="{StaticResource GameProfileStyle}" Height="160">
                    <Grid>
                        <!-- Left accent bar -->
                        <Rectangle Width="3" Height="160" Fill="#FF00C8FF" HorizontalAlignment="Left"/>

                        <!-- Top border -->
                        <Rectangle Height="1" Fill="#FF00C8FF" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Bottom border -->
                        <Rectangle Height="1" Fill="#FF0096C0" VerticalAlignment="Bottom"/>

                        <!-- Right border -->
                        <Rectangle Width="1" Fill="#FF00B4E0" HorizontalAlignment="Right" Margin="0,0,0,0"/>

                        <!-- Angled corner effect (top-left) -->
                        <Path Fill="#FF050A0F" Data="M0,0 L15,0 L0,15 Z" HorizontalAlignment="Left" VerticalAlignment="Top"/>

                        <!-- Diagonal effect for top-left corner -->
                        <Rectangle Width="15" Height="1" Fill="#FF00C8FF" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,15,0,0"/>
                        <Rectangle Width="1" Height="15" Fill="#FF00C8FF" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Content -->
                        <Grid Margin="20,10,20,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Game info -->
                            <StackPanel Grid.Column="0">
                                <!-- Title with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBlock Text=">> "
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FF00DCFF"/>
                                    <TextBlock Text="Fortnite 2025"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FF00DCFF"/>
                                </StackPanel>

                                <!-- Description with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="CFG://"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FF00B4E0"
                                               VerticalAlignment="Top"
                                               Margin="0,0,5,0"/>
                                    <TextBlock Text="2025 Pro Config: Ultra-competitive settings with DX12 optimizations, DLSS 3.5 integration, and zero input delay profile. Includes custom shader tweaks for enemy visibility through builds."
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               Foreground="White"
                                               TextWrapping="Wrap"
                                               MaxWidth="550"/>
                                </StackPanel>

                                <!-- 2025 label and apply button -->
                                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                                    <TextBlock Text="2025"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                                    <Button Content="APPLY CONFIG"
                                            Command="{Binding ApplyProfileCommand}"
                                            CommandParameter="{Binding GameProfiles[0]}"
                                            Style="{StaticResource ApplyButtonStyle}"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- ASCII Art -->
                            <TextBlock Grid.Column="1"
                                       Text="/\__/\&#x0a;/      \&#x0a;|  |/\|  |&#x0a;|  /  \  |&#x0a; \      /&#x0a;  \____/"
                                       FontFamily="Consolas"
                                       FontSize="11"
                                       Foreground="#FF00FFFF"
                                       VerticalAlignment="Center"
                                       HorizontalAlignment="Right"
                                       Margin="20,0,0,0"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Call of Duty 2025 -->
                <Border Style="{StaticResource GameProfileStyle}" Height="180">
                    <Grid>
                        <!-- Left accent bar -->
                        <Rectangle Width="3" Height="180" Fill="#FFFF3232" HorizontalAlignment="Left"/>

                        <!-- Top border -->
                        <Rectangle Height="1" Fill="#FFFF3232" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Bottom border -->
                        <Rectangle Height="1" Fill="#FFDC2828" VerticalAlignment="Bottom"/>

                        <!-- Right border -->
                        <Rectangle Width="1" Fill="#FFFF3232" HorizontalAlignment="Right" Margin="0,0,0,0"/>

                        <!-- Angled corner effect (top-left) -->
                        <Path Fill="#FF050A0F" Data="M0,0 L15,0 L0,15 Z" HorizontalAlignment="Left" VerticalAlignment="Top"/>

                        <!-- Diagonal effect for top-left corner -->
                        <Rectangle Width="15" Height="1" Fill="#FFFF3232" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,15,0,0"/>
                        <Rectangle Width="1" Height="15" Fill="#FFFF3232" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Content -->
                        <Grid Margin="20,10,20,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Game info -->
                            <StackPanel Grid.Column="0">
                                <!-- Title with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBlock Text=">> "
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232">
                                        <TextBlock.Effect>
                                            <DropShadowEffect Color="#FFFF3232" BlurRadius="10" ShadowDepth="0" Opacity="0.7"/>
                                        </TextBlock.Effect>
                                    </TextBlock>
                                    <TextBlock Text="Call of Duty 2025"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232">
                                        <TextBlock.Effect>
                                            <DropShadowEffect Color="#FFFF3232" BlurRadius="10" ShadowDepth="0" Opacity="0.7"/>
                                        </TextBlock.Effect>
                                    </TextBlock>
                                </StackPanel>

                                <!-- Description with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="CFG://"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"
                                               VerticalAlignment="Top"
                                               Margin="0,0,5,0"/>
                                    <TextBlock Text="2025 Pro Series Config: Next-gen AI-enhanced aim assist calibration, quantum rendering pipeline optimization, and neural-haptic feedback integration. Features advanced visibility through smoke and flash effects with custom FOV optimization for maximum target acquisition."
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               Foreground="White"
                                               TextWrapping="Wrap"
                                               MaxWidth="550"/>
                                </StackPanel>

                                <!-- 2025 label and apply button -->
                                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                                    <TextBlock Text="2025"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                                    <Button Content="APPLY CONFIG"
                                            Command="{Binding ApplyProfileCommand}"
                                            CommandParameter="{Binding GameProfiles[1]}"
                                            Style="{StaticResource ApplyButtonStyle}">
                                        <Button.Effect>
                                            <DropShadowEffect Color="#FFFF3232" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                                        </Button.Effect>
                                    </Button>
                                </StackPanel>
                            </StackPanel>

                            <!-- ASCII Art -->
                            <TextBlock Grid.Column="1"
                                       Text="   _____  ____  _____    ___   ___  ___  ___ &#x0a;  / ____||  _ \|  __ \  |__ \ / _ \|__ \|__ \&#x0a; | |     | |_) | |  | |    ) | | | |  ) |  ) |&#x0a; | |     |  _ &lt;| |  | |   / /| | | | / /  / / &#x0a; | |____ | |_) | |__| |  / /_| |_| |/ /_ / /_ &#x0a;  \_____||____/|_____/  |____|\___/|____|____|"
                                       FontFamily="Consolas"
                                       FontSize="8"
                                       Foreground="#FFFF3232"
                                       VerticalAlignment="Center"
                                       HorizontalAlignment="Right"
                                       Margin="20,0,0,0">
                                <TextBlock.Effect>
                                    <DropShadowEffect Color="#FFFF3232" BlurRadius="5" ShadowDepth="0" Opacity="0.5"/>
                                </TextBlock.Effect>
                            </TextBlock>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Valorant -->
                <Border Style="{StaticResource GameProfileStyle}" Height="160">
                    <Grid>
                        <!-- Left accent bar -->
                        <Rectangle Width="3" Height="160" Fill="#FFFF3232" HorizontalAlignment="Left"/>

                        <!-- Top border -->
                        <Rectangle Height="1" Fill="#FFFF3232" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Bottom border -->
                        <Rectangle Height="1" Fill="#FFDC2828" VerticalAlignment="Bottom"/>

                        <!-- Right border -->
                        <Rectangle Width="1" Fill="#FFFF3232" HorizontalAlignment="Right" Margin="0,0,0,0"/>

                        <!-- Angled corner effect (top-left) -->
                        <Path Fill="#FF0A0F14" Data="M0,0 L15,0 L0,15 Z" HorizontalAlignment="Left" VerticalAlignment="Top"/>

                        <!-- Diagonal effect for top-left corner -->
                        <Rectangle Width="15" Height="1" Fill="#FFFF3232" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,15,0,0"/>
                        <Rectangle Width="1" Height="15" Fill="#FFFF3232" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Content -->
                        <Grid Margin="20,10,20,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Game info -->
                            <StackPanel Grid.Column="0">
                                <!-- Title with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBlock Text=">> "
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"/>
                                    <TextBlock Text="Valorant 2025"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"/>
                                </StackPanel>

                                <!-- Description with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="CFG://"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"
                                               VerticalAlignment="Top"
                                               Margin="0,0,5,0"/>
                                    <TextBlock Text="2025 Pro Config: Optimized for maximum competitive advantage with enhanced enemy outlines, reduced visual clutter, and sub-1ms input latency. Includes registry tweaks for Riot Vanguard optimization."
                                               FontFamily="Segoe UI"
                                               FontSize="11"
                                               Foreground="White"
                                               TextWrapping="Wrap"
                                               MaxWidth="550"/>
                                </StackPanel>

                                <!-- 2025 label and apply button -->
                                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                                    <TextBlock Text="2025"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                                    <Button Content="APPLY CONFIG"
                                            Command="{Binding ApplyProfileCommand}"
                                            CommandParameter="{Binding GameProfiles[2]}"
                                            Style="{StaticResource ApplyButtonStyle}"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- ASCII Art -->
                            <TextBlock Grid.Column="1"
                                       Text="  /\  &#x0a; /  \ &#x0a;/    \&#x0a;\    /&#x0a; \  / &#x0a;  \/  "
                                       FontFamily="Consolas"
                                       FontSize="11"
                                       Foreground="#FFFF3232"
                                       VerticalAlignment="Center"
                                       HorizontalAlignment="Right"
                                       Margin="20,0,0,0"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Counter-Strike 2 -->
                <Border Style="{StaticResource GameProfileStyle}" Height="160">
                    <Grid>
                        <!-- Left accent bar -->
                        <Rectangle Width="3" Height="160" Fill="#FF00FF96" HorizontalAlignment="Left"/>

                        <!-- Top border -->
                        <Rectangle Height="1" Fill="#FF00FF96" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Bottom border -->
                        <Rectangle Height="1" Fill="#FF00DC82" VerticalAlignment="Bottom"/>

                        <!-- Right border -->
                        <Rectangle Width="1" Fill="#FF00FF96" HorizontalAlignment="Right" Margin="0,0,0,0"/>

                        <!-- Angled corner effect (top-left) -->
                        <Path Fill="#FF0A0F14" Data="M0,0 L15,0 L0,15 Z" HorizontalAlignment="Left" VerticalAlignment="Top"/>

                        <!-- Diagonal effect for top-left corner -->
                        <Rectangle Width="15" Height="1" Fill="#FF00FF96" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,15,0,0"/>
                        <Rectangle Width="1" Height="15" Fill="#FF00FF96" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Content -->
                        <Grid Margin="20,10,20,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Game info -->
                            <StackPanel Grid.Column="0">
                                <!-- Title with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBlock Text=">> "
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FF00FF96"/>
                                    <TextBlock Text="Counter-Strike 2"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FF00FF96"/>
                                </StackPanel>

                                <!-- Description with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="CFG://"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FF00FF96"
                                               VerticalAlignment="Top"
                                               Margin="0,0,5,0"/>
                                    <TextBlock Text="2025 Pro Config: Source 2 engine optimizations with custom shader settings for maximum visibility. Includes advanced network settings for optimal peeker's advantage and reduced server variance."
                                               FontFamily="Segoe UI"
                                               FontSize="11"
                                               Foreground="White"
                                               TextWrapping="Wrap"
                                               MaxWidth="550"/>
                                </StackPanel>

                                <!-- 2025 label and apply button -->
                                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                                    <TextBlock Text="2025"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                                    <Button Content="APPLY CONFIG"
                                            Command="{Binding ApplyProfileCommand}"
                                            CommandParameter="{Binding GameProfiles[4]}"
                                            Style="{StaticResource ApplyButtonStyle}"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- ASCII Art -->
                            <TextBlock Grid.Column="1"
                                       Text="   _   &#x0a;  | |  &#x0a; /   \ &#x0a;|     |&#x0a; \___/ &#x0a;   |   "
                                       FontFamily="Consolas"
                                       FontSize="11"
                                       Foreground="#FF00FF96"
                                       VerticalAlignment="Center"
                                       HorizontalAlignment="Right"
                                       Margin="20,0,0,0"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Apex Legends -->
                <Border Style="{StaticResource GameProfileStyle}" Height="160">
                    <Grid>
                        <!-- Left accent bar -->
                        <Rectangle Width="3" Height="160" Fill="#FFFF9600" HorizontalAlignment="Left"/>

                        <!-- Top border -->
                        <Rectangle Height="1" Fill="#FFFF9600" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Bottom border -->
                        <Rectangle Height="1" Fill="#FFDC8200" VerticalAlignment="Bottom"/>

                        <!-- Right border -->
                        <Rectangle Width="1" Fill="#FFFF9600" HorizontalAlignment="Right" Margin="0,0,0,0"/>

                        <!-- Angled corner effect (top-left) -->
                        <Path Fill="#FF0A0F14" Data="M0,0 L15,0 L0,15 Z" HorizontalAlignment="Left" VerticalAlignment="Top"/>

                        <!-- Diagonal effect for top-left corner -->
                        <Rectangle Width="15" Height="1" Fill="#FFFF9600" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,15,0,0"/>
                        <Rectangle Width="1" Height="15" Fill="#FFFF9600" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Content -->
                        <Grid Margin="20,10,20,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Game info -->
                            <StackPanel Grid.Column="0">
                                <!-- Title with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBlock Text=">> "
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FFFF9600"/>
                                    <TextBlock Text="Apex Legends"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FFFF9600"/>
                                </StackPanel>

                                <!-- Description with prefix -->
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="CFG://"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF9600"
                                               VerticalAlignment="Top"
                                               Margin="0,0,5,0"/>
                                    <TextBlock Text="2025 Pro Config: Source engine tweaks with optimized particle effects for better visibility. Includes advanced autoexec commands for reduced recoil patterns and enhanced aim assist calibration for controller players."
                                               FontFamily="Segoe UI"
                                               FontSize="11"
                                               Foreground="White"
                                               TextWrapping="Wrap"
                                               MaxWidth="550"/>
                                </StackPanel>

                                <!-- 2025 label and apply button -->
                                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                                    <TextBlock Text="2025"
                                               FontFamily="Consolas"
                                               FontSize="11"
                                               FontWeight="Bold"
                                               Foreground="#FFFF3232"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                                    <Button Content="APPLY CONFIG"
                                            Command="{Binding ApplyProfileCommand}"
                                            CommandParameter="{Binding GameProfiles[3]}"
                                            Style="{StaticResource ApplyButtonStyle}"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- ASCII Art -->
                            <TextBlock Grid.Column="1"
                                       Text="   /\   &#x0a;  /  \  &#x0a; / /\ \ &#x0a;/ /  \ \&#x0a;\/    \/"
                                       FontFamily="Consolas"
                                       FontSize="11"
                                       Foreground="#FFFF9600"
                                       VerticalAlignment="Center"
                                       HorizontalAlignment="Right"
                                       Margin="20,0,0,0"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Request Game button -->
                <Border Background="#FF050505" BorderThickness="1" BorderBrush="#FFFF3232" Height="60" Margin="0,20,0,0">
                    <Grid>
                        <!-- Angled corner effect (top-left) -->
                        <Path Fill="#FF050505" Data="M0,0 L15,0 L0,15 Z" HorizontalAlignment="Left" VerticalAlignment="Top"/>

                        <!-- Diagonal effect for top-left corner -->
                        <Rectangle Width="15" Height="1" Fill="#FFFF3232" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0,15,0,0"/>
                        <Rectangle Width="1" Height="15" Fill="#FFFF3232" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="15,0,0,0"/>

                        <!-- Content -->
                        <Button Command="{Binding RequestGameCommand}" Background="Transparent" BorderThickness="0">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                                <TextBlock Text=">> "
                                           FontFamily="Consolas"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           Foreground="#FFFF3232"/>
                                <TextBlock Text="REQUEST 2025 GAME CONFIG"
                                           FontFamily="Consolas"
                                           FontSize="14"
                                           FontWeight="Bold"
                                           Foreground="White"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
