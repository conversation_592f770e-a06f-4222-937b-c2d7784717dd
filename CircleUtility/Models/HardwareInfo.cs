// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for hardware information
    /// </summary>
    public class HardwareInfo
    {
        /// <summary>
        /// Gets or sets the CPU information
        /// </summary>
        public CPUInfo CPU { get; set; }

        /// <summary>
        /// Gets or sets the GPU information
        /// </summary>
        public GPUInfo GPU { get; set; }

        /// <summary>
        /// Gets or sets the RAM information
        /// </summary>
        public RAMInfo RAM { get; set; }

        /// <summary>
        /// Gets or sets the storage information
        /// </summary>
        public List<StorageInfo> Storage { get; set; }

        /// <summary>
        /// Gets or sets the network adapter information
        /// </summary>
        public List<NetworkAdapterInfo> NetworkAdapters { get; set; }

        /// <summary>
        /// Gets or sets the motherboard information
        /// </summary>
        public MotherboardInfo Motherboard { get; set; }

        /// <summary>
        /// Gets or sets the operating system information
        /// </summary>
        public OSInfo OperatingSystem { get; set; }

        /// <summary>
        /// Initializes a new instance of the HardwareInfo class
        /// </summary>
        public HardwareInfo()
        {
            CPU = new CPUInfo();
            GPU = new GPUInfo();
            RAM = new RAMInfo();
            Storage = new List<StorageInfo>();
            NetworkAdapters = new List<NetworkAdapterInfo>();
            Motherboard = new MotherboardInfo();
            OperatingSystem = new OSInfo();
        }
    }

    /// <summary>
    /// Model for CPU information
    /// </summary>
    public class CPUInfo
    {
        /// <summary>
        /// Gets or sets the CPU name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the CPU manufacturer
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the number of CPU cores
        /// </summary>
        public int Cores { get; set; }

        /// <summary>
        /// Gets or sets the number of logical processors (threads)
        /// </summary>
        public int LogicalProcessors { get; set; }

        /// <summary>
        /// Gets or sets the number of threads
        /// </summary>
        public int Threads { get; set; }

        /// <summary>
        /// Gets or sets the base clock speed in GHz
        /// </summary>
        public double BaseClockSpeed { get; set; }

        /// <summary>
        /// Gets or sets the maximum clock speed in MHz
        /// </summary>
        public int MaxClockSpeed { get; set; }

        /// <summary>
        /// Gets or sets the CPU architecture
        /// </summary>
        public string Architecture { get; set; }

        /// <summary>
        /// Gets or sets the L2 cache size in KB
        /// </summary>
        public int L2CacheSize { get; set; }

        /// <summary>
        /// Gets or sets the L3 cache size in KB
        /// </summary>
        public int L3CacheSize { get; set; }

        /// <summary>
        /// Gets the formatted maximum clock speed
        /// </summary>
        public string FormattedMaxClockSpeed => $"{MaxClockSpeed / 1000.0:F2} GHz";

        /// <summary>
        /// Gets the formatted L2 cache size
        /// </summary>
        public string FormattedL2CacheSize => $"{L2CacheSize / 1024.0:F2} MB";

        /// <summary>
        /// Gets the formatted L3 cache size
        /// </summary>
        public string FormattedL3CacheSize => $"{L3CacheSize / 1024.0:F2} MB";
    }

    // GPUInfo is defined in GPUInfo.cs

    /// <summary>
    /// Model for RAM information
    /// </summary>
    public class RAMInfo
    {
        /// <summary>
        /// Gets or sets the total RAM capacity in GB
        /// </summary>
        public long TotalCapacity { get; set; }

        /// <summary>
        /// Gets or sets the RAM modules
        /// </summary>
        public List<RAMModuleInfo> Modules { get; set; }

        /// <summary>
        /// Initializes a new instance of the RAMInfo class
        /// </summary>
        public RAMInfo()
        {
            Modules = new List<RAMModuleInfo>();
        }
    }

    /// <summary>
    /// Model for RAM module information
    /// </summary>
    public class RAMModuleInfo
    {
        /// <summary>
        /// Gets or sets the RAM module manufacturer
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the RAM module part number
        /// </summary>
        public string PartNumber { get; set; }

        /// <summary>
        /// Gets or sets the RAM module capacity in GB
        /// </summary>
        public long Capacity { get; set; }

        /// <summary>
        /// Gets or sets the RAM module speed in MHz
        /// </summary>
        public int Speed { get; set; }
    }

    /// <summary>
    /// Model for storage information
    /// </summary>
    public class StorageInfo
    {
        /// <summary>
        /// Gets or sets the storage model
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// Gets or sets the storage manufacturer
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the storage interface type
        /// </summary>
        public string InterfaceType { get; set; }

        /// <summary>
        /// Gets or sets the storage size in GB
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Gets the formatted storage size
        /// </summary>
        public string FormattedSize => Size >= 1024 ? $"{Size / 1024.0:F2} TB" : $"{Size:F2} GB";
    }

    /// <summary>
    /// Model for network adapter information
    /// </summary>
    public class NetworkAdapterInfo
    {
        /// <summary>
        /// Gets or sets the network adapter name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the network adapter manufacturer
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the network adapter MAC address
        /// </summary>
        public string MACAddress { get; set; }

        /// <summary>
        /// Gets or sets the network adapter type
        /// </summary>
        public string AdapterType { get; set; }

        /// <summary>
        /// Gets or sets the network adapter speed in Mbps
        /// </summary>
        public long Speed { get; set; }

        /// <summary>
        /// Gets the formatted network adapter speed
        /// </summary>
        public string FormattedSpeed => Speed >= 1000 ? $"{Speed / 1000.0:F2} Gbps" : $"{Speed:F2} Mbps";
    }

    /// <summary>
    /// Model for motherboard information
    /// </summary>
    public class MotherboardInfo
    {
        /// <summary>
        /// Gets or sets the motherboard manufacturer
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Gets or sets the motherboard model
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// Gets or sets the motherboard serial number
        /// </summary>
        public string SerialNumber { get; set; }
    }

    /// <summary>
    /// Model for operating system information
    /// </summary>
    public class OSInfo
    {
        /// <summary>
        /// Gets or sets the operating system name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the operating system version
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the operating system architecture
        /// </summary>
        public string Architecture { get; set; }

        /// <summary>
        /// Gets or sets the operating system build number
        /// </summary>
        public string BuildNumber { get; set; }

        /// <summary>
        /// Gets or sets the operating system install date
        /// </summary>
        public DateTime InstallDate { get; set; }
    }
}
