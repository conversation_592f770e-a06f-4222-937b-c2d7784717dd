using System.Windows.Controls;
using CircleUtility.ViewModels;

namespace CircleUtility.Views
{
    /// <summary>
    /// Interaction logic for AdminView.xaml
    /// </summary>
    public partial class AdminView : UserControl
    {
        /// <summary>
        /// Initializes a new instance of the AdminView class
        /// </summary>
        public AdminView()
        {
            InitializeComponent();

            // Set the DataContext to a new instance of AdminViewModel
            DataContext = new AdminViewModel();
        }
    }
}
