namespace CircleUtility.Models
{
    public enum LogLevel
    {
        DEBUG,
        INFO,
        SUCCESS,
        WARNING,
        ERROR,
        CRITICAL
    }

    public enum ServiceStatus
    {
        NotInitialized,
        Initializing,
        Running,
        Stopping,
        Stopped,
        Failed
    }

    public enum HardwareType
    {
        CPU,
        GPU,
        RAM,
        Storage,
        Network,
        Motherboard,
        System,
        Other
    }

    public enum OptimizationType
    {
        Performance,
        PowerEfficiency,
        Balanced,
        Gaming,
        SystemTweak,
        NetworkTweak,
        RegistryTweak,
        Custom,
        LowLatency
    }

    public enum SecurityLevel
    {
        Low,
        Medium,
        High,
        Custom
    }

    public enum Theme
    {
        Light,
        Dark,
        System,
        Custom
    }

    public enum WindowState
    {
        Normal,
        Minimized,
        Maximized,
        Loading,
        Error
    }

    public enum UpdateStatus
    {
        UpToDate,
        UpdateAvailable,
        UpdateInProgress,
        UpdateFailed,
        CheckingForUpdates
    }

    public enum BackupType
    {
        Full,
        Incremental,
        Differential,
        Settings
    }

    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error,
        System
    }

    public enum WDDMVersion
    {
        Unknown,
        Unsupported,
        WDDM_1_0,
        WDDM_1_1,
        WDDM_1_2,
        WDDM_1_3,
        WDDM_2_0,
        WDDM_2_1,
        WDDM_2_2,
        WDDM_2_3,
        WDDM_2_4,
        WDDM_2_5,
        WDDM_2_6,
        WDDM_2_7,
        WDDM_2_8,
        WDDM_2_9,
        WDDM_3_0,
        WDDM_3_1,
        WDDM_3_2,
        LatestKnown
    }

    public enum HAGSStatus
    {
        Unknown,
        Unsupported,
        Disabled,
        Enabled
    }

    public enum PageFileUsageType
    {
        Unknown,
        Disabled,
        SystemManaged,
        Custom
    }
} 