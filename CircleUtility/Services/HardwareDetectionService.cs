// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Text.Json;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for detecting and caching hardware information
    /// </summary>
    public class HardwareDetectionService : IHardwareDetectionService
    {
        private static HardwareDetectionService _instance;
        private readonly LoggingService _logger;
        private readonly string _cacheDirectory;
        private readonly string _hardwareCacheFilePath;
        private HardwareInfo _cachedHardwareInfo;
        private DateTime _lastCacheUpdate;
        private readonly TimeSpan _cacheExpirationTime = TimeSpan.FromHours(1);
        private bool _isInitialized;

        // Memory cache for frequently accessed hardware components
        private readonly Dictionary<string, object> _memoryCache = new Dictionary<string, object>();
        private readonly Dictionary<string, DateTime> _memoryCacheTimestamps = new Dictionary<string, DateTime>();
        private readonly TimeSpan _memoryCacheExpiration = TimeSpan.FromMinutes(5); // Memory cache valid for 5 minutes
        private readonly object _memoryCacheLock = new object();

        /// <summary>
        /// Initializes a new instance of the HardwareDetectionService class
        /// </summary>
        private HardwareDetectionService()
        {
            _logger = LoggingService.Instance;
            _cacheDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Cache");
            _hardwareCacheFilePath = Path.Combine(_cacheDirectory, "hardware_cache.json");
            _lastCacheUpdate = DateTime.MinValue;

            // Ensure cache directory exists
            if (!Directory.Exists(_cacheDirectory))
            {
                Directory.CreateDirectory(_cacheDirectory);
            }

            // Initialize the service
            Initialize();
        }

        /// <summary>
        /// Gets the singleton instance of the hardware detection service
        /// </summary>
        public static HardwareDetectionService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new HardwareDetectionService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the hardware detection service
        /// </summary>
        private void Initialize()
        {
            try
            {
                // Load cached hardware info
                LoadHardwareCache();

                _isInitialized = true;
                _logger.Log("Hardware detection service initialized", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing hardware detection service: {ex.Message}", LogLevel.ERROR);
                _isInitialized = false;
            }
        }

        /// <summary>
        /// Loads hardware cache from disk
        /// </summary>
        private void LoadHardwareCache()
        {
            try
            {
                if (File.Exists(_hardwareCacheFilePath))
                {
                    string json = File.ReadAllText(_hardwareCacheFilePath);
                    HardwareCacheData cacheData = JsonSerializer.Deserialize<HardwareCacheData>(json);

                    if (cacheData != null)
                    {
                        _cachedHardwareInfo = cacheData.HardwareInfo;
                        _lastCacheUpdate = cacheData.LastUpdated;

                        _logger.Log("Hardware cache loaded", LogLevel.INFO);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error loading hardware cache: {ex.Message}", LogLevel.ERROR);
                _cachedHardwareInfo = null;
                _lastCacheUpdate = DateTime.MinValue;
            }
        }

        /// <summary>
        /// Saves hardware cache to disk
        /// </summary>
        private void SaveHardwareCache()
        {
            try
            {
                if (_cachedHardwareInfo != null)
                {
                    HardwareCacheData cacheData = new HardwareCacheData
                    {
                        HardwareInfo = _cachedHardwareInfo,
                        LastUpdated = DateTime.Now
                    };

                    string json = JsonSerializer.Serialize(cacheData, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(_hardwareCacheFilePath, json);

                    _logger.Log("Hardware cache saved", LogLevel.INFO);
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error saving hardware cache: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets hardware information
        /// </summary>
        /// <returns>The hardware information</returns>
        public HardwareInfo GetHardwareInfo()
        {
            return GetHardwareInfo(false);
        }

        /// <summary>
        /// Gets hardware information
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the cache</param>
        /// <returns>The hardware information</returns>
        public HardwareInfo GetHardwareInfo(bool forceRefresh)
        {
            try
            {
                // Check if cache is valid
                if (!forceRefresh && _cachedHardwareInfo != null && (DateTime.Now - _lastCacheUpdate) < _cacheExpirationTime)
                {
                    _logger.Log("Using cached hardware info", LogLevel.INFO);
                    return _cachedHardwareInfo;
                }

                // If forcing refresh, clear memory cache
                if (forceRefresh)
                {
                    _logger.Log("Forcing hardware refresh, clearing memory cache", LogLevel.INFO);
                    ClearMemoryCache();
                }

                // Detect hardware
                _logger.Log("Detecting hardware...", LogLevel.INFO);
                _cachedHardwareInfo = DetectHardware();
                _lastCacheUpdate = DateTime.Now;

                // Save cache
                SaveHardwareCache();

                return _cachedHardwareInfo;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting hardware info: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Gets hardware information asynchronously
        /// </summary>
        /// <returns>The hardware information</returns>
        public async Task<HardwareInfo> GetHardwareInfoAsync()
        {
            return await GetHardwareInfoAsync(false);
        }

        /// <summary>
        /// Gets hardware information asynchronously
        /// </summary>
        /// <param name="forceRefresh">Whether to force a refresh of the cache</param>
        /// <returns>The hardware information</returns>
        public async Task<HardwareInfo> GetHardwareInfoAsync(bool forceRefresh)
        {
            return await Task.Run(() => GetHardwareInfo(forceRefresh));
        }

        /// <summary>
        /// Detects hardware
        /// </summary>
        /// <returns>The hardware information</returns>
        private HardwareInfo DetectHardware()
        {
            try
            {
                HardwareInfo hardwareInfo = new HardwareInfo();

                // Detect CPU
                hardwareInfo.CPU = DetectCPU();

                // Detect GPU
                hardwareInfo.GPU = DetectGPU();

                // Detect RAM
                hardwareInfo.RAM = DetectRAM();

                // Detect storage
                hardwareInfo.Storage = DetectStorage();

                // Detect network adapters
                hardwareInfo.NetworkAdapters = DetectNetworkAdapters();

                // Detect motherboard
                hardwareInfo.Motherboard = DetectMotherboard();

                // Detect operating system
                hardwareInfo.OperatingSystem = DetectOperatingSystem();

                return hardwareInfo;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting hardware: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Gets or adds an item to the memory cache
        /// </summary>
        /// <typeparam name="T">The type of the item</typeparam>
        /// <param name="key">The cache key</param>
        /// <param name="factory">The factory function to create the item if not in cache</param>
        /// <returns>The cached or newly created item</returns>
        private T GetOrAddToMemoryCache<T>(string key, Func<T> factory)
        {
            lock (_memoryCacheLock)
            {
                // Check if the item is in the cache and not expired
                if (_memoryCache.TryGetValue(key, out object cachedItem) &&
                    _memoryCacheTimestamps.TryGetValue(key, out DateTime timestamp) &&
                    (DateTime.Now - timestamp) < _memoryCacheExpiration)
                {
                    return (T)cachedItem;
                }

                // Item not in cache or expired, create new item
                T newItem = factory();

                // Add or update cache
                _memoryCache[key] = newItem;
                _memoryCacheTimestamps[key] = DateTime.Now;

                return newItem;
            }
        }

        /// <summary>
        /// Clears the memory cache
        /// </summary>
        public void ClearMemoryCache()
        {
            lock (_memoryCacheLock)
            {
                _memoryCache.Clear();
                _memoryCacheTimestamps.Clear();
                _logger.Log("Memory cache cleared", LogLevel.INFO);
            }
        }

        /// <summary>
        /// Detects CPU information
        /// </summary>
        /// <returns>The CPU information</returns>
        private CPUInfo DetectCPU()
        {
            return GetOrAddToMemoryCache("CPU", () =>
            {
                try
                {
                    CPUInfo cpuInfo = new CPUInfo();

                    using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            cpuInfo.Name = obj["Name"]?.ToString();
                            cpuInfo.Manufacturer = obj["Manufacturer"]?.ToString();
                            cpuInfo.Cores = Convert.ToInt32(obj["NumberOfCores"]);
                            cpuInfo.LogicalProcessors = Convert.ToInt32(obj["NumberOfLogicalProcessors"]);
                            cpuInfo.MaxClockSpeed = Convert.ToInt32(obj["MaxClockSpeed"]);
                            cpuInfo.Architecture = obj["Architecture"]?.ToString();
                            cpuInfo.L2CacheSize = Convert.ToInt32(obj["L2CacheSize"] ?? 0);
                            cpuInfo.L3CacheSize = Convert.ToInt32(obj["L3CacheSize"] ?? 0);
                            break; // Only get the first CPU
                        }
                    }

                    return cpuInfo;
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error detecting CPU: {ex.Message}", LogLevel.ERROR);
                    return new CPUInfo { Name = "Unknown" };
                }
            });
        }

        /// <summary>
        /// Detects GPU information
        /// </summary>
        /// <returns>The GPU information</returns>
        private GPUInfo DetectGPU()
        {
            return GetOrAddToMemoryCache("GPU", () =>
            {
                try
                {
                    GPUInfo gpuInfo = new GPUInfo();

                    using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            gpuInfo.Name = obj["Name"]?.ToString();
                            // Set vendor based on manufacturer
                            string manufacturer = obj["AdapterCompatibility"]?.ToString();
                            if (manufacturer != null)
                            {
                                if (manufacturer.Contains("NVIDIA", StringComparison.OrdinalIgnoreCase))
                                {
                                    gpuInfo.Vendor = GPUVendor.NVIDIA;
                                }
                                else if (manufacturer.Contains("AMD", StringComparison.OrdinalIgnoreCase) ||
                                         manufacturer.Contains("ATI", StringComparison.OrdinalIgnoreCase))
                                {
                                    gpuInfo.Vendor = GPUVendor.AMD;
                                }
                                else if (manufacturer.Contains("Intel", StringComparison.OrdinalIgnoreCase))
                                {
                                    gpuInfo.Vendor = GPUVendor.Intel;
                                }
                                else
                                {
                                    gpuInfo.Vendor = GPUVendor.Other;
                                }
                            }
                            gpuInfo.DriverVersion = obj["DriverVersion"]?.ToString();
                            gpuInfo.VideoMemory = Convert.ToInt64(obj["AdapterRAM"] ?? 0) / (1024 * 1024); // Convert to MB
                            gpuInfo.CurrentResolution = $"{obj["CurrentHorizontalResolution"]} x {obj["CurrentVerticalResolution"]}";
                            gpuInfo.RefreshRate = Convert.ToInt32(obj["CurrentRefreshRate"] ?? 0);
                            break; // Only get the first GPU
                        }
                    }

                    return gpuInfo;
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error detecting GPU: {ex.Message}", LogLevel.ERROR);
                    return new GPUInfo { Name = "Unknown" };
                }
            });
        }

        /// <summary>
        /// Detects RAM information
        /// </summary>
        /// <returns>The RAM information</returns>
        private RAMInfo DetectRAM()
        {
            return GetOrAddToMemoryCache("RAM", () =>
            {
                try
                {
                    RAMInfo ramInfo = new RAMInfo();
                    long totalMemory = 0;

                    using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMemory"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            totalMemory += Convert.ToInt64(obj["Capacity"]);
                            ramInfo.Modules.Add(new RAMModuleInfo
                            {
                                Manufacturer = obj["Manufacturer"]?.ToString(),
                                PartNumber = obj["PartNumber"]?.ToString(),
                                Capacity = Convert.ToInt64(obj["Capacity"]) / (1024 * 1024 * 1024), // Convert to GB
                                Speed = Convert.ToInt32(obj["Speed"] ?? 0)
                            });
                        }
                    }

                    ramInfo.TotalCapacity = totalMemory / (1024 * 1024 * 1024); // Convert to GB

                    return ramInfo;
                }
                catch (Exception ex)
                {
                    _logger.Log($"Error detecting RAM: {ex.Message}", LogLevel.ERROR);
                    return new RAMInfo { TotalCapacity = 0 };
                }
            });
        }

        /// <summary>
        /// Detects storage information
        /// </summary>
        /// <returns>The storage information</returns>
        private List<StorageInfo> DetectStorage()
        {
            try
            {
                List<StorageInfo> storageInfoList = new List<StorageInfo>();

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        StorageInfo storageInfo = new StorageInfo
                        {
                            Model = obj["Model"]?.ToString(),
                            Manufacturer = obj["Manufacturer"]?.ToString(),
                            InterfaceType = obj["InterfaceType"]?.ToString(),
                            Size = Convert.ToInt64(obj["Size"] ?? 0) / (1024 * 1024 * 1024) // Convert to GB
                        };

                        storageInfoList.Add(storageInfo);
                    }
                }

                return storageInfoList;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting storage: {ex.Message}", LogLevel.ERROR);
                return new List<StorageInfo>();
            }
        }

        /// <summary>
        /// Detects network adapter information
        /// </summary>
        /// <returns>The network adapter information</returns>
        private List<NetworkAdapterInfo> DetectNetworkAdapters()
        {
            try
            {
                List<NetworkAdapterInfo> networkAdapterInfoList = new List<NetworkAdapterInfo>();

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_NetworkAdapter WHERE PhysicalAdapter = True"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        NetworkAdapterInfo networkAdapterInfo = new NetworkAdapterInfo
                        {
                            Name = obj["Name"]?.ToString(),
                            Manufacturer = obj["Manufacturer"]?.ToString(),
                            MACAddress = obj["MACAddress"]?.ToString(),
                            AdapterType = obj["AdapterType"]?.ToString(),
                            Speed = Convert.ToInt64(obj["Speed"] ?? 0) / (1000 * 1000) // Convert to Mbps
                        };

                        networkAdapterInfoList.Add(networkAdapterInfo);
                    }
                }

                return networkAdapterInfoList;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting network adapters: {ex.Message}", LogLevel.ERROR);
                return new List<NetworkAdapterInfo>();
            }
        }

        /// <summary>
        /// Detects motherboard information
        /// </summary>
        /// <returns>The motherboard information</returns>
        private MotherboardInfo DetectMotherboard()
        {
            try
            {
                MotherboardInfo motherboardInfo = new MotherboardInfo();

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        motherboardInfo.Manufacturer = obj["Manufacturer"]?.ToString();
                        motherboardInfo.Model = obj["Product"]?.ToString();
                        motherboardInfo.SerialNumber = obj["SerialNumber"]?.ToString();
                        break; // Only get the first motherboard
                    }
                }

                return motherboardInfo;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting motherboard: {ex.Message}", LogLevel.ERROR);
                return new MotherboardInfo { Manufacturer = "Unknown", Model = "Unknown" };
            }
        }

        /// <summary>
        /// Detects operating system information
        /// </summary>
        /// <returns>The operating system information</returns>
        private OSInfo DetectOperatingSystem()
        {
            try
            {
                OSInfo osInfo = new OSInfo();

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        osInfo.Name = obj["Caption"]?.ToString();
                        osInfo.Version = obj["Version"]?.ToString();
                        osInfo.Architecture = obj["OSArchitecture"]?.ToString();
                        osInfo.BuildNumber = obj["BuildNumber"]?.ToString();
                        osInfo.InstallDate = ManagementDateTimeConverter.ToDateTime(obj["InstallDate"]?.ToString());
                        break; // Only get the first OS
                    }
                }

                return osInfo;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error detecting operating system: {ex.Message}", LogLevel.ERROR);
                return new OSInfo { Name = "Unknown", Version = "Unknown" };
            }
        }
    }

    /// <summary>
    /// Model for hardware cache data
    /// </summary>
    public class HardwareCacheData
    {
        /// <summary>
        /// Gets or sets the hardware information
        /// </summary>
        public HardwareInfo HardwareInfo { get; set; }

        /// <summary>
        /// Gets or sets the last updated date
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }
}


