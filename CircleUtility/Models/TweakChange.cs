using System;
using System.Text.Json.Serialization;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a tweak change
    /// </summary>
    public class TweakChange
    {
        /// <summary>
        /// Gets or sets the unique identifier for the change
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the type of change
        /// </summary>
        public TweakChangeType Type { get; set; }

        /// <summary>
        /// Gets or sets the name of the tweak
        /// </summary>
        public string TweakName { get; set; }

        /// <summary>
        /// Gets or sets the type of tweak
        /// </summary>
        public OptimizationType TweakType { get; set; }

        /// <summary>
        /// Gets or sets the date the change was applied
        /// </summary>
        public DateTime AppliedDate { get; set; }

        /// <summary>
        /// Gets or sets the date the change was reverted (if applicable)
        /// </summary>
        public DateTime? RevertedDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the change is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the registry key path (for registry changes)
        /// </summary>
        public string RegistryKeyPath { get; set; }

        /// <summary>
        /// Gets or sets the registry value name (for registry changes)
        /// </summary>
        public string RegistryValueName { get; set; }

        /// <summary>
        /// Gets or sets the file path (for file system changes)
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// Gets or sets the backup file path (for file system changes)
        /// </summary>
        public string BackupFilePath { get; set; }

        /// <summary>
        /// Gets or sets the service name (for service changes)
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// Gets or sets the original value
        /// </summary>
        public string OriginalValue { get; set; }

        /// <summary>
        /// Gets or sets the new value
        /// </summary>
        public string NewValue { get; set; }

        /// <summary>
        /// Gets or sets additional data for the change (if applicable)
        /// </summary>
        public string AdditionalData { get; set; }

        /// <summary>
        /// Gets the formatted applied date
        /// </summary>
        [JsonIgnore]
        public string FormattedAppliedDate => AppliedDate.ToString("MMM dd, yyyy HH:mm:ss");

        /// <summary>
        /// Gets the formatted reverted date
        /// </summary>
        [JsonIgnore]
        public string FormattedRevertedDate => RevertedDate?.ToString("MMM dd, yyyy HH:mm:ss") ?? "Not reverted";

        /// <summary>
        /// Gets a description of the change
        /// </summary>
        [JsonIgnore]
        public string Description
        {
            get
            {
                switch (Type)
                {
                    case TweakChangeType.Registry:
                        return $"Registry: {RegistryKeyPath}\\{RegistryValueName} changed from '{OriginalValue}' to '{NewValue}'";
                    case TweakChangeType.FileSystem:
                        return $"File: {FilePath} modified (backup at {BackupFilePath})";
                    case TweakChangeType.Service:
                        return $"Service: {ServiceName} start type changed from {OriginalValue} to {NewValue}";
                    default:
                        return $"Other: {TweakName}";
                }
            }
        }
    }

    /// <summary>
    /// Represents the type of tweak change
    /// </summary>
    public enum TweakChangeType
    {
        /// <summary>
        /// Registry change
        /// </summary>
        Registry,

        /// <summary>
        /// File system change
        /// </summary>
        FileSystem,

        /// <summary>
        /// Service configuration change
        /// </summary>
        Service,

        /// <summary>
        /// Other change
        /// </summary>
        Other
    }

    /// <summary>
    /// Event arguments for tweak change events
    /// </summary>
    public class TweakChangeEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the change
        /// </summary>
        public TweakChange Change { get; set; }
    }
}
