// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for game information
    /// </summary>
    public class GameInfo
    {
        /// <summary>
        /// Gets or sets the name of the game
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the process name of the game
        /// </summary>
        public string ProcessName { get; set; }

        /// <summary>
        /// Gets or sets the genre of the game
        /// </summary>
        public string Genre { get; set; }

        /// <summary>
        /// Gets or sets the publisher of the game
        /// </summary>
        public string Publisher { get; set; }

        /// <summary>
        /// Gets or sets the executable path of the game
        /// </summary>
        public string ExecutablePath { get; set; }

        /// <summary>
        /// Gets or sets the installation directory of the game
        /// </summary>
        public string InstallationDirectory { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether optimization is available for this game
        /// </summary>
        public bool OptimizationAvailable { get; set; }

        /// <summary>
        /// Gets or sets the icon path for the game
        /// </summary>
        public string IconPath { get; set; }

        /// <summary>
        /// Gets or sets the banner image path for the game
        /// </summary>
        public string BannerPath { get; set; }

        /// <summary>
        /// Gets or sets the ASCII art for the game
        /// </summary>
        public string AsciiArt { get; set; }

        /// <summary>
        /// Gets or sets the recommended settings for the game
        /// </summary>
        public Dictionary<string, string> RecommendedSettings { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Gets or sets the optimization tips for the game
        /// </summary>
        public List<string> OptimizationTips { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the launch parameters for the game
        /// </summary>
        public string LaunchParameters { get; set; }

        /// <summary>
        /// Gets or sets the last played date
        /// </summary>
        public DateTime? LastPlayed { get; set; }

        /// <summary>
        /// Gets or sets the total play time in minutes
        /// </summary>
        public int TotalPlayTimeMinutes { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the game is currently installed
        /// </summary>
        public bool IsInstalled { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the game is currently running
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// Gets or sets the version of the game
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the release date of the game
        /// </summary>
        public DateTime? ReleaseDate { get; set; }

        /// <summary>
        /// Gets or sets the minimum system requirements for the game
        /// </summary>
        public SystemRequirements MinimumRequirements { get; set; } = new SystemRequirements();

        /// <summary>
        /// Gets or sets the recommended system requirements for the game
        /// </summary>
        public SystemRequirements RecommendedRequirements { get; set; } = new SystemRequirements();

        /// <summary>
        /// Gets or sets the performance profile for the game
        /// </summary>
        public GamePerformanceProfile PerformanceProfile { get; set; } = new GamePerformanceProfile();

        /// <summary>
        /// Gets the formatted play time
        /// </summary>
        public string FormattedPlayTime
        {
            get
            {
                if (TotalPlayTimeMinutes < 60)
                {
                    return $"{TotalPlayTimeMinutes} minutes";
                }
                else if (TotalPlayTimeMinutes < 1440) // Less than a day
                {
                    int hours = TotalPlayTimeMinutes / 60;
                    int minutes = TotalPlayTimeMinutes % 60;
                    return $"{hours} hour{(hours != 1 ? "s" : "")} {minutes} minute{(minutes != 1 ? "s" : "")}";
                }
                else
                {
                    int days = TotalPlayTimeMinutes / 1440;
                    int hours = (TotalPlayTimeMinutes % 1440) / 60;
                    return $"{days} day{(days != 1 ? "s" : "")} {hours} hour{(hours != 1 ? "s" : "")}";
                }
            }
        }

        /// <summary>
        /// Gets the last played formatted string
        /// </summary>
        public string LastPlayedFormatted
        {
            get
            {
                if (!LastPlayed.HasValue)
                {
                    return "Never";
                }

                TimeSpan timeSince = DateTime.Now - LastPlayed.Value;
                
                if (timeSince.TotalDays < 1)
                {
                    if (timeSince.TotalHours < 1)
                    {
                        return "Just now";
                    }
                    return $"{(int)timeSince.TotalHours} hour{((int)timeSince.TotalHours != 1 ? "s" : "")} ago";
                }
                else if (timeSince.TotalDays < 30)
                {
                    return $"{(int)timeSince.TotalDays} day{((int)timeSince.TotalDays != 1 ? "s" : "")} ago";
                }
                else
                {
                    return LastPlayed.Value.ToString("MMM dd, yyyy");
                }
            }
        }

        /// <summary>
        /// Gets the status of the game
        /// </summary>
        public string Status
        {
            get
            {
                if (IsRunning)
                {
                    return "Running";
                }
                else if (IsInstalled)
                {
                    return "Installed";
                }
                else
                {
                    return "Not Installed";
                }
            }
        }

        /// <summary>
        /// Gets the status color code
        /// </summary>
        public string StatusColorCode
        {
            get
            {
                if (IsRunning)
                {
                    return "#00FF00"; // Green
                }
                else if (IsInstalled)
                {
                    return "#00C8FF"; // Blue
                }
                else
                {
                    return "#FF8800"; // Orange
                }
            }
        }
    }

    /// <summary>
    /// Model for system requirements
    /// </summary>
    public class SystemRequirements
    {
        /// <summary>
        /// Gets or sets the CPU requirement
        /// </summary>
        public string CPU { get; set; }

        /// <summary>
        /// Gets or sets the RAM requirement
        /// </summary>
        public string RAM { get; set; }

        /// <summary>
        /// Gets or sets the GPU requirement
        /// </summary>
        public string GPU { get; set; }

        /// <summary>
        /// Gets or sets the storage requirement
        /// </summary>
        public string Storage { get; set; }

        /// <summary>
        /// Gets or sets the OS requirement
        /// </summary>
        public string OS { get; set; }

        /// <summary>
        /// Gets or sets the DirectX requirement
        /// </summary>
        public string DirectX { get; set; }

        /// <summary>
        /// Gets or sets the network requirement
        /// </summary>
        public string Network { get; set; }
    }

    /// <summary>
    /// Model for game performance profile
    /// </summary>
    public class GamePerformanceProfile
    {
        /// <summary>
        /// Gets or sets the CPU intensity (0-100)
        /// </summary>
        public int CpuIntensity { get; set; }

        /// <summary>
        /// Gets or sets the GPU intensity (0-100)
        /// </summary>
        public int GpuIntensity { get; set; }

        /// <summary>
        /// Gets or sets the RAM usage (0-100)
        /// </summary>
        public int RamUsage { get; set; }

        /// <summary>
        /// Gets or sets the storage usage (0-100)
        /// </summary>
        public int StorageUsage { get; set; }

        /// <summary>
        /// Gets or sets the network usage (0-100)
        /// </summary>
        public int NetworkUsage { get; set; }

        /// <summary>
        /// Gets or sets the input sensitivity (0-100)
        /// </summary>
        public int InputSensitivity { get; set; }

        /// <summary>
        /// Gets or sets the average FPS at 1080p with medium settings
        /// </summary>
        public int AverageFps1080p { get; set; }

        /// <summary>
        /// Gets or sets the average FPS at 1440p with medium settings
        /// </summary>
        public int AverageFps1440p { get; set; }

        /// <summary>
        /// Gets or sets the average FPS at 4K with medium settings
        /// </summary>
        public int AverageFps4K { get; set; }

        /// <summary>
        /// Gets the CPU intensity description
        /// </summary>
        public string CpuIntensityDescription
        {
            get
            {
                if (CpuIntensity >= 80) return "Very High";
                if (CpuIntensity >= 60) return "High";
                if (CpuIntensity >= 40) return "Medium";
                if (CpuIntensity >= 20) return "Low";
                return "Very Low";
            }
        }

        /// <summary>
        /// Gets the GPU intensity description
        /// </summary>
        public string GpuIntensityDescription
        {
            get
            {
                if (GpuIntensity >= 80) return "Very High";
                if (GpuIntensity >= 60) return "High";
                if (GpuIntensity >= 40) return "Medium";
                if (GpuIntensity >= 20) return "Low";
                return "Very Low";
            }
        }

        /// <summary>
        /// Gets the RAM usage description
        /// </summary>
        public string RamUsageDescription
        {
            get
            {
                if (RamUsage >= 80) return "Very High";
                if (RamUsage >= 60) return "High";
                if (RamUsage >= 40) return "Medium";
                if (RamUsage >= 20) return "Low";
                return "Very Low";
            }
        }
    }
}
