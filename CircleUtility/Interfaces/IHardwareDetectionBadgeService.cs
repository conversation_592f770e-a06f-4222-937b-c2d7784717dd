namespace CircleUtility.Interfaces
{
    /// <summary>
    /// Interface for hardware detection badge service
    /// </summary>
    public interface IHardwareDetectionBadgeService
    {
        /// <summary>
        /// Gets hardware badges
        /// </summary>
        /// <returns>Hardware badges information</returns>
        object GetHardwareBadges();

        /// <summary>
        /// Updates hardware badges
        /// </summary>
        void UpdateBadges();
    }
}
