// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for providing hardware-specific optimization recommendations
    /// </summary>
    public class HardwareRecommendationService : IHardwareRecommendationService
    {
        // Use a static field for the logger to avoid initialization in the constructor
        private static readonly LoggingService _staticLogger = LoggingService.Instance;

        // Use fields instead of properties to avoid initialization during property access
        private bool _isInitialized;
        private bool _isInitializing;

        // Services will be injected after initialization
        private IHardwareDetectionService _hardwareDetectionService;
        private IHardwareOptimizationService _optimizationService;
        private IHardwareCompatibilityService _compatibilityService;

        /// <summary>
        /// Initializes a new instance of the HardwareRecommendationService class
        /// </summary>
        /// <summary>
        /// Parameterless constructor for singleton pattern
        /// </summary>
        private HardwareRecommendationService()
        {
            // Initialize with default values
        }


        /// <summary>
        /// Initializes a new instance of the HardwareRecommendationService class with dependencies
        /// </summary>
        /// <param name="hardwareDetectionService">The hardware detection service</param>
        /// <param name="optimizationService">The hardware optimization service</param>
        /// <param name="compatibilityService">The hardware compatibility service</param>

        /// <summary>
        /// Initializes a new instance of the HardwareRecommendationService class with dependencies (for testing)
        /// </summary>
        /// <param name="hardwareDetectionService">The hardware detection service</param>
        /// <param name="optimizationService">The hardware optimization service</param>
        /// <param name="compatibilityService">The hardware compatibility service</param>
        /// <summary>
        /// Parameterless constructor for singleton pattern
        /// </summary>


        /// <summary>
        /// Public constructor for DI compatibility
        /// </summary>
        /// <param name="forDI">Indicates if the constructor is called for DI</param>
        /// <summary>
        /// Parameterless constructor for singleton pattern
        /// </summary>


        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task InitializeAsync()
        {
            // If already initialized or initializing, return
            if (_isInitialized || _isInitializing)
            {
                return;
            }

            try
            {
                _isInitializing = true;
                _staticLogger.Log("Initializing hardware recommendation service...", LogLevel.INFO);

                // Check if dependencies are injected
                if (_hardwareDetectionService == null || _optimizationService == null || _compatibilityService == null)
                {
                    _staticLogger.Log("Dependencies not injected yet, deferring initialization", LogLevel.WARNING);
                    _isInitializing = false;
                    return;
                }

                // Perform some async work to make this method truly async
                await Task.Delay(1); // Minimal delay to make the compiler happy

                // Verify that the services we depend on are initialized
                if (_hardwareDetectionService != null && !_hardwareDetectionService.IsInitialized)
                {
                    _staticLogger.Log("Waiting for HardwareDetectionService to initialize...", LogLevel.INFO);
                    await Task.Delay(100); // Give it a moment
                }

                if (_optimizationService != null && !_optimizationService.IsInitialized)
                {
                    _staticLogger.Log("Waiting for HardwareOptimizationService to initialize...", LogLevel.INFO);
                    await Task.Delay(100); // Give it a moment
                }

                if (_compatibilityService != null && !_compatibilityService.IsInitialized)
                {
                    _staticLogger.Log("Waiting for HardwareCompatibilityService to initialize...", LogLevel.INFO);
                    await Task.Delay(100); // Give it a moment
                }

                _isInitialized = true;
                _isInitializing = false;
                _staticLogger.Log("Hardware recommendation service initialized", LogLevel.SUCCESS);
            }
            catch (Exception ex)
            {
                _isInitializing = false;
                _staticLogger.Log($"Error initializing hardware recommendation service: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Gets recommended optimizations for the current hardware
        /// </summary>
        /// <returns>The recommended optimizations</returns>
        public async Task<List<OptimizationRecommendation>> GetRecommendedOptimizationsAsync()
        {
            List<OptimizationRecommendation> recommendations = new List<OptimizationRecommendation>();

            try
            {
                // Check if initialized
                if (!_isInitialized)
                {
                    await InitializeAsync();

                    // If still not initialized, return empty list
                    if (!_isInitialized)
                    {
                        _staticLogger.Log("Service not initialized, cannot get recommendations", LogLevel.ERROR);
                        return recommendations;
                    }
                }

                // Check if dependencies are injected
                if (_hardwareDetectionService == null || _optimizationService == null || _compatibilityService == null)
                {
                    _staticLogger.Log("Dependencies not injected, cannot get recommendations", LogLevel.ERROR);
                    return recommendations;
                }

                // Get all optimizations
                var allOptimizations = _optimizationService.Optimizations;

                if (allOptimizations == null || allOptimizations.Count == 0)
                {
                    _staticLogger.Log("No optimizations available", LogLevel.WARNING);
                    return recommendations;
                }

                // Get hardware info
                var hardwareInfo = _hardwareDetectionService.GetHardwareInfo();

                if (hardwareInfo == null)
                {
                    _staticLogger.Log("Failed to get hardware info for recommendations", LogLevel.ERROR);
                    return recommendations;
                }

                // Check compatibility of each optimization
                foreach (var optimization in allOptimizations)
                {
                    var compatibilityResult = await _compatibilityService.ValidateOptimizationAsync(optimization);

                    if (compatibilityResult.IsCompatible)
                    {
                        // Calculate recommendation score based on various factors
                        int score = CalculateRecommendationScore(optimization, hardwareInfo);

                        // Add to recommendations if score is high enough
                        if (score >= 70)
                        {
                            recommendations.Add(new OptimizationRecommendation
                            {
                                Optimization = optimization,
                                RecommendationScore = score,
                                RecommendationReason = GetRecommendationReason(optimization, hardwareInfo, score),
                                HardwareCompatibility = ConvertToHardwareCompatibility(compatibilityResult),
                                ConfidenceLevel = CalculateConfidenceLevel(optimization, hardwareInfo)
                            });
                        }
                    }
                }

                // Sort recommendations by score (descending)
                recommendations = recommendations
                    .OrderByDescending(r => r.RecommendationScore)
                    .ToList();
            }
            catch (Exception ex)
            {
                _staticLogger.Log($"Error getting recommended optimizations: {ex.Message}", LogLevel.ERROR);
            }

            return recommendations;
        }

        /// <summary>
        /// Gets recommended tweaks for a specific hardware component
        /// </summary>
        /// <param name="hardwareType">The hardware type</param>
        /// <returns>A list of optimization recommendations</returns>
        public async Task<List<OptimizationRecommendation>> GetRecommendedTweaksForHardwareAsync(HardwareType hardwareType)
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                // Get all recommendations
                var allRecommendations = await GetRecommendedOptimizationsAsync();

                // Filter by hardware type
                return allRecommendations
                    .Where(r => r.Optimization.HardwareType == hardwareType)
                    .ToList();
            }
            catch (Exception ex)
            {
                _staticLogger.Log($"Error getting recommended tweaks for hardware type {hardwareType}: {ex.Message}", LogLevel.ERROR);
                return new List<OptimizationRecommendation>();
            }
        }

        /// <summary>
        /// Gets the recommendation score for a specific optimization
        /// </summary>
        /// <param name="optimizationName">The optimization name</param>
        /// <returns>The recommendation score (0-100)</returns>
        public async Task<int> GetRecommendationScoreAsync(string optimizationName)
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                // Get all recommendations
                var allRecommendations = await GetRecommendedOptimizationsAsync();

                // Find the recommendation
                var recommendation = allRecommendations.FirstOrDefault(r => r.Optimization.Name == optimizationName);

                // Return the score if found, otherwise return a default score
                return recommendation?.RecommendationScore ?? 50;
            }
            catch (Exception ex)
            {
                _staticLogger.Log($"Error getting recommendation score for optimization {optimizationName}: {ex.Message}", LogLevel.ERROR);
                return 50;
            }
        }

        /// <summary>
        /// Gets recommended power profiles for the current hardware
        /// </summary>
        /// <returns>The recommended power profiles</returns>
        public async Task<List<PowerProfileRecommendation>> GetRecommendedPowerProfilesAsync()
        {
            try
            {
                // Check if initialized
                if (!_isInitialized)
                {
                    await InitializeAsync();

                    // If still not initialized, return empty list
                    if (!_isInitialized)
                    {
                        _staticLogger.Log("Service not initialized, cannot get power profile recommendations", LogLevel.ERROR);
                        return new List<PowerProfileRecommendation>();
                    }
                }

                List<PowerProfileRecommendation> recommendations = new List<PowerProfileRecommendation>();

                // Check if dependencies are injected
                if (_hardwareDetectionService == null || _optimizationService == null || _compatibilityService == null)
                {
                    _staticLogger.Log("Dependencies not injected, cannot get power profile recommendations", LogLevel.ERROR);
                    return recommendations;
                }

                // Get all power profiles
                var allProfiles = _optimizationService.PowerProfiles;

                if (allProfiles == null || allProfiles.Count == 0)
                {
                    _staticLogger.Log("No power profiles available", LogLevel.WARNING);
                    return recommendations;
                }

                // Get hardware info
                var hardwareInfo = _hardwareDetectionService.GetHardwareInfo();

                if (hardwareInfo == null)
                {
                    _staticLogger.Log("Failed to get hardware info for power profile recommendations", LogLevel.ERROR);
                    return recommendations;
                }

                // Check compatibility of each power profile
                foreach (var profile in allProfiles)
                {
                    var compatibilityResult = await _compatibilityService.ValidatePowerProfileAsync(profile);

                    if (compatibilityResult.IsCompatible)
                    {
                        // Calculate recommendation score based on various factors
                        int score = CalculateRecommendationScore(profile, hardwareInfo);

                        // Add to recommendations if score is high enough
                        if (score >= 70)
                        {
                            recommendations.Add(new PowerProfileRecommendation
                            {
                                PowerProfile = profile,
                                RecommendationScore = score,
                                RecommendationReason = GetRecommendationReason(profile, hardwareInfo, score),
                                HardwareCompatibility = ConvertToHardwareCompatibility(compatibilityResult),
                                ConfidenceLevel = CalculateConfidenceLevel(profile, hardwareInfo)
                            });
                        }
                    }
                }

                // Sort recommendations by score (descending)
                recommendations = recommendations
                    .OrderByDescending(r => r.RecommendationScore)
                    .ToList();

                return recommendations;
            }
            catch (Exception ex)
            {
                _staticLogger.Log($"Error getting recommended power profiles: {ex.Message}", LogLevel.ERROR);
                return new List<PowerProfileRecommendation>();
            }
        }

        /// <summary>
        /// Calculates the recommendation score for an optimization (exposed for testing)
        /// </summary>
        /// <param name="optimization">The optimization</param>
        /// <param name="hardwareInfo">The hardware info</param>
        /// <returns>The recommendation score</returns>
        public int CalculateRecommendationScoreForTesting(HardwareSpecificOptimization optimization, HardwareInfo hardwareInfo)
        {
            return CalculateRecommendationScore(optimization, hardwareInfo);
        }

        private int CalculateRecommendationScore(HardwareSpecificOptimization optimization, HardwareInfo hardwareInfo)
        {
            // Base score starts at 50
            int score = 50;

            // Add points for performance impact
            score += optimization.PerformanceImpact / 5;

            // Add points for input latency impact
            score += optimization.InputLatencyImpact / 5;

            // Subtract points for risk
            switch (optimization.Risk)
            {
                case RiskLevel.Safe:
                    // No penalty
                    break;
                case RiskLevel.Low:
                    score -= 5;
                    break;
                case RiskLevel.Medium:
                    score -= 10;
                    break;
                case RiskLevel.High:
                    score -= 20;
                    break;
                case RiskLevel.VeryHigh:
                    score -= 30;
                    break;
            }

            // Add points for hardware-specific optimizations
            if (optimization.HardwareType == HardwareType.CPU &&
                !string.IsNullOrEmpty(optimization.Manufacturer) &&
                hardwareInfo.CPU?.Manufacturer.Contains(optimization.Manufacturer, StringComparison.OrdinalIgnoreCase) == true)
            {
                score += 15;
            }

            if (optimization.HardwareType == HardwareType.GPU &&
                !string.IsNullOrEmpty(optimization.Manufacturer) &&
                hardwareInfo.GPU?.Vendor.ToString().Contains(optimization.Manufacturer, StringComparison.OrdinalIgnoreCase) == true)
            {
                score += 15;
            }

            // Ensure score is within bounds
            score = Math.Max(0, Math.Min(100, score));

            return score;
        }

        private int CalculateRecommendationScore(PowerManagementProfile profile, HardwareInfo hardwareInfo)
        {
            // Base score starts at 50
            int score = 50;

            // Add points for performance impact
            score += profile.PerformanceImpact / 5;

            // Add points for input latency impact
            score += profile.InputLatencyImpact / 5;

            // Subtract points for thermal impact
            score -= profile.ThermalImpact / 10;

            // Subtract points for risk
            switch (profile.Risk)
            {
                case RiskLevel.Safe:
                    // No penalty
                    break;
                case RiskLevel.Low:
                    score -= 5;
                    break;
                case RiskLevel.Medium:
                    score -= 10;
                    break;
                case RiskLevel.High:
                    score -= 20;
                    break;
                case RiskLevel.VeryHigh:
                    score -= 30;
                    break;
            }

            // Add points for hardware-specific profiles
            if (profile.HardwareCompatibility.Count > 0)
            {
                string cpuModel = hardwareInfo.CPU?.Name ?? string.Empty;
                string gpuModel = hardwareInfo.GPU?.Name ?? string.Empty;

                foreach (string hardware in profile.HardwareCompatibility)
                {
                    if (cpuModel.Contains(hardware, StringComparison.OrdinalIgnoreCase) ||
                        gpuModel.Contains(hardware, StringComparison.OrdinalIgnoreCase))
                    {
                        score += 20;
                        break;
                    }
                }
            }

            // Ensure score is within bounds
            score = Math.Max(0, Math.Min(100, score));

            return score;
        }

        private string GetRecommendationReason(HardwareSpecificOptimization optimization, HardwareInfo hardwareInfo, int score)
        {
            if (score >= 90)
            {
                return $"Highly recommended for your {GetHardwareTypeString(optimization.HardwareType)} configuration";
            }
            else if (score >= 80)
            {
                return $"Recommended for optimal performance with your {GetHardwareTypeString(optimization.HardwareType)}";
            }
            else if (score >= 70)
            {
                return $"Good performance improvement for your system";
            }
            else
            {
                return "May provide some performance benefits";
            }
        }

        private string GetRecommendationReason(PowerManagementProfile profile, HardwareInfo hardwareInfo, int score)
        {
            if (score >= 90)
            {
                return "Highly recommended for your hardware configuration";
            }
            else if (score >= 80)
            {
                return "Recommended for optimal performance with your system";
            }
            else if (score >= 70)
            {
                return $"Good balance of performance and efficiency for your system";
            }
            else
            {
                return "May provide some performance benefits";
            }
        }

        /// <summary>
        /// Calculates the confidence level for an optimization recommendation
        /// </summary>
        /// <param name="optimization">The optimization</param>
        /// <param name="hardwareInfo">The hardware info</param>
        /// <returns>The confidence level (0-100)</returns>
        private int CalculateConfidenceLevel(HardwareSpecificOptimization optimization, HardwareInfo hardwareInfo)
        {
            // Base confidence level starts at 70
            int confidenceLevel = 70;

            // Adjust based on hardware match
            if (optimization.HardwareType == HardwareType.CPU)
            {
                if (!string.IsNullOrEmpty(optimization.Manufacturer) &&
                    hardwareInfo.CPU?.Manufacturer.Contains(optimization.Manufacturer, StringComparison.OrdinalIgnoreCase) == true)
                {
                    confidenceLevel += 15;

                    // Additional confidence if we have a specific model match
                    if (!string.IsNullOrEmpty(optimization.ModelPattern) &&
                        hardwareInfo.CPU?.Name.Contains(optimization.ModelPattern, StringComparison.OrdinalIgnoreCase) == true)
                    {
                        confidenceLevel += 10;
                    }
                }
            }
            else if (optimization.HardwareType == HardwareType.GPU)
            {
                if (!string.IsNullOrEmpty(optimization.Manufacturer) &&
                    hardwareInfo.GPU?.Vendor.ToString().Contains(optimization.Manufacturer, StringComparison.OrdinalIgnoreCase) == true)
                {
                    confidenceLevel += 15;

                    // Additional confidence if we have a specific model match
                    if (!string.IsNullOrEmpty(optimization.ModelPattern) &&
                        hardwareInfo.GPU?.Name.Contains(optimization.ModelPattern, StringComparison.OrdinalIgnoreCase) == true)
                    {
                        confidenceLevel += 10;
                    }
                }
            }

            // Adjust based on risk level
            switch (optimization.Risk)
            {
                case RiskLevel.Safe:
                    confidenceLevel += 5;
                    break;
                case RiskLevel.Low:
                    // No adjustment
                    break;
                case RiskLevel.Medium:
                    confidenceLevel -= 5;
                    break;
                case RiskLevel.High:
                    confidenceLevel -= 10;
                    break;
                case RiskLevel.VeryHigh:
                    confidenceLevel -= 15;
                    break;
            }

            // Adjust based on testing status
            if (optimization.TestingStatus == TestingStatus.Extensive)
            {
                confidenceLevel += 10;
            }
            else if (optimization.TestingStatus == TestingStatus.Limited)
            {
                confidenceLevel -= 5;
            }
            else if (optimization.TestingStatus == TestingStatus.Minimal)
            {
                confidenceLevel -= 10;
            }

            // Ensure confidence level is within bounds
            confidenceLevel = Math.Max(0, Math.Min(100, confidenceLevel));

            return confidenceLevel;
        }

        /// <summary>
        /// Calculates the confidence level for a power profile recommendation
        /// </summary>
        /// <param name="profile">The power profile</param>
        /// <param name="hardwareInfo">The hardware info</param>
        /// <returns>The confidence level (0-100)</returns>
        private int CalculateConfidenceLevel(PowerManagementProfile profile, HardwareInfo hardwareInfo)
        {
            // Base confidence level starts at 70
            int confidenceLevel = 70;

            // Adjust based on hardware match
            string cpuModel = hardwareInfo.CPU?.Name ?? string.Empty;
            string gpuModel = hardwareInfo.GPU?.Name ?? string.Empty;

            foreach (string hardware in profile.HardwareCompatibility)
            {
                if (cpuModel.Contains(hardware, StringComparison.OrdinalIgnoreCase) ||
                    gpuModel.Contains(hardware, StringComparison.OrdinalIgnoreCase))
                {
                    confidenceLevel += 15;
                    break;
                }
            }

            // Adjust based on risk level
            switch (profile.Risk)
            {
                case RiskLevel.Safe:
                    confidenceLevel += 5;
                    break;
                case RiskLevel.Low:
                    // No adjustment
                    break;
                case RiskLevel.Medium:
                    confidenceLevel -= 5;
                    break;
                case RiskLevel.High:
                    confidenceLevel -= 10;
                    break;
                case RiskLevel.VeryHigh:
                    confidenceLevel -= 15;
                    break;
            }

            // Adjust based on thermal impact
            if (profile.ThermalImpact < 30)
            {
                confidenceLevel += 10;
            }
            else if (profile.ThermalImpact < 50)
            {
                confidenceLevel += 5;
            }
            else if (profile.ThermalImpact > 70)
            {
                confidenceLevel -= 10;
            }
            else if (profile.ThermalImpact > 60)
            {
                confidenceLevel -= 5;
            }

            // Ensure confidence level is within bounds
            confidenceLevel = Math.Max(0, Math.Min(100, confidenceLevel));

            return confidenceLevel;
        }

        /// <summary>
        /// Converts a CompatibilityResult to a HardwareCompatibility
        /// </summary>
        /// <param name="result">The compatibility result</param>
        /// <returns>The hardware compatibility</returns>
        private HardwareCompatibility ConvertToHardwareCompatibility(CompatibilityResult result)
        {
            // Convert confidence to score
            int compatibilityScore = 50;
            switch (result.Confidence)
            {
                case CompatibilityConfidence.Low:
                    compatibilityScore = 30;
                    break;
                case CompatibilityConfidence.Medium:
                    compatibilityScore = 60;
                    break;
                case CompatibilityConfidence.High:
                    compatibilityScore = 90;
                    break;
            }

            return new HardwareCompatibility
            {
                IsCompatible = result.IsCompatible,
                IncompatibilityReason = result.IncompatibilityReason,
                CompatibilityScore = compatibilityScore,
                IsVerified = result.LastValidated > DateTime.Now.AddDays(-7) // Consider verified if validated within the last week
            };
        }

        private string GetHardwareTypeString(HardwareType hardwareType)
        {
            switch (hardwareType)
            {
                case HardwareType.CPU:
                    return "CPU";
                case HardwareType.GPU:
                    return "GPU";
                case HardwareType.RAM:
                    return "RAM";
                case HardwareType.Storage:
                    return "storage";
                case HardwareType.Network:
                    return "network";
                case HardwareType.Motherboard:
                    return "motherboard";
                case HardwareType.System:
                    return "system";
                default:
                    return "hardware";
            }
        }
    }

    /// <summary>
    /// Optimization recommendation
    /// </summary>
    public class OptimizationRecommendation
    {
        /// <summary>
        /// Gets or sets the optimization
        /// </summary>
        public HardwareSpecificOptimization Optimization { get; set; }

        /// <summary>
        /// Gets or sets the recommendation score (0-100)
        /// </summary>
        public int RecommendationScore { get; set; }

        /// <summary>
        /// Gets or sets the recommendation reason
        /// </summary>
        public string RecommendationReason { get; set; }

        /// <summary>
        /// Gets or sets the hardware compatibility
        /// </summary>
        public HardwareCompatibility HardwareCompatibility { get; set; }

        /// <summary>
        /// Gets or sets the confidence level (0-100)
        /// </summary>
        public int ConfidenceLevel { get; set; }
    }

    /// <summary>
    /// Power profile recommendation
    /// </summary>
    public class PowerProfileRecommendation
    {
        /// <summary>
        /// Gets or sets the power profile
        /// </summary>
        public PowerManagementProfile PowerProfile { get; set; }

        /// <summary>
        /// Gets or sets the recommendation score (0-100)
        /// </summary>
        public int RecommendationScore { get; set; }

        /// <summary>
        /// Gets or sets the recommendation reason
        /// </summary>
        public string RecommendationReason { get; set; }

        /// <summary>
        /// Gets or sets the hardware compatibility
        /// </summary>
        public HardwareCompatibility HardwareCompatibility { get; set; }

        /// <summary>
        /// Gets or sets the confidence level (0-100)
        /// </summary>
        public int ConfidenceLevel { get; set; }
    }
}








