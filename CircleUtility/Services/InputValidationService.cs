// Created by Arsenal on 5-17-25 12:15PM
using System;
using CircleUtility.Models;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using CircleUtility.Helpers;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for validating user input
    /// </summary>
    public class InputValidationService
    {
        private static InputValidationService _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of the InputValidationService
        /// </summary>
        public static InputValidationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new InputValidationService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private InputValidationService()
        {
            // Initialize service
        }
        private readonly LoggingService _logger;
        private readonly Dictionary<string, List<ValidationRule>> _validationRules;

        /// <summary>
        /// Initializes a new instance of the InputValidationService class
        /// </summary>
        private InputValidationService(LoggingService logger)
        {
            _logger = logger;
            _validationRules = new Dictionary<string, List<ValidationRule>>();
            
            // Initialize validation rules
            InitializeValidationRules();
        }

        /// <summary>
        /// Initializes validation rules
        /// </summary>
        private void InitializeValidationRules()
        {
            try
            {
                // Username validation rules
                AddValidationRule("Username", new ValidationRule
                {
                    PropertyName = "Username",
                    ValidationFunc = (value) => !string.IsNullOrWhiteSpace(value?.ToString()),
                    ErrorMessage = "Username cannot be empty."
                });
                
                AddValidationRule("Username", new ValidationRule
                {
                    PropertyName = "Username",
                    ValidationFunc = (value) => value?.ToString().Length >= 3 && value?.ToString().Length <= 20,
                    ErrorMessage = "Username must be between 3 and 20 characters."
                });
                
                AddValidationRule("Username", new ValidationRule
                {
                    PropertyName = "Username",
                    ValidationFunc = (value) => Regex.IsMatch(value?.ToString() ?? "", @"^[a-zA-Z0-9_]+$"),
                    ErrorMessage = "Username can only contain letters, numbers, and underscores."
                });
                
                // Password validation rules
                AddValidationRule("Password", new ValidationRule
                {
                    PropertyName = "Password",
                    ValidationFunc = (value) => !string.IsNullOrWhiteSpace(value?.ToString()),
                    ErrorMessage = "Password cannot be empty."
                });
                
                AddValidationRule("Password", new ValidationRule
                {
                    PropertyName = "Password",
                    ValidationFunc = (value) => value?.ToString().Length >= 8,
                    ErrorMessage = "Password must be at least 8 characters."
                });
                
                AddValidationRule("Password", new ValidationRule
                {
                    PropertyName = "Password",
                    ValidationFunc = (value) =>
                    {
                        string password = value?.ToString() ?? "";
                        bool hasUpperCase = password.Any(char.IsUpper);
                        bool hasLowerCase = password.Any(char.IsLower);
                        bool hasDigit = password.Any(char.IsDigit);
                        bool hasSpecialChar = password.Any(c => !char.IsLetterOrDigit(c));
                        return hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar;
                    },
                    ErrorMessage = "Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character."
                });
                
                // Email validation rules
                AddValidationRule("Email", new ValidationRule
                {
                    PropertyName = "Email",
                    ValidationFunc = (value) => !string.IsNullOrWhiteSpace(value?.ToString()),
                    ErrorMessage = "Email cannot be empty."
                });
                
                AddValidationRule("Email", new ValidationRule
                {
                    PropertyName = "Email",
                    ValidationFunc = (value) => Regex.IsMatch(value?.ToString() ?? "", @"^[^@\s]+@[^@\s]+\.[^@\s]+$"),
                    ErrorMessage = "Invalid email format."
                });
                
                // File path validation rules
                AddValidationRule("FilePath", new ValidationRule
                {
                    PropertyName = "FilePath",
                    ValidationFunc = (value) => !string.IsNullOrWhiteSpace(value?.ToString()),
                    ErrorMessage = "File path cannot be empty."
                });
                
                AddValidationRule("FilePath", new ValidationRule
                {
                    PropertyName = "FilePath",
                    ValidationFunc = (value) => ValidationHelper.IsValidPath(value?.ToString() ?? ""),
                    ErrorMessage = "Invalid file path."
                });
                
                // Directory path validation rules
                AddValidationRule("DirectoryPath", new ValidationRule
                {
                    PropertyName = "DirectoryPath",
                    ValidationFunc = (value) => !string.IsNullOrWhiteSpace(value?.ToString()),
                    ErrorMessage = "Directory path cannot be empty."
                });
                
                AddValidationRule("DirectoryPath", new ValidationRule
                {
                    PropertyName = "DirectoryPath",
                    ValidationFunc = (value) => ValidationHelper.IsValidPath(value?.ToString() ?? ""),
                    ErrorMessage = "Invalid directory path."
                });
                
                // URL validation rules
                AddValidationRule("Url", new ValidationRule
                {
                    PropertyName = "Url",
                    ValidationFunc = (value) => !string.IsNullOrWhiteSpace(value?.ToString()),
                    ErrorMessage = "URL cannot be empty."
                });
                
                AddValidationRule("Url", new ValidationRule
                {
                    PropertyName = "Url",
                    ValidationFunc = (value) =>
                    {
                        string url = value?.ToString() ?? "";
                        return Uri.TryCreate(url, UriKind.Absolute, out Uri uri) &&
                               (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps);
                    },
                    ErrorMessage = "Invalid URL format."
                });
                
                // IP address validation rules
                AddValidationRule("IpAddress", new ValidationRule
                {
                    PropertyName = "IpAddress",
                    ValidationFunc = (value) => !string.IsNullOrWhiteSpace(value?.ToString()),
                    ErrorMessage = "IP address cannot be empty."
                });
                
                AddValidationRule("IpAddress", new ValidationRule
                {
                    PropertyName = "IpAddress",
                    ValidationFunc = (value) => System.Net.IPAddress.TryParse(value?.ToString() ?? "", out _),
                    ErrorMessage = "Invalid IP address format."
                });
                
                // Port validation rules
                AddValidationRule("Port", new ValidationRule
                {
                    PropertyName = "Port",
                    ValidationFunc = (value) =>
                    {
                        if (int.TryParse(value?.ToString(), out int port))
                        {
                            return port >= 1 && port <= 65535;
                        }
                        return false;
                    },
                    ErrorMessage = "Port must be between 1 and 65535."
                });
                
                _logger.Log("Validation rules initialized", LogLevel.INFO);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error initializing validation rules: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Adds a validation rule
        /// </summary>
        /// <param name="propertyName">The property name</param>
        /// <param name="rule">The validation rule</param>
        public void AddValidationRule(string propertyName, ValidationRule rule)
        {
            try
            {
                if (!_validationRules.ContainsKey(propertyName))
                {
                    _validationRules[propertyName] = new List<ValidationRule>();
                }
                
                _validationRules[propertyName].Add(rule);
            }
            catch (Exception ex)
            {
                _logger.Log($"Error adding validation rule: {ex.Message}", LogLevel.ERROR);
            }
        }

        /// <summary>
        /// Validates a property
        /// </summary>
        /// <param name="propertyName">The property name</param>
        /// <param name="value">The property value</param>
        /// <param name="errorMessage">The error message if validation fails</param>
        /// <returns>True if valid, false otherwise</returns>
        public bool ValidateProperty(string propertyName, object value, out string errorMessage)
        {
            errorMessage = null;
            
            try
            {
                // Check if property has validation rules
                if (!_validationRules.ContainsKey(propertyName))
                {
                    return true;
                }
                
                // Apply validation rules
                foreach (ValidationRule rule in _validationRules[propertyName])
                {
                    if (!rule.ValidationFunc(value))
                    {
                        errorMessage = rule.ErrorMessage;
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error validating property: {ex.Message}", LogLevel.ERROR);
                errorMessage = "An error occurred during validation.";
                return false;
            }
        }

        /// <summary>
        /// Validates an object
        /// </summary>
        /// <param name="obj">The object to validate</param>
        /// <param name="propertyNames">The property names to validate</param>
        /// <param name="validationErrors">The validation errors</param>
        /// <returns>True if valid, false otherwise</returns>
        public bool ValidateObject(object obj, string[] propertyNames, out Dictionary<string, string> validationErrors)
        {
            validationErrors = new Dictionary<string, string>();
            
            try
            {
                if (obj == null)
                {
                    validationErrors["Object"] = "Object cannot be null.";
                    return false;
                }
                
                bool isValid = true;
                
                // Validate each property
                foreach (string propertyName in propertyNames)
                {
                    // Get property value
                    object value = GetPropertyValue(obj, propertyName);
                    
                    // Validate property
                    if (!ValidateProperty(propertyName, value, out string errorMessage))
                    {
                        validationErrors[propertyName] = errorMessage;
                        isValid = false;
                    }
                }
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error validating object: {ex.Message}", LogLevel.ERROR);
                validationErrors["Object"] = "An error occurred during validation.";
                return false;
            }
        }

        /// <summary>
        /// Gets a property value from an object
        /// </summary>
        /// <param name="obj">The object</param>
        /// <param name="propertyName">The property name</param>
        /// <returns>The property value</returns>
        private object GetPropertyValue(object obj, string propertyName)
        {
            try
            {
                // Get property info
                var property = obj.GetType().GetProperty(propertyName);
                
                if (property != null)
                {
                    // Get property value
                    return property.GetValue(obj);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error getting property value: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Sanitizes a string input
        /// </summary>
        /// <param name="input">The input to sanitize</param>
        /// <returns>The sanitized input</returns>
        public string SanitizeInput(string input)
        {
            try
            {
                if (string.IsNullOrEmpty(input))
                {
                    return input;
                }
                
                // Remove potentially dangerous characters
                string sanitized = Regex.Replace(input, @"[<>&'\""]", "");
                
                // Trim whitespace
                sanitized = sanitized.Trim();
                
                return sanitized;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sanitizing input: {ex.Message}", LogLevel.ERROR);
                return "";
            }
        }

        /// <summary>
        /// Sanitizes a file path
        /// </summary>
        /// <param name="filePath">The file path to sanitize</param>
        /// <returns>The sanitized file path</returns>
        public string SanitizeFilePath(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    return filePath;
                }
                
                // Remove invalid characters
                string sanitized = string.Join("_", filePath.Split(Path.GetInvalidPathChars()));
                
                return sanitized;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sanitizing file path: {ex.Message}", LogLevel.ERROR);
                return "";
            }
        }

        /// <summary>
        /// Sanitizes a URL
        /// </summary>
        /// <param name="url">The URL to sanitize</param>
        /// <returns>The sanitized URL</returns>
        public string SanitizeUrl(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                {
                    return url;
                }
                
                // Check if URL is valid
                if (Uri.TryCreate(url, UriKind.Absolute, out Uri uri))
                {
                    // Only allow HTTP and HTTPS schemes
                    if (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps)
                    {
                        return uri.AbsoluteUri;
                    }
                }
                
                return "";
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sanitizing URL: {ex.Message}", LogLevel.ERROR);
                return "";
            }
        }
    }

    /// <summary>
    /// Model for validation rules
    /// </summary>
    public class ValidationRule
    {
        /// <summary>
        /// Gets or sets the property name
        /// </summary>
        public string PropertyName { get; set; }
        
        /// <summary>
        /// Gets or sets the validation function
        /// </summary>
        public Func<object, bool> ValidationFunc { get; set; }
        
        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}


