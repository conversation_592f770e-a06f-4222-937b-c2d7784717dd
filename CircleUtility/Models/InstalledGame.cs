// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Text.Json.Serialization;

namespace CircleUtility.Models
{
    /// <summary>
    /// Model for installed games
    /// </summary>
    public class InstalledGame
    {
        /// <summary>
        /// Gets or sets the name of the game
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the installation path
        /// </summary>
        public string InstallPath { get; set; }

        /// <summary>
        /// Gets or sets the platform (Steam, Epic, etc.)
        /// </summary>
        public string Platform { get; set; }

        /// <summary>
        /// Gets or sets the platform-specific ID
        /// </summary>
        public string PlatformId { get; set; }

        /// <summary>
        /// Gets or sets the executable name
        /// </summary>
        public string ExecutableName { get; set; }

        /// <summary>
        /// Gets or sets the date the game was detected
        /// </summary>
        public DateTime DetectionDate { get; set; }

        /// <summary>
        /// Gets or sets the last played date
        /// </summary>
        public DateTime? LastPlayedDate { get; set; }

        /// <summary>
        /// Gets or sets the total play time in minutes
        /// </summary>
        public long PlayTimeMinutes { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the game has been optimized
        /// </summary>
        public bool IsOptimized { get; set; }

        /// <summary>
        /// Gets or sets the date the game was last optimized
        /// </summary>
        public DateTime? LastOptimizedDate { get; set; }

        /// <summary>
        /// Gets or sets the game profile name used for optimization
        /// </summary>
        public string OptimizationProfile { get; set; }

        /// <summary>
        /// Gets or sets the game version
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the game size in bytes
        /// </summary>
        public long SizeBytes { get; set; }

        /// <summary>
        /// Gets or sets the game icon path
        /// </summary>
        public string IconPath { get; set; }

        /// <summary>
        /// Gets or sets the game banner path
        /// </summary>
        public string BannerPath { get; set; }

        /// <summary>
        /// Gets or sets the game category
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the game tags
        /// </summary>
        public string[] Tags { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the game is a favorite
        /// </summary>
        public bool IsFavorite { get; set; }

        /// <summary>
        /// Gets or sets the user notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets the formatted detection date
        /// </summary>
        [JsonIgnore]
        public string FormattedDetectionDate => DetectionDate.ToString("MMM dd, yyyy");

        /// <summary>
        /// Gets the formatted last played date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastPlayedDate => LastPlayedDate?.ToString("MMM dd, yyyy") ?? "Never";

        /// <summary>
        /// Gets the formatted last optimized date
        /// </summary>
        [JsonIgnore]
        public string FormattedLastOptimizedDate => LastOptimizedDate?.ToString("MMM dd, yyyy") ?? "Never";

        /// <summary>
        /// Gets the formatted play time
        /// </summary>
        [JsonIgnore]
        public string FormattedPlayTime
        {
            get
            {
                if (PlayTimeMinutes < 60)
                {
                    return $"{PlayTimeMinutes} minutes";
                }
                else if (PlayTimeMinutes < 60 * 24)
                {
                    int hours = (int)(PlayTimeMinutes / 60);
                    int minutes = (int)(PlayTimeMinutes % 60);
                    return $"{hours} hours, {minutes} minutes";
                }
                else
                {
                    int days = (int)(PlayTimeMinutes / (60 * 24));
                    int hours = (int)((PlayTimeMinutes % (60 * 24)) / 60);
                    int minutes = (int)(PlayTimeMinutes % 60);
                    return $"{days} days, {hours} hours, {minutes} minutes";
                }
            }
        }

        /// <summary>
        /// Gets the formatted size
        /// </summary>
        [JsonIgnore]
        public string FormattedSize
        {
            get
            {
                if (SizeBytes < 1024)
                {
                    return $"{SizeBytes} B";
                }
                else if (SizeBytes < 1024 * 1024)
                {
                    return $"{SizeBytes / 1024.0:F2} KB";
                }
                else if (SizeBytes < 1024 * 1024 * 1024)
                {
                    return $"{SizeBytes / (1024.0 * 1024.0):F2} MB";
                }
                else
                {
                    return $"{SizeBytes / (1024.0 * 1024.0 * 1024.0):F2} GB";
                }
            }
        }

        /// <summary>
        /// Gets the optimization status
        /// </summary>
        [JsonIgnore]
        public string OptimizationStatus => IsOptimized ? "Optimized" : "Not Optimized";

        /// <summary>
        /// Gets the optimization status color
        /// </summary>
        [JsonIgnore]
        public string OptimizationStatusColor => IsOptimized ? "#00FF00" : "#FF0000";
    }
}
