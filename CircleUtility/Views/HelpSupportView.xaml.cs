using System.Windows;
using System.Windows.Controls;
using CircleUtility.ViewModels;

namespace CircleUtility.Views
{
    public partial class HelpSupportView : UserControl
    {
        private readonly HelpSupportViewModel _viewModel;
        public HelpSupportView(string username = null)
        {
            InitializeComponent();
            _viewModel = new HelpSupportViewModel(username);
            DataContext = _viewModel;
            SendFeedbackButton.Click += SendFeedbackButton_Click;
        }

        private void SendFeedbackButton_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.FeedbackText = FeedbackTextBox.Text;
            _viewModel.IsAnonymous = AnonymousCheckBox.IsChecked == true;
            _viewModel.SendFeedbackCommand.Execute(null);
            if (_viewModel.IsStatusVisible)
            {
                MessageBox.Show(_viewModel.StatusMessage, "Feedback", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
} 